# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Schoolroom is a comprehensive educational platform monorepo using Turborepo with 5 main applications:
- **Student App** (`apps/student`) - Learning platform for students
- **Teacher App** (`apps/teacher`) - Teaching management interface  
- **AI Course Tool** (`apps/ai-produce-course`) - AI-powered course content generation
- **Operations Management** (`apps/operations`) - Backend admin dashboard
- **Question Bank** (`apps/question-bank`) - Question management system

## Technology Stack

- **Framework**: React 19 + Next.js 15 + TypeScript 5.8
- **Package Manager**: pnpm + Turborepo for monorepo management
- **UI Libraries**: Tailwind CSS v4 + Ant Design + Radix UI
- **Math Rendering**: KaTeX + MathJax for complex mathematical formulas
- **Animation**: Framer Motion + Lottie React
- **Video**: Remotion for video rendering and playback

## Development Commands

### Setup
```bash
pnpm install
```

### Development
```bash
# Run specific app
pnpm dev -F=student
pnpm dev -F=teacher
pnpm dev -F=ai-produce-course
pnpm dev -F=operations
pnpm dev -F=question-bank

# Run all apps
pnpm dev
```

### Build
```bash
# Build specific app
pnpm build:prod -F=student
pnpm build:prod -F=teacher

# Build all
pnpm build:prod
```

### Testing
```bash
# Run tests for specific package
pnpm test -F=@schoolroom/core
pnpm test -F=@schoolroom/ui

# API tests
pnpm test:api
```

### Linting & Type Checking
```bash
pnpm lint
pnpm type-check
```

## Architecture

### Shared Packages (`packages/`)
- `@schoolroom/core` - Core business logic and utilities
- `@schoolroom/ui` - Shared UI components
- `@schoolroom/lib` - Common libraries and utilities
- `@schoolroom/config` - Shared configuration (ESLint, TypeScript, Tailwind)
- `@schoolroom/guide-core` - Interactive guide components
- `@schoolroom/whiteboard` - Drawing/whiteboard functionality
- `@schoolroom/video` - Video processing and playback

### Key Features
- **Math Formula Support**: Extensive KaTeX and MathJax integration for mathematical content
- **Interactive Whiteboard**: Custom drawing and annotation tools
- **Real-time Communication**: Socket.IO integration for live features
- **Multi-language Support**: i18n implementation
- **Permission System**: Role-based access control

### Application Structure
Each app follows Next.js 15 structure with:
- App Router (`app/` directory)
- Server Components by default
- Client Components marked with `"use client"`
- Shared components in `components/`
- API routes in `app/api/`

## Important Notes

- Use `pnpm` for package management (not npm or yarn)
- All apps share common packages - check existing functionality before creating new
- Math formula rendering requires special handling - see existing KaTeX/MathJax implementations
- Video features use Remotion - check `@schoolroom/video` package for utilities
- UI components should be added to `@schoolroom/ui` for reusability across apps
- Configuration changes often need to be made in multiple places due to monorepo structure

## Development Workflow

1. Changes to shared packages require rebuilding dependent apps
2. Use Turborepo's cache for faster builds: `pnpm build --cache-dir=.turbo`
3. Test changes across multiple apps when modifying shared packages
4. Check both student and teacher apps for UI consistency
5. Math formula changes require testing in both editing and display modes