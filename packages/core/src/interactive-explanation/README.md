# 互动讲题浮层组件

## 📋 组件概述

互动讲题浮层组件是一个可拖拽的浮层容器，用于承载四阶段互动学习内容。组件基于react-draggable实现，提供流畅的拖拽体验和位置记忆功能。

## 🎯 设计特性

### 核心功能
- ✅ **高度拖拽调整**：手动实现上下拖拽调整浮层高度
- ✅ **移动端支持**：支持触摸事件，PC端和移动端都可正常拖拽
- ✅ **高度记忆**：自动保存和恢复浮层高度
- ✅ **边界约束**：确保浮层高度在合理范围内(200px - 90%屏幕高度)
- ✅ **防止滚动穿透**：浮层显示时阻止背景页面滚动
- ✅ **固定底部按钮**：底部按钮固定，中间内容可滚动
- ✅ **多种关闭方式**：关闭按钮、ESC键、点击遮罩
- ✅ **智能列表渲染**：支持复杂Markdown格式，自动提取加粗标题和嵌套列表
- ✅ **列表类型控制**：后端返回无序格式，前端可控制渲染为有序/无序列表
- ✅ **语义化样式**：所有元素都有语义化class便于调试

### 布局结构
```
FloatingPanel
├── 遮罩层 (.floating-panel-overlay)
└── 浮层容器 (fixed bottom-0, 可调整高度)
    ├── 头部导航 (.floating-panel-header)
    │   ├── 左侧标题
    │   ├── 中间拖拽手柄 (.floating-panel-drag-handle) - 支持鼠标和触摸
    │   └── 关闭按钮 (.floating-panel-close-btn)
    ├── 内容区域 (.floating-panel-content) - 可滚动
    └── 底部按钮 (.floating-panel-footer) - 固定在底部
        └── 下一步按钮 (.floating-panel-next-btn)
```

## 🚀 使用方法

### 基础用法

```tsx
import { InteractiveExplanationFloatingPanel } from '../view/interactive-explanation-floating-panel';

function MyComponent() {
  const [isVisible, setIsVisible] = useState(false);

  return (
    <>
      <button onClick={() => setIsVisible(true)}>
        打开互动讲题
      </button>
      
      <InteractiveExplanationFloatingPanel
        isVisible={isVisible}
        onClose={() => setIsVisible(false)}
      >
        {/* 你的内容组件 */}
        <div className="p-6">
          <h2>互动讲题内容</h2>
        </div>
      </InteractiveExplanationFloatingPanel>
    </>
  );
}
```

### Props API

| 属性 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| `isVisible` | `boolean` | - | 控制浮层显示/隐藏 |
| `onClose` | `() => void` | - | 关闭浮层的回调函数 |
| `children` | `React.ReactNode` | - | 浮层内容 |
| `className` | `string` | - | 自定义样式类名 |

## 🎨 设计规范

### Figma设计对照

根据提供的Figma设计稿 (https://www.figma.com/design/j6wNSQxDFuZHscu1SVMuIA/AI%E8%AF%BE%E4%BD%93%E9%AA%8C%E7%89%88?node-id=5975-6178&m=dev)：

#### ✅ 已实现的设计元素
- **整体布局**：头部导航 + 内容区域 + 底部按钮
- **拖拽指示器**：三个小圆点作为拖拽手柄
- **关闭按钮**：右上角X按钮，使用press-button组件
- **阴影效果**：深度阴影(shadow-2xl)
- **圆角设计**：统一使用rounded-xl
- **底部按钮**：橙色"下一步"按钮

#### 🎯 设计细节
- **浮层尺寸**：宽度66.67%(2/3屏幕)，最小高度200px
- **拖拽区域**：整个头部区域可拖拽
- **边框样式**：浅灰色边框(border-gray-200)
- **背景色**：纯白色背景
- **分割线**：头部和底部使用浅灰色分割线

## 🔧 技术实现

### 核心依赖
- **原生事件处理**: 手动实现鼠标和触摸拖拽，无第三方依赖
- **@repo/ui/components/press-button**: 按钮组件
- **lucide-react**: 图标组件
- **tailwindcss**: 样式系统

### 关键特性实现

#### 1. 高度拖拽功能
```tsx
// 鼠标事件处理
const handleMouseDown = useCallback((e: React.MouseEvent) => {
  setIsDragging(true);
  setDragStartY(e.clientY);
  setDragStartHeight(height);
  e.preventDefault();
}, [height]);

// 触摸事件处理
const handleTouchStart = useCallback((e: React.TouchEvent) => {
  if (e.touches.length > 0) {
    setIsDragging(true);
    setDragStartY(e.touches[0].clientY);
    setDragStartHeight(height);
    e.preventDefault();
  }
}, [height]);
```

#### 2. 高度约束
```tsx
const constrainHeight = useCallback((newHeight: number): number => {
  if (typeof window === 'undefined') return newHeight;

  const maxHeight = window.innerHeight * MAX_HEIGHT_RATIO;
  return Math.max(MIN_HEIGHT, Math.min(newHeight, maxHeight));
}, []);
```

#### 3. 高度持久化
```tsx
// 保存高度
const saveHeight = useCallback((newHeight: number) => {
  localStorage.setItem(STORAGE_KEY, newHeight.toString());
}, []);

// 恢复高度
useEffect(() => {
  const savedHeight = localStorage.getItem(STORAGE_KEY);
  if (savedHeight) {
    const parsed = parseInt(savedHeight, 10);
    if (parsed >= MIN_HEIGHT) {
      setHeight(parsed);
    }
  }
}, []);
```

#### 4. 防止滚动穿透
```tsx
useEffect(() => {
  if (isVisible) {
    document.body.style.overflow = 'hidden';
    return () => {
      document.body.style.overflow = '';
    };
  }
}, [isVisible]);
```

## 🧪 测试和调试

### Demo页面
访问 `/interactive-explanation/demo` 查看组件演示和功能测试。

### 语义化Class
所有关键元素都有语义化class，便于调试和测试：
- `.floating-panel-overlay` - 遮罩层
- `.floating-panel-header` - 头部区域
- `.floating-panel-drag-handle` - 拖拽手柄
- `.floating-panel-close-btn` - 关闭按钮
- `.floating-panel-content` - 内容区域
- `.floating-panel-footer` - 底部区域
- `.floating-panel-next-btn` - 下一步按钮

### 调试技巧
1. **拖拽问题**：检查`.floating-panel-drag-handle`元素是否正确设置
2. **位置问题**：查看localStorage中的`interactive-explanation-position`数据
3. **样式问题**：使用语义化class快速定位元素
4. **响应式问题**：测试不同屏幕尺寸下的表现

## 📝 开发注意事项

### 必须遵循的规范
1. **语义化Class**：所有新增元素必须添加语义化class
2. **TypeScript类型**：严格使用类型定义，避免any
3. **样式管理**：统一使用TailwindCSS，避免内联样式
4. **组件复用**：仅复用press-button和FormatMath组件
5. **错误处理**：添加适当的错误边界和异常处理

### 性能优化
1. **useCallback**：所有事件处理函数使用useCallback包装
2. **React.memo**：考虑对子组件使用memo优化
3. **懒加载**：大型内容组件考虑懒加载
4. **防抖节流**：拖拽事件考虑节流处理

## 🔄 后续开发计划

### Phase 1: 基础完善
- [ ] 添加拖拽性能优化(节流)
- [ ] 完善错误边界处理
- [ ] 添加单元测试

### Phase 2: 功能扩展
- [ ] 集成头部导航组件
- [ ] 集成底部按钮组件
- [ ] 添加内容区域滚动管理

### Phase 3: 体验优化
- [ ] 添加拖拽动画效果
- [ ] 优化响应式布局
- [ ] 添加无障碍支持

---

**组件版本**: v1.0.0  
**创建时间**: 2025-01-07  
**最后更新**: 2025-01-07  
**维护人员**: 前端开发团队
