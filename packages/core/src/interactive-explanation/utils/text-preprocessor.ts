/**
 * 文本预处理工具函数
 * 统一处理数学公式转义、加粗标记、列表格式等Markdown格式
 */

/**
 * 预处理FormatMath组件的内容
 * 处理所有Markdown格式：数学公式转义、加粗、列表等
 * @param htmlContent 原始HTML内容
 * @param listType 列表类型：'ordered' | 'unordered' | 'auto'
 * @returns 预处理后的HTML内容
 */
export const preprocessFormatMath = (htmlContent: string, listType: 'ordered' | 'unordered' | 'auto' = 'auto'): string => {
  if (!htmlContent) return htmlContent;

  let processedText = htmlContent;

  // 1. 处理数学公式转义：\\( 转换为 \(，\\) 转换为 \)
  processedText = processedText.replace(/\\\\\(/g, '\\(');
  processedText = processedText.replace(/\\\\\)/g, '\\)');

  // 2. 处理加粗标记：**文本** 转换为 <strong>文本</strong>
  processedText = processedText.replace(/\*\*(.+?)\*\*/g, '<strong>$1</strong>');

  // 3. 处理列表：根据listType参数决定渲染方式
  if (listType === 'ordered') {
    // 强制有序列表：将 - 或 * 开头的行也转换为有序列表
    let orderCounter = 0;
    processedText = processedText.replace(/^(\s*)([\*\-]|\d+\.)\s+(.+)$/gm, (_, indent, __, content) => {
      orderCounter++;

      const level = Math.floor(indent.length / 2);
      const paddingLeft = level * 20;
      return `<div style="display: flex; gap: 8px;  padding-left: ${paddingLeft}px;margin-top: ${orderCounter === 1 ? '0' : '1rem'};">
        <div style="flex-shrink: 0; display: flex; align-items: center; justify-content: center; width: 28px; height: 20px; background-color: rgba(51,48,45,0.1); border-radius: 4px; font-size: 15px; font-weight: bold; margin-top: 4px;">${orderCounter}</div>
        <div style="flex: 1;">${content}</div>
      </div>`;
    });
  } else if (listType === 'unordered') {
    // 强制无序列表：将数字开头的行也转换为无序列表
    processedText = processedText.replace(/^(\s*)([\*\-]|\d+\.)\s+(.+)$/gm, (_, indent, __, content) => {
      const level = Math.floor(indent.length / 2);
      const paddingLeft = level * 20;
      return `<div style="display: flex; gap: 8px;  padding-left: ${paddingLeft}px; position: relative;">
        <div style="flex-shrink: 0; width: 4px; height: 4px; background-color: rgba(51, 48, 45, 0.95); border-radius: 50%; position: absolute; left: ${paddingLeft}px; top: 1em; transform: translateY(-50%);"></div>
        <div style="flex: 1; padding-left: 16px;">${content}</div>
      </div>`;
    });
  } else {
    // auto模式：保持原有格式
    // 处理无序列表：- 或 * 开头的行
    processedText = processedText.replace(/^(\s*)([\*\-])\s+(.+)$/gm, (_, indent, __, content) => {
      const level = Math.floor(indent.length / 2);
      const paddingLeft = level * 20;
      return `<div style="display: flex; gap: 8px;  padding-left: ${paddingLeft}px; position: relative;">
        <div style="flex-shrink: 0; width: 4px; height: 4px; background-color: rgba(51, 48, 45, 0.95); border-radius: 50%; position: absolute; left: ${paddingLeft}px; top: 1em; transform: translateY(-50%);"></div>
        <div style="flex: 1; padding-left: 16px;">${content}</div>
      </div>`;
    });

    // 处理有序列表：数字. 开头的行
    let orderCounter = 0;
    processedText = processedText.replace(/^(\s*)(\d+)\.\s+(.+)$/gm, (_, indent, __, content) => {
      orderCounter++;
      const level = Math.floor(indent.length / 2);
      const paddingLeft = level * 20;
      return `<div style="display: flex; gap: 8px;  padding-left: ${paddingLeft}px;">
        <div style="flex-shrink: 0; display: flex; align-items: center; justify-content: center; width: 28px; height: 20px; background-color: rgba(51,48,45,0.1); border-radius: 4px; font-size: 15px; font-weight: bold; margin-top: 4px;">${orderCounter}</div>
        <div style="flex: 1;">${content}</div>
      </div>`;
    });
  }

  // processedText = processedText.replace(/\n\n/g, '<br><br>');
  // processedText = processedText.replace(/\n/g, '<br>');

  return processedText;
};
