"use client";

import React from 'react';
import { FormatMath } from '@repo/core/exercise/components/format-math';
import { preprocessFormatMath } from '../utils/text-preprocessor';
import { cn } from '@repo/ui/lib/utils';

interface EnhancedFormatMathProps {
  htmlContent: string;
  questionId?: string;
  ossHost?: string;
  resourceApiHost?: string;
  className?: string;
  listType?: 'ordered' | 'unordered' | 'auto'; // 强制指定列表类型，默认auto
}

/**
 * 增强版FormatMath组件
 * 自动对输入内容进行Markdown预处理（数学公式转义、加粗等）
 */
export const EnhancedFormatMath: React.FC<EnhancedFormatMathProps> = ({
  htmlContent,
  questionId,
  ossHost,
  resourceApiHost,
  className,
  listType = 'auto'
}) => {
  // 自动预处理内容
  const processedContent = preprocessFormatMath(htmlContent, listType);

  return (
    <FormatMath
      htmlContent={processedContent}
      questionId={questionId}
      ossHost={ossHost}
      resourceApiHost={resourceApiHost}
      className={cn(className, 'enhanced-format-math text-text-1')}
    />
  );
};

export default EnhancedFormatMath;
