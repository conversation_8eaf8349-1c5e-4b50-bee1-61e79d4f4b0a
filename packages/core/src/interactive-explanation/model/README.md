# Interactive Explanation Model

互动解题记录上报功能的数据模型层，提供互动讲题记录的数据提交接口。

## 🎯 功能概述

互动解题记录模块用于：
- 上报学生在互动讲题过程中的操作记录
- 跟踪当前进度和分步指导完成情况
- 记录每个步骤的作答状态

## 📋 可用 Hooks

| Hook 名称 | 描述 | 主要接口 | 依赖 API |
|-----------|------|----------|----------|
| `useReportInteractiveExplanation` | 上报互动解题记录 | `reportInteractiveExplanation, isReporting, reportError` | POST `/api/v1/study_session/ai_explantion_record/report` |

## 📊 核心数据结构

### 请求参数类型
- `ReportInteractiveExplanationPayload` - 上报互动解题记录的完整参数
- `InteractiveExplanationRecord` - 互动解题记录数据
- `StepByStepGuideItem` - 分步指导项

### 响应数据类型
- `ApiResponse` - API响应数据（空对象）
- `ReportResult` - 处理结果包装

## 🔧 使用示例

### 基础使用
```typescript
import { useReportInteractiveExplanation } from '@/models/interactive-explanation';

function InteractiveExplanationComponent() {
  const { 
    reportInteractiveExplanation, 
    isReporting, 
    reportError 
  } = useReportInteractiveExplanation();

  const handleReport = async () => {
    try {
      await reportInteractiveExplanation({
        questionId: "question_001",
        studySessionId: 3001,
        record: {
          current_progress: 2,
          stepByStepGuide: [
            { isAnswer: true },
            { isAnswer: false },
            { isAnswer: true }
          ]
        }
      });
      console.log('互动解题记录上报成功');
    } catch (error) {
      console.error('上报失败:', error);
    }
  };

  return (
    <button onClick={handleReport} disabled={isReporting}>
      {isReporting ? '上报中...' : '上报解题记录'}
    </button>
  );
}
```

### 完整流程示例
```typescript
import { useReportInteractiveExplanation } from '@/models/interactive-explanation';

function InteractiveLessonComponent() {
  const { 
    reportInteractiveExplanation, 
    isReporting, 
    reportError 
  } = useReportInteractiveExplanation();

  const [currentProgress, setCurrentProgress] = useState(0);
  const [stepRecords, setStepRecords] = useState<{ isAnswer: boolean }[]>([]);

  const handleStepComplete = (stepIndex: number, isAnswered: boolean) => {
    // 更新步骤记录
    const newRecords = [...stepRecords];
    newRecords[stepIndex] = { isAnswer: isAnswered };
    setStepRecords(newRecords);

    // 更新当前进度
    setCurrentProgress(stepIndex + 1);

    // 自动上报记录
    reportInteractiveExplanation({
      questionId: "current_question_id",
      studySessionId: 3001,
      record: {
        current_progress: stepIndex + 1,
        stepByStepGuide: newRecords
      }
    });
  };

  if (reportError) {
    return <div>上报失败: {reportError.message}</div>;
  }

  return (
    <div>
      <div>当前进度: {currentProgress}</div>
      <div>上报状态: {isReporting ? '上报中...' : '就绪'}</div>
      {/* 互动步骤组件 */}
    </div>
  );
}
```

## 📝 数据结构详情

### ReportInteractiveExplanationPayload
```typescript
interface ReportInteractiveExplanationPayload {
  questionId: string;           // 题目ID
  studySessionId: number;       // 课中会话ID（非负数）
  record: {
    current_progress: number;   // 当前进度（非负数）
    stepByStepGuide: Array<{
      isAnswer?: boolean;       // 是否作答
    }>;
  };
}
```

## ⚠️ 使用注意事项

1. **数据约束**: 
   - `studySessionId` 和 `current_progress` 必须为非负数
   - `questionId` 必须为有效的题目ID字符串

2. **错误处理**: Hook提供 `reportError` 状态用于错误处理

3. **加载状态**: 使用 `isReporting` 状态显示上报进度

4. **API响应**: API返回空对象，主要关注操作成功/失败状态

## 🧪 测试覆盖

- ✅ 基础功能测试 - 验证上报功能正常工作
- ✅ 参数校验测试 - 确保参数类型正确
- ✅ 错误处理测试 - 验证异常情况处理
- ✅ 状态管理测试 - 测试loading和error状态

## 📚 相关文档

- [API文档](../../../docs/互动讲题/互动讲题-api.md)
- [Model层开发指南](../../../.cursor/rules/generate/gen-model-guide.md)