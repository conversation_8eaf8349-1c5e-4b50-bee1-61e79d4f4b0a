'use client';

import { post } from '../../../../../apps/stu/app/utils/fetcher';
import useSWRMutation from 'swr/mutation';

// ==================== 类型定义 ====================

export interface StepByStepGuideItem {
  /** 是否作答 */
  isAnswer?: boolean;
}

export interface InteractiveExplanationRecord {

  /** 逐步讲解进度 */
  stepByStepGuideCurrentProgress: number;
  /** 当前进度，非负数 */
  currentProgress: number;
  /** 分步指导记录 */
  stepByStepGuide: StepByStepGuideItem[];
}

export interface ReportInteractiveExplanationPayload {
  /** 题目id */
  questionId: string;
  /** 课中会话id，非负数 */
  studySessionId: number;
  /** 互动解题记录 */
  record: InteractiveExplanationRecord;
  /** 版本id */
  version: number;
}

export interface ApiResponse {
  // API返回空对象，保持简单结构
}

export interface ReportResult {
  success: boolean;
  message?: string;
}

// ==================== Hook 实现 ====================

function handleApiError(error: any, defaultMessage = "网络请求失败") {
  return error?.message?.message || error?.message || defaultMessage;
}

export function useReportInteractiveExplanation() {
  const { trigger, isMutating, error } = useSWRMutation(

    '/api/v1/study_session/ai_explanation_record/report',
    async (url, { arg }: { arg: ReportInteractiveExplanationPayload }) => {
      try {
        const response = await post<ApiResponse>(url, { arg });
        return {
          success: true,
          message: '互动解题记录上报成功',
          data: response
        };
      } catch (err) {
        const errorMessage = handleApiError(err, "互动解题记录上报失败");
        throw new Error(errorMessage);
      }
    }
  );

  return {
    reportInteractiveExplanation: trigger,
    isReporting: isMutating,
    reportError: error
  };
}