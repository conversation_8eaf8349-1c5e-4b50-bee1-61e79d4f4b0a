/**
 * 互动讲题模块的数据模型类型定义
 * 复用exercise模块中的AiExplanation和AiExplanationRecord类型
 */

// 互动讲题特有的类型定义
export interface InteractiveExplanationProps {
  /** 是否显示浮层 */
  isVisible: boolean;
  /** 关闭浮层回调 */
  onClose: () => void;
  /** 初始数据 */
  initialData?: any;
  /** 初始记录 */
  initialRecord?: any;
  /** 进度变更回调 */
  onProgressChange?: (record: any) => void;
  /** 答题回调 */
  onAnswer?: (stepIndex: number, isCorrect: boolean) => void;
  /** 题目ID */
  questionId: string;
  /** 学习会话ID */
  studySessionId: number;
}

// 进度上报相关类型
export interface ReportInteractiveExplanationPayload {
  /** 题目id */
  questionId: string;
  /** 课中会话id，非负数 */
  studySessionId: number;
  /** 互动解题记录 */
  record: any;
}

export interface ApiResponse {
  // API返回空对象，保持简单结构
}

export interface ReportResult {
  success: boolean;
  message?: string;
}
