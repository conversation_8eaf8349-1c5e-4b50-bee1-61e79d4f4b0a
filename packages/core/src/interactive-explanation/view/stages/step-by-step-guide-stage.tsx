"use client";

import ExplanationIcon from "@repo/core/public/assets/interactive-explanation/explanation.svg";
import { AiExplanation, AiExplanationRecord } from "@repo/core/types";
import { cn } from "@repo/ui/lib/utils";
import React from "react";
import ContentCard from "../components/content-card";
import GuideText from "../components/guide-text";
import PText from "../components/p-text";
import StepByStepNavigation from "../components/step-by-step-navigation";

interface StepByStepGuideStageProps {
  data?: AiExplanation["stepByStepGuide"];
  className?: string;
  record?: AiExplanationRecord; // 互动记录
  onStepProgress?: (stepIndex: number) => void; // 步骤进度回调
  onAnswer?: (stepIndex: number, isCorrect: boolean) => void; // 答题回调
}

export const StepByStepGuideStage: React.FC<StepByStepGuideStageProps> = ({
  data,
  className,
  record,
  onStepProgress,
  onAnswer,
}) => {
  // 使用record中的当前步骤，如果没有则默认为1
  const currentStep = record?.stepByStepGuideCurrentProgress
    ? record.stepByStepGuideCurrentProgress + 1
    : 1;

  // 从record中获取已完成的步骤
  const completedSteps =
    record?.stepByStepGuide.reduce(
      (acc, step, index) => {
        if (step.isAnswer) {
          acc[index + 1] = true;
        }
        return acc;
      },
      {} as Record<number, boolean>
    ) || {};

  const handleStepChange = (step: number) => {
    // 通知父组件步骤变更
    onStepProgress?.(step - 1); // 转换为0基索引
  };

  const handleCorrectAnswer = (stepIndex: number) => {
    // 通知父组件答题结果
    onAnswer?.(stepIndex - 1, true); // 转换为0基索引
  };

  const renderStepContent = () => {
    if (!data?.steps || data.steps.length === 0) {
      return (
        <div className="step-content p-6">
          <div className="rounded-lg border border-[#FFEAA7] bg-[#FFF3CD] p-4">
            <p className="text-[#856404]">暂无步骤数据</p>
          </div>
        </div>
      );
    }

    const stepData = data.steps[currentStep - 1];
    if (!stepData) {
      return (
        <div className="step-content p-6">
          <div className="rounded-lg border border-[#FFEAA7] bg-[#FFF3CD] p-4">
            <p className="text-[#856404]">未知步骤</p>
          </div>
        </div>
      );
    }

    // 获取当前步骤的答题状态
    const stepRecord = record?.stepByStepGuide[currentStep - 1];
    const isAnswered = stepRecord?.isAnswer || false;
    const stepId = `step-${currentStep}`;

    // 获取引导语：优先使用record中的guideText
    const guideText = stepRecord?.guideText || "";

    return (
      <div className="step-content step-content-wrapper px-6">
        <GuideText text={guideText} />
        <PText text="请你想一想" className="mb-3" />

        {/* 思考题部分 - 使用ContentCard问题模式 */}
        <div className="think-about-section mb-6">
          <ContentCard
            mode="question"
            answerConfig={{
              questionData: {
                questionStem: `${stepData.thinkAboutIt.questionStem}`,
                questionOptionList: stepData.thinkAboutIt.questionOptionList,
                correctAnswers:
                  stepData.thinkAboutIt.questionAnswer.answerOptionList.map(
                    (a) => a.optionKey || ""
                  ),
              },
              onCorrectAnswer: () => handleCorrectAnswer(currentStep),
              // 状态保持：如果已经答过题，恢复正确状态
              initialIsCorrect: isAnswered ? true : null,
              initialSelectedOption: isAnswered
                ? stepData.thinkAboutIt.questionAnswer.answerOptionList[0]
                    ?.optionKey || ""
                : "",
              stepId: stepId,
            }}
            className="mb-4"
          />

          {/* 完成状态提示 */}
          {completedSteps[currentStep] && (
            <ContentCard
              icon={<ExplanationIcon />}
              title="解析"
              content={stepData.thinkAboutIt.questionExplanation}
            />
          )}
        </div>

        <PText text="一起算一算" className="mb-3" />

        {/* 一起算算部分 */}
        <div className="calculate-together-section">
          <ContentCard content={stepData.calculateTogether} />
        </div>
      </div>
    );
  };

  return (
    <div className={cn("step-by-step-guide-stage", className)}>
      {/* 二级导航 */}
      <div className="step-navigation-wrapper mb-12 px-6 pt-0">
        <StepByStepNavigation
          currentStep={currentStep}
          onStepChange={handleStepChange}
          steps={data?.steps}
        />
      </div>

      {/* 步骤内容 */}
      <div className="step-content-wrapper">{renderStepContent()}</div>
    </div>
  );
};

export default StepByStepGuideStage;
