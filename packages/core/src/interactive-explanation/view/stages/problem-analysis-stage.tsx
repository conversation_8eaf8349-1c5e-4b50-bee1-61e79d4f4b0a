"use client";

import React from 'react';
import { cn } from '@repo/ui/lib/utils';
import GuideText from '../components/guide-text';
import ContentCard from '../components/content-card';
import KeyInfoIcon from '@repo/core/public/assets/interactive-explanation/key-info.svg';

interface ProblemAnalysisData {
  guideText: string;
  analysisItems: string; // 改为字符串，用分隔符分割
  keyInfo: {
    title: string;
    content: string;
    icon?: React.ReactNode;
  };
}

interface ProblemAnalysisStageProps {
  data: ProblemAnalysisData;
  className?: string;
}

export const ProblemAnalysisStage: React.FC<ProblemAnalysisStageProps> = ({
  data,
  className
}) => {
  return (
    <div className={cn("problem-analysis-stage gap-y-6 mt-6", className)}>
      {/* 虚拟导师引导语 */}
      <GuideText text={data.guideText} />

      {/* 题目条件分析 - 有序列表 */}
      <ContentCard
        content={data.analysisItems}
        className="mb-6"
      />

      {/* 关键信息卡片 */}
      <ContentCard
        title={data.keyInfo.title}
        content={data.keyInfo.content}
        icon={<KeyInfoIcon />}
      />
    </div>
  );
};

export default ProblemAnalysisStage;
