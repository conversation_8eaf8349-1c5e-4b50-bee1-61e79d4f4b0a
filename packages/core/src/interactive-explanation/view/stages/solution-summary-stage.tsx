"use client";

import React from 'react';
import { cn } from '@repo/ui/lib/utils';
import GuideText from '../components/guide-text';
import ContentCard from '../components/content-card';
import FinalAnswerIcon from '@repo/core/public/assets/interactive-explanation/final-answer.svg';

interface SolutionSummaryData {
  guideText: string;
  solutionSteps: string; // 改为字符串，用分隔符分割
  finalAnswer: string;
}

interface SolutionSummaryStageProps {
  data: SolutionSummaryData;
  className?: string;
}

export const SolutionSummaryStage: React.FC<SolutionSummaryStageProps> = ({
  data,
  className
}) => {
  return (
    <div className={cn("solution-summary-stage mt-6", className)}>
      {/* 虚拟导师引导语 */}
      <GuideText text={data.guideText} />

      {/* 解题思路卡片 - 有序列表 */}
      <ContentCard
        content={data.solutionSteps}
        listType="ordered"
        className="mb-8"
      />

      {/* 最终答案卡片 */}
      <ContentCard
        title="最终答案"
        content={data.finalAnswer}
        icon={<FinalAnswerIcon />}
      />
    </div>
  );
};

export default SolutionSummaryStage;
