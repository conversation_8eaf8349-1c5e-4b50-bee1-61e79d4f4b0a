"use client";

import React from 'react';
import { cn } from '@repo/ui/lib/utils';
import GuideText from '../components/guide-text';
import ContentCard from '../components/content-card';
import TargetIcon from '@repo/core/public/assets/interactive-explanation/target.svg';
import ExamPointIcon from '@repo/core/public/assets/interactive-explanation/exam-point.svg';
import SolutionBreakthroughIcon from '@repo/core/public/assets/interactive-explanation/breakthrough.svg';
import PText from '../components/p-text';

interface ExamPointAnalysisData {
  guideText: string;
  target: string;
  examPoint: string;
  solutionBreakthrough: string;
}

interface ExamPointAnalysisStageProps {
  data: ExamPointAnalysisData;
  className?: string;
}

export const ExamPointAnalysisStage: React.FC<ExamPointAnalysisStageProps> = ({
  data,
  className
}) => {
  return (
    <div className={cn("exam-point-analysis-stage gap-y-6  mt-6", className)}>
      {/* 虚拟导师引导语 */}
      <GuideText text={data.guideText} />
      <PText text="首先，我们需要明确目标：" className="mb-4" />

      {/* 目标卡片 */}
      <ContentCard
        listType="ordered"
        title="目标"
        content={data.target}
        icon={<TargetIcon />}
      />

      <PText text="其次，我们需得知核心考点：" className="mb-4 mt-8" />

      {/* 考点卡片 */}
      <ContentCard
        listType="ordered"
        title="考点"
        content={data.examPoint}
        icon={<ExamPointIcon />}
      />

      <PText text="目标和考点都清楚了，我们来找找这道题的解题突破口：" className="mb-4 mt-8" />
      {/* 解题突破口卡片 */}
      <ContentCard
        title="解题突破口"
        content={data.solutionBreakthrough}
        icon={<SolutionBreakthroughIcon />}
      />
    </div>
  );
};

export default ExamPointAnalysisStage;
