"use client";

import { signal } from "@preact-signals/safe-react";
import { AiExplanationRecord } from "@repo/core/types";
import Button from "@repo/ui/components/press-button";
import React, { memo, useCallback, useEffect, useMemo } from "react";
import { aiExplanation, aiExplanationRecord } from "../mock/ai-explanation";
import { useReportInteractiveExplanation } from "../model";
import { StageType } from "../types";
import StageContentRenderer from "./components/stage-content-renderer";
import StageNavigation from "./components/stage-navigation";
import { FloatingPanel } from "./floating-panel";

// 基于 signal 的记录管理, 切换题目不会清理记录数据，实现本地缓存互动解题记录
const recordsSignal = signal<Record<string, AiExplanationRecord>>({});
interface InteractiveExplanationProps {
  /** 是否显示浮层 */
  isVisible: boolean;
  /** 关闭浮层回调 */
  onClose: () => void;
  /** 初始数据（可选，默认使用mock数据） */
  initialData?: typeof aiExplanation;
  /** 初始记录（可选，默认使用mock数据） */
  initialRecord?: AiExplanationRecord;
  /** 进度变更回调 */
  onProgressChange?: (record: AiExplanationRecord) => void;
  /** 答题回调 */
  onAnswer?: (stepIndex: number, isCorrect: boolean) => void;
  /** 题目ID */
  questionId: string;
  /** 学习会话ID */
  studySessionId?: number;
  /** 是否是预览模式 */
  preview?: boolean;
}

/**
 * 互动讲题主组件
 * 整合aiExplanation数据和aiExplanationRecord记录
 */
export const InteractiveExplanation: React.FC<InteractiveExplanationProps> =
  memo(
    ({
      isVisible,
      onClose,
      initialData = aiExplanation,
      initialRecord = aiExplanationRecord,
      onProgressChange,
      onAnswer,
      questionId,
      studySessionId = 0,
      // preview = false,
    }) => {
      // 当前记录状态

      const currentRecord = recordsSignal.value[questionId] ?? initialRecord;

      // 进度上报hook
      const { reportInteractiveExplanation, isReporting, reportError } =
        useReportInteractiveExplanation();

      // 上报进度的函数
      const reportProgress = useCallback(
        async (updatedRecord: AiExplanationRecord) => {
          if (!studySessionId) {
            return;
          }
          const payload = {
            questionId,
            studySessionId,
            version: updatedRecord.version,
            record: {
              stepByStepGuideCurrentProgress:
                updatedRecord.stepByStepGuideCurrentProgress,
              currentProgress: updatedRecord.currentProgress,
              stepByStepGuide: updatedRecord.stepByStepGuide.map((step) => ({
                isAnswer: step.isAnswer,
              })),
            },
          };

          console.log("🚀 准备上报进度:", payload);

          try {
            const result = await reportInteractiveExplanation(payload);
            console.log("✅ 进度上报成功:", result);
          } catch (error) {
            console.error("❌ 进度上报失败:", error);
          }
        },
        [reportInteractiveExplanation, questionId, studySessionId]
      );

      // 当前阶段映射
      const stageMapping = useMemo(
        () => [
          StageType.ProblemAnalysis,
          StageType.ExamPointAnalysis,
          StageType.StepByStepGuide,
          StageType.SolutionSummary,
        ],
        []
      );

      const currentStage =
        stageMapping[currentRecord.currentProgress ?? 0] ||
        StageType.ProblemAnalysis;

      // 构造符合组件接口的数据结构
      const stageData = {
        problemAnalysis: {
          guideText: currentRecord.problemAnalysisGuideText,
          analysisItems: `${initialData.problemAnalysis.questionRequirement}`,
          keyInfo: {
            title: "关键信息",
            content: initialData.problemAnalysis.essentialInfo,
            icon: (
              <div className="interactive-explanation-key-info-icon flex h-[30px] w-[30px] items-center justify-center rounded bg-white">
                <div className="flex h-[24px] w-[22px] items-center justify-center rounded bg-[#FFC55A]">
                  <div className="h-[12px] w-[13px] rounded bg-[#FFF2F2]"></div>
                </div>
              </div>
            ),
          },
        },
        examPointAnalysis: {
          guideText: currentRecord.examPointAnalysisGuideText ?? "",
          target: initialData.examPointAnalysis.target,
          examPoint: initialData.examPointAnalysis.examPoint,
          solutionBreakthrough:
            initialData.examPointAnalysis.solutionBreakthrough,
        },
        stepByStepGuide: initialData.stepByStepGuide,
        solutionSummary: {
          guideText: currentRecord.solutionSummaryGuideText,
          solutionSteps: initialData.solutionSummary.solutionIdea,
          finalAnswer: initialData.solutionSummary.finalAnswer,
        },
      };

      // 阶段切换处理
      const handleStageChange = useCallback(
        (stage: StageType) => {
          const stageIndex = stageMapping.indexOf(stage);
          if (
            stageIndex !== -1 &&
            stageIndex !== currentRecord.currentProgress
          ) {
            const newRecord = {
              ...currentRecord,
              currentProgress: stageIndex,
            };
            recordsSignal.value = {
              ...recordsSignal.value,
              [questionId]: newRecord,
            };
            onProgressChange?.(newRecord);
            // 上报进度
            reportProgress(newRecord);
          }
        },
        [currentRecord, stageMapping, onProgressChange, reportProgress]
      );

      // 步骤进度处理（逐步讲解阶段）
      const handleStepProgress = useCallback(
        (stepIndex: number) => {
          if (currentRecord.currentProgress === 2) {
            // 逐步讲解阶段
            const newRecord = {
              ...currentRecord,
              stepByStepGuideCurrentProgress: stepIndex,
            };
            recordsSignal.value = {
              ...recordsSignal.value,
              [questionId]: newRecord,
            };
            onProgressChange?.(newRecord);
            // 上报进度
            reportProgress(newRecord);
          }
        },
        [currentRecord, onProgressChange, reportProgress]
      );

      // 答题处理
      const handleAnswer = useCallback(
        (stepIndex: number, isCorrect: boolean) => {
          if (currentRecord.currentProgress === 2) {
            // 逐步讲解阶段
            const newStepByStepGuide = [...currentRecord.stepByStepGuide];
            if (newStepByStepGuide[stepIndex]) {
              newStepByStepGuide[stepIndex] = {
                ...newStepByStepGuide[stepIndex],
                isAnswer: true,
              };
            }

            const newRecord = {
              ...currentRecord,
              stepByStepGuide: newStepByStepGuide,
            };
            recordsSignal.value = {
              ...recordsSignal.value,
              [questionId]: newRecord,
            };
            onProgressChange?.(newRecord);
            onAnswer?.(stepIndex, isCorrect);

            // 只有答题正确时才上报进度
            if (isCorrect) {
              reportProgress(newRecord);
            }
          }
        },
        [currentRecord, onProgressChange, onAnswer, reportProgress]
      );

      // 下一步处理
      const handleNext = useCallback(() => {
        // 如果当前在逐步讲解阶段，优先控制二级导航
        if (currentRecord.currentProgress === 2) {
          // 逐步讲解阶段
          const totalSteps = initialData.stepByStepGuide.steps.length;
          const currentStepIndex = currentRecord.stepByStepGuideCurrentProgress;

          // 如果还有下一步，则进入下一步
          if (currentStepIndex < totalSteps - 1) {
            const newRecord = {
              ...currentRecord,
              stepByStepGuideCurrentProgress: currentStepIndex + 1,
            };

            recordsSignal.value = {
              ...recordsSignal.value,
              [questionId]: newRecord,
            };

            onProgressChange?.(newRecord);
            // 上报进度
            reportProgress(newRecord);
            return;
          }
          // 如果已经是最后一步，则跳转到下一个一级阶段
        }

        console.log(
          "🚀 当前阶段:",
          currentRecord.currentProgress,
          stageMapping.length
        );

        // 其他阶段或逐步讲解已完成，切换到下一个一级阶段
        if (currentRecord.currentProgress < stageMapping.length - 1) {
          const newRecord = {
            ...currentRecord,
            currentProgress: currentRecord.currentProgress + 1,
          };

          recordsSignal.value = {
            ...recordsSignal.value,
            [questionId]: newRecord,
          };
          onProgressChange?.(newRecord);
          // 上报进度
          reportProgress(newRecord);
        }
      }, [
        currentRecord,
        stageMapping.length,
        onProgressChange,
        initialData.stepByStepGuide.steps.length,
        reportProgress,
      ]);

      // 监听外部数据变化
      useEffect(() => {
        if (!recordsSignal.value[questionId]) {
          recordsSignal.value = {
            ...recordsSignal.value,
            [questionId]: initialRecord,
          };
        }
      }, [initialRecord]);

      const showNextButton = useMemo(() => {
        // 只有在最后一个阶段（思路总结）才不显示按钮
        if (
          currentRecord.currentProgress &&
          currentRecord.currentProgress >= stageMapping.length - 1
        ) {
          return false;
        }
        // 其他所有情况都显示按钮，包括逐步讲解的最后一步
        return true;
      }, [currentRecord, stageMapping.length]);

      return (
        <FloatingPanel
          isVisible={isVisible}
          onClose={onClose}
          className="interactive-explanation-panel"
          header={
            <StageNavigation
              currentStage={currentStage}
              onStageChange={handleStageChange}
              stepCount={initialData.stepByStepGuide.steps.length}
              className="interactive-explanation-navigation"
            />
          }
          footer={
            showNextButton && (
              <div className="flex justify-end bg-white p-8">
                <Button
                  color="orange"
                  className="floating-panel-next-btn"
                  onClick={handleNext}
                  disabled={
                    !!currentRecord.currentProgress &&
                    currentRecord.currentProgress >= stageMapping.length - 1 &&
                    (currentRecord.currentProgress !== 2 ||
                      currentRecord.stepByStepGuideCurrentProgress >=
                        initialData.stepByStepGuide.steps.length - 1)
                  }
                >
                  下一步
                </Button>
              </div>
            )
          }
        >
          <div className="interactive-explanation-content">
            <StageContentRenderer
              currentStage={currentStage}
              data={stageData}
              record={currentRecord}
              onStepProgress={handleStepProgress}
              onAnswer={handleAnswer}
            />
          </div>
        </FloatingPanel>
      );
    }
  );

InteractiveExplanation.displayName = "InteractiveExplanation";

export default InteractiveExplanation;
