"use client";

import React, { memo } from 'react';
import { StageType } from '../../types';
import { AiExplanation, AiExplanationRecord } from '@repo/core/types';
import ProblemAnalysisStage from '../stages/problem-analysis-stage';
import ExamPointAnalysisStage from '../stages/exam-point-analysis-stage';
import StepByStepGuideStage from '../stages/step-by-step-guide-stage';
import SolutionSummaryStage from '../stages/solution-summary-stage';

// 基于实际mock数据的类型定义 - 扩展统一类型以支持UI特定字段
interface ProblemAnalysisData {
  guideText: string;
  analysisItems: string;
  keyInfo: {
    title: string;
    content: string;
    icon: React.ReactNode;
  };
}

interface ExamPointAnalysisData {
  guideText: string;
  target: string;
  examPoint: string;
  solutionBreakthrough: string;
}

interface SolutionSummaryData {
  guideText: string;
  solutionSteps: string;
  finalAnswer: string;
}

// 扩展的阶段数据，包含UI特定字段和统一的AiExplanation数据
interface StageData {
  problemAnalysis?: ProblemAnalysisData;
  examPointAnalysis?: ExamPointAnalysisData;
  stepByStepGuide?: AiExplanation['stepByStepGuide']; // 🔥 使用统一类型
  solutionSummary?: SolutionSummaryData;
}

interface StageContentRendererProps {
  currentStage: StageType;
  data: StageData; // 从外部传入的数据
  className?: string;
  record?: AiExplanationRecord; // 互动记录
  onStepProgress?: (stepIndex: number) => void; // 步骤进度回调
  onAnswer?: (stepIndex: number, isCorrect: boolean) => void; // 答题回调
}

export const StageContentRenderer: React.FC<StageContentRendererProps> = memo(({
  currentStage,
  data,
  className,
  record,
  onStepProgress,
  onAnswer,
}) => {
  const renderStageContent = () => {
    switch (currentStage) {
      case StageType.ProblemAnalysis:
        return data.problemAnalysis ? (
          <ProblemAnalysisStage
            data={data.problemAnalysis}
            className={className}
          />
        ) : null;

      case StageType.ExamPointAnalysis:
        return data.examPointAnalysis ? (
          <ExamPointAnalysisStage
            data={data.examPointAnalysis}
            className={className}
          />
        ) : null;

      case StageType.StepByStepGuide:
        return data.stepByStepGuide ? (
          <StepByStepGuideStage
            data={data.stepByStepGuide}
            className={className}
            record={record}
            onStepProgress={onStepProgress}
            onAnswer={onAnswer}
          />
        ) : null;

      case StageType.SolutionSummary:
        return data.solutionSummary ? (
          <SolutionSummaryStage
            data={data.solutionSummary}
            className={className}
          />
        ) : null;

      default:
        return (
          <div className="unknown-stage p-8 text-center">
            <div className="bg-[#FFF3CD] rounded-lg p-6 border border-[#FFEAA7]">
              <h3 className="text-lg font-semibold text-[#856404] mb-2">
                未知阶段
              </h3>
              <p className="text-[#856404]">
                当前阶段 &ldquo;{currentStage}&rdquo; 暂未支持
              </p>
            </div>
          </div>
        );
    }
  };

  return (
    <div className="stage-content-renderer px-6 pb-6">
      {renderStageContent()}
    </div>
  );
});

StageContentRenderer.displayName = 'StageContentRenderer';

export default StageContentRenderer;
