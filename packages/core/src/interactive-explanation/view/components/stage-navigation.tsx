"use client";

import React from 'react';
import { cn } from '@repo/ui/lib/utils';
import { StageType } from '../../types';

interface StageNavigationProps {
  currentStage: StageType;
  onStageChange: (stage: StageType) => void;
  stepCount?: number; // 逐步讲解的步数
  className?: string;
}

export const StageNavigation: React.FC<StageNavigationProps> = ({
  currentStage,
  onStageChange,
  stepCount = 5,
  className
}) => {
  // 内部定义的stages
  const stages = [
    { id: 'problem-analysis' as StageType, label: '题目分析' },
    { id: 'exam-point-analysis' as StageType, label: '考点分析' },
    { id: 'step-by-step-guide' as StageType, label: `逐步讲解` },
    { id: 'solution-summary' as StageType, label: '思路总结' },
  ];

  // 获取当前阶段的索引
  const getCurrentStageIndex = () => {
    return stages.findIndex(stage => stage.id === currentStage);
  };

  // 判断阶段状态
  const getStageState = (index: number) => {
    const currentIndex = getCurrentStageIndex();
    if (index < currentIndex) return 'completed';
    if (index === currentIndex) return 'current';
    return 'not-started';
  };

  // 获取SVG组件
  const getStageSVG = (index: number, isActive: boolean) => {
    const fillColor = isActive
      ? "url(#gradient)"
      : "rgba(31, 29, 27, 0.05)";

    if (index === 0) {
      // 第一个 - 左边圆角
      return (
        <svg xmlns="http://www.w3.org/2000/svg" width="177" height="48" viewBox="0 0 177 48" fill="none" className="w-full h-full">
          <defs>
            <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%">
              <stop offset="5.62%" stopColor="#FFA453" />
              <stop offset="99.21%" stopColor="#FF8D29" />
            </linearGradient>
          </defs>
          <path d="M0 8C0 3.58172 3.58172 0 8 0H162.292C164.564 0 166.642 1.28401 167.658 3.31672L175.317 18.6334C177.006 22.0118 177.006 25.9882 175.317 29.3666L167.658 44.6833C166.642 46.716 164.564 48 162.292 48H8C3.58173 48 0 44.4183 0 40V8Z" fill={fillColor} />
        </svg>
      );
    } else if (index === stages.length - 1) {
      // 最后一个 - 右边圆角
      return (
        <svg xmlns="http://www.w3.org/2000/svg" width="175" height="48" viewBox="0 0 175 48" fill="none" className="w-full h-full">
          <defs>
            <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%">
              <stop offset="5.62%" stopColor="#FFA453" />
              <stop offset="99.21%" stopColor="#FF8D29" />
            </linearGradient>
          </defs>
          <path d="M1.17082 4.34164C0.173469 2.34694 1.62396 0 3.8541 0H167C171.418 0 175 3.58172 175 8V40C175 44.4183 171.418 48 167 48H3.85409C1.62395 48 0.173469 45.6531 1.17082 43.6584L9.21115 27.5777C10.3373 25.3255 10.3373 22.6745 9.21115 20.4223L1.17082 4.34164Z" fill={fillColor} />
        </svg>
      );
    } else {
      // 中间的 - 两边都是箭头
      return (
        <svg xmlns="http://www.w3.org/2000/svg" width="176" height="48" viewBox="0 0 176 48" fill="none" className="w-full h-full">
          <defs>
            <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%">
              <stop offset="5.62%" stopColor="#FFA453" />
              <stop offset="99.21%" stopColor="#FF8D29" />
            </linearGradient>
          </defs>
          <path d="M1.17082 4.34164C0.173469 2.34694 1.62396 0 3.8541 0H161.292C163.564 0 165.642 1.28401 166.658 3.31672L174.317 18.6334C176.006 22.0118 176.006 25.9882 174.317 29.3666L166.658 44.6833C165.642 46.716 163.564 48 161.292 48H3.8541C1.62395 48 0.173469 45.6531 1.17082 43.6584L9.21115 27.5777C10.3373 25.3255 10.3373 22.6745 9.21115 20.4223L1.17082 4.34164Z" fill={fillColor} />
        </svg>
      );
    }
  };

  return (
    <div className={cn("stage-navigation flex items-center justify-cente h-max-content mt-8", className)}>
      {stages.map((stage, index) => {
        const stageState = getStageState(index);
        const isActive = stageState === 'current';
        const isStepByStepGuide = stage.id === StageType.StepByStepGuide;

        return (
          <div
            key={stage.id}
            className="relative cursor-pointer transition-all duration-200 text-[1.0625rem] font-[700]"
            onClick={() => onStageChange(stage.id)}
            style={{ width: index === 0 ? '177px' : index === stages.length - 1 ? '175px' : '176px' }}
          >
            {/* SVG背景 */}
            <div className="relative w-full h-12">
              {getStageSVG(index, isActive)}

              {/* 内容覆盖层 */}
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="flex items-center gap-x-2">
                  {/* 序号 */}
                  <span className={cn(
                    "inline-flex items-center justify-center w-6 h-6 rounded-full  leading-none",
                    isActive ? "bg-white border-none text-main-orange" : "bg-white border border-[rgba(31,29,27,0.15)] text-text-5"
                  )}>
                    {index + 1}
                  </span>

                  {/* 标签文本 */}
                  <span className={cn(
                    "stage-navigation-label select-none",
                    isActive ? "text-white" : "text-gray-700"
                  )}>
                    {stage.label}
                    {isStepByStepGuide && <span className={cn("text-text-2 font-normal ml-1 opacity-80", isActive ? "text-white" : "")}>({stepCount}步)</span>}
                  </span>
                </div>
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default StageNavigation;
