/**
 * 互动讲题相关类型定义
 */

// 浮层位置信息
export interface FloatingPanelPosition {
  x: number;
  y: number;
}

// 浮层状态
export interface FloatingPanelState {
  isVisible: boolean;
  position: FloatingPanelPosition;
  isDragging: boolean;
}

// 浮层组件Props
export interface FloatingPanelProps {
  isVisible: boolean;
  onClose: () => void;
  children?: React.ReactNode;
  className?: string;
}

// 阶段类型
export enum StageType {
  ProblemAnalysis = 'problem-analysis',
  ExamPointAnalysis = 'exam-point-analysis',
  StepByStepGuide = 'step-by-step-guide',
  SolutionSummary = 'solution-summary',
}

// 导航状态
export type NavigationState = 'not-started' | 'current' | 'completed';

// 阶段导航项
export interface StageNavigationItem {
  id: StageType;
  label: string;
  state: NavigationState;
  stepCount?: number; // 逐步讲解阶段的步骤数
}

// 虚拟导师引导语
export interface VirtualTutorGuide {
  id: string;
  text: string;
  stage: StageType;
}

// 内容卡片类型
export type ContentCardType = 'condition' | 'key-info' | 'target' | 'exam-point' | 'breakthrough' | 'solution-step' | 'summary';

// 内容卡片
export interface ContentCard {
  id: string;
  type: ContentCardType;
  title: string;
  content: string | string[];
  order: number;
}

// 问答交互选项
export interface QAOption {
  id: string;
  text: string;
  isCorrect: boolean;
}

// 问答交互题目
export interface QAQuestion {
  id: string;
  question: string;
  options: QAOption[];
  explanation: string;
  calculationProcess?: string; // 计算过程
}

// 阶段内容数据
export interface StageContent {
  stage: StageType;
  guide: VirtualTutorGuide;
  cards: ContentCard[];
  qaQuestion?: QAQuestion; // 仅逐步讲解阶段有
}

// 互动讲题完整数据
export interface InteractiveExplanationData {
  questionId: string;
  title: string;
  stages: StageContent[];
}

// 用户交互状态
export interface UserInteractionState {
  currentStage: StageType;
  currentStep: number; // 逐步讲解阶段的当前步骤
  completedStages: StageType[];
  qaAttempts: Record<string, number>; // 问答题的尝试次数
  qaAnswers: Record<string, string>; // 用户的答案记录
}

// 进度上报数据
export interface ProgressReportData {
  questionId: string;
  stage: StageType;
  step?: number;
  action: 'enter' | 'complete' | 'answer' | 'retry';
  timestamp: number;
  data?: any; // 额外数据
}
