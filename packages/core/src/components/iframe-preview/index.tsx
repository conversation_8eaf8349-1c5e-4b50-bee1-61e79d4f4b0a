"use client";

import React, { useEffect, useRef, useState } from "react";
import { useIframeParent } from "./hooks/use-iframe-message";

interface IframePreviewProps {
  /** iframe 页面的 URL */
  src: string;
  /** iframe 的宽度 */
  width: number;
  /** iframe 的高度 */
  height: number;
  /** 要传递给 iframe 的数据 */
  data?: unknown;
  /** 关闭回调 */
  onClose: () => void;
  /** 预览标题 */
  title?: string;
}

/**
 * 通用 iframe 预览组件
 * 通过 iframe 隔离环境，实现指定尺寸的预览效果
 */
export const IframePreview: React.FC<IframePreviewProps> = ({
  src,
  width,
  height,
  data,
  onClose,
  title = "iframe 预览",
}) => {
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const [isIframeReady, setIsIframeReady] = useState(false);

  // 使用 iframe 消息通信 hooks
  const { sendData } = useIframeParent(iframeRef, {
    IFRAME_READY: () => {
      setIsIframeReady(true);
    },
    CLOSE_PREVIEW: () => {
      onClose();
    },
  });

  // 当 iframe 准备就绪且数据变化时，发送数据到 iframe
  useEffect(() => {
    if (isIframeReady && data) {
      sendData(data);
    }
  }, [isIframeReady, data, sendData]);

  // 处理键盘事件
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "Escape") {
        onClose();
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, [onClose]);

  return (
    <div className="iframe-preview-overlay fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4">
      <div
        className="iframe-preview-container relative overflow-hidden rounded-lg bg-white shadow-2xl"
        style={{
          height: `${height + 40}px`,
        }}
      >
        {/* 工具栏 */}
        <div className="iframe-toolbar absolute left-0 right-0 top-[-0] z-10 flex items-center justify-between bg-gray-600 bg-opacity-90 px-4 py-2 text-white">
          <div className="flex items-center gap-3">
            <span className="text-sm font-medium">{title}</span>
            <div className="flex items-center gap-2 text-xs text-gray-300">
              <span>
                {width}×{height}
              </span>
              <span>•</span>
              <span className="text-green-400">使用 iframe 模拟设备环境</span>
              <span>•</span>
              <span>{isIframeReady ? "已连接" : "连接中..."}</span>
            </div>
          </div>

          <div className="flex items-center gap-2">
            {/* 关闭按钮 */}
            <button
              onClick={onClose}
              className="rounded p-1 transition-colors hover:bg-white hover:bg-opacity-20"
              title="关闭 (ESC)"
            >
              <svg
                className="h-4 w-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>
        </div>

        {/* iframe 容器 */}
        <div
          className="iframe-container"
          style={{
            width: `${width}px`,
            height: `${height}px`,
            marginTop: "40px",
          }}
        >
          <iframe
            ref={iframeRef}
            src={src}
            width="100%"
            height="100%"
            style={{
              border: "none",
              display: "block",
              backgroundColor: "white",
            }}
            title={title}
            sandbox="allow-same-origin allow-scripts allow-forms"
          />
        </div>

        {/* 加载状态 */}
        {!isIframeReady && (
          <div className="iframe-loading absolute inset-0 flex items-center justify-center bg-white bg-opacity-90">
            <div className="text-center">
              <div className="mb-2 text-lg font-medium text-gray-600">
                正在加载预览...
              </div>
              <div className="text-sm text-gray-500">iframe 环境初始化中</div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default IframePreview;
