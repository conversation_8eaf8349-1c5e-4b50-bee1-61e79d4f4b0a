# IframePreview 通用组件

## 🎯 功能说明

通用的 iframe 预览组件，通过 iframe 隔离环境实现指定尺寸的预览效果。

## 📋 Props

```typescript
interface IframePreviewProps {
  /** iframe 页面的 URL */
  src: string;
  /** iframe 的宽度 */
  width: number;
  /** iframe 的高度 */
  height: number;
  /** 要传递给 iframe 的数据 */
  data?: unknown;
  /** 关闭回调 */
  onClose: () => void;
  /** 预览标题 */
  title?: string;
}
```

## 🚀 使用方式

### 基础用法

```tsx
import IframePreview from "@/ui-kit/components/iframe-preview";

<IframePreview
  src="/ui-kit/exercise-preview/iframe"
  width={1000}
  height={600}
  title="练习组件预览"
  data={{
    questionList,
    studyType,
    previewMode,
  }}
  onClose={handleClose}
/>
```

### 不同尺寸预设

```tsx
// iPad 尺寸
<IframePreview
  src="/preview-page"
  width={1000}
  height={600}
  title="iPad 预览"
  data={data}
  onClose={onClose}
/>

// iPhone 尺寸
<IframePreview
  src="/preview-page"
  width={375}
  height={667}
  title="iPhone 预览"
  data={data}
  onClose={onClose}
/>

// 桌面尺寸
<IframePreview
  src="/preview-page"
  width={1920}
  height={1080}
  title="桌面预览"
  data={data}
  onClose={onClose}
/>
```

## 🔧 通信机制

### 标准消息类型

组件使用固定的消息类型进行通信：

- `IFRAME_READY` - iframe 准备就绪
- `PREVIEW_DATA` - 传递预览数据
- `CLOSE_PREVIEW` - 请求关闭预览

### iframe 页面实现

```tsx
// iframe 内部页面需要实现的消息监听
useEffect(() => {
  const handleMessage = (event: MessageEvent) => {
    if (event.origin !== window.location.origin) return;

    const { type, data } = event.data;

    switch (type) {
      case 'PREVIEW_DATA':
        // 处理接收到的数据
        setPreviewData(data);
        break;
    }
  };

  window.addEventListener('message', handleMessage);
  
  // 通知父页面准备就绪
  window.parent.postMessage({ type: 'IFRAME_READY' }, window.location.origin);

  return () => {
    window.removeEventListener('message', handleMessage);
  };
}, []);

// 请求关闭预览
const handleClose = () => {
  window.parent.postMessage({ type: 'CLOSE_PREVIEW' }, window.location.origin);
};
```

## 🎨 界面特性

### 工具栏
- 显示预览标题
- 显示尺寸信息 (width×height)
- 显示连接状态
- 关闭按钮

### 交互
- ESC 键关闭预览
- 点击关闭按钮
- 半透明遮罩背景

### 样式
- 居中显示
- 圆角阴影
- 响应式适配

## 🔍 技术优势

### 完全隔离
- 独立的 window 对象
- 独立的 DOM 环境
- 独立的事件系统
- 独立的 CSS 环境

### 零侵入
- 无需修改现有代码
- 无需 API 劫持
- 无需样式重写
- 利用浏览器原生机制

### 通用性
- 支持任意尺寸
- 支持任意 iframe 页面
- 支持任意数据传递
- 可复用于不同场景

## 📝 注意事项

1. **同源策略**: iframe 页面必须与主页面同源
2. **消息安全**: 只接受同源的 postMessage
3. **数据序列化**: 传递的 data 会被序列化，不支持函数等复杂类型
4. **性能考虑**: iframe 会创建独立的渲染环境，有一定的性能开销

## 🎯 适用场景

- 组件预览工具
- 设备尺寸模拟
- 隔离环境测试
- 多尺寸适配验证
- 第三方内容嵌入

## 🔄 扩展方向

需要时可以添加以下功能：

- 自定义消息类型
- 缩放控制
- 多设备预设
- 自定义工具栏
- 更多交互事件
- 性能监控
- 错误处理
