# useIframeMessage Hooks

## 🎯 功能说明

专门处理 iframe 与父窗口之间消息通信的 React Hooks 集合，简化 postMessage 的使用。

## 📋 Hooks 列表

### 1. useIframeMessage (基础 Hook)

通用的 iframe 消息通信 Hook。

```typescript
const { sendToIframe, sendToParent, addHandler, removeHandler } = useIframeMessage({
  handlers: {
    MESSAGE_TYPE: (data) => {
      // 处理消息
    }
  },
  checkOrigin: true,
  allowedOrigins: []
});
```

### 2. useIframeParent (父页面专用)

简化父页面的消息处理。

```typescript
const { sendData, sendToIframe, addHandler, removeHandler } = useIframeParent(iframeRef, {
  IFRAME_READY: () => {
    console.log('iframe 准备就绪');
  },
  CLOSE_PREVIEW: () => {
    console.log('请求关闭预览');
  }
});
```

### 3. useIframeChild (子页面专用)

简化子页面的消息处理。

```typescript
const { notifyReady, requestClose, sendToParent, addHandler, removeHandler } = useIframeChild({
  PREVIEW_DATA: (data) => {
    console.log('收到预览数据:', data);
  }
});
```

## 🚀 使用示例

### 父页面 (IframePreview 组件)

```tsx
import { useIframeParent } from "../hooks/use-iframe-message";

export const IframePreview = ({ src, width, height, data, onClose }) => {
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const [isIframeReady, setIsIframeReady] = useState(false);

  // 使用父页面 hooks
  const { sendData } = useIframeParent(iframeRef, {
    IFRAME_READY: () => {
      setIsIframeReady(true);
    },
    CLOSE_PREVIEW: () => {
      onClose();
    },
  });

  // 当数据变化时发送到 iframe
  useEffect(() => {
    if (isIframeReady && data) {
      sendData(data);
    }
  }, [isIframeReady, data, sendData]);

  return (
    <iframe ref={iframeRef} src={src} />
  );
};
```

### 子页面 (iframe 内部)

```tsx
import { useIframeChild } from "../../hooks/use-iframe-message";

export default function IframePreviewPage() {
  const [previewData, setPreviewData] = useState(null);

  // 使用子页面 hooks
  const { notifyReady, requestClose } = useIframeChild({
    PREVIEW_DATA: (data) => {
      setPreviewData(data);
    },
    UPDATE_CONFIG: (data) => {
      // 处理配置更新
    },
  });

  // 通知父页面准备就绪
  useEffect(() => {
    notifyReady();
  }, [notifyReady]);

  const handleClose = () => {
    requestClose();
  };

  return (
    <div>
      <button onClick={handleClose}>关闭</button>
      {/* 渲染内容 */}
    </div>
  );
}
```

## 🔧 API 参考

### useIframeMessage Options

```typescript
interface UseIframeMessageOptions {
  /** 消息处理器映射 */
  handlers?: MessageHandler;
  /** 是否验证同源 */
  checkOrigin?: boolean;
  /** 允许的来源列表，为空则只允许同源 */
  allowedOrigins?: string[];
}
```

### MessageHandler

```typescript
interface MessageHandler {
  [messageType: string]: (data: any) => void;
}
```

### 返回值

```typescript
interface UseIframeMessageReturn {
  /** 发送消息到 iframe */
  sendToIframe: (iframeRef: React.RefObject<HTMLIFrameElement>, type: string, data?: any) => void;
  /** 发送消息到父窗口 */
  sendToParent: (type: string, data?: any) => void;
  /** 添加消息处理器 */
  addHandler: (type: string, handler: (data: any) => void) => void;
  /** 移除消息处理器 */
  removeHandler: (type: string) => void;
}
```

## 🛡️ 安全特性

### 同源验证

默认只接受同源的消息，防止 XSS 攻击：

```typescript
// 自动验证消息来源
if (event.origin !== window.location.origin) {
  console.warn('Rejected message from unauthorized origin:', event.origin);
  return;
}
```

### 自定义来源

可以配置允许的来源列表：

```typescript
const { sendToParent } = useIframeMessage({
  allowedOrigins: ['https://trusted-domain.com'],
  checkOrigin: true
});
```

### 错误处理

自动捕获和记录处理器中的错误：

```typescript
try {
  handler(data);
} catch (error) {
  console.error(`Error handling message type "${type}":`, error);
}
```

## 📝 标准消息类型

### 系统消息

- `IFRAME_READY` - iframe 准备就绪
- `CLOSE_PREVIEW` - 请求关闭预览
- `PREVIEW_DATA` - 传递预览数据

### 自定义消息

可以定义任意的消息类型：

```typescript
const { sendToParent } = useIframeChild({
  CUSTOM_EVENT: (data) => {
    // 处理自定义事件
  },
  USER_ACTION: (data) => {
    // 处理用户操作
  }
});

// 发送自定义消息
sendToParent('CUSTOM_EVENT', { action: 'click', target: 'button' });
```

## 💡 最佳实践

### 1. 消息类型命名

使用大写字母和下划线的命名规范：

```typescript
// ✅ 推荐
IFRAME_READY
PREVIEW_DATA
USER_ACTION

// ❌ 不推荐
iframeReady
preview-data
userAction
```

### 2. 数据结构

保持数据结构简单，避免传递函数或复杂对象：

```typescript
// ✅ 推荐
sendData({
  questionList: [...],
  studyType: 'AI_COURSE',
  config: { theme: 'dark' }
});

// ❌ 不推荐
sendData({
  callback: () => {},
  element: document.getElementById('test')
});
```

### 3. 错误处理

在消息处理器中添加适当的错误处理：

```typescript
const { notifyReady } = useIframeChild({
  PREVIEW_DATA: (data) => {
    try {
      if (!data || !data.questionList) {
        throw new Error('Invalid preview data');
      }
      setQuestionList(data.questionList);
    } catch (error) {
      console.error('Failed to process preview data:', error);
    }
  }
});
```

### 4. 内存清理

Hooks 会自动清理事件监听器，但如果手动添加处理器，记得清理：

```typescript
useEffect(() => {
  addHandler('TEMP_EVENT', handleTempEvent);
  
  return () => {
    removeHandler('TEMP_EVENT');
  };
}, [addHandler, removeHandler]);
```

## 🔄 迁移指南

### 从原生 postMessage 迁移

**之前：**
```typescript
// 父页面
useEffect(() => {
  const handleMessage = (event) => {
    if (event.origin !== window.location.origin) return;
    const { type, data } = event.data;
    if (type === 'IFRAME_READY') {
      setIsReady(true);
    }
  };
  window.addEventListener('message', handleMessage);
  return () => window.removeEventListener('message', handleMessage);
}, []);

// 发送数据
iframeWindow.postMessage({ type: 'PREVIEW_DATA', data }, origin);
```

**现在：**
```typescript
// 父页面
const { sendData } = useIframeParent(iframeRef, {
  IFRAME_READY: () => setIsReady(true)
});

// 发送数据
sendData(data);
```

这样的封装让 iframe 通信变得更加简洁和安全！
