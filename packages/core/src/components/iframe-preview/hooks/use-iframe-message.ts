"use client";

import { useCallback, useEffect, useRef } from 'react';

interface MessageHandler {
  [messageType: string]: (data: any) => void;
}

interface UseIframeMessageOptions {
  /** 消息处理器映射 */
  handlers?: MessageHandler;
  /** 是否验证同源 */
  checkOrigin?: boolean;
  /** 允许的来源列表，为空则只允许同源 */
  allowedOrigins?: string[];
}

interface UseIframeMessageReturn {
  /** 发送消息到 iframe */
  sendToIframe: (iframeRef: React.RefObject<HTMLIFrameElement | null>, type: string, data?: any) => void;
  /** 发送消息到父窗口 */
  sendToParent: (type: string, data?: any) => void;
  /** 添加消息处理器 */
  addHandler: (type: string, handler: (data: any) => void) => void;
  /** 移除消息处理器 */
  removeHandler: (type: string) => void;
}

/**
 * iframe 消息通信 Hook
 * 统一处理 iframe 与父窗口之间的消息通信
 */
export const useIframeMessage = (options: UseIframeMessageOptions = {}): UseIframeMessageReturn => {
  const {
    handlers = {},
    checkOrigin = true,
    allowedOrigins = []
  } = options;

  const handlersRef = useRef<MessageHandler>(handlers);

  // 更新处理器引用
  useEffect(() => {
    handlersRef.current = { ...handlersRef.current, ...handlers };
  }, [handlers]);

  // 消息监听器
  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      // 来源验证
      if (checkOrigin) {
        const isAllowedOrigin = allowedOrigins.length > 0
          ? allowedOrigins.includes(event.origin)
          : event.origin === window.location.origin;

        if (!isAllowedOrigin) {
          console.warn('Rejected message from unauthorized origin:', event.origin);
          return;
        }
      }

      const { type, data } = event.data;

      if (!type) {
        console.warn('Received message without type:', event.data);
        return;
      }

      // 调用对应的处理器
      const handler = handlersRef.current[type];
      if (handler) {
        try {
          handler(data);
        } catch (error) {
          console.error(`Error handling message type "${type}":`, error);
        }
      }
    };

    window.addEventListener('message', handleMessage);

    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, [checkOrigin, allowedOrigins]);

  // 发送消息到 iframe
  const sendToIframe = useCallback((
    iframeRef: React.RefObject<HTMLIFrameElement | null>,
    type: string,
    data?: any
  ) => {
    if (!iframeRef.current) {
      console.warn('Cannot send message: iframe ref is null');
      return;
    }

    const iframeWindow = iframeRef.current.contentWindow;
    if (!iframeWindow) {
      console.warn('Cannot send message: iframe contentWindow is null');
      return;
    }

    const message = { type, data };
    const targetOrigin = window.location.origin;

    try {
      iframeWindow.postMessage(message, targetOrigin);
    } catch (error) {
      console.error('Failed to send message to iframe:', error);
    }
  }, []);

  // 发送消息到父窗口
  const sendToParent = useCallback((type: string, data?: any) => {
    if (window.parent === window) {
      console.warn('Cannot send message: not in iframe context');
      return;
    }

    const message = { type, data };
    const targetOrigin = window.location.origin;

    try {
      window.parent.postMessage(message, targetOrigin);
    } catch (error) {
      console.error('Failed to send message to parent:', error);
    }
  }, []);

  // 动态添加处理器
  const addHandler = useCallback((type: string, handler: (data: any) => void) => {
    handlersRef.current[type] = handler;
  }, []);

  // 移除处理器
  const removeHandler = useCallback((type: string) => {
    delete handlersRef.current[type];
  }, []);

  return {
    sendToIframe,
    sendToParent,
    addHandler,
    removeHandler,
  };
};

/**
 * iframe 父页面专用 Hook
 * 简化父页面的消息处理
 */
export const useIframeParent = (
  iframeRef: React.RefObject<HTMLIFrameElement | null>,
  handlers: MessageHandler = {}
) => {
  const { sendToIframe, addHandler, removeHandler } = useIframeMessage({ handlers });

  const sendData = useCallback((data: any) => {
    sendToIframe(iframeRef, 'PREVIEW_DATA', data);
  }, [sendToIframe, iframeRef]);

  return {
    sendData,
    sendToIframe: (type: string, data?: any) => sendToIframe(iframeRef, type, data),
    addHandler,
    removeHandler,
  };
};

/**
 * iframe 子页面专用 Hook
 * 简化子页面的消息处理
 */
export const useIframeChild = (handlers: MessageHandler = {}) => {
  const { sendToParent, addHandler, removeHandler } = useIframeMessage({ handlers });

  const notifyReady = useCallback(() => {
    sendToParent('IFRAME_READY');
  }, [sendToParent]);

  const requestClose = useCallback(() => {
    sendToParent('CLOSE_PREVIEW');
  }, [sendToParent]);


  // 通知父页面准备就绪
  useEffect(() => {
    notifyReady();
  }, [notifyReady]);

  return {
    notifyReady,
    requestClose,
    sendToParent,
    addHandler,
    removeHandler,
  };
};

export default useIframeMessage;
