'use client';

import React, { useState, useEffect, useRef } from 'react';

interface TimerDisplayProps {
  timerControl: {
    isActive: boolean | null;
    onTimeUpdate: (timeMs: number) => void;
    shouldReset: string;
    initialTime?: number; // 🔧 新增：初始时间（毫秒），用于恢复练习
  };
  className?: string;
  // 预览模式相关
  previewMode?: boolean; // 是否为预览模式
  previewDurationMs?: number; // 预览模式下显示的固定用时（毫秒）
}

/**
 * 格式化毫秒为 MM:SS 格式
 */
const formatTime = (totalMs: number): string => {
  const totalSeconds = Math.floor(totalMs / 1000);
  const hours = Math.floor(totalSeconds / 3600);
  const minutes = Math.floor((totalSeconds % 3600) / 60);
  const seconds = totalSeconds % 60;

  const formattedHours = hours > 0 ? `${hours.toString().padStart(2, '0')}:` : '';
  const formattedMinutes = `${minutes.toString().padStart(2, '0')}:`;
  const formattedSeconds = seconds.toString().padStart(2, '0');

  return `${formattedHours}${formattedMinutes}${formattedSeconds}`;
};

/**
 * 练习计时器显示组件
 *
 * 🔧 独立计时版本：内部管理计时状态，避免 ViewModel 重新渲染
 * - 内部使用 useState 管理显示时间（毫秒）
 * - 通过 onTimeUpdate 回调同步时间给 ViewModel（毫秒）
 * - 只有这个组件会因计时而重新渲染
 * - 达到59:59后停止计时，但不影响其他操作
 * - 使用毫秒精度提高计时准确性
 */
export const TimerDisplay: React.FC<TimerDisplayProps> = React.memo(({
  timerControl,
  className = '',
  previewMode = false,
  previewDurationMs,
}) => {
  const [displayMs, setDisplayMs] = useState(0);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const lastResetRef = useRef(timerControl.shouldReset);
  const startTimeRef = useRef<number | null>(null);

  // 最大显示时间：59分59秒 = 3599000毫秒
  const MAX_DISPLAY_TIME_MS = 59 * 60 * 1000 + 59 * 1000; // 3599000毫秒
  // 测试
  // const MAX_DISPLAY_TIME_MS = 5000; // 5秒 测试

  // 检测重置信号
  useEffect(() => {
    if (lastResetRef.current !== timerControl.shouldReset) {
      const initialTime = timerControl.initialTime || 0;
      setDisplayMs(initialTime);
      startTimeRef.current = null;
      lastResetRef.current = timerControl.shouldReset;
    }
  }, [timerControl.shouldReset, timerControl.initialTime]);

  // 管理计时器
  useEffect(() => {
    if (timerControl.isActive) {
      // 记录开始时间
      if (startTimeRef.current === null) {
        // 🔧 考虑初始时间：开始时间 = 当前时间 - 已有的显示时间
        startTimeRef.current = Date.now() - displayMs;
      }

      intervalRef.current = setInterval(() => {
        if (startTimeRef.current !== null) {
          const currentTime = Date.now();
          const elapsedMs = currentTime - startTimeRef.current;

          setDisplayMs(_prev => {
            // 检查是否达到最大显示时间
            if (elapsedMs >= MAX_DISPLAY_TIME_MS) {
              // 达到59:59后停止计时，保持在最大值
              timerControl.onTimeUpdate(MAX_DISPLAY_TIME_MS);
              return MAX_DISPLAY_TIME_MS;
            }

            // 正常计时，使用精确的时间差
            timerControl.onTimeUpdate(elapsedMs);
            return elapsedMs;
          });
        }
      }, 50); // 50ms更新一次，提高精度但不过度消耗性能
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
      // 暂停时保持当前时间，不重置 startTimeRef
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, [timerControl.isActive, timerControl.onTimeUpdate]);

  // 当计时器重新激活时，调整开始时间以保持连续性
  useEffect(() => {
    if (timerControl.isActive && startTimeRef.current !== null) {
      // 重新激活时，调整开始时间 = 当前时间 - 已经过的时间
      startTimeRef.current = Date.now() - displayMs;
    }
  }, [timerControl.isActive, displayMs]);

  // 预览模式：显示固定用时
  if (previewMode) {
    const displayTime = previewDurationMs || 0;
    return (
      <div className={`timer-display flex items-center whitespace-nowrap text-text-4 text-[1.0625rem] leading-[125%] select-none ${className}`}>
        <span className="mr-[0.125rem]">用时</span>
        <span >{formatTime(displayTime)}</span>
      </div>
    );
  }

  // 正常模式：显示实时计时
  return (
    <div className={`timer-display flex items-center whitespace-nowrap text-text-4 text-[1.0625rem] leading-[125%] select-none ${className}`}>
      <span className="mr-[0.125rem]">用时</span>
      <span >{formatTime(displayMs)}</span>
    </div>
  );
});

TimerDisplay.displayName = 'TimerDisplay';
export default TimerDisplay;