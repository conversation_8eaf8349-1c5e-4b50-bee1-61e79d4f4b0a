// apps/stu/app/ui-kit/progress-bar-demo2/ProgressBar.tsx
"use client";

import { computed } from "@preact-signals/safe-react";
import { AnimatePresence, motion } from "framer-motion";
import Lottie, { LottieComponentProps } from "lottie-react";
import Image from "next/image";
import React, { useEffect, useMemo, useState } from "react";

// 音频文件 CDN 路径配置
const getBaseUrl = () => {
  const env = process.env.NODE_ENV;
  if (env === "development") {
    return "";
  } else if (env === "production") {
    // 生产环境使用 CDN 路径
    return "https://static.xiaoluxue.com/stu/_next/public";
  } else {
    // 其他所有环境使用 CDN 路径
    return "https://static.test.xiaoluxue.cn/stu/_next/public";
  }
};

// 音频文件路径配置 - 使用字符串路径而不是直接导入
const audioFiles = {
  correct: `${getBaseUrl()}/audios/single-correct.wav`,
  incorrect: `${getBaseUrl()}/audios/incorrect.wav`,
  continuous: `${getBaseUrl()}/audios/continuous-correct.wav`,
};

// NOTE: The following two imports are placeholders.
// You should replace them with your actual project paths.
import { EnhancedCanvasText } from "@repo/core/components/EnhancedCanvasText";
import ProgressBarTextBackground from "@repo/core/public/assets/stu-exercise/images/progress-bar-text-background.png";

// Lottie animation imports
import allCorrectAnimation from "@repo/core/public/assets/stu-exercise/lottie/all-correct-blocks.json";
import continuousCorrectAnimation from "@repo/core/public/assets/stu-exercise/lottie/continuous-correct-blocks.json";
import fullScreenAnimation from "@repo/core/public/assets/stu-exercise/lottie/full-screen-block.json";
import singleCorrectAnimation from "@repo/core/public/assets/stu-exercise/lottie/single-correct-circle.json";
import { cn } from "@repo/ui/lib/utils";
import {
  ANIMATION_CONFIG,
  animationQueue,
  ProgressAnimationMode,
} from "./data";

//================================================================
// 1. Type Definitions
//================================================================
// 定义进度显示模式类型
export type ProgressDisplayMode = "number" | "progress";

//================================================================
// 2. Audio Player
//================================================================
const audioCache: Map<string, HTMLAudioElement> = new Map();
const defaultAudioFiles = {
  continuous_2_4: audioFiles.correct,
  continuous_4_8: audioFiles.continuous,
  continuous_8_plus: audioFiles.continuous,
  all_correct: audioFiles.continuous,
  incorrect: audioFiles.incorrect,
};

const playAudioEffect = (type: keyof typeof defaultAudioFiles) => {
  const audioUrl = defaultAudioFiles[type];
  if (!audioUrl) return;
  try {
    let audio = audioCache.get(audioUrl);
    if (!audio) {
      audio = new Audio(audioUrl);
      audio.volume = 0.7;
      audioCache.set(audioUrl, audio);
    }
    audio.currentTime = 0;
    audio
      .play()
      .catch((error) => console.warn(`[Audio] Play failed: ${type}`, error));
  } catch (error) {
    console.warn(`[Audio] Load failed: ${type}`, error);
  }
};

const handleAnimationComplete = () => {
  const state = animationQueue.value;
  if (!state.currentItem) return;
  state.currentItem.onComplete?.();
  animationQueue.value = { ...state, currentItem: null };
  processQueue();
};

const processQueue = () => {
  const state = animationQueue.value;
  if (state.currentItem || state.queue.length === 0) return;

  const [nextItem] = state.queue;
  const remainingQueue = state.queue.slice(1);
  animationQueue.value = {
    queue: remainingQueue,
    currentItem: nextItem || null,
  };

  const config = nextItem?.mode && ANIMATION_CONFIG[nextItem.mode];
  if (config?.audioType) playAudioEffect(config.audioType);

  setTimeout(handleAnimationComplete, config?.duration || 0);
};

const enqueueAnimation = (
  mode: ProgressAnimationMode,
  text?: string,
  onComplete?: () => void
) => {
  const id = `anim_${Date.now()}`;
  animationQueue.value = {
    ...animationQueue.value,
    queue: [...animationQueue.value.queue, { id, mode, text, onComplete }],
  };

  processQueue();
};

//================================================================
// 4. Reactive State Signals
//================================================================
const currentAnimation = computed(() => animationQueue.value.currentItem);
const currentEffects = computed(() => {
  const mode = currentAnimation.value?.mode;
  return mode ? ANIMATION_CONFIG[mode].effects : [];
});

const showEndCircleBurst = computed(() =>
  currentEffects.value.includes("endCircleBurst")
);
const showLightningWrap = computed(() =>
  currentEffects.value.includes("lightningWrap")
);
const showFullBarBlast = computed(() =>
  currentEffects.value.includes("fullBarBlast")
);
const showFullScreenConfetti = computed(() =>
  currentEffects.value.includes("fullScreenConfetti")
);
const showStreakBlocks = computed(() =>
  currentEffects.value.includes("streakBlocks")
);

const explosionTextToShow = computed(() => currentAnimation.value?.text || "");
const showExplosionText = computed(
  () => !!explosionTextToShow.value && currentAnimation.value?.mode !== "static"
);

//================================================================
// 5. Internal View Components
//================================================================
const DelayLottie = React.memo(
  ({
    animationData,
    loop,
    className,
    rendererSettings,
  }: {
    animationData: Record<string, unknown>;
    loop: boolean;
    className: string;
    rendererSettings?: LottieComponentProps["rendererSettings"];
  }) => {
    const [play, setPlay] = useState(false);
    useEffect(() => {
      const timer = setTimeout(() => setPlay(true), 0);
      return () => clearTimeout(timer);
    }, []);

    if (!play) return null;
    return (
      <Lottie
        animationData={animationData}
        loop={loop}
        className={className}
        rendererSettings={rendererSettings}
      />
    );
  }
);
DelayLottie.displayName = "DelayLottie";

const BaseProgressBar = React.memo(
  ({
    progress,
    activeType,
  }: {
    progress: number;
    activeType: ProgressAnimationMode;
  }) => {
    const normalizedProgress = Math.min(Math.max(0, progress), 100);

    // 🔥 优化宽度计算：确保始终有最小宽度，同时保持动画流畅性
    const { progressWidth, initialWidth } = useMemo(() => {
      const baseWidth = 280 * (normalizedProgress / 100);

      // 始终保持最小宽度 20px，确保进度条可见性
      const finalWidth = Math.max(20, baseWidth + 4);

      // 动画初始宽度逻辑：
      // - 如果是从 0 开始，初始宽度为 20px（最小宽度）
      // - 如果计算宽度已经大于最小宽度，从 0 开始动画更自然
      const initWidth = baseWidth <= 20 ? 20 : 0;

      return { progressWidth: finalWidth, initialWidth: initWidth };
    }, [normalizedProgress]);

    const textToRender = explosionTextToShow.value;
    const isAnimating = !!currentAnimation.value;

    console.log(`textToRender`, {
      textToRender,
      progressWidth,
      normalizedProgress,
    });

    return (
      <div className="progress-bar-container relative h-4 w-[17.5rem] flex-shrink-0 overflow-visible rounded-[1.25rem] bg-[rgba(31,29,27,0.08)]">
        <motion.div
          className="progress-bar-fill absolute -top-0.5 left-0 z-20 h-5 translate-x-[-2px]"
          initial={{ width: initialWidth }}
          animate={{ width: progressWidth }}
          transition={{
            type: "spring",
            stiffness: 800,
            damping: 40,
            mass: 0.5,
            velocity: 0,
          }}
          style={{
            willChange: "width, transform",
            transform: "translateZ(0)",
            backfaceVisibility: "hidden",
          }}
        >
          {/* 🔥 优化：始终显示进度条内容，因为现在总是有最小宽度 */}
          <motion.div
            className="progress-bar-inner absolute inset-0 z-10 h-5 rounded-[10px] border-2 border-white/60"
            style={{ borderColor: "rgba(255, 255, 255, 0.6)" }}
            animate={{ opacity: isAnimating ? 1 : 0.4 }}
            transition={{ duration: 0.3, ease: "easeInOut" }}
            initial={{ opacity: 0 }}
          >
            <div
              className="progress-bar-highlight z-1 absolute left-[6px] right-[6px] top-[3px] h-[3px] rounded-full opacity-50 mix-blend-plus-lighter"
              style={{ backgroundColor: "rgba(255, 255, 255, 0.3)" }}
            />
            <motion.div
              className="progress-bar-gradient relative mt-0 h-4 w-full rounded-lg"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.3, ease: "easeInOut" }}
              style={{
                background: `linear-gradient(90deg, #3E98FF 0%, #3EDD72 ${100}%)`,
              }}
            />
          </motion.div>
          <AnimatePresence>
            {showEndCircleBurst.value && (
              <DelayLottie
                animationData={singleCorrectAnimation}
                loop={false}
                className="pointer-events-none absolute right-0 top-1/2 h-20 w-20 -translate-y-1/2 translate-x-[48%]"
              />
            )}
            {showStreakBlocks.value && (
              <DelayLottie
                animationData={continuousCorrectAnimation}
                loop={false}
                className="continuousCorrectAnimation z-15 pointer-events-none absolute left-[-25%] top-1/2 !h-[90px] w-[150%] -translate-y-1/2"
                rendererSettings={{ preserveAspectRatio: "none" }}
              />
            )}
            {showLightningWrap.value && (
              <motion.div
                key="lightningWrap"
                className="pointer-events-none absolute left-[-7%] top-[-60%] z-[-1] h-[220%] w-[115%]"
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: [0, 1, 0], scale: 1 }}
                transition={{
                  duration: 1.0,
                  times: [0, 0.5, 1],
                  ease: "easeInOut",
                }}
              >
                <Image
                  src={`${getBaseUrl()}/images/lightning.webp`}
                  alt="Lightning Wrap"
                  width={50}
                  height={50}
                  unoptimized
                  className="h-full w-full"
                />
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>
        {showFullBarBlast.value && (
          <DelayLottie
            animationData={allCorrectAnimation}
            loop={false}
            className="z-15 pointer-events-none absolute left-[-25%] top-1/2 w-[150%] -translate-y-1/2"
          />
        )}
        {showFullScreenConfetti.value && (
          <DelayLottie
            animationData={fullScreenAnimation}
            loop={false}
            className="z-15 pointer-events-none fixed inset-0 flex items-center justify-center"
          />
        )}
        <AnimatePresence>
          {showExplosionText.value && (
            <motion.div
              className="absolute right-0 top-1/2 -translate-y-[70%] translate-x-[110%] rotate-[-10deg]"
              initial={{ scale: 0, opacity: 0 }}
              animate={{ scale: [0, 1.3, 1, 1, 0], opacity: [0, 1, 1, 1, 0] }}
              transition={{
                duration: 1.0,
                times: [0, 0.2, 0.3, 0.8, 1.0],
                ease: "easeInOut",
              }}
            >
              {/* 如果是错误图片透明度为 0 */}
              <div
                className={cn(
                  "absolute left-1/2 top-1/2 z-[-1] h-[100%] w-[110%] shrink-0 -translate-x-[49%] -translate-y-[35%] opacity-100",
                  activeType == "incorrect" ? "opacity-0" : ""
                )}
              >
                <Image
                  className="h-full w-full"
                  src={ProgressBarTextBackground}
                  alt="ProgressBarTextBackground"
                />
              </div>

              <EnhancedCanvasText
                className="-skew-x-[10deg]"
                text={textToRender}
                fontSize={20}
                fontWeight={900}
                color="#0DA7D6"
                strokeColor="rgba(255, 255, 255, 0.90)"
                strokeWidth={2}
              />
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    );
  }
);
BaseProgressBar.displayName = "BaseProgressBar";

//================================================================
// 6. Main Component
//================================================================
interface ProgressBarProps {
  progress: number;
  activeType: ProgressAnimationMode;
  explosionText?: string;
  onAnimationComplete?: () => void;

  // 预览模式
  isPreviewMode?: boolean;
  displayMode?: "number" | "progress";
  currentIndex?: number;
  totalCount?: number;
  className?: string;
}

export const ProgressBar: React.FC<ProgressBarProps> = ({
  progress,
  activeType,
  explosionText,
  onAnimationComplete,

  // 预览模式
  isPreviewMode = false,
  displayMode = "number",
  currentIndex = 0,
  totalCount = 1,
  className = "",
}) => {
  // 🔥 根据显示模式决定渲染方式
  // 预览模式下：根据 displayMode 决定显示进度条还是数字
  // 非预览模式下：始终显示进度条
  const shouldShowNumber = isPreviewMode && displayMode === "number";

  const animationMode: ProgressAnimationMode = useMemo(() => {
    return activeType;
  }, [activeType]);

  useEffect(() => {
    if (animationMode === "default") return;
    enqueueAnimation(animationMode, explosionText, onAnimationComplete);
  }, [animationMode, explosionText, onAnimationComplete]);

  // 数字模式：显示 "当前/总数" 格式
  if (shouldShowNumber) {
    const displayCurrent =
      typeof currentIndex === "number" ? currentIndex + 1 : 1;
    const displayTotal = totalCount || 1;

    return (
      <div
        className={`preview-progress-display flex items-center ${className}`}
      >
        <span className="text-text-4 text-sm font-medium">
          {displayCurrent} / {displayTotal}
        </span>
      </div>
    );
  }

  return <BaseProgressBar progress={progress} activeType={animationMode} />;
};
