// packages/core/src/exercise/components/ProgressBar/useProgressBar.ts
import React, { useCallback, useState } from "react";
import { ANIMATION_CONFIG, ProgressAnimationMode } from "./data";

export interface HandleProgressParams {
    type: Exclude<ProgressAnimationMode, | "continuous_2_4"
        | "continuous_4_8"
        | "continuous_8_plus">;
    text?: string;
    progress: number;
    correctComboCount?: number;
    isAllCorrect?: boolean;
}
export interface ProgressBarProps {
    progress: number;
    activeType: ProgressAnimationMode;
    explosionText: string;
}
export const useProgressBar = ({ initialProgress }: { initialProgress?: number } = {}) => {
    // 🔥 优化：确保初始进度值的一致性，避免不必要的重渲染
    const [progress, setProgress] = useState(() => initialProgress || 0);
    const [explosionText, setExplosionText] = useState("");
    const [isAnimating, setIsAnimating] = useState(false);
    const [activeType, setActiveType] = useState<ProgressAnimationMode>("static");


    const getActiveType = useCallback((type: HandleProgressParams["type"], { isAllCorrect = false, correctComboCount = 0 }: { isAllCorrect?: boolean, correctComboCount?: number }) => {
        if (isAllCorrect) return "all_correct";

        if (type === "single_correct") {
            if (correctComboCount >= 8) return "continuous_8_plus";
            if (correctComboCount >= 4) return "continuous_4_8";
            if (correctComboCount >= 2) return "continuous_2_4";
            if (correctComboCount >= 1) return "single_correct";
        }
        return type;
    }, []);

    // ✨ 核心变更：创建一个统一的 handleProgress 函数
    const handleProgress = useCallback(
        ({
            type,
            text,
            progress,
            correctComboCount,
            isAllCorrect,
        }: HandleProgressParams) => {
            if (isAnimating) return;
            setIsAnimating(true);

            const _activeType = getActiveType(type, { isAllCorrect, correctComboCount });
            // 1. 更新所有核心数据状态
            React.startTransition(() => {
                setProgress(progress);
                setExplosionText(text || "");
                setActiveType(_activeType);
            });


            // 3. 根据类型计算动画时长，并设置延时来重置状态
            const animationDuration =
                ANIMATION_CONFIG[_activeType].duration;

            setTimeout(() => {
                // 重置对应的动画触发器
                React.startTransition(() => {
                    setActiveType("static");
                    setExplosionText("");
                    setIsAnimating(false);
                });
            }, animationDuration);
        },
        [isAnimating, getActiveType] // 依赖项现在只关心 isAnimating
    );

    const reset = useCallback(() => {
        React.startTransition(() => {
            setProgress(0);
            setExplosionText("");
            setIsAnimating(false);
        });
    }, []);

    const progressBarProps = {
        progress,
        activeType,
        explosionText,
    };

    // ✨ 控制器现在只暴露 handleProgress 和 reset，更加简洁
    const controllers = {
        handleProgress,
        reset,
        isAnimating,
    };

    return { progressBarProps, ...controllers } as const;
};