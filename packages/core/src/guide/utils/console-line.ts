import { Line } from "@repo/core/types/data/widget-guide";
/**
 * debug Line
 */
export const consoleLine = (label: string = "", line: Line | undefined) => {
  if (!line) return;
  console.log(
    label,
    line.tag,
    "[",
    line.inFrame,
    line.outFrame,
    "]",
    line.content
      .map((it) =>
        typeof it.content === "string"
          ? it.content
          : it.content.map((it) => it.content).join("")
      )
      .join(""),

    line.id
  );
};
