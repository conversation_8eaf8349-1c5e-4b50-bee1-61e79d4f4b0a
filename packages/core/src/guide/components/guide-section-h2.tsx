import { DrawElement } from "@repo/core/types/data/widget-guide";
import { cn } from "@repo/ui/lib/utils";
import { FC, useMemo } from "react";
import { useGuideContext } from "../context/guide-context";
import { toH3Parts } from "../utils/h3-parts";
import { Cover, GuideTitle } from "./guide-line";
import { GuideSectionH3 } from "./guide-section-h3";

export const GuideSectionH2: FC<{
  sketchProps?: {
    onDrawChange?: (paths: DrawElement[] | null) => void;
    mode?: "draw" | "edit";
    changeLine?: (draw: string, id: string) => void;
    startFrame?: number;
    outFrame?: number;
    duration?: number;
    eraserMode?: boolean;
    highlighter?: boolean;
  };
  className?: string;
}> = ({ className, sketchProps }) => {
  const { title, index, totalGuideCount, data, theme } = useGuideContext();
  const { content } = data;
  const h3Parts = useMemo(() => toH3Parts(content), [content]);

  return (
    <section
      data-name="section::h2"
      className={cn(
        "relative flex h-max min-h-full w-4/5 flex-col items-center",
        className
      )}
    >
      <div className="min-w-(--width-guide)">
        <GuideTitle title={title} />
        <div className="pb-18 pl-8">
          <div
            className={cn(
              "gap-18 w-section-h3 flex flex-col rounded-[20px] pb-10 pt-[60px]",
              index == 0 && "bg-[#FFEDD8] px-8",
              index == totalGuideCount - 1 && "bg-[#FFEDD8] px-8"
            )}
          >
            {/* 改为H3Part */}
            {h3Parts.map((part, index) => (
              <GuideSectionH3
                index={index}
                data={part}
                key={index}
                sketchProps={sketchProps}
              />
            ))}
          </div>
        </div>
      </div>
      {index === totalGuideCount - 1 && (
        <Cover className="w-full" name={theme.backCover ?? ""} />
      )}
    </section>
  );
};
