import { useSignal } from "@preact-signals/safe-react";
import { useEffect, useRef } from "@preact-signals/safe-react/react";
import { FC } from "react";
import { useCurrentFrame } from "remotion";
import {
  SectionH3Provider,
  SectionH3ProviderProps,
  useSectionH3Context,
} from "../context/section-h3-context";
import { useSketchCanvasRef } from "../context/sketch-canvas-context";
import { HandDrawn } from "./guide-hand-drawn";
import { GuideSectionH4 } from "./guide-section-h4";
import { SketchBoard } from "./sketch-board";

const Draw = () => {
  const { h3Line, sketchProps, partFrames } = useSectionH3Context();
  const { onDrawChange, eraserMode, highlighter } = sketchProps || {};
  const { canvasRef, strokeColor, strokeWidth } = useSketchCanvasRef();
  const frame = useCurrentFrame();
  if (!h3Line) {
    return null;
  }

  const { id, draw } = h3Line;

  if (sketchProps?.mode !== "draw" && draw) {
    return <HandDrawn data={draw} frame={frame} partFrames={partFrames} />;
  }

  if (sketchProps?.mode === "draw") {
    return (
      <SketchBoard
        startFrame={sketchProps?.startFrame || 0}
        outFrame={sketchProps?.outFrame || 0}
        itemId={id ?? ""}
        className="z-100 absolute left-0 top-0 h-full w-full"
        svgData={""}
        // readOnly={isReadOnly}
        onChange={onDrawChange}
        ref={canvasRef}
        strokeColor={strokeColor}
        strokeWidth={strokeWidth}
        allPaths={draw || []}
        animated={true}
        eraserMode={eraserMode}
        highlighter={highlighter}
      />
    );
  }
};

const SectionH3: FC = () => {
  const { ref, parts, mergedReferences } = useSectionH3Context();
  const svgRef = useRef<SVGSVGElement>(null);
  const scale = useSignal(1);
  const pathList = useSignal<
    {
      left: number;
      top: number;
      height: number;
      width: number;
    }[]
  >([]);
  const countList = useSignal<
    {
      left: number;
      top: number;
      height: number;
      width: number;
      count: number;
    }[]
  >([]);

  useEffect(() => {
    if (!ref.current) return;
    if (!svgRef.current) return
  }, [mergedReferences, ref]);

  return (
    <section
      data-name="section::h3"
      ref={ref}
      className="relative flex flex-col gap-6"
    >
      {parts.map((part, index) => (
        <GuideSectionH4 key={index} data={part} />
      ))}

      <Draw />
      <svg
        className="pointer-events-none"
        ref={svgRef}
        xmlns="http://www.w3.org/2000/svg"
        width="100"
        height="2"
        overflow="visible"
        fill="none"
      >
        {pathList.value.map((rect, index) => (
          <path
            key={index}
            d={`M${rect.left} ${rect.top + rect.height / scale.value}H${rect.width / scale.value}`}
            stroke="#33302D"
            strokeOpacity="0.4"
            strokeWidth="2"
            strokeDasharray="2 4"
          />
        ))}
        {countList.value.map((rect, index) => (
          <g
            transform={`translate(${rect.left}, ${rect.top + rect.height / scale.value})`}
            key={index}
          >
            <rect
              x="0"
              y="0"
              width={(String(rect.count).length * 8 + 8) / scale.value}
              height={14 / scale.value}
              rx="4"
              ry="4"
              fill="#1F1D1B66"
            />
            <text
              x={(String(rect.count).length * 8 + 8) / scale.value / 2}
              y={7 / scale.value}
              fontSize="8"
              dominantBaseline="middle"
              textAnchor="middle"
              fill="#FFFFFF"
            >
              {rect.count}
            </text>
          </g>
        ))}
      </svg>
    </section>
  );
};

export const GuideSectionH3: FC<Omit<SectionH3ProviderProps, "children">> = (
  props
) => {
  return (
    <SectionH3Provider {...props}>
      <SectionH3 />
    </SectionH3Provider>
  );
};
