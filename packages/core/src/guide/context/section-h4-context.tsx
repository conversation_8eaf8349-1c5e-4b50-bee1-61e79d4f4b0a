import { useSignal } from "@preact-signals/safe-react";
import { Line } from "@repo/core/types/data/widget-guide";
import { createContext, FC, ReactNode, useContext, useEffect } from "react";
import { useCurrentFrame } from "remotion";

type SectionH4ContextType = {
  data: Line[];
  isTalked: boolean;
  isTalking: boolean;
};

const SectionH4Context = createContext<SectionH4ContextType>(
  {} as SectionH4ContextType
);

const useSectionH4Context = () => useContext(SectionH4Context);

interface SectionH4ProviderProps {
  data: Line[];
  children: ReactNode;
}

const SectionH4Provider: FC<SectionH4ProviderProps> = ({ children, data }) => {
  const isTalked = useSignal(false);
  const isTalking = useSignal(false);
  const frame = useCurrentFrame();

  useEffect(() => {
    if (frame === 0) return;
    const last = data.at(-1);
    if (last) {
      isTalked.value = frame >= last.outFrame;
    } else {
      isTalked.value = false;
    }
  }, [isTalked, frame, data]);

  useEffect(() => {
    if (frame === 0) return;
    const first = data.at(0);
    const last = data.at(-1);
    if (first && last) {
      isTalking.value = frame >= first.inFrame && frame <= last.outFrame;
    } else {
      isTalking.value = false;
    }
  }, [isTalking, frame, data]);

  const value = {
    data,
    isTalked: isTalked.value,
    isTalking: isTalking.value,
  };

  return <SectionH4Context value={value}>{children}</SectionH4Context>;
};

export {
  SectionH4Context,
  SectionH4Provider,
  useSectionH4Context,
  type SectionH4ContextType,
};
