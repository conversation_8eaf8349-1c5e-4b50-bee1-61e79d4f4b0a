{"name": "@repo/core", "version": "0.0.0", "description": "", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "GIL", "license": "ISC", "exports": {"./hooks/*": "./src/hooks/*.ts", "./hooks/use-captcha": "./src/hooks/use-captcha.tsx", "./course": {"types": "./src/course/index.tsx", "default": "./src/course/index.tsx"}, "./player/*": {"types": "./src/player/*.tsx", "default": "./src/player/*.tsx"}, "./guide/*": {"types": "./src/guide/*.tsx", "default": "./src/guide/*.tsx"}, "./exercise/*": {"types": "./src/exercise/*.tsx", "default": "./src/exercise/*.tsx"}, "./notion/*": {"types": "./src/components/notion/*.tsx", "default": "./src/components/notion/*.tsx"}, "./components/*": {"types": "./src/components/*/index.tsx", "default": "./src/components/*/index.tsx"}, "./views/*": {"types": "./src/components/views/*.tsx", "default": "./src/components/views/*.tsx"}, "./types/*": "./src/types/*.ts", "./public/*": "./public/*"}, "dependencies": {"@emotion/css": "^11.13.5", "@preact-signals/safe-react": "^0.9.0", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@remotion/media-utils": "catalog:remotion4", "@remotion/paths": "catalog:remotion4", "@remotion/player": "catalog:remotion4", "@remotion/preload": "catalog:remotion4", "@remotion/tailwind-v4": "catalog:remotion4", "@remotion/zod-types": "catalog:remotion4", "@repo/lib": "workspace:*", "@repo/ui": "workspace:*", "ahooks": "^3.8.5", "@repo/react-sketch-canvas": "workspace:*", "better-react-mathjax": "^2.3.0", "class-variance-authority": "^0.7.1", "dompurify": "^3.2.6", "framer-motion": "^12.4.2", "immer": "^10.1.1", "katex": "^0.16.21", "lodash": "^4.17.21", "lottie-react": "^2.4.1", "lucide-react": "^0.475.0", "motion": "^12.4.7", "next": "15.3.2", "react": "catalog:react19", "react-dom": "catalog:react19", "react-sketch-canvas": "^6.2.0", "remotion": "catalog:remotion4", "@repo/rough-notation": "workspace:*", "swr": "^2.3.2", "use-debounce": "^10.0.5", "uuid": "^11.1.0", "zod": "3.22.3"}, "devDependencies": {"@repo/config-eslint": "workspace:*", "@repo/config-typescript": "workspace:*", "@tailwindcss/postcss": "^4.1.3", "@types/katex": "^0.16.7", "@types/lodash": "^4.17.17", "@types/node": "^20", "@types/react": "catalog:react19", "@types/react-dom": "catalog:react19", "postcss": "^8.5.3", "tailwindcss": "^4.1.3", "typescript": "5.8.2"}}