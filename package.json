{"name": "schoolroom-monorepo", "private": true, "scripts": {"build": "turbo run build", "build:dev": "turbo run build:dev", "build:test": "turbo run build:test", "build:prod": "turbo run build:prod", "build:local": "turbo run build:local", "dev": "turbo run dev", "lint": "turbo run lint", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "check-types": "turbo run check-types", "prepare": "command -v husky >/dev/null 2>&1 && husky || echo 'husky command not found, skipping prepare'", "start": "turbo run start", "deploy:dev": "turbo run deploy:dev", "sh": "sh deploy-local.sh", "test:models": "node packages/test-utils/scripts/test-models.js", "test:api": "node packages/test-utils/scripts/test-api.js"}, "devDependencies": {"@repo/config-eslint": "workspace:*", "@repo/config-typescript": "workspace:*", "@types/ramda": "^0.30.2", "husky": "^9.1.7", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.14", "turbo": "^2.5.4", "typescript": "5.8.2", "eslint": "^9.28.0"}, "packageManager": "pnpm@10.13.1", "engines": {"node": ">=20"}, "resolutions": {"**": "/cache/"}}