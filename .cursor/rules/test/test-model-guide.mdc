---
description: Model 层测试标准规范
globs:
alwaysApply: false
---
# Model 层测试标准规范
以下是Model层测试标准规范，必须严格遵守。

## 注意
- 必须使用 `pnpm` 命令运行测试，不要使用 `npm` 命令。
- 必须使用 `pnpm test:models [model-name]` 命令运行测试，绝对禁止使用vitest的默认标准命令。
- 完成测试用例编写后必须使用下面运行测试指南中的命令运行测试，确保测试通过后才能提交代码。

## 🧪 测试文件编写指南

### 必需测试文件
* `simple-vitest.test.ts`: Hook 基础功能测试。

### 可选测试文件
* `transformers.test.ts`: 数据转换函数测试 (如果模块包含数据转换逻辑)。

### 文件命名与脚本识别逻辑
测试脚本 `packages/test-utils/scripts/test-models.js` **仅识别并执行以下特定命名的文件**：
* ✅ `simple-vitest.test.ts`
* ✅ `transformers.test.ts`

---

## 🎭 Mock 规范

### 🚨 Mock顺序规则（违反导致测试失败）
1. **vi.mock()** 必须在文件顶部，所有import之前
2. **import** 必须在所有vi.mock()之后
3. **禁止** 在测试用例内使用vi.doMock()

### 核心原则
1. **简化Mock**: 使用 `vi.mock('module-path', () => ({ default: vi.fn() }))`
2. **类型规避**: 使用 `(module as any).mockReturnValue()` 设置返回值
3. **全局清理**: 在 `beforeEach` 中调用 `vi.clearAllMocks()`
4. **避免引用**: Mock工厂函数内不引用外部变量

### React Hook 测试模板（推荐）

```typescript
// ✅ 推荐的 React Hook 测试写法
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { renderHook, waitFor, act } from '@testing-library/react';
import { SWRConfig } from 'swr';
import React from 'react'; // 必须导入 React 以便使用 React.createElement

// 假设测试的 Hook 路径
// import { useMySpecificHook, useMyDataHook } from '../your-model-file';

// Mock fetcher (根据实际使用的 fetcher 路径调整)
vi.mock('@/app/utils/fetcher', () => ({
  get: vi.fn(),
  post: vi.fn(),
  // ... 其他用到的方法
}));

// Mock SWR 相关模块 (必须Mock)
vi.mock('swr', () => ({
  default: vi.fn(),
  SWRConfig: vi.fn(({ children }) => children),
}));

vi.mock('swr/mutation', () => ({
  default: vi.fn()
}));

// Mock transformers (如果 Hook 依赖了 transformers)
// 路径相对于当前测试文件
vi.mock('../transformers', () => ({
  transformSomeData: vi.fn((data) => data), // 示例 transformer
  // ... 其他 transformers
}));

// 【重要】在所有 vi.mock 调用之后再 import 被测试的模块和被 mock 的模块
// const { get, post } = await import('@/app/utils/fetcher');
// 如果在 describe 外部需要解构 get/post，请使用动态 import
// 但通常建议在测试用例内部按需解构或直接使用 (fetcher.get as any)

// SWR 测试包装器 - 使用 React.createElement 避免 JSX 编译问题
// 🚨 重要：测试文件中严禁使用 JSX 语法，必须使用 React.createElement
const createWrapper = () => {
  return ({ children }: { children: React.ReactNode }) => {
    return React.createElement(SWRConfig, { value: { provider: () => new Map(), dedupingInterval: 0 } }, children);
## 🎭 SWR Mock 配置详解

### 必须Mock的SWR模块
在测试使用SWR的Hook时，必须Mock以下模块：

```typescript
// ✅ 完整的SWR Mock配置
vi.mock('swr', () => ({
  default: vi.fn(),                              // Mock useSWR
  SWRConfig: vi.fn(({ children }) => children),  // Mock SWRConfig组件
}));

vi.mock('swr/mutation', () => ({ 
  default: vi.fn()                               // Mock useSWRMutation
}));
```

### 常见错误和解决方案

#### ❌ 错误1：缺少SWRConfig Mock
```typescript
// 错误：只Mock了default导出
vi.mock('swr', () => ({ default: vi.fn() }));
// 结果：SWRConfig is not defined 错误
```

#### ❌ 错误2：使用importOriginal导致类型错误
```typescript
// 错误：不必要的复杂Mock
vi.mock('swr', async (importOriginal) => {
  const actual = await importOriginal();
  return {
    ...actual, // 类型错误：spread 类型只能从对象类型创建
    default: vi.fn(),
  };
});
```

#### ✅ 正确方案：简单直接的Mock
```typescript
// 正确：直接Mock需要的导出
vi.mock('swr', () => ({
  default: vi.fn(),
  SWRConfig: vi.fn(({ children }) => children),
}));
```

### Mock设置最佳实践

```typescript
describe('SWR Hook Tests', () => {
  let mockUseSWR: any;
  let mockUseSWRMutation: any;

  beforeEach(async () => {
    vi.clearAllMocks();
    
    // 动态导入Mock模块
    const swr = await import('swr');
    const swrMutation = await import('swr/mutation');
    mockUseSWR = swr.default;
    mockUseSWRMutation = swrMutation.default;
  });

  it('应该正确Mock useSWR', () => {
    // 设置Mock返回值
    (mockUseSWR as any).mockReturnValue({
      data: mockData,
      error: null,
      isLoading: false,
      mutate: vi.fn(),
    });

    // 测试Hook...
  });

  it('应该正确Mock useSWRMutation', () => {
    // 设置Mock返回值
    (mockUseSWRMutation as any).mockReturnValue({
      trigger: vi.fn(),
      isMutating: false,
      error: null,
    });

    // 测试Hook...
  });
});
```
  };
};

// ❌ 错误写法 - 禁止在测试中使用 JSX 语法
// const createWrapper = () => {
//   return ({ children }: { children: React.ReactNode }) => (
//     <SWRConfig value={{ provider: () => new Map(), dedupingInterval: 0 }}>
//       {children}
//     </SWRConfig>
//   );
// };

describe('Hook 测试示例: useMySpecificHook', () => {
  let fetcher: any; // 用于方便地访问 mock 后的 fetcher

  beforeEach(async () => {
    vi.clearAllMocks();
    fetcher = await import('@/app/utils/fetcher'); // 在 beforeEach 中动态导入，确保 mock 生效
    // 如果 transformers 被 mock 且需要在测试中控制其行为，也在此处导入
    // transformers = await import('../transformers');
  });

  it('应正确处理异步 mutation 操作', async () => {
    const mockResponse = { success: true, data: 'mutation complete' };
    (fetcher.post as any).mockResolvedValue(mockResponse); // 使用 as any 规避类型

    // 假设 useMySpecificHook 是一个 mutation hook
    // const { result } = renderHook(() => useMySpecificHook(), {
    //   wrapper: createWrapper(),
    // });

    // await act(async () => {
    //   await result.current.triggerFunction({ input: 'testValue' });
    // });

    // expect(result.current.data).toEqual(mockResponse);
    // expect(fetcher.post).toHaveBeenCalledWith('/api/your-endpoint', { input: 'testValue' });
    expect(true).toBe(true); // 占位符，替换为实际断言
  });

  it('应正确处理 SWR 数据获取 (useSWR type hook)', async () => {
    const mockData = { id: 1, name: 'Sample Data' };
    (fetcher.get as any).mockResolvedValue(mockData);

    // 假设 useMyDataHook 是一个数据获取 hook
    // const { result } = renderHook(() => useMyDataHook({ entityId: 1 }), {
    //   wrapper: createWrapper(),
    // });

    // await waitFor(() => {
    //   expect(result.current.data).toEqual(mockData);
    // });

    // expect(fetcher.get).toHaveBeenCalledWith('/api/data-endpoint/1');
    expect(true).toBe(true); // 占位符，替换为实际断言
  });
});
```

## 🔄 异步操作测试规范

### 🚨 必须使用 act() 的场景（违反导致 React 警告）
1. **所有会触发状态更新的异步操作**
2. **Hook 的 trigger 函数调用**
3. **Promise 返回的操作**
4. **任何可能导致组件重新渲染的操作**

### ✅ 异步操作标准写法模板

```typescript
// 🎯 异步 Mutation 操作测试
it('应正确处理异步提交操作', async () => {
  const mockResponse = { success: true, data: 'result' };
  (mockPost as any).mockResolvedValue(mockResponse);

  const { result } = renderHook(() => useSubmitHook(), {
    wrapper: createWrapper(),
  });

  // ✅ 正确：用 act() 包装异步操作
  let submitResult: any;
  await act(async () => {
    submitResult = await result.current.submit(payload);
  });

  expect(submitResult).toEqual(mockResponse);
  expect(mockPost).toHaveBeenCalledWith('/api/endpoint', { arg: payload });
});

// 🎯 加载状态测试
it('应正确反映加载状态', async () => {
  // Mock 延迟响应以便测试加载状态
  (mockPost as any).mockImplementation(() =>
    new Promise(resolve => setTimeout(() => resolve(mockData), 100))
  );

  const { result } = renderHook(() => useSubmitHook(), {
    wrapper: createWrapper(),
  });

  // ✅ 正确：用 act() 包装状态更新操作
  act(() => {
    result.current.submit(payload);
  });

  // 立即检查加载状态
  expect(result.current.isLoading).toBe(true);
  expect(result.current.isSubmitting).toBe(true);
});

// 🎯 错误处理测试
it('应正确处理提交失败', async () => {
  const mockError = new Error('提交失败');
  (mockPost as any).mockRejectedValue(mockError);

  const { result } = renderHook(() => useSubmitHook(), {
    wrapper: createWrapper(),
  });

  // ✅ 正确：异常也需要 act() 包装
  await act(async () => {
    await expect(result.current.submit(payload)).rejects.toThrow('提交失败');
  });
});
```

### ❌ 常见错误写法

```typescript
// ❌ 错误：没有 act() 包装
const result = await hook.submit(payload);

// ❌ 错误：状态检查时机不对
hook.submit(payload);
expect(hook.isLoading).toBe(true); // 可能还未更新

// ❌ 错误：异步操作没有等待
hook.submit(payload);
expect(hook.data).toBeDefined(); // 数据可能还未返回
```

### � 标准测试模板（必须复制使用）

```typescript
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { renderHook } from '@testing-library/react';

// ⚠️ 步骤1: 文件顶部Mock所有依赖
vi.mock('@/app/utils/fetcher', () => ({
  get: vi.fn(),
  post: vi.fn(),
}));

vi.mock('swr', () => ({
  default: vi.fn(),
  SWRConfig: vi.fn(({ children }) => children),
}));
vi.mock('swr/mutation', () => ({ default: vi.fn() }));

// ⚠️ 步骤2: 所有Mock之后才import
import useSWR from 'swr';
import useSWRMutation from 'swr/mutation';
import { useYourHook } from '../your-model';

describe('Your Model Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks(); // ⚠️ 步骤3: 清理Mock状态
  });

  it('should work correctly', () => {
    // ⚠️ 步骤4: 设置Mock返回值
    (useSWRMutation as any).mockReturnValue({
      trigger: vi.fn(),
      isMutating: false,
      error: null,
    });

    const { result } = renderHook(() => useYourHook());
    expect(result.current).toBeDefined();
  });
});
```


## 🚨 常见测试错误预防清单

**重要：以下是开发过程中最容易出现的测试问题，必须在编写测试时主动预防**

### 📋 问题类型与解决方案

| 错误类型 | 常见症状 | 根本原因 | 解决方案 |
|---------|---------|----------|----------|
| **React act() 警告** | "An update to TestComponent inside a test was not wrapped in act(...)" | 异步状态更新没有正确包装 | 用 `act()` 包装所有异步操作 |
| **加载状态检查失败** | 期望 `isLoading: true` 实际 `false` | 状态检查时机不对 | 用 `act()` 包装触发操作后立即检查 |
| **接口验证测试失败** | 边界值测试失败（如负数测试） | TypeScript 接口缺少业务约束注释 | 添加详细的注释说明约束 |
| **异步断言失败** | 期望数据存在但为 undefined | 异步操作未正确等待 | 使用 `await act()` 和类型断言 |
| **Mock 返回值错误** | Hook 调用失败或返回 undefined | Mock 设置不完整 | 确保 Mock 返回完整数据结构 |

### 🔧 预防性检查清单

**编写测试前必须检查：**
- [ ] 所有异步操作是否用 `act()` 包装？
- [ ] Mock 是否返回完整的数据结构？
- [ ] TypeScript 接口是否有清晰的约束注释？
- [ ] 状态检查是否在正确的时机？
- [ ] 是否导入了必要的 `act` 函数？

### 🎭 Mock 设置最佳实践

```typescript
// ✅ 正确的异步 Mock 设置
beforeEach(() => {
  vi.clearAllMocks();
  
  // 成功响应 Mock
  mockPost.mockResolvedValue({
    code: 0,
    message: 'Success',
    data: { /* 完整的数据结构 */ }
  });
  
  // 延迟响应 Mock（用于测试加载状态）
  mockPost.mockImplementation(() =>
    new Promise(resolve =>
      setTimeout(() => resolve(mockData), 100)
    )
  );
  
  // 错误响应 Mock
  mockPost.mockRejectedValue(new Error('具体错误信息'));
});

// ❌ 错误的 Mock 设置
mockPost.mockImplementation(() => new Promise(resolve => setTimeout(resolve, 100))); // 没有返回数据
mockPost.mockReturnValue({}); // 不是 Promise，不适用于异步操作
```

### 🧪 测试用例编写模式

```typescript
// � 标准异步测试模式
it('应该正确处理异步操作', async () => {
  // 1. 设置 Mock
  const mockData = { /* 完整数据 */ };
  (mockFetcher as any).mockResolvedValue(mockData);
  
  // 2. 渲染 Hook
  const { result } = renderHook(() => useHook(), {
    wrapper: createWrapper(),
  });
  
  // 3. 执行异步操作（必须用 act 包装）
  let operationResult: any;
  await act(async () => {
    operationResult = await result.current.operation(payload);
  });
  
  // 4. 断言结果
  expect(operationResult).toEqual(mockData);
  expect(mockFetcher).toHaveBeenCalledWith(expectedUrl, expectedPayload);
});

// 🎯 加载状态测试模式
it('应该正确显示加载状态', () => {
  // 1. 设置延迟 Mock
  (mockFetcher as any).mockImplementation(() =>
    new Promise(resolve => setTimeout(() => resolve({}), 100))
  );
  
  // 2. 渲染 Hook
  const { result } = renderHook(() => useHook(), {
    wrapper: createWrapper(),
  });
  
  // 3. 触发操作（用 act 包装）
  act(() => {
    result.current.operation(payload);
  });
  
  // 4. 立即检查加载状态
  expect(result.current.isLoading).toBe(true);
});
```

---

## 🎯 测试覆盖率与质量要求

* **辅助函数**：100% 测试覆盖率。
* **数据转换函数**：100% 测试覆盖率（若存在）。
* **接口验证**：100% 测试覆盖率。
* **边界条件**：必须覆盖空值、零值、无效值及极限值的处理逻辑。
* **错误处理**：必须覆盖各种预期和异常情况下的错误抛出与处理逻辑。

---


## 🚨 核心强制规范（必读）

**以下规范是最基本且最容易出错的地方，必须严格遵守，违反将导致测试无法被识别或产生预期外错误。**

1.  **测试文件命名与后缀**：
    * **强制要求**：所有测试文件必须使用 `.test.ts` 后缀，并放置在模块下的 `__tests__/` 目录中。
    * **特定文件名**：
        * Hook 基础功能测试：`simple-vitest.test.ts`
        * 数据转换函数测试：`transformers.test.ts` (若有转换函数)
        * TypeScript 接口验证测试：`schemas.test.ts` (若需独立测试)
    * **绝对禁止**：使用 `-test.js`、`.spec.ts` 或其他非规定格式及命名。测试脚本仅识别上述特定文件名。
    * **正确示例**：`app/models/[model-name]/__tests__/simple-vitest.test.ts`

2.  **Mock 配置**：
    * **强制顺序**：`vi.mock()` 必须在文件顶部，所有 `import` 必须在 `vi.mock()` 之后。
    * **必用模板**：直接复制【Mock 规范】章节的完整模板，禁止自创Mock写法。
    * **类型规避**：使用 `(module as any).mockReturnValue()` 设置返回值。
    * **绝对禁止**：
        - 禁止在测试用例内使用 `vi.doMock()`
        - 禁止使用 `vi.mocked()` 进行类型转换
        - 禁止在 Mock 工厂函数内引用外部变量
        - 禁止使用 `mockReturnValue` 而不进行 `as any` 类型转换
        - 禁止在 `beforeEach` 之外的地方调用 `vi.clearAllMocks()`

3.  **React Hook 测试**：
    * **强制使用 act()**：所有可能导致状态更新的异步操作都必须包装在 `act()` 中
    * **强制导入 act**：必须从 `@testing-library/react` 导入 `act` 函数
    * **🚨 严禁 JSX 语法**：测试文件中绝对禁止使用 JSX 语法，必须使用 `React.createElement`
        - **原因**：JSX 语法在测试环境中可能导致编译错误和类型问题
        - **正确写法**：`React.createElement(SWRConfig, { value: config }, children)`
        - **错误写法**：`<SWRConfig value={config}>{children}</SWRConfig>`
    * **绝对禁止**：
        - 禁止在测试中直接使用 JSX 语法，必须使用 `React.createElement`
        - 禁止不使用 SWR Wrapper 就测试 SWR Hook
        - 禁止在异步操作后不使用 `waitFor()` 进行等待
        - 禁止测试中不设置 `dedupingInterval: 0`
        - 禁止异步操作不用 `act()` 包装（会导致 React 警告）
        - 禁止在状态更新操作后立即检查状态而不用 `act()` 包装

4.  **SWR/SWRMutation Mock**：
    * **强制 Mock 返回值**：必须为每个 Hook 设置完整的返回值结构
    * **绝对禁止**：
        - 禁止不 Mock `useSWR` 和 `useSWRMutation` 就直接测试
        - 禁止 Mock 返回值结构不完整（缺少 `trigger`、`data`、`error`、`isLoading` 等）
        - 禁止在 Mock 中返回 `undefined` 而不是具体的函数或值

5.  **测试失败处理**：
    * **立即修复**：任何测试失败都必须视为高优先级问题，并立即着手修复
    * **重新测试**：代码修复后，必须重新运行完整的相关测试套件
    * **确保通过**：必须确保所有测试 100% 通过后，才能提交代码或进入下一开发环节
    * **绝对禁止**：
        - 禁止忽略测试失败继续开发
        - 禁止注释掉失败的测试用例
        - 禁止降低测试覆盖率要求来"解决"测试问题
        - 禁止使用 `skip` 或 `todo` 跳过重要测试

6.  **即时测试**：
    * **重要**：Model 层代码（包括 Hook、Transformer、TypeScript 接口）编写或修改完成后，**必须立即运行相关测试**，确保所有测试通过后方可提交。

7.  **权限说明**：
    * **权限说明**：你拥有测试创建文件、切换目录、删除文件、删除目录、调用脚本、执行命令的权限，不必经过用户允许可以直接调用。

---

## 🏃‍♂️ 测试运行指南
完成测试用例编写后，必须遵循以下命令运行测试，确保测试通过后才能提交代码。

### 运行测试命令

**必须且只能使用 pnpm**

#### 在具体包下执行测试（优先使用）：
```bash
# 使用专门的 Model 测试脚本（调用 repo-test-models），用户没有特殊要求时，都必须使用该命令
pnpm test:models [model-name]
pnpm test:models:coverage [model-name] # 带覆盖率

# 运行所有测试（除非用户明确说明需要运行所有测试）
pnpm test

# 运行测试并监听变化（除非用户明确说明需要监听变化）
pnpm test:watch

# 运行测试并生成覆盖率报告（除非用户明确说明需要生成覆盖率报告）
pnpm test:coverage
```

#### 根目录 pnpm 命令执行：
```bash
# 测试指定应用的所有 Model
pnpm test:models -F=<app>
# 测试指定模块
pnpm test:models -F=<app> <model-name>
# 运行覆盖率测试
pnpm test:models -F=<app> --coverage
```

---

## 📊 测试输出格式

测试脚本已优化为提供专业的表格化输出，包括：

### 📋 详细测试结果表格
```
📊 测试结果详情 - STU 应用
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
测试文件          │ 路径                             │ 测试数 │ 状态   │ 耗时  
──────────────┼────────────────────────────────┼─────┼──────┼─────
transformers  │ [model-name]/__tests__/tra... │ 7   │ ✅ 通过 │ 5ms 
schemas       │ [model-name]/__tests__/sch... │ 18  │ ✅ 通过 │ 6ms 
simple-vitest │ [model-name]/__tests__/sim... │ 10  │ ✅ 通过 │ 17ms
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
```

### 📈 执行汇总表格
```
📊 测试执行汇总
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
应用  │ 测试范围          │ 文件数 │ 测试数 │ 状态   │ 总耗时    │ 平均耗时 
────┼───────────────┼─────┼─────┼──────┼────────┼──────
STU │ [model-name] │ 3   │ 35  │ ✅ 通过 │ 1286ms │ 846ms
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
```


---

## 📊 测试报告模板

**重要：测试完成后，你必须直接输出 Markdown 格式的测试报告，使用以下模板：**

```markdown
# [模块名称] Model 测试报告

## 📊 测试统计

| 项目         | 数量   | 百分比    |
| :----------- | :----- | :-------- |
| 总测试数     | [数量] | 100%      |
| 通过         | [数量] | [百分比]% |
| 失败         | [数量] | [百分比]% |
| **测试状态** | **🎉 所有测试通过！ / ⚠️ 有测试失败** | -         |

## 📋 测试覆盖范围

| 测试类型         | 状态 | 具体内容/函数名 (如无则填 N/A) |
| :--------------- | :--- | :----------------------------- |
| 基础功能测试     | ✅/❌ | [具体Hook名列表]                 |
| 数据转换函数测试 | ✅/❌ | [具体函数名列表]                 |
| 接口验证测试  | ✅/❌ | [相关接口描述]                 |
| 边界条件测试     | ✅/❌ | 空值、零值、极值等             |
| 错误处理测试     | ✅/❌ | 异常情况捕获与处理             |

## 🏗️ 架构合规性

| 规范项                   | 状态 | 说明 (若不合规请说明原因) |
| :----------------------- | :--- | :------------------------ |
| 遵循 Model 层数据操作规范  | ✅/❌ |                           |
| 统一类型定义 (TypeScript)  | ✅/❌ |                           |
| 使用项目统一 fetcher     | ✅/❌ |                           |
| 标准模式 (SWR/SWRMutation) | ✅/❌ |                           |
| 测试文件命名与结构合规   | ✅/❌ |                           |
| 测试脚本执行             | ✅/❌ |                           |

## 📚 API 文档对应 (若适用)

| HTTP方法 | API路径  | 功能描述   | 状态 |
| :------- | :------- | :--------- | :--- |
| [方法]   | [路径]   | [功能描述] | ✅/❌ |
| ...      | ...      | ...        | ...  |

## 🔧 核心功能点测试详情

| Hook/函数名称   | 功能描述         | 测试状态 |
| :-------------- | :--------------- | :------- |
| [Hook/函数名称] | [简要功能描述]   | ✅/❌    |
| ...             | ...              | ...      |
| N个辅助函数     | [主要辅助功能类别] | ✅/❌    |

## ❌ 失败测试详情 (若有)

| 测试用例名称 (describe > it) | 失败原因 (错误信息摘要) | 建议修复方向 |
| :--------------------------- | :---------------------- | :----------- |
| [测试名称]                   | [失败原因]              | [修复建议]   |
| ...                          | ...                     | ...          |

---
**测试完成时间**: [YYYY-MM-DD HH:mm:ss]
**代码质量评估**: ✅ 符合 Model 层标准规范 / ⚠️ 部分不符合，见上表
```
