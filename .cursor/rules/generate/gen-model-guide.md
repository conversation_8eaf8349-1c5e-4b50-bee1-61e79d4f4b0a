---
description: 
globs: 
alwaysApply: false
---
# Model 层代码编写指南

## 🎯 核心原则

1. **只做数据相关操作** - API 调用、数据转换、缓存
2. **统一类型定义** - 使用 TypeScript 接口定义所有数据类型
3. **不处理业务逻辑** - 复杂业务逻辑放 ViewModel
4. **不处理任何 Mock 逻辑** - 不得包含 Mock API 的具体实现逻辑
5. **工作流程** 
  - 编写代码
  - 编写文档
  - 编写测试用例（必须使用指南 test-model-guide.mdc）
  - 运行测试（必须使用指南 test-model-guide.mdc），强制使用 `pnpm run test:model [model-name]` 命令执行测试
  - 修复测试（必须基于指南中的说明 test-model-guide.mdc）
  - 输出model 层自查清单和测试报告

## 📂 目录结构

### 🎯 结构选择策略

根据模块复杂度选择合适的文件组织方式：

#### 1. 简单模块（推荐） - 单文件模式
**适用条件**：接口数量 ≤ 3个 且 类型定义 ≤ 10个 且 无复杂数据转换

```
apps/(tch|stu|admin)/app/models/
├── simple-module/
│   ├── index.ts             # 包含所有内容的单文件
│   ├── README.md            # 模块文档
│   └── __tests__/           # 测试目录
│       └── simple-vitest.test.ts    # 基础功能测试
```

**单文件模式示例**：
```typescript
// index.ts - 包含所有内容
'use client';

import { post } from '../../utils/fetcher';
import useSWRMutation from 'swr/mutation';

// ==================== 类型定义 ====================

export interface RequestPayload {
  id: string;
  data: Record<string, any>;
}

export interface ApiResponse {
  // 简单的API响应结构
}

export interface Result {
  success: boolean;
  message?: string;
}

// ==================== Hook 实现 ====================

function handleApiError(error: any, defaultMessage = "网络请求失败") {
  return error?.message?.message || error?.message || defaultMessage;
}

export function useSubmitData() {
  const { trigger, isMutating, error } = useSWRMutation(
    '/api/endpoint',
    async (url, { arg }: { arg: RequestPayload }) => {
      try {
        const response = await post<ApiResponse>(url, { arg });
        return { success: true, message: '操作成功' };
      } catch (err) {
        const errorMessage = handleApiError(err, "操作失败");
        throw new Error(errorMessage);
      }
    }
  );

  return { submitData: trigger, isSubmitting: isMutating, submitError: error };
}
```

#### 2. 复杂模块 - 分文件模式
**适用条件**：接口数量 > 3个 或 类型定义 > 10个 或 有复杂数据转换逻辑

```
apps/(tch|stu|admin)/app/models/
├── complex-module/
│   ├── [module-name]-model.ts    # 主要 Model 文件，包含所有 Hooks
│   ├── types.ts                  # TypeScript 类型定义（前端传参和后端返回数据）
│   ├── transformers.ts           # 数据转换函数（仅在需要时创建）
│   ├── index.ts                  # 统一导出
│   ├── README.md                 # 模块文档
│   └── __tests__/                # 测试目录
│       ├── simple-vitest.test.ts    # 基础功能测试
│       └── transformers.test.ts     # 转换层测试（仅在有转换逻辑时创建）
├── global-index.ts              # 统一导出文件
└── README.md                    # Model 层文档索引
```

### 📏 复杂度判断标准

| 维度 | 简单模块阈值 | 复杂模块特征 |
|------|-------------|-------------|
| **接口数量** | ≤ 3个 API | > 3个 API |
| **类型定义** | ≤ 10个接口/类型 | > 10个接口/类型 |
| **Hook数量** | ≤ 3个 Hook | > 3个 Hook |
| **数据转换** | 无需转换或简单映射 | 复杂字段映射、数据重组 |
| **文件长度** | 单文件 < 200行 | 单文件 > 200行 |

### 目录结构规范
- **[model-name]**: 结合实际的 api 文档来定义 model-name
- **优先简单模式** - 默认使用单文件模式，除非确实需要分离
- **按需拆分** - 只有在复杂度超过阈值时才拆分文件
- **渐进式扩展** - 可以从简单模式开始，随着复杂度增加再拆分
- **transformers.ts 按需创建** - 默认不创建，仅在需要复杂数据转换时添加


## 📝 类型定义规范

### 后端返回数据 - 用 TypeScript 接口
```typescript
export interface ApiUser {
  user_id: number;
  user_name: string;
  status: 'active' | 'inactive';
}
```

### 前端传参 - 用 TypeScript 接口
```typescript
export interface CreateUserPayload {
  name: string;
  email: string;
  role: 'admin' | 'user';
}
```

## 🚨 重要说明：默认不使用 Zod
- **默认策略**：Model 层默认不使用 Zod 进行运行时验证
- **仅使用 TypeScript**：依赖 TypeScript 的编译时类型检查确保类型安全
- **特殊情况**：只有当用户明确要求使用 Zod 时才添加 schemas.ts 文件和相关验证逻辑
- **简化流程**：减少依赖和复杂度，提高开发效率

## 📋 TypeScript 接口设计最佳实践

### 🎯 数值字段类型定义
**重要：数值字段应明确标注业务约束和期望值范围**

```typescript
// ✅ 正确的类型定义
export interface ApiProgress {
  /** 当前进度，非负数 */
  currentProgress: number;
  /** 预计总数，非负数 */
  totalEstimated: number;
  /** 进度百分比，范围 0-100 */
  progressPercentage: number;
  /** 当前位置，非负数 */
  currentPosition: number;
}

export interface ApiTiming {
  /** 当前时间，非负数 */
  currentTime: number;
  /** 总时间，非负数 */
  totalTime: number;
}
```

### 🔍 业务逻辑类型要求
- **进度相关字段**：使用注释说明非负数约束，百分比说明 0-100 范围
- **计数相关字段**：使用注释说明非负数约束
- **时间相关字段**：使用注释说明非负数约束
- **枚举值字段**：使用 union types 且与 API 文档一致
- **ID 类字段**：使用注释说明有效 ID 要求

### ⚠️ TypeScript 接口强制检查清单
写接口时必须检查：
- [ ] 所有数值字段是否有明确的注释说明约束？
- [ ] 百分比字段是否注明 0-100 范围？
- [ ] 计数字段是否注明非负数约束？
- [ ] 枚举值是否与 API 文档完全匹配？
- [ ] 可选字段是否正确使用了 `?` 或 `| null | undefined`？
## 🌐 网络请求规范

### 统一 Fetcher 使用
**必须使用项目统一的 fetcher，禁止直接使用 fetch API**

- 如果 fetcher 中缺少所需的 HTTP 方法，必须停止任务并询问用户，如：
  ```
  大帅：我发现当前项目的 fetcher 工具中缺少 DELETE 方法。
  
  当前可用方法：GET, POST
  需要的方法：DELETE
  
  请问您希望我：
  1. 为 fetcher 添加 DELETE 方法
  2. 使用其他替代方案
  3. 暂停此任务
  ```

```typescript
// ✅ 正确：使用当前项目下的 fetcher
import { get, post } from '@/app/utils/fetcher';

// ❌ 错误：绝对禁止直接使用 fetch
const response = await fetch('/api/users');
```

### Fetcher 特性
- **自动添加设备ID** - 每个请求自动携带 `device_id` 头部
- **统一错误处理** - 自动处理 HTTP 错误和业务错误码
- **类型安全** - 支持泛型，确保返回数据类型安全
- **标准化响应** - 自动解析 `{ code, data, message }` 格式

### GET 请求规范
```typescript
// 基础 GET 请求
const users = await get<User[]>('/api/users', {});

// 带查询参数的 GET 请求
const users = await get<User[]>('/api/users', {
  query: { page: '1', limit: '10' }
});
```

### POST 请求规范
```typescript
// 基础 POST 请求
const result = await post<CreateUserResult>('/api/users', {
  arg: { name: 'John', email: '<EMAIL>' }
});

// 无参数 POST 请求
const result = await post<Result>('/api/action', {});
```

## 🔄 标准模式

### 数据获取 Hook
```typescript
import { get } from '@/app/utils/fetcher';

export function useUsers() {
  const { data, error, isLoading, mutate } = useSWR('/api/users', async (url) => {
    const response = await get<ApiUser[]>(url, {});
    return transformUsersData(response); // 内部使用 TypeScript 类型检查
  });
  
  return { users: data, error, isLoading, refreshUsers: mutate };
}
```

### 数据提交 Hook
```typescript
import { post } from '@/app/utils/fetcher';

export function useSubmitAnswer() {
  const { trigger, isMutating, error } = useSWRMutation('/api/answers',
    async (url, { arg }: { arg: SubmitAnswerPayload }) => {
      const response = await post<ApiAnswerResult>(url, { arg }); // arg 用 TS 类型
      return transformAnswerResult(response); // response 使用 TypeScript 类型检查
    }
  );
  
  return { submitAnswer: trigger, isSubmitting: isMutating, submitError: error };
}
```

### 带查询参数的数据获取 Hook
```typescript
import { get } from '@/app/utils/fetcher';

export function useUserList(params?: { page?: number; limit?: number }) {
  const shouldFetch = params?.page && params?.limit;
  const queryParams = shouldFetch
    ? { page: params.page.toString(), limit: params.limit.toString() }
    : undefined;

  const { data, error, isLoading, mutate } = useSWR(
    shouldFetch ? ['/api/users', queryParams] : null,
    async ([url, query]) => {
      const response = await get<ApiUser[]>(url, { query });
      return transformUsersData(response);
    }
  );

  return { users: data, error, isLoading, refreshUsers: mutate };
}
```
### 🔄 数据转换策略（默认不需要）

**⚠️ 重要决策：默认情况下不创建转换函数，直接使用 API 返回的数据结构**

#### 🚫 默认策略：无转换函数
```typescript
// ✅ 推荐的默认方式 - 无转换函数
export function useReportInteractiveExplanation() {
  const { trigger, isMutating, error } = useSWRMutation(
    '/api/endpoint',
    async (url, { arg }: { arg: ReportPayload }) => {
      const response = await post<ApiResponse>(url, { arg });
      // 直接返回 API 响应，或简单包装
      return {
        success: true,
        message: '操作成功',
        data: response
      };
    }
  );
  return { reportData: trigger, isReporting: isMutating, reportError: error };
}
```

#### 🎯 何时需要添加转换函数？
**只有在以下情况下才考虑添加 transformers.ts 文件：**

1. **字段名不匹配** - 后端使用 `user_id`，前端需要 `id`
2. **复杂数据重组** - 需要将多个 API 字段合并或拆分
3. **数据类型转换** - 日期字符串转 Date 对象，数字字符串转 number
4. **业务逻辑计算** - 基于 API 数据计算衍生字段
5. **兼容性处理** - 处理不同版本 API 的数据格式差异

#### 🚨 何时绝对不需要转换函数？
- **API 返回空对象 `{}`** - 如互动解题记录上报接口
- **简单的增删改查** - 标准 CRUD 操作
- **数据结构已经理想** - 后端设计合理，直接使用
- **单纯的状态包装** - 只是添加 success/message 等状态字段

#### 💡 转换函数示例（仅在需要时）
```typescript
// ✅ 复杂转换场景 - 才需要创建 transformers.ts
function transformUserProfile(apiData: ApiUserProfile): UserProfile {
  return {
    id: apiData.user_id,                           // 字段名转换
    fullName: `${apiData.first_name} ${apiData.last_name}`, // 数据合并
    birthDate: new Date(apiData.birth_date),       // 类型转换
    isActive: apiData.status === 'active',         // 业务逻辑转换
    permissions: apiData.roles?.map(role => role.permissions).flat() || [] // 复杂重组
  };
}

// ❌ 不必要的转换 - 直接使用原数据
function transformSimpleResponse(apiData: ApiResponse): ApiResponse {
  return apiData; // 这种转换毫无意义
}
```

#### 📋 转换函数决策清单
在考虑是否添加转换函数时，问自己：
- [ ] API 数据是否需要字段重命名？
- [ ] 是否需要复杂的数据重组或计算？
- [ ] 是否需要类型转换（字符串→数字、日期等）？
- [ ] 前端是否真的需要与 API 不同的数据结构？
- [ ] 这种转换是否会在多个地方重复使用？

**如果以上问题答案都是"否"，则不需要创建转换函数。**

## ❌ 禁止操作

```typescript
// ❌ UI 状态和业务逻辑
const [isLoading, setIsLoading] = useState(false);
const handleUserClick = () => { /* 应在 ViewModel */ };

// ❌ 路由和组件生命周期
const router = useRouter();
useEffect(() => { /* 应在 ViewModel */ }, []);

// ❌ 直接使用 fetch API
const response = await fetch('/api/users');
const data = await fetch('/api/users', { method: 'POST' });

// ❌ 自定义 fetcher 函数
async function customFetcher(url: string) {
  return fetch(url).then(res => res.json());
}
```

## ✅ 模块结构检查清单

### 📁 文件结构决策
写 Model 层代码前先判断复杂度：
- [ ] 接口数量 ≤ 3个？→ 使用单文件模式
- [ ] 类型定义 ≤ 10个？→ 使用单文件模式  
- [ ] 无复杂数据转换？→ 使用单文件模式
- [ ] 超过以上任一阈值？→ 使用分文件模式

### 🔄 数据转换决策
- [ ] API 返回的数据结构是否直接可用？
- [ ] 是否真的需要字段重命名或数据重组？
- [ ] 如果不需要复杂转换，是否避免创建 transformers.ts？
- [ ] 是否避免了不必要的数据包装？

### 📝 代码质量检查
写 Model 层代码时问自己：
- [ ] 是否只做数据获取相关操作？
- [ ] 后端数据用了 TypeScript 接口定义？
- [ ] 前端传参用了 TypeScript 类型？
- [ ] 没有处理业务逻辑？
- [ ] 使用了项目统一的 fetcher？
- [ ] 正确导入了 `get`、`post` 等方法？
- [ ] 生成的 Hook 数量是否匹配 API 文档中的接口数量？
- [ ] 生成的代码参数是否匹配 API 文档规范？

## 🔍 TypeScript 接口设计检查清单

写 TypeScript 接口时必须检查：
- [ ] 所有数值字段是否有明确的注释说明约束？
- [ ] 进度相关字段是否注明非负数约束？
- [ ] 百分比字段是否注明 0-100 范围？
- [ ] 计数字段是否注明非负数约束？
- [ ] 时间相关字段是否注明非负数约束？
- [ ] 枚举值是否使用 union types 且与 API 文档一致？
- [ ] 可选字段是否正确使用了 `?` 或 `| null | undefined`？
- [ ] ID 类字段是否注明有效 ID 要求？
## � 自查清单模板

**重要：Model 层代码完成后，必须使用以下模板输出自查清单报告**

```markdown
# 📋 Model 层自查清单报告

## 🎯 核心原则检查

| 检查项 | 状态 | 说明 |
|--------|------|------|
| 只做数据相关操作 | ✅ | 仅包含 API 调用、数据转换、缓存 |
| 统一类型定义 | ✅ | 使用 TypeScript 接口统一定义所有数据类型 |
| 不处理业务逻辑 | ✅ | 复杂业务逻辑留给 ViewModel |
| 不处理 Mock 逻辑 | ✅ | 无 Mock API 具体实现 |

## 📂 模块结构检查

| 检查项 | 状态 | 说明 |
|--------|------|------|
| 文件结构决策合理 | ✅ | 根据复杂度选择单文件/分文件模式 |
| 转换函数按需创建 | ✅ | 仅在真正需要时才创建 transformers.ts |
| 避免过度工程化 | ✅ | 没有创建不必要的文件和抽象 |

## 📝 类型定义检查

| 检查项 | 状态 | 说明 |
|--------|------|------|
| 后端数据 TypeScript 接口 | ✅ | 所有 API 响应都有对应的 TypeScript 接口 |
| 前端传参 TypeScript 类型 | ✅ | 所有请求参数都有 TypeScript 类型定义 |
| 数据转换函数 | ✅/N/A | 处理后端字段与前端期望的差异（如需要） |


## 📊 代码质量/规范检查

| 检查项 | 状态 | 说明 |
|--------|------|------|
| API 数量匹配 | ✅ | 生成的 Hook 数量与 API 文档一致 |
| 参数完全匹配 | ✅ | 所有参数与 API 文档规范一致 |
| 导入路径正确 | ✅ | 正确导入 `get`、`post` 方法 |
| 类型安全 | ✅ | 完整的 TypeScript 类型覆盖 |
| 使用项目统一 fetcher | ✅ | 导入并使用 `@/app/utils/fetcher` |

## 📚 文档完成检查

| 检查项 | 状态 | 说明 |
|--------|------|------|
| 模块 README | ✅ | 包含使用示例和 API 说明 |
| 全局 README 更新 | ✅ | 在全局 models README 中添加新模块 |
| 统一导出更新 | ✅ | 在 models/index.ts 中导出新模块 |


## 🧪 测试完成检查

| 测试类型 | 状态 | 说明 |
|----------|------|------|
| 基础功能测试 | ✅ | 所有 Hook 的基本功能测试通过 |
| 转换函数测试 | ✅ | 数据转换逻辑测试通过（如有） |
| 错误处理测试 | ✅ | 异常情况处理测试通过 |
| Mock 规范遵循 | ✅ | 使用正确的 Vitest Mock 语法 |

---
**自查完成时间**: [时间]
**整体状态**: 🎉 所有检查项通过 / ⚠️ 存在问题需要修复
```

### 使用说明
- ✅ 表示已完成的项目
- ❌ 表示未完成或有问题的项目
- ⚠️ 表示需要注意的项目
- 🎉 表示全部完成
- 🎉 表示全部完成


##  文档化规范 (README.md)

### Model 层 README 位置
- `app/models/README.md` - 应用内 Model 层文档
- `packages/[package]/models/README.md` - 共享包 Model 层文档

### README 内容规范

```markdown
# Models

## 可用 Model Hooks

| Hook 名称 | 描述 | 主要接口 | 依赖 API |
|-----------|------|----------|----------|
| `useUsers` | 获取用户列表数据 | `users, isLoading, error, refreshUsers` | GET `/api/users` |
| `useSubmitAnswer` | 提交答案数据 | `submitAnswer, isSubmitting, submitError` | POST `/api/answers` |

## 核心数据结构

- `ApiUser`, `User` - 用户数据类型
- `ApiAnswer`, `Answer` - 答案数据类型
```

### 维护要求
- **新增模块时**：必须同步更新 README.md
- **开发前检查**：查阅 README.md 了解可复用模块
- **Code Review**：检查文档更新情况

## 🔧 命名规范

- **组件/Hooks**：PascalCase (`useUsers`, `UserList`)
- **文件名**：kebab-case (`user-model.ts`, `use-user-viewmodel.ts`)
- **类型定义**：PascalCase (`CreateUserPayload`, `ApiUserSchema`) 


## 🎭 数据 Mocking 策略

**Model 层和 ViewModel 层严禁包含 Mock 数据实现。**

### 1. API 中心化 Mock (首选)
- 使用 ApiFox、Postman Mock Servers 等专业工具
- 通过环境变量 `NEXT_PUBLIC_API_HOST` 指向 Mock 服务器
- 团队共享一致的 Mock API，避免数据不一致

### 2. 前端项目级 Mock API

#### 使用边界判断
**✅ 适合使用前端 Mock API 的场景：**
- **数据流转性强的业务**：数据状态有强依赖关系，随机 Mock 无法匹配真实业务流程
- **复杂业务流程**：多步骤操作，需要模拟状态变化和流程推进
- **前端先行开发**：不依赖后端进度，独立验证前端逻辑
- **特殊边缘场景**：模拟特定错误状态、异常流程等外部 Mock 工具难以支持的情况

**❌ 不适合使用前端 Mock API 的场景：**
- **简单数据展示**：静态列表、基础 CRUD 操作
- **外部 Mock 工具已满足**：ApiFox 等工具能完全覆盖的场景
- **无状态依赖**：数据间无关联关系的场景



## 🧪 下一步操作
完成 model 层代码编写后必须输出自查清单报告，并准备好进入测试流程

测试流程准备工作：
1. 必须主动说明你将使用model 测试指南 [test-model-guide.mdc](../test/test-model-guide.mdc)处理测试流程
2. 必须主动像用户询问是否需要进入测试流程，用户确认后你必须读取test-model-guide.mdc 然后开始处理测试流程
3. 测试用例编写和运行测试方式都在测试指南中说明，你必须完全严格遵守
4. 有任何问题你可以终端任务并向用户提问

如果以上准备工作确认无误，请自行进入测试流程！