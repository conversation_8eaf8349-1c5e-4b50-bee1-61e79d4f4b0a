/**
 * This injects Tailwind's base styles and any base styles registered by
 * plugins.
 */
 @tailwind base;

 a {
@apply text-blue-500;
 }

 /**
   * This injects Tailwind's component classes and any component classes
   * registered by plugins.
   */
 @tailwind components;

 .btn {
   @apply inline-block py-3 px-3 leading-none rounded shadow font-semibold text-sm
 }

 .btn-indigo {
   @apply bg-indigo-500 text-white;
 }

 .btn-indigo:hover {
   @apply bg-indigo-600;
 }

 /**
   * This injects Tailwind's utility classes and any utility classes registered
   * by plugins.
   */

 @tailwind utilities;

 @layer components {
   .btn-blue {
     @apply inline-flex items-center bg-indigo-600 border border-transparent rounded-md shadow-sm py-2 px-4 text-sm font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-0;
   }
 }
