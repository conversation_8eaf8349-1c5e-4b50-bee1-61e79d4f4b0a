// config/config.test.ts test环境对应的配置文件
import { defineConfig } from '@umijs/max';
import type { IConfigFromPlugins } from '@@/core/pluginConfig';

/**
 * 导出的多环境变量命名约定：一律大写且采用下划线分割单词
 * 注意：在添加变量后，需要在src/typing.d.ts内添加该变量的声明，否则在使用变量时IDE会报错。
 */

// 输出当前配置文件被加载的信息
console.log('======== 加载测试环境配置 config.test_xlx.ts ========');

export default defineConfig({
  define: {
    REACT_APP_ENV: 'test_xlx',
    API_URL: 'https://admin-api.test.xiaoluxue.cn', // API地址
    API_SECRET_KEY: 'XXXXXXXXXXXXXXXX', // API调用密钥
  },
}) as IConfigFromPlugins