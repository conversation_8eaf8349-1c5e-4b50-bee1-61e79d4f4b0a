import { request } from '@umijs/max';
import { ClassCreateParams, ClassDeleteParams, ClassDetail, ClassDetailParams, ClassUpdateParams, GradeInfo, GradeScheduleTemplateParams, ScheduleData, TeacherSchedule, } from './type';
import { TeacherInfo } from '@/pages/partner-school/schoolManagement/components/TeacherAccount/type';


// 获取班级树
export async function getClassTree(params: {
  class_school_id: number;
  class_school_year_id: number;
}) {
  return request<API.ResponseBody<{
    total: number;
    list: GradeInfo[];
  }>>(`/api/v1/class/tree`, {
    method: 'GET',
    params,
  });
}

// 创建年级课表模板
export async function createGradeScheduleTemplate(params: GradeScheduleTemplateParams) {
  return request<API.ResponseBody<any>>(`/api/v1/grade_schedule_tpl/create`, {
    method: 'POST',
    data: params,
  });
}

// 更新年级课表模板
export async function updateGradeScheduleTemplate(params: GradeScheduleTemplateParams & { id: number }) {
  return request<API.ResponseBody<any>>(`/api/v1/grade_schedule_tpl/update`, {
    method: 'POST',
    data: params,
  });
}

// 获取年级课表模板详情
export async function getSchedule(params: {
  school_id: number;
  school_year_id: number;
  semester_id: number;
  start_date: string;
  end_date: string;
  type: 'class' | 'teacher';
  class_id?: number;
  teacher_id?: number;
  week?: number;
}) {
  return request<API.ResponseBody<ScheduleData>>(`/api/v1/schedule/weeklyGetClassSchedules`, {
    method: 'GET',
    params,
  });
}

// 下载年级课表模板
export async function downloadGradeScheduleTemplate() {
  return request(`/api/v1/grade_schedule_tpl/download_template`, {
    method: 'GET',
    responseType: 'blob',
  });
}

// 获取年级课表模板详情
export async function getGradeScheduleTemplateDetail(params: {
  school_id?: number;
  school_year_id?: number;
  grade?: number;
  semester_id?: number;
  week_start_date?: string;
  week_end_date?: string;
}) {
  return request<API.ResponseBody<ScheduleData>>(`/api/v1/grade_schedule_tpl/detail`, {
    method: 'GET',
    params,
  });
}

// 班级相关接口
// 创建班级
export async function createClass(params: ClassCreateParams) {
  return request<API.ResponseBody<any>>(`/api/v1/class/create`, {
    method: 'POST',
    data: params,
  });
}

// 更新班级
export async function updateClass(params: ClassUpdateParams) {
  return request<API.ResponseBody<any>>(`/api/v1/class/update`, {
    method: 'POST',
    data: params,
  });
}

// 获取班级详情
export async function getClassDetail(params: ClassDetailParams) {
  return request<API.ResponseBody<ClassDetail>>(`/api/v1/class/detail`, {
    method: 'GET',
    params,
  });
}

// 删除班级
export async function deleteClass(params: ClassDeleteParams) {
  return request<API.ResponseBody<any>>(`/api/v1/class/delete`, {
    method: 'POST',
    data: params,
  });
}

// 获取教师列表
export async function listTeachers(params: {
  schoolID: number;
}) {
  return request<API.ResponseBody<{
    total: number;
    list: TeacherInfo[];
  }>>(`/api/v1/user_school/listTeachers`, {
    method: 'POST',
    data: params,
  });
}



// 获取教师课表
export async function getTeacherSchedule(params: {
  school_id: number;
  school_year_id: number;
  teacher_id: number;
  start_date: string;
  end_date: string;
}) {
  return request<API.ResponseBody<{
    schedule:TeacherSchedule
  }>>(`/api/v1/schedule/weeklyGetTeacherSchedules`, {
    method: 'GET',
    params,
  });
}
