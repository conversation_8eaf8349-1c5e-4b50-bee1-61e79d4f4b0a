// src/services/dict.ts
import { request } from '@umijs/max';

// Base dictionary item interface
export interface DictItem {
  meta_dict_id: number;
  meta_dict_type: string;
  meta_dict_key: string;
  meta_dict_name: string;
  meta_dict_parent_id: number;
  meta_dict_level: number;
  meta_dict_orderby: number;
  meta_dict_status: number;
  meta_dict_cascade_dict_ids: string;
  meta_dict_child_count: number;
  meta_dict_standard_code: string;
  meta_dict_scope: string;
  children?: DictItem[];
}

// Dictionary types enum

export enum DictTypeEnum {
  PHASE = 'phase',
  EDU_SYSTEM = 'edu_system',
  SCHOOL_FEATURE = 'school_feature',
  SCHOOL_PROPERTY = 'school_property',
  SCHOOL_CP = 'school_cp',
  SUBJECT = 'subject',
}

// 获取字典类型枚举的具体值的联合类型
export type DictTypeValue = typeof DictTypeEnum[keyof typeof DictTypeEnum];

export type DictType = keyof typeof DictTypeEnum;

// API response interface
export type DictResponse = Record<DictType, DictItem>
// Formatted option interface for UI components
export interface DictOption extends DictItem {
  label: string;
  value: string | number;
  children?: DictOption[];
}

// Dictionary API function
export async function getDictByType(dictTypes?: DictTypeValue[]) {
  return request<API.ResponseBody<DictResponse>>(`/api/v1/dict/batch_tree`, {
    method: 'POST',
    data: {
      meta_dict_types: dictTypes || Object.values(DictTypeEnum),
      meta_dict_scope: 'sys',
    },
  });
}

// Helper function to transform DictItem to DictOption
export function transformToDictOption(item?: DictItem): DictOption {
  if (!item) {
    return {} as DictOption;
  }
  const option = {
    label: item.meta_dict_name,
    value: Number(item.meta_dict_key),
    ...item,
  };

  if (item.children?.length) {
    option.children = item.children.map(transformToDictOption);
  }

  return option as DictOption;
}
