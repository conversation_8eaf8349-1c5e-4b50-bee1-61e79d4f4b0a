import { request } from '@umijs/max';

// 上传文件
export async function uploadFile(file: File, type: string) {
  // 开发环境使用mock数据
  if (process.env.NODE_ENV === 'development') {
    return new Promise((resolve) => {
      setTimeout(() => {
        if (file.size < 5000000) {
          resolve({
            success: true,
            fileId: `file_${Date.now()}`,
          });
        } else {
          resolve({
            success: false,
            error: '文件大小超过限制',
          });
        }
      }, 1500);
    });
  }

  const formData = new FormData();
  formData.append('file', file);
  formData.append('type', type);

  return request('/api/upload', {
    method: 'POST',
    data: formData,
  });
}

// 下载模板
export async function downloadTemplate(type: string) {
  if (process.env.NODE_ENV === 'development') {
    return new Promise((resolve) => {
      setTimeout(() => {
        // 模拟返回一个空的 Blob 对象
        resolve(new Blob(['模拟模板文件内容'], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' }));
      }, 1000);
    });
  }

  return request(`/api/template/${type}`, {
    method: 'GET',
    responseType: 'blob',
  });
}

// 导入数据
export async function importData(fileId: string, params?: Record<string, any>) {
  if (process.env.NODE_ENV === 'development') {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          success: true,
          taskId: `task_${Date.now()}`,
        });
      }, 1000);
    });
  }

  return request('/api/import', {
    method: 'POST',
    data: {
      fileId,
      ...params,
    },
  });
}

