import { goLogin } from '@/pages/user/login';
import { message as Message } from 'antd';
export const getToken = () => {
  const token = localStorage.getItem('token');
  return token ? `Bearer ${token}` : '';
};
const baseURL = process.env.NODE_ENV === 'development' ? '' : API_URL || '';

const fetcher = async <T>(url: string | URL | globalThis.Request, init?: RequestInit) => {
  const response = await fetch(baseURL + url, {
    ...init,
    headers: {
      Authorization: getToken(),
      ...init?.headers,
    },
  });
  if (!response.ok) {
    Message.error('服务器错误，请稍后再试！');
    const error = new Error('服务器错误，请稍后再试！');
    // 将额外的信息附加到错误对象上。
    error.message = await response.json();
    console.log('fetcher error', error);

    throw error;
  }
  const res = await response.json();
  const { code, message, data } = res;
  // console.log('fetcher res', res);
  if (code === 401) {
    Message.error(res.message || '登录已过期，请重新登录');
    localStorage.removeItem('token');
    goLogin();
    return;
  }

  if (code !== 0) {
    Message.error(message + url);
    throw new Error(message);
  }
  return data as T;
};

export async function get<T>(url: string) {
  return await fetcher<T>(url, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
  });
}

export async function getWithQuery<T>(url: string, { arg }: { arg: object }) {
  const params = new URLSearchParams(arg as Record<string, string>);
  return get<T>(`${url}?${params.toString()}`);
}

export async function search<T>({ url, query }: { url: string; query: object }) {
  const queryParams = new URLSearchParams();

  Object.entries(query).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      queryParams.append(key, String(value));
    }
  });
  const queryString = queryParams.toString();
  const fullUrl = queryString ? `${url}?${queryString}` : url;

  return await fetcher<T>(fullUrl, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
  });
}

export async function post<T, U extends object = object>(url: string, { arg }: { arg: U }) {
  return await fetcher<T>(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(arg),
  });
}

// formdata 上传
export async function upload<T>(url: string, { arg }: { arg: FormData }) {
  return await fetcher<T>(url, {
    method: 'POST',
    body: arg,
  });
}

// 抽离下载文件的功能为独立函数
export function downloadFile(blob?: Blob, fileName?: string) {
  if (!blob) {
    return;
  }
  const objectUrl = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = objectUrl;
  link.download = fileName || 'downloaded_file';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(objectUrl);
}

export default fetcher;
