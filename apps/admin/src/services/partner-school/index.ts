import { request } from '@umijs/max';
import type {
  CreateSchoolParams,
  RegionItem,
  SchoolListParams,
  UpdateSchoolParams,
  MySchoolListParams,
} from './types';
import { SchoolItem } from '@/types/school';
import { Menu } from '@/types/menu';

/**
 * 获取合作校列表
 */
export async function getSchoolList(data: SchoolListParams) {
  return request<API.ResponseBody<SchoolItem[]>>(`/api/v1/school/listSchools`, {
    method: 'POST',
    data,
  });
}

/**
 * 获取我的合作校列表
 */
export async function getMySchoolList(data: MySchoolListParams) {
  return request<API.ResponseBody<SchoolItem[]>>(`/api/v1/school/listMySchools`, {
    method: 'POST',
    data,
  });
}

/**
 * 创建合作校
 */
export async function createSchool(data: CreateSchoolParams) {
  return request<API.ResponseBody<SchoolItem>>(`/api/v1/school/createSchool`, {
    method: 'POST',
    data,
  });
}

/**
 * 更新合作校信息
 */
export async function updateSchool(data: UpdateSchoolParams) {
  return request<API.ResponseBody<SchoolItem>>(`/api/v1/school/updateSchool`, {
    method: 'POST',
    data,
  });
}

/**
 * 删除合作校
 */
export async function deleteSchool(schoolId: string) {
  return request<API.ResponseBody<null>>(`/delete/${schoolId}`, {
    method: 'DELETE',
  });
}

/**
 * 获取合作校详情
 */
export async function getSchoolDetail(schoolID: number) {
  return request<API.ResponseBody<SchoolItem>>(`/api/v1/school/getSchoolByID`, {
    method: 'GET',
    params: {
      schoolID,
    },
  });
}

/**
 *获取顶级省份列表
 */
export async function getTopProvinceList() {
  return request<API.ResponseBody<RegionItem[]>>(`/api/v1/dict/listAllTopProvinces`, {
    method: 'GET',
  });
}

/**
 * 获取子区域列表
 */
export async function getProvinceList(regionDictCode: number) {
  return request<API.ResponseBody<RegionItem[]>>(`/api/v1/dict/listSubProvinces`, {
    method: 'GET',
    params: {
      regionDictCode,
    },
  });
}

/**
 * 获取学校权限列表
 */
export async function getSchoolPermissions(schoolId: number, userId: number) {
  return request<API.ResponseBody<{ list: Menu[] }>>(`/api/v1/user_school_menu/list`, {
    method: 'GET',
    params: {
      userId,
      schoolId,
    },
  });
}
