// 创建学校参数
export type CreateSchoolParams = {
  schoolNumber: string;
  schoolName: string;
  schoolRegionID: number;
  schoolAddress: string;
  schoolEduLevel: number;
  schoolEduSystem: number;
  schoolNature: number;
  schoolTag: string;
  schoolFeature: number;
  remark: string;
};

// 更新学校参数
export interface UpdateSchoolParams extends CreateSchoolParams {
  schoolId: number;
}
export interface RegionInfo {
  regionDictName: string;
  regionDictCode: number;
  regionDictParentCode: number;
}

// 查询参数类型
export interface SchoolListParams {
  schoolKey?: string;
  page: number;
  pageSize: number;
  schoolStatus?: number;
  schoolRegionID?: number;
  userID?: number;
}

// 我的学校查询参数类型
export interface MySchoolListParams extends SchoolListParams {
  userID: number;
}

// API响应类型
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  total?: number;
  message?: string;
}


export interface RegionItem {
  regionDictName: string;
  regionDictCode: number;
  regionDictParentCode: number;
  subList?: RegionItem[];
}
