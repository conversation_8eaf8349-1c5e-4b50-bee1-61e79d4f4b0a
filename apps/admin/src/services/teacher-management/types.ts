// 教师工作信息 - 年级班级
export interface JobClassInfo {
  jobGrade: number;
  jobClass: number[];
}


// 创建教师参数

// 查询参数
export interface TeacherListParams {
  schoolID: number;
  teacherName?: string;
  teacherPhone?: string;
  teacherSubject?: number;
  teacherEmployStatus?: number;
  teacherGrade?: number;
  teacherClass?: number;
}

// API响应类型
export interface TeacherResponse {
  code: number;
  data: TeacherInfo[];
  total: number;
  message?: string;
} 