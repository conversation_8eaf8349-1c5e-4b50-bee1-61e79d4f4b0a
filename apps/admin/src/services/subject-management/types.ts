export interface SubjectMaterialResponse {
  status: number;
  code: number;
  message: string;
  response_time: number;
  data: SubjectMaterialData;
}

export interface SubjectMaterialData {
  subject_material_school: SubjectMaterialSchool;
  subject_material_grade: SubjectMaterialGrade;
  subject_knowledge_tree: SubjectKnowledgeTree;
  subject_material_school_year: SubjectMaterialSchoolYear;
  class_type_subject_materials: ClassTypeSubjectMaterial[];
}

export interface ClassTypeSubjectMaterial {
  subject_material_class_type: SubjectMaterialClassType;
  subject_materials: SubjectMaterial[];
}

export interface SubjectMaterial {
  subject_material_subject: SubjectMaterialSubject;
  subject_material_material: SubjectMaterialMaterial[];
}

export interface SubjectMaterialMaterial {
  subject_material_material_id: number;
  subject_material_material_name: string;
  enable: number;
}

export interface SubjectMaterialSubject {
  subject_material_subject_id: number;
  subject_material_subject_name: string;
  enable: number;
}

export interface SubjectMaterialClassType {
  class_type_id: number;
  class_type_name: string;
}

export interface SubjectMaterialSchoolYear {
  subject_material_school_year_id: number;
  subject_material_school_year_name: string;
}

export interface SubjectKnowledgeTree {
  subject_knowledge_tree_id: number;
  subject_knowledge_tree_name: string;
}

export interface SubjectMaterialGrade {
  subject_material_grade_id: number;
  subject_material_grade_name: string;
}

export interface SubjectMaterialSchool {
  subject_material_school_id: number;
  subject_material_school_name: string;
}

export interface GetSubjectMaterialParams {
  subject_material_school_id: number;
  subject_material_grade: number;
  subject_material_school_year_id: number;
} 