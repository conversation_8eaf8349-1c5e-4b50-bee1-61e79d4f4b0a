import { request } from '@umijs/max';
import type { SubjectMaterialResponse, GetSubjectMaterialParams } from './types';

export async function getSubjectMaterials(params: GetSubjectMaterialParams) {
  return request<SubjectMaterialResponse>('/api/v1/subject_material/all', {
    method: 'GET',
    params,
  });
} 

// 获取学科教材详情
export async function getSubjectMaterialDetail(params: GetSubjectMaterialParams) {
  return request<SubjectMaterialResponse>('/api/v1/subject_material/detail', {
    method: 'GET',
    params,
  });
}