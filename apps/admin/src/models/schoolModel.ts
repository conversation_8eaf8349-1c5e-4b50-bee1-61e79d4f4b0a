// src/models/useSchoolModel.ts
import { getSchoolDetail } from '@/services/partner-school'; // 假设的API服务
import { SchoolItem } from '@/types/school';

import { useCallback, useState } from 'react';

export default () => {
  const [schoolInfo, setSchoolInfo] = useState<SchoolItem>();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const fetchSchoolInfo = useCallback(async (id: number) => {
    // console.log('获取学校信息....');
    setLoading(true);
    setError(null);
    try {
      const response = await getSchoolDetail(Number(id));
      setSchoolInfo(response.data);
      return response.data;
    } catch (err: any) {
      setError(err);
      console.error('获取学校信息失败:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  const updateSchoolInfo = useCallback((newInfo: Partial<SchoolItem>) => {
    setSchoolInfo((prevInfo) => {
      if (!prevInfo) return prevInfo;
      return {
        ...prevInfo,
        ...newInfo,
      } as SchoolItem;
    });
  }, []);

  return {
    schoolInfo,
    loading,
    error,
    fetchSchoolInfo,
    updateSchoolInfo,
  };
};
