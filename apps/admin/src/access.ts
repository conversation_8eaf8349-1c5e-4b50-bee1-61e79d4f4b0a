// const transformedMenus = [
//   {
//     menuId: 100,
//     menuName: "合作学校",
//     menuParentId: 0,
//     menuOrderNum: 1,
//     menuPath: "/partner-school/list",
//     menuComponent: "partner-school/list",
//     menuIsCache: 0,
//     menuType: 1,
//     menuVisible: 0,
//     menuPerms: "",
//     menuIcon: "",
//     menuStatus: 1,
//     menuPlatformId: 1,
//     remark: "",
//     createrId: 0,
//     updaterId: 0,
//     deleted: 0,
//     createTime: "2025-03-21 19:48:54",
//     updateTime: "2025-03-24 15:45:24"
//   },
//   {
//     menuId: 101,
//     menuName: "学校管理",
//     menuParentId: 0,
//     menuOrderNum: 2,
//     menuPath: "/partner-school/management",
//     menuComponent: "partner-school/schoolManagement",
//     menuIsCache: 0,
//     menuType: 1,
//     menuVisible: 0,
//     menuPerms: "",
//     menuIcon: "",
//     menuStatus: 1,
//     menuPlatformId: 1,
//     remark: "",
//     createrId: 0,
//     updaterId: 0,
//     deleted: 0,
//     createTime: "2025-03-21 19:48:54",
//     updateTime: "2025-03-24 15:45:24"
//   },
//   {
//     menuId: 102,
//     menuName: "教务管理",
//     menuParentId: 101,
//     menuOrderNum: 1,
//     menuPath: "/partner-school/management/academic",
//     menuComponent: "",
//     menuIsCache: 0,
//     menuType: 1,
//     menuVisible: 0,
//     menuPerms: "",
//     menuIcon: "",
//     menuStatus: 1,
//     menuPlatformId: 1,
//     remark: "",
//     createrId: 0,
//     updaterId: 0,
//     deleted: 0,
//     createTime: "2025-03-21 19:48:54",
//     updateTime: "2025-03-24 15:45:24"
//   },
//   {
//     menuId: 103,
//     menuName: "学期管理",
//     menuParentId: 102,
//     menuOrderNum: 1,
//     menuPath: "/partner-school/management/academic/semester",
//     menuComponent: "partner-school/schoolManagement/components/SemesterManagement",
//     menuIsCache: 0,
//     menuType: 1,
//     menuVisible: 0,
//     menuPerms: "",
//     menuIcon: "",
//     menuStatus: 1,
//     menuPlatformId: 1,
//     remark: "",
//     createrId: 0,
//     updaterId: 0,
//     deleted: 0,
//     createTime: "2025-03-21 19:48:54",
//     updateTime: "2025-03-24 15:45:24"
//   },
//   {
//     menuId: 104,
//     menuName: "学科教材管理",
//     menuParentId: 102,
//     menuOrderNum: 2,
//     menuPath: "/partner-school/management/academic/subject",
//     menuComponent: "partner-school/schoolManagement/components/SubjectManagement",
//     menuIsCache: 0,
//     menuType: 1,
//     menuVisible: 0,
//     menuPerms: "",
//     menuIcon: "",
//     menuStatus: 1,
//     menuPlatformId: 1,
//     remark: "",
//     createrId: 0,
//     updaterId: 0,
//     deleted: 0,
//     createTime: "2025-03-21 19:48:54",
//     updateTime: "2025-03-24 15:45:24"
//   },
//   {
//     menuId: 105,
//     menuName: "课表管理",
//     menuParentId: 102,
//     menuOrderNum: 3,
//     menuPath: "/partner-school/management/academic/schedule",
//     menuComponent: "partner-school/schoolManagement/components/ScheduleManagement",
//     menuIsCache: 0,
//     menuType: 1,
//     menuVisible: 0,
//     menuPerms: "",
//     menuIcon: "",
//     menuStatus: 1,
//     menuPlatformId: 1,
//     remark: "",
//     createrId: 0,
//     updaterId: 0,
//     deleted: 0,
//     createTime: "2025-03-21 19:48:54",
//     updateTime: "2025-03-24 15:45:24"
//   },
//   {
//     menuId: 106,
//     menuName: "老师课表",
//     menuParentId: 102,
//     menuOrderNum: 4,
//     menuPath: "/partner-school/management/academic/teacherSchedule",
//     menuComponent: "partner-school/schoolManagement/components/TeacherScheduleManagement",
//     menuIsCache: 0,
//     menuType: 1,
//     menuVisible: 0,
//     menuPerms: "",
//     menuIcon: "",
//     menuStatus: 1,
//     menuPlatformId: 1,
//     remark: "",
//     createrId: 0,
//     updaterId: 0,
//     deleted: 0,
//     createTime: "2025-03-21 19:48:54",
//     updateTime: "2025-03-24 15:45:24"
//   },
//   {
//     menuId: 107,
//     menuName: "账号管理",
//     menuParentId: 101,
//     menuOrderNum: 2,
//     menuPath: "/partner-school/management/account",
//     menuComponent: "",
//     menuIsCache: 0,
//     menuType: 1,
//     menuVisible: 0,
//     menuPerms: "",
//     menuIcon: "",
//     menuStatus: 1,
//     menuPlatformId: 1,
//     remark: "",
//     createrId: 0,
//     updaterId: 0,
//     deleted: 0,
//     createTime: "2025-03-21 19:48:54",
//     updateTime: "2025-03-24 15:45:24"
//   },
//   {
//     menuId: 108,
//     menuName: "学生管理",
//     menuParentId: 107,
//     menuOrderNum: 1,
//     menuPath: "/partner-school/management/account/student",
//     menuComponent: "partner-school/schoolManagement/components/StudentManagement",
//     menuIsCache: 0,
//     menuType: 1,
//     menuVisible: 0,
//     menuPerms: "",
//     menuIcon: "",
//     menuStatus: 1,
//     menuPlatformId: 1,
//     remark: "",
//     createrId: 0,
//     updaterId: 0,
//     deleted: 0,
//     createTime: "2025-03-21 19:48:54",
//     updateTime: "2025-03-24 15:45:24"
//   },
//   {
//     menuId: 109,
//     menuName: "教师管理",
//     menuParentId: 107,
//     menuOrderNum: 2,
//     menuPath: "/partner-school/management/account/teacher",
//     menuComponent: "partner-school/schoolManagement/components/TeacherManagement",
//     menuIsCache: 0,
//     menuType: 1,
//     menuVisible: 0,
//     menuPerms: "",
//     menuIcon: "",
//     menuStatus: 1,
//     menuPlatformId: 1,
//     remark: "",
//     createrId: 0,
//     updaterId: 0,
//     deleted: 0,
//     createTime: "2025-03-21 19:48:54",
//     updateTime: "2025-03-24 15:45:24"
//   },
//   {
//     menuId: 110,
//     menuName: "权限管理",
//     menuParentId: 101,
//     menuOrderNum: 3,
//     menuPath: "/partner-school/management/permission",
//     menuComponent: "partner-school/schoolManagement/components/PermissionManagement",
//     menuIsCache: 0,
//     menuType: 1,
//     menuVisible: 0,
//     menuPerms: "",
//     menuIcon: "",
//     menuStatus: 1,
//     menuPlatformId: 1,
//     remark: "",
//     createrId: 0,
//     updaterId: 0,
//     deleted: 0,
//     createTime: "2025-03-21 19:48:54",
//     updateTime: "2025-03-24 15:45:24"
//   },
//   {
//     menuId: 111,
//     menuName: "我的合作学校",
//     menuParentId: 0,
//     menuOrderNum: 3,
//     menuPath: "/my-partner-school/list",
//     menuComponent: "partner-school/list",
//     menuIsCache: 0,
//     menuType: 1,
//     menuVisible: 0,
//     menuPerms: "",
//     menuIcon: "",
//     menuStatus: 1,
//     menuPlatformId: 1,
//     remark: "",
//     createrId: 0,
//     updaterId: 0,
//     deleted: 0,
//     createTime: "2025-03-21 19:48:54",
//     updateTime: "2025-03-24 15:45:24"
//   },
//   {
//     menuId: 112,
//     menuName: "我的学校管理",
//     menuParentId: 0,
//     menuOrderNum: 4,
//     menuPath: "/my-partner-school/management",
//     menuComponent: "partner-school/schoolManagement",
//     menuIsCache: 0,
//     menuType: 1,
//     menuVisible: 0,
//     menuPerms: "",
//     menuIcon: "",
//     menuStatus: 1,
//     menuPlatformId: 1,
//     remark: "",
//     createrId: 0,
//     updaterId: 0,
//     deleted: 0,
//     createTime: "2025-03-21 19:48:54",
//     updateTime: "2025-03-24 15:45:24"
//   },
//   {
//     menuId: 113,
//     menuName: "教务管理",
//     menuParentId: 112,
//     menuOrderNum: 1,
//     menuPath: "/my-partner-school/management/academic",
//     menuComponent: "",
//     menuIsCache: 0,
//     menuType: 1,
//     menuVisible: 0,
//     menuPerms: "",
//     menuIcon: "",
//     menuStatus: 1,
//     menuPlatformId: 1,
//     remark: "",
//     createrId: 0,
//     updaterId: 0,
//     deleted: 0,
//     createTime: "2025-03-21 19:48:54",
//     updateTime: "2025-03-24 15:45:24"
//   },
//   {
//     menuId: 114,
//     menuName: "学期管理",
//     menuParentId: 113,
//     menuOrderNum: 1,
//     menuPath: "/my-partner-school/management/academic/semester",
//     menuComponent: "partner-school/schoolManagement/components/SemesterManagement",
//     menuIsCache: 0,
//     menuType: 1,
//     menuVisible: 0,
//     menuPerms: "",
//     menuIcon: "",
//     menuStatus: 1,
//     menuPlatformId: 1,
//     remark: "",
//     createrId: 0,
//     updaterId: 0,
//     deleted: 0,
//     createTime: "2025-03-21 19:48:54",
//     updateTime: "2025-03-24 15:45:24"
//   },
//   {
//     menuId: 115,
//     menuName: "学科教材管理",
//     menuParentId: 113,
//     menuOrderNum: 2,
//     menuPath: "/my-partner-school/management/academic/subject",
//     menuComponent: "partner-school/schoolManagement/components/SubjectManagement",
//     menuIsCache: 0,
//     menuType: 1,
//     menuVisible: 0,
//     menuPerms: "",
//     menuIcon: "",
//     menuStatus: 1,
//     menuPlatformId: 1,
//     remark: "",
//     createrId: 0,
//     updaterId: 0,
//     deleted: 0,
//     createTime: "2025-03-21 19:48:54",
//     updateTime: "2025-03-24 15:45:24"
//   },
//   {
//     menuId: 116,
//     menuName: "课表管理",
//     menuParentId: 113,
//     menuOrderNum: 3,
//     menuPath: "/my-partner-school/management/academic/schedule",
//     menuComponent: "partner-school/schoolManagement/components/ScheduleManagement",
//     menuIsCache: 0,
//     menuType: 1,
//     menuVisible: 0,
//     menuPerms: "",
//     menuIcon: "",
//     menuStatus: 1,
//     menuPlatformId: 1,
//     remark: "",
//     createrId: 0,
//     updaterId: 0,
//     deleted: 0,
//     createTime: "2025-03-21 19:48:54",
//     updateTime: "2025-03-24 15:45:24"
//   },
//   {
//     menuId: 117,
//     menuName: "老师课表",
//     menuParentId: 113,
//     menuOrderNum: 4,
//     menuPath: "/my-partner-school/management/academic/teacherSchedule",
//     menuComponent: "partner-school/schoolManagement/components/TeacherScheduleManagement",
//     menuIsCache: 0,
//     menuType: 1,
//     menuVisible: 0,
//     menuPerms: "",
//     menuIcon: "",
//     menuStatus: 1,
//     menuPlatformId: 1,
//     remark: "",
//     createrId: 0,
//     updaterId: 0,
//     deleted: 0,
//     createTime: "2025-03-21 19:48:54",
//     updateTime: "2025-03-24 15:45:24"
//   },
//   {
//     menuId: 118,
//     menuName: "账号管理",
//     menuParentId: 112,
//     menuOrderNum: 2,
//     menuPath: "/my-partner-school/management/account",
//     menuComponent: "",
//     menuIsCache: 0,
//     menuType: 1,
//     menuVisible: 0,
//     menuPerms: "",
//     menuIcon: "",
//     menuStatus: 1,
//     menuPlatformId: 1,
//     remark: "",
//     createrId: 0,
//     updaterId: 0,
//     deleted: 0,
//     createTime: "2025-03-21 19:48:54",
//     updateTime: "2025-03-24 15:45:24"
//   },
//   {
//     menuId: 119,
//     menuName: "学生管理",
//     menuParentId: 118,
//     menuOrderNum: 1,
//     menuPath: "/my-partner-school/management/account/student",
//     menuComponent: "partner-school/schoolManagement/components/StudentManagement",
//     menuIsCache: 0,
//     menuType: 1,
//     menuVisible: 0,
//     menuPerms: "",
//     menuIcon: "",
//     menuStatus: 1,
//     menuPlatformId: 1,
//     remark: "",
//     createrId: 0,
//     updaterId: 0,
//     deleted: 0,
//     createTime: "2025-03-21 19:48:54",
//     updateTime: "2025-03-24 15:45:24"
//   },
//   {
//     menuId: 120,
//     menuName: "教师管理",
//     menuParentId: 118,
//     menuOrderNum: 2,
//     menuPath: "/my-partner-school/management/account/teacher",
//     menuComponent: "partner-school/schoolManagement/components/TeacherManagement",
//     menuIsCache: 0,
//     menuType: 1,
//     menuVisible: 0,
//     menuPerms: "",
//     menuIcon: "",
//     menuStatus: 1,
//     menuPlatformId: 1,
//     remark: "",
//     createrId: 0,
//     updaterId: 0,
//     deleted: 0,
//     createTime: "2025-03-21 19:48:54",
//     updateTime: "2025-03-24 15:45:24"
//   },
//   {
//     menuId: 121,
//     menuName: "权限管理",
//     menuParentId: 112,
//     menuOrderNum: 3,
//     menuPath: "/my-partner-school/management/permission",
//     menuComponent: "partner-school/schoolManagement/components/PermissionManagement",
//     menuIsCache: 0,
//     menuType: 1,
//     menuVisible: 0,
//     menuPerms: "",
//     menuIcon: "",
//     menuStatus: 1,
//     menuPlatformId: 1,
//     remark: "",
//     createrId: 0,
//     updaterId: 0,
//     deleted: 0,
//     createTime: "2025-03-21 19:48:54",
//     updateTime: "2025-03-24 15:45:24"
//   },
//   {
//     menuId: 122,
//     menuName: "系统权限管理",
//     menuParentId: 0,
//     menuOrderNum: 5,
//     menuPath: "/system-permission-management",
//     menuComponent: "system-permission-management",
//     menuIsCache: 0,
//     menuType: 1,
//     menuVisible: 0,
//     menuPerms: "",
//     menuIcon: "",
//     menuStatus: 1,
//     menuPlatformId: 1,
//     remark: "",
//     createrId: 0,
//     updaterId: 0,
//     deleted: 0,
//     createTime: "2025-03-21 19:48:54",
//     updateTime: "2025-03-24 15:45:24"
//   },
//   {
//     menuId: 123,
//     menuName: "用户管理",
//     menuParentId: 122,
//     menuOrderNum: 1,
//     menuPath: "/system-permission-management/user",
//     menuComponent: "system-permission-management/user",
//     menuIsCache: 0,
//     menuType: 1,
//     menuVisible: 0,
//     menuPerms: "",
//     menuIcon: "",
//     menuStatus: 1,
//     menuPlatformId: 1,
//     remark: "",
//     createrId: 0,
//     updaterId: 0,
//     deleted: 0,
//     createTime: "2025-03-21 19:48:54",
//     updateTime: "2025-03-24 15:45:24"
//   },
//   {
//     menuId: 124,
//     menuName: "角色管理",
//     menuParentId: 122,
//     menuOrderNum: 2,
//     menuPath: "/system-permission-management/role",
//     menuComponent: "system-permission-management/role",
//     menuIsCache: 0,
//     menuType: 1,
//     menuVisible: 0,
//     menuPerms: "",
//     menuIcon: "",
//     menuStatus: 1,
//     menuPlatformId: 1,
//     remark: "",
//     createrId: 0,
//     updaterId: 0,
//     deleted: 0,
//     createTime: "2025-03-21 19:48:54",
//     updateTime: "2025-03-24 15:45:24"
//   }
// ];

/**
 * 权限
 * @see https://umijs.org/zh-CN/plugins/plugin-access
 * */
export default function access(initialState: { currentUser?: API.UserInfo } | undefined) {
  const { currentUser } = initialState ?? {};
  const { menus } = currentUser ?? {};
  const accessMenus = menus || [];
  // const accessMenus = transformedMenus
  // console.log("权限处理 menus", menus);
  // 获取当前环境
  const env = process.env;
  const baseURL = env.NODE_ENV === 'development' ? '' : API_URL || '';
  console.log('env', env, baseURL, REACT_APP_ENV);
  const accessMap: Record<string, boolean> = {
    '/': true,
    canReadAllSchool: accessMenus.some((menu) => menu.menuPath === '/partner-school/list'),
  };

  // 处理菜单权限
  accessMenus?.forEach((menu) => {
    // 如果是总部管理员，禁用所有 my-partner-school 相关权限
    if (accessMap.canReadAllSchool && menu.menuPath.includes('/my-partner-school')) {
      accessMap[menu.menuPath] = false;
    } else {
      accessMap[menu.menuPath] = true;
    }
  });

  // console.log('权限处理 accessMap', accessMap);

  return accessMap;
}
