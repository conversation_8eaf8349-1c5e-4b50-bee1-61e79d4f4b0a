import dayjs from "dayjs";
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';

dayjs.extend(utc);
dayjs.extend(timezone);

export const scrollToTop = (cls: string) => {
  const pageContainer = document.querySelector(cls);
  if (pageContainer) {
    pageContainer.scrollTo({ top: 0, behavior: 'smooth' });
  }
};


export const parseQueryString = (_query: string) => {
  const query = _query || window.location.search;
  const params = new URLSearchParams(query);
  const result: Record<string, string> = {};
  params.forEach((value, key) => {
    result[key] = value;
  });
  return result;
}

// 从 url 中获取 token 并保存到 localStorage
export const saveTokenFormUrl = () => {
  const { search } = window.location;
  const query = parseQueryString(search);
  const { token } = query;
  if (token) {
    localStorage.setItem('token', token);
  }
  return token || localStorage.getItem('token');
}
// 移除 url 中的指定参数
export const removeFieldFromUrl = (url: string, field: string) => {
  const urlObj = new URL(url);
  urlObj.searchParams.delete(field);
  return urlObj.toString();
}


// 将页面状态保存到 localStorage
export const savePageState = (key: string, state: any) => {
  const token = localStorage.getItem('token');
  const listParamsInfo = {
    token,
    state,
  };
  localStorage.setItem(key, JSON.stringify(listParamsInfo));
}

// 从 localStorage 中获取页面状态
export const getPageState = (key: string) => {
  const pageState = localStorage.getItem(key);
  if (!pageState) {
    return null;
  }
  const { state, token } = JSON.parse(pageState);
  const currentToken = localStorage.getItem('token');
  if (token !== currentToken) {
    return null;
  }
  return state;
}

/**
 * 为富文本中的 img 标签添加 host
 * @param richText 原始富文本字符串
 * @param host 要添加的主机地址 (如 'https://example.com/')
 * @returns 处理后的富文本字符串
 */
export function addHostToImgSrc(richText: string, host: string): string {
  // 确保host以斜杠结尾
  const normalizedHost = host.endsWith('/') ? host : host + '/';

  // 使用正则表达式匹配并替换img标签的src属性
  return richText.replace(/<img[^>]+src="([^"]*)"[^>]*>/g, (match, src) => {
    // 如果src已经是完整URL或者是data URL，则不处理
    if (src.startsWith('http://') ||
      src.startsWith('https://') ||
      src.startsWith('data:')) {
      return match;
    }

    // 移除src可能的前导斜杠
    const cleanSrc = src.startsWith('/') ? src.substring(1) : src;

    // 构建新的img标签
    return match.replace(`src="${src}"`, `src="${normalizedHost}${cleanSrc}"`);
  });
}

export const getBeiJingTimeStr = (zeroZoneTimestamp: number, format: string = 'YYYY/MM/DD HH:mm:ss') => {
  return dayjs.utc(zeroZoneTimestamp).format(format);
}
export const getFormatTimeStr = (timpstamp: number, format: string = 'YYYY/MM/DD HH:mm:ss') => {
  return dayjs(timpstamp).format(format);
}

