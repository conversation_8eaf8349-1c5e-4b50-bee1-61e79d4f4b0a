/**
 * 判断日期是否在今天之前
 * @param date 日期字符串，格式：YYYY-MM-DD
 * @param includeToday 是否包含今天
 * @returns boolean
 */
export const isBeforeToday = (date: string, includeToday: boolean = false): boolean => {
  if (!date) return false;
  
  try {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    const targetDate = new Date(date);
    targetDate.setHours(0, 0, 0, 0);
    
    console.log('日期比较详情', {
      today: today.toISOString(),
      targetDate: targetDate.toISOString(),
      isBeforeToday: targetDate < today
    });

    if (includeToday) {
      return targetDate <= today;
    }

    return targetDate < today;
  } catch (error) {
    console.error('日期比较错误', error);
    return false;
  }
};

/**
 * 获取指定周的开始和结束日期
 * @param startDate 学期开始日期
 * @param weekNumber 第几周
 * @returns { startDate: string, endDate: string }
 */
export const getWeekDateRange = (startDate: string, weekNumber: number) => {
  const start = new Date(startDate);
  const startDay = start.getDay() || 7; // 将周日转换为7
  
  // 计算本周一的日期
  const monday = new Date(start);
  monday.setDate(start.getDate() + (weekNumber - 1) * 7 - (startDay - 1));
  
  // 计算本周日的日期
  const sunday = new Date(monday);
  sunday.setDate(monday.getDate() + 6);
  
  return {
    startDate: monday.toISOString().split('T')[0],
    endDate: sunday.toISOString().split('T')[0]
  };
};

/**
 * 根据周几获取日期
 * @param startDate 周一的日期
 * @param dayIndex 周几的索引（0-6，0表示周一）
 * @returns string 日期字符串，格式：YYYY-MM-DD
 */
export const getDateByWeekday = (startDate: string, dayIndex: number): string => {
  if (!startDate) {
    console.warn('getDateByWeekday: startDate 为空');
    return '';
  }
    console.log('getDateByWeekday', {
        startDate,
        dayIndex,
        result: ''
    });
  
  try {
    const date = new Date(startDate);
    if (isNaN(date.getTime())) {
      console.error('getDateByWeekday: 无效的日期', startDate);
      return '';
    }
    
    date.setDate(date.getDate() + dayIndex);
    return date.toISOString().split('T')[0];
  } catch (error) {
    console.error('getDateByWeekday 错误:', error);
    return '';
  }
}; 