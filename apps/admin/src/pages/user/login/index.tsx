import { Footer } from '@/components';
import { getUserInfo, sendVerificationCode, login, confirmLogin } from '@/services/user';
import {
  LockOutlined,
  MobileOutlined,
  UserOutlined,
} from '@ant-design/icons';
import { Helmet, useIntl, useModel, history } from '@umijs/max';
import { Tabs, Form, Input, Button, Modal, message } from 'antd';
import React, { useState, useEffect } from 'react';
import Settings from '../../../../config/defaultSettings';
import { generateFingerprint } from '@/utils/fingerprint';
import { parseQueryString } from '@/utils/common';

// const Lang = () => {
//   return (
//     <div className="w-[42px] h-[42px] leading-[42px] fixed right-4 rounded hover:bg-gray-100" data-lang>
//       {SelectLang && <SelectLang />}
//     </div>
//   );
// };
export const goLogin = () => {
  const urlParams = new URL(window.location.href).searchParams;
  const redirect = urlParams.get('redirect');
  localStorage.clear();
  if (redirect) {
    history.push(`/user/login?redirect=${redirect}`);
  } else {
    history.push('/user/login');
  }
}

const Login: React.FC = () => {
  const [form] = Form.useForm();
  const [type, setType] = useState<string>('mobile');
  const { initialState, setInitialState } = useModel('@@initialState');
  const intl = useIntl();
  const [loading, setLoading] = useState(false);
  const [countdown, setCountdown] = useState(0);
  const [fingerprint, setFingerprint] = useState<string | null>(null);

  useEffect(() => {
    const generateFingerprintInfo = async () => {
      const fingerprint = await generateFingerprint({ debug: true });
      setFingerprint(fingerprint);
    };
    generateFingerprintInfo();
    const { error } = parseQueryString(window.location.search)
    if (error) {
      message.error(error)
    }
  }, []);




  // 处理 URL 参数
  const getUrlParams = () => {
    const urlParams = new URL(window.location.href).searchParams;
    const redirect = urlParams.get('redirect');
    return {
      redirect,
      isExternalUrl: redirect?.startsWith('http') && !redirect?.includes(window.location.origin)
    };
  };

  // 构建带参数的 URL
  const buildRedirectUrl = (baseUrl: string, token: string, fingerprint?: string) => {
    const separator = baseUrl.includes('?') ? '&' : '?';
    const fingerprintParam = fingerprint ? `&fp=${fingerprint}` : '';
    const url = `${baseUrl}${separator}token=${token}${fingerprintParam}`;
    console.log(`buildRedirectUrl `, url)
    return url;
  };

  // 处理跳转逻辑
  const handleRedirect = (token: string, fingerprint?: string) => {
    const { redirect, isExternalUrl } = getUrlParams();
    const defaultUrl = '/';

    if (redirect) {
      if (isExternalUrl) {
        // 外部 URL 跳转
        window.location.href = buildRedirectUrl(redirect, token, fingerprint);
      } else {
        // 同域 URL 跳转
        history.replace(buildRedirectUrl(redirect, token, fingerprint));
      }
    } else {
      // 默认跳转
      history.replace(buildRedirectUrl(defaultUrl, token, fingerprint));
    }
  };

  useEffect(() => {
    const checkLoginStatus = async () => {
      const pathname = history.location.pathname;
      const token = localStorage.getItem('token');
      const urlParams = new URL(window.location.href).searchParams;
      const redirect = urlParams.get('redirect');

      if (token && pathname === '/user/login' && initialState?.currentUser && !redirect) {
        return history.replace('/');
      }
      if (token && pathname === '/user/login' && redirect) {
        return handleRedirect(token, fingerprint || '');
      }
    };

    checkLoginStatus();
  }, []);

  const handleLoginSuccess = async (data: API.LoginResultData) => {
    const defaultLoginSuccessMessage = intl.formatMessage({
      id: 'pages.login.success',
      defaultMessage: '登录成功！',
    });
    localStorage.setItem('token', data.token);

    message.success(defaultLoginSuccessMessage);
    const { data: userInfo } = await getUserInfo();
    setInitialState({
      ...initialState,
      currentUser: userInfo,
    });

    // 保存 userId 到本地存储
    if (userInfo?.userId) {
      localStorage.setItem('userId', userInfo.userId.toString());
    }

    // 生成指纹并处理跳转
    handleRedirect(data.token, fingerprint || '');
  }
  const handleConfirmLogin = async (values: API.LoginParams) => {
    Modal.confirm({
      title: '提示',
      content: '当前账号已在其他设备登录，是否确认登录？',
      onOk: async () => {
        const res = await confirmLogin({
          ...values,
          platForm: 1
        });
        if (res.status === 200) {
          handleLoginSuccess(res.data);
        } else {
          message.error(res.message);
        }
      }
    });
  }

  const handleSubmit = async (values: API.LoginParams) => {
    try {
      setLoading(true);
      const res = await login({
        ...values,
        platForm: 1
      });
      if (res.status === 200) {
        if (res.data.isAlreadyLogin) {
          handleConfirmLogin(values);
          return;
        }

        handleLoginSuccess(res.data);

      } else {
        message.error(res.message);
      }
    } catch (error) {
      console.log(`login error`, error);
    } finally {
      setLoading(false);
    }
  };

  const handleGetCaptcha = async () => {
    try {
      const phone = form.getFieldValue('phoneNumber');
      if (!phone) {
        message.error('请输入手机号');
        return;
      }
      if (!/^1[3-9]\d{9}$/.test(phone)) {
        message.error('手机号格式错误');
        return;
      }

      const result = await sendVerificationCode({
        phone_number: phone,
        business_type: 'login',
      });

      if (result.status === 200) {
        message.success(result.data.message);
        setCountdown(60);
        const timer = setInterval(() => {
          setCountdown((prev) => {
            if (prev <= 1) {
              clearInterval(timer);
              return 0;
            }
            return prev - 1;
          });
        }, 1000);
      } else {
        message.error(result.data.message);
      }
    } catch (error) {
      message.error('获取验证码失败');
    }
  };

  return (
    <div className="flex flex-col h-screen overflow-auto bg-[url('https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/V-_oS6r-i7wAAAAAAAAAAAAAFl94AQBr')] bg-cover">
      <Helmet>
        <title>
          {intl.formatMessage({
            id: 'menu.login',
            defaultMessage: '登录页',
          })}
          - {Settings.title}
        </title>
      </Helmet>
      {/* <Lang /> */}
      <div className="flex-1 py-8">
        <div className="w-[328px] mx-auto pt-8">
          <div className="text-center">
            <div className="h-11 leading-11">
              <img alt="logo" className="h-11 mr-4 align-top inline-block" src="/logo.jpeg" />
              <span className="text-[33px] text-[rgba(0,0,0,0.85)] font-bold font-['Avenir','Helvetica_Neue','Arial','Helvetica','sans-serif'] relative top-0.5">运营管理平台</span>
            </div>
            <div className="mt-3 mb-10 text-sm text-[rgba(0,0,0,0.6)]">
              {intl.formatMessage({ id: 'pages.layouts.userLayout.title' })}
            </div>
          </div>

          <Form
            form={form}
            onFinish={handleSubmit}
            initialValues={{ autoLogin: true }}
          >
            <Tabs
              activeKey={type}
              onChange={setType}
              centered
              items={[
                {
                  key: 'mobile',
                  label: intl.formatMessage({
                    id: 'pages.login.phoneLogin.tab',
                    defaultMessage: '手机号登录',
                  }),
                },
              ]}
            />

            {type === 'account' && (
              <>
                <Form.Item
                  name="username"
                  rules={[
                    {
                      required: true,
                      message: '请输入用户名!',
                    },
                  ]}
                >
                  <Input
                    size="large"
                    prefix={<UserOutlined />}
                    placeholder="用户名: admin or user"
                  />
                </Form.Item>
                <Form.Item
                  name="password"
                  rules={[
                    {
                      required: true,
                      message: '请输入密码！',
                    },
                  ]}
                >
                  <Input.Password
                    size="large"
                    prefix={<LockOutlined />}
                    placeholder="密码: ant.design"
                  />
                </Form.Item>
              </>
            )}

            {type === 'mobile' && (
              <>
                <Form.Item
                  name="phoneNumber"
                  rules={[
                    {
                      required: true,
                      message: '请输入手机号！',
                    },
                    {
                      pattern: /^1[3-9]\d{9}$/,
                      message: '手机号格式错误！',
                    },
                  ]}
                >
                  <Input
                    size="large"
                    prefix={<MobileOutlined />}
                    placeholder="手机号"
                  />
                </Form.Item>
                <Form.Item
                  name="verificationCode"
                  rules={[
                    {
                      required: true,
                      message: '请输入验证码！',
                    },
                  ]}
                >
                  <div className="flex">
                    <Input
                      size="large"
                      prefix={<LockOutlined />}
                      placeholder="请输入验证码"
                      className="flex-1"
                    />
                    <Button
                      size="large"
                      disabled={countdown > 0}
                      onClick={handleGetCaptcha}
                      className="ml-2 w-[120px]"
                    >
                      {countdown > 0 ? `${countdown}s` : '获取验证码'}
                    </Button>
                  </div>
                </Form.Item>
              </>
            )}

            <Form.Item>
              <Button
                size="large"
                type="primary"
                htmlType="submit"
                loading={loading}
                className="w-full"
              >
                登录
              </Button>
            </Form.Item>
          </Form>
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default Login;
