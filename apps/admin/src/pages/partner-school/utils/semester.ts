import { GradeSetting } from '@/services/semester-management/type';
import dayjs from 'dayjs';

// 判断是否所有年级的所有学期都未设置时间
export const isAllSemestersUnset = (gradeSettings: GradeSetting[]) => {
  if (!gradeSettings || gradeSettings.length === 0) return true;

  // 检查每个年级
  return gradeSettings.every((grade) => {
    // 检查年级下的每个学期
    return grade.semesters.every((semester) => {
      // 如果任何一个学期有开始时间或结束时间，则返回 false
      return !semester.start_date && !semester.end_date;
    });
  });
};

// 添加获取默认学期日期的函数
export const getDefaultSemesterDates = (): Record<number, { start: string; end: string }> => {
  const now = dayjs();
  const currentYear = now.year();

  // 获取本学年的9月1日
  const firstSemesterStart = dayjs(`${currentYear}-09-01`);
  // 获取下一年的3月1日
  const secondSemesterStart = dayjs(`${currentYear + 1}-03-01`);

  // 如果当前日期在9月1日之前，年份需要往前推一年
  if (now.isBefore(firstSemesterStart)) {
    return {
      0: {
        start: firstSemesterStart.subtract(1, 'year').format('YYYY-MM-DD'),
        end: secondSemesterStart.subtract(1, 'year').format('YYYY-MM-DD'),
      },
      2: {
        start: secondSemesterStart.subtract(1, 'year').format('YYYY-MM-DD'),
        end: firstSemesterStart.format('YYYY-MM-DD'),
      },
    };
  }

  return {
    0: {
      start: firstSemesterStart.format('YYYY-MM-DD'),
      end: secondSemesterStart.format('YYYY-MM-DD'),
    },
    2: {
      start: secondSemesterStart.format('YYYY-MM-DD'),
      end: firstSemesterStart.add(1, 'year').format('YYYY-MM-DD'),
    },
  };
};

/**
 * 检查学期设置是否有效
 * - 如果开始日期和结束日期都未设置，则不验证
 * - 如果开始日期未设置，则返回错误信息
 * - 如果结束日期未设置，则返回错误信息
 * - 如果开始日期大于结束日期，则返回错误信息
 * @param gradeSettings 年级设置
 * @returns 是否有效，错误信息
 */
export const checkSemesterSetting = (
  gradeSettings: GradeSetting[],
): { valid: boolean; message: string } => {
  console.log(`gradeSettings`, gradeSettings);
  const ret = {
    valid: true,
    message: '',
  };
  for (const grade of gradeSettings) {
    const { grade_name, semesters } = grade;
    for (const semester of semesters) {
      const { semester_name, start_date, end_date } = semester;
      const msg = `${grade_name}的${semester_name}`;
      // 如果开始日期和结束日期都未设置，则跳过
      if (!start_date && !end_date) {
        continue;
      }
      if (!start_date) {
        ret.valid = false;
        ret.message = `${msg}开始日期未设置`;
        return ret;
      }
      if (!end_date) {
        ret.valid = false;
        ret.message = `${msg}结束日期未设置`;
        return ret;
      }
      if (dayjs(start_date).isAfter(dayjs(end_date))) {
        ret.valid = false;
        ret.message = `${msg}开始日期不能大于结束日期`;
        return ret;
      }
    }
  }

  return ret;
};
