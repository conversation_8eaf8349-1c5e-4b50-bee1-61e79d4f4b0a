import { CooperationType, PaymentPolicy, TrialPolicy } from '@/types/cooperation';

/**
 * 判断是否为试用类型
 * @param type 合作类型
 */
export const isTrialType = (type: CooperationType): boolean => {
  return type === CooperationType.TRIAL || type === CooperationType.TRIAL_EXTENSION;
};

/**
 * 判断是否为付费类型
 * @param type 合作类型
 */
export const isPaymentType = (type: CooperationType): boolean => {
  return type === CooperationType.PAYMENT
};

/**
 * 判断是否为停用类型
 * @param type 合作类型
 */
export const isDisableType = (type: CooperationType): boolean => {
  return type === CooperationType.DISABLE;
};


/**
 * 获取类型对应的标签文本
 * @param type 合作类型
 */
export const getTypeLabel = (type: CooperationType): string => {

  const _type = type.toString();
  switch (_type) {
    case CooperationType.TRIAL:
      return '试用申报';
    case CooperationType.TRIAL_EXTENSION:
      return '试用延期申报';
    case CooperationType.PAYMENT:
      return '付费申报';
    case CooperationType.DISABLE:
      return '停用申报';
    default:
      return '未知操作';
  }
};

/**
 * 获取类型对应的颜色
 * @param type 合作类型或历史记录类型
 */
export const getTypeColor = (type: CooperationType): string => {
  const _type = type.toString();
  switch (_type) {
    case CooperationType.TRIAL:
      return 'success'; // 绿色
    case CooperationType.TRIAL_EXTENSION:
      return 'processing'; // 蓝色
    case CooperationType.PAYMENT:
      return 'processing'; // 蓝色
    case CooperationType.DISABLE:
      return 'error'; // 红色
    default:
      return '#d9d9d9'; // 灰色
  }
};

/**
 * 获取类型对应的学生数量标签
 * @param count 学生数量
 */
export const getStudentCountLabel = (count: number): string => {
  return `${count}名学生`;
};

/**
 * 获取类型对应的操作人标签
 * @param operatorName 操作人名称
 */
export const getOperatorLabel = (operatorName: string): string => {
  return `操作人: ${operatorName}`;
};

/**
 * 获取试用政策标签
 * @param policy 试用政策
 */
export const getTrialPolicyLabel = (policy: TrialPolicy): string => {
  switch (policy) {
    case TrialPolicy.POLICY_1:
      return '试用政策1';
    case TrialPolicy.POLICY_2:
      return '试用政策2';
    case TrialPolicy.POLICY_3:
      return '试用政策3';
    default:
      return '未知试用政策';
  }
};

/**
 * 获取付费政策标签
 * @param policy 付费政策
 */
export const getPaymentPolicyLabel = (policy: PaymentPolicy): string => {
  switch (policy) {
    case PaymentPolicy.POLICY_1:
      return '付费政策1';
    case PaymentPolicy.POLICY_2:
      return '付费政策2';
    case PaymentPolicy.POLICY_3:
      return '付费政策3';
    default:
      return '未知付费政策';
  }
};
