import { CooperationType } from '@/types/cooperation';
import { StudentStatus } from '@/types/students';
import { Student } from '../types';

// 根据合作类型筛选学生
export const filterStudentsByCooperationType = (
  students: Student[],
  cooperationType: CooperationType,
) => {
  switch (cooperationType) {
    case CooperationType.TRIAL:
      return students.filter(
        (student) =>
          student.userStatus === StudentStatus.DEFAULT ||
          student.userStatus === StudentStatus.DISABLED,
      );
    case CooperationType.TRIAL_EXTENSION:
      return students.filter((student) => student.userStatus === StudentStatus.TRIAL);
    case CooperationType.PAYMENT:
    case CooperationType.DISABLE:
      return students;
    default:
      return students;
  }
};
