.container {
  min-height: calc(100vh - 120px);
  background-color: #f0f2f5;
}
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0;
  padding: 12px 0;
  background-color: #fff;
  border-bottom: 1px solid #f0f0f0;
}
.backButton {
  padding-left: 16px;
  color: #1890ff;
  font-size: 14px;
}
.schoolName {
  flex: 1;
  margin: 0 !important;
  font-weight: 500;
  font-size: 16px;
  text-align: center;
}
.placeholder {
  width: 100px;
}
.tabs {
  padding: 0 8px;
  background-color: #fff;
  border-radius: 0;
  box-shadow: none;
  padding-top: 8px;
}
.academicContainer {
  height: auto;
  // min-height: calc(100vh - 230px);
  margin-top: 0;
  padding: 0;
  background-color: #fff;
  border-radius: 0;
}
.secondTabContent {
  padding: 16px;
  height: auto;
  // min-height: calc(100% - 46px);
  background-color: #F8F9F7;
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.05);
}
.subTabs {
  padding: 0 8px;
  margin-bottom: 0;
  .ant-tabs-nav-list .ant-tabs-tab {
    font-size: 12px !important;
    margin-right: 16px !important;
  }
}
:global {
  .ant-tabs-nav {
    margin-bottom: 0 !important;
  }
  .ant-tabs-tab {
    // font-size: 12px !important;
    padding: 12px 16px !important;
  }
  .ant-tabs-tab-active {
    font-weight: 500 !important;
  }
  .ant-tabs-ink-bar {
    height: 2px !important;
  }
}
