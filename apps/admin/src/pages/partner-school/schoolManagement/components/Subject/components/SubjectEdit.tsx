import { ExclamationCircleFilled } from '@ant-design/icons';
import { Checkbox, Col, Row, Tooltip } from 'antd';
import { useSubjectContext } from '../contexts';
import styles from '../index.less';
import { ClassType, Material } from '../types';

interface SubjectEditProps {
  classType: ClassType;
  classTypeIndex: number;
}

const SubjectEdit = (props: SubjectEditProps) => {
  const { editData, setEditData, errors, setErrors } = useSubjectContext();
  const { classType, classTypeIndex } = props;

  // 处理学科选择变化
  const handleSubjectChange = (classTypeIndex: number, subjectIndex: number, checked: boolean) => {
    if (!editData) return;

    const newData = JSON.parse(JSON.stringify(editData));
    newData.classTypes[classTypeIndex].subjects[subjectIndex].enable = checked ? 1 : 0;

    // 如果取消选择学科，同时取消所有教材选择
    if (!checked) {
      newData.classTypes[classTypeIndex].subjects[subjectIndex].materials.forEach((m: Material) => {
        m.enable = 0;
      });

      // 清除错误
      const errorId = `${newData.classTypes[classTypeIndex].id}-${newData.classTypes[classTypeIndex].subjects[subjectIndex].id}`;
      if (errors.has(errorId)) {
        const newErrors = new Set(errors);
        newErrors.delete(errorId);
        setErrors(newErrors);
      }
    }

    setEditData(newData);
  };
  // 处理教材选择变化
  const handleMaterialChange = (
    classTypeIndex: number,
    subjectIndex: number,
    materialIndex: number,
    checked: boolean,
  ) => {
    if (!editData) return;

    const newData = JSON.parse(JSON.stringify(editData));
    const subject = newData.classTypes[classTypeIndex].subjects[subjectIndex];

    // 更新教材状态
    subject.materials[materialIndex].enable = checked ? 1 : 0;

    // 检查该学科是否还有选中的教材
    const hasEnabledMaterial = subject.materials.some((m: Material) => m.enable === 1);

    // 错误处理
    const errorId = `${newData.classTypes[classTypeIndex].id}-${subject.id}`;
    const newErrors = new Set(errors);

    if (!hasEnabledMaterial && subject.enable === 1) {
      // 没有选中的教材，添加错误
      newErrors.add(errorId);
    } else {
      // 有选中的教材，清除错误
      newErrors.delete(errorId);
    }

    setErrors(newErrors);
    setEditData(newData);
  };

  // 检查状态函数
  const hasError = (classTypeId: number, subjectId: number): boolean => {
    return errors.has(`${classTypeId}-${subjectId}`);
  };
  return (
    <div className={styles.subjectGrid}>
      <Row gutter={[24, 24]}>
        {classType.subjects.map((subject, subjectIndex) => (
          <Col span={8} key={subject.id}>
            <div className={styles.subjectEditItem}>
              <div className={styles.subjectHeader}>
                <Checkbox
                  checked={subject.enable === 1}
                  onChange={(e) =>
                    handleSubjectChange(classTypeIndex, subjectIndex, e.target.checked)
                  }
                >
                  {subject.name}：
                </Checkbox>
                {hasError(classType.id, subject.id) && (
                  <Tooltip title="请至少选择一个教材版本">
                    <ExclamationCircleFilled className={styles.errorIcon} />
                  </Tooltip>
                )}
              </div>
              <div className={styles.textbooksContainer}>
                {subject.materials.map((material, materialIndex) => (
                  <Checkbox
                    key={material.id}
                    checked={material.enable === 1}
                    onChange={(e) =>
                      handleMaterialChange(
                        classTypeIndex,
                        subjectIndex,
                        materialIndex,
                        e.target.checked,
                      )
                    }
                    disabled={subject.enable !== 1}
                    className={`${styles.textbookCheckbox} ${
                      hasError(classType.id, subject.id) ? styles.errorCheckbox : ''
                    }`}
                  >
                    {material.name}
                  </Checkbox>
                ))}
              </div>
            </div>
          </Col>
        ))}
      </Row>
    </div>
  );
};

export default SubjectEdit;
