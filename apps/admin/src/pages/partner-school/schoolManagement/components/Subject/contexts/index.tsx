import { SchoolItem } from '@/types/school';
import { useModel } from '@umijs/max';
import { message } from 'antd';
import { MessageInstance } from 'antd/es/message/interface';
import { createContext, FC, useContext, useState } from 'react';
import { SubjectMaterialState } from '../types';

interface SubjectContextType {
  messageApi: MessageInstance;
  schoolInfo: SchoolItem | undefined;
  activeGrade: string;
  setActiveGrade: (activeGrade: string) => void;
  isEditing: boolean;
  setIsEditing: (isEditing: boolean) => void;
  data: SubjectMaterialState | null;
  setData: (data: SubjectMaterialState | null) => void;
  editData: SubjectMaterialState | null;
  setEditData: (editData: SubjectMaterialState | null) => void;
  errors: Set<string>;
  setErrors: (errors: Set<string>) => void;
}
const SubjectContext = createContext<SubjectContextType>({
  messageApi: {} as MessageInstance,
  schoolInfo: undefined,
  activeGrade: '',
  setActiveGrade: () => {},
  isEditing: false,
  setIsEditing: () => {},
  data: null,
  setData: () => {},
  editData: null,
  setEditData: () => {},
  errors: new Set(),
  setErrors: () => {},
});

const useSubjectContext = () => useContext(SubjectContext);

interface SubjectContextProviderProps {
  children: React.ReactNode;
}

const SubjectContextProvider: FC<SubjectContextProviderProps> = ({ children }) => {
  const { schoolInfo } = useModel('schoolModel');
  const [messageApi, contextHolder] = message.useMessage();
  const [activeGrade, setActiveGrade] = useState<string>(
    schoolInfo?.phraseGrade?.[0]?.id?.toString() || '1',
  );
  const [isEditing, setIsEditing] = useState<boolean>(false);
  const [data, setData] = useState<SubjectMaterialState | null>(null);
  const [editData, setEditData] = useState<SubjectMaterialState | null>(null);
  const [errors, setErrors] = useState<Set<string>>(new Set());
  return (
    <SubjectContext
      value={{
        messageApi,
        schoolInfo,
        activeGrade,
        setActiveGrade,
        isEditing,
        setIsEditing,
        data,
        setData,
        editData,
        setEditData,
        errors,
        setErrors,
      }}
    >
      {children}
      {contextHolder}
    </SubjectContext>
  );
};

export { SubjectContextProvider, useSubjectContext };
