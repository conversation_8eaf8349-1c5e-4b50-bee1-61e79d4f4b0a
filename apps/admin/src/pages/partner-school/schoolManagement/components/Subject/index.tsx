import { post } from '@/services/fetcher';
import { getSubjectMaterials } from '@/services/subject-management';
import { EditOutlined } from '@ant-design/icons';
import { Button, Empty, Modal, Spin, Tabs, Typography } from 'antd';
import React, { useEffect, useState } from 'react';
import useSWRMutation from 'swr/mutation';
import ApplyOthersGrade from './components/ApplyOthersGrade';
import ClassTypeCard from './components/ClassTypeCard';
import SubjectEdit from './components/SubjectEdit';
import SubjectView from './components/SubjectView';
import { SubjectContextProvider, useSubjectContext } from './contexts';
import styles from './index.less';
import { ClassType } from './types';

const { TabPane } = Tabs;
const { Title } = Typography;

const SubjectManagement: React.FC = () => {
  const {
    messageApi,
    schoolInfo,
    activeGrade,
    setActiveGrade,
    isEditing,
    setIsEditing,
    data,
    setData,
    editData,
    setEditData,
    setErrors,
  } = useSubjectContext();
  const { trigger: updateSubjectMaterial } = useSWRMutation(
    '/api/v1/subject_material/update',
    post,
  );

  const [loading, setLoading] = useState<boolean>(true);

  const fetchData = async () => {
    try {
      setLoading(true);
      const response = await getSubjectMaterials({
        subject_material_school_id: schoolInfo?.schoolId || 1,
        subject_material_grade: parseInt(activeGrade),
        subject_material_school_year_id: schoolInfo?.schoolYear.school_year_id || 0,
      });

      if (response.status === 200 && response.data) {
        // 转换数据结构
        const classTypes = response.data.class_type_subject_materials.map((ct) => ({
          id: ct.subject_material_class_type.class_type_id,
          name: ct.subject_material_class_type.class_type_name,
          subjects: ct.subject_materials.map((sm) => ({
            id: sm.subject_material_subject.subject_material_subject_id,
            name: sm.subject_material_subject.subject_material_subject_name,
            enable: sm.subject_material_subject.enable,
            materials: sm.subject_material_material.map((m) => ({
              id: m.subject_material_material_id,
              name: m.subject_material_material_name,
              enable: m.enable,
            })),
          })),
        }));

        setData({
          originalData: response.data,
          classTypes,
        });
        setEditData({
          originalData: response.data,
          classTypes: JSON.parse(JSON.stringify(classTypes)),
        });
      }
    } catch (error) {
      console.error('获取学科列表出错:', error);
      messageApi.error('获取学科列表出错');
    } finally {
      setLoading(false);
    }
  };

  // 获取数据
  useEffect(() => {
    fetchData();
  }, [activeGrade, schoolInfo]);

  // 编辑功能
  const handleEdit = () => {
    if (data) {
      // 深拷贝数据
      setEditData({
        originalData: data.originalData,
        classTypes: JSON.parse(JSON.stringify(data.classTypes)),
      });
      setIsEditing(true);
      setErrors(new Set());
    }
  };

  const handleCancel = () => {
    Modal.confirm({
      title: '确认取消',
      content: '取消编辑将丢失所有未保存的更改，是否继续？',
      okText: '确认',
      cancelText: '取消',
      onOk: () => {
        setIsEditing(false);
        setErrors(new Set());
        // 重置编辑数据
        if (data) {
          setEditData({
            originalData: data.originalData,
            classTypes: JSON.parse(JSON.stringify(data.classTypes)),
          });
        }
      },
    });
  };

  // 生成保存请求体
  const buildSavePayload = () => {
    if (!editData || !editData.originalData) return null;

    const { originalData, classTypes } = editData;

    // 克隆原始数据结构，但使用我们编辑后的值更新
    return {
      ...originalData,
      class_type_subject_materials: classTypes.map((classType) => ({
        subject_material_class_type: {
          class_type_id: classType.id,
          class_type_name: classType.name,
        },
        subject_materials: classType.subjects.map((subject) => ({
          subject_material_subject: {
            subject_material_subject_id: subject.id,
            subject_material_subject_name: subject.name,
            enable: subject.enable,
          },
          subject_material_material: subject.materials.map((material) => ({
            subject_material_material_id: material.id,
            subject_material_material_name: material.name,
            enable: material.enable,
          })),
        })),
      })),
    };
  };

  // 验证数据
  const validateData = (): boolean => {
    if (!editData) return false;

    const newErrors = new Set<string>();
    let isValid = true;

    editData.classTypes.forEach((classType) => {
      classType.subjects.forEach((subject) => {
        // 只检查启用的学科
        if (subject.enable === 1) {
          const hasMaterials = subject.materials.some((m) => m.enable === 1);
          if (!hasMaterials) {
            // 构建唯一错误ID
            const errorId = `${classType.id}-${subject.id}`;
            newErrors.add(errorId);
            isValid = false;

            messageApi.error(`${classType.name}中的${subject.name}必须至少选择一个教材版本`);
          }
        }
      });
    });

    setErrors(newErrors);
    return isValid;
  };

  // 保存数据
  const handleSave = () => {
    if (!validateData()) return;

    const payload = buildSavePayload();
    updateSubjectMaterial(payload, {
      onSuccess: () => {
        // 更新视图数据
        setData(JSON.parse(JSON.stringify(editData)));
        setIsEditing(false);
        setErrors(new Set());
        messageApi.success('保存成功');
        fetchData(); // 刷新数据
      },
      onError: () => {
        messageApi.error('保存失败');
      },
    });
  };

  const renderClassTypeCard = (classType: ClassType, index: number) => (
    <ClassTypeCard
      classType={classType}
      isEditing={isEditing}
      editData={editData || { originalData: {}, classTypes: [] }}
      setEditData={setEditData}
    >
      {isEditing ? (
        <SubjectEdit classType={classType} classTypeIndex={index} />
      ) : (
        <SubjectView classType={classType} />
      )}
    </ClassTypeCard>
  );

  // 加载中状态
  if (loading && !data) {
    return (
      <div className={styles.loadingContainer}>
        <Spin size="large" tip="加载中...">
          <Empty description="" />
        </Spin>
      </div>
    );
  }

  // 主渲染
  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <Title level={5} className={styles.academicYear}>
          {schoolInfo?.schoolYear?.school_year || ''}
        </Title>
        {isEditing ? (
          <div className={styles.editButtons}>
            <Button onClick={handleCancel} className={styles.cancelButton}>
              取消
            </Button>
            <Button type="primary" onClick={handleSave} className={styles.saveButton}>
              保存
            </Button>
          </div>
        ) : (
          <Button
            type="primary"
            icon={<EditOutlined />}
            onClick={handleEdit}
            className={styles.editButton}
          >
            编辑
          </Button>
        )}
      </div>

      <Tabs
        activeKey={activeGrade}
        onChange={setActiveGrade}
        className={styles.gradeTabs}
        tabBarExtraContent={isEditing && <ApplyOthersGrade buildSavePayload={buildSavePayload} />}
      >
        {schoolInfo?.phraseGrade?.map((grade) => (
          <TabPane tab={grade.name} key={grade.id}>
            {data && (
              <div className={styles.gradeContent}>
                {(isEditing ? editData : data)?.classTypes?.map((classType, index) =>
                  renderClassTypeCard(classType, index),
                )}
              </div>
            )}
          </TabPane>
        ))}
      </Tabs>
    </div>
  );
};

const SubjectManagementWithContext = () => {
  return (
    <SubjectContextProvider>
      <SubjectManagement />
    </SubjectContextProvider>
  );
};

export default SubjectManagementWithContext;
