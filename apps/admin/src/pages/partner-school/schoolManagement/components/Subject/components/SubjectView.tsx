/**
 * 学科预览
 */
import { Col, Row } from 'antd';
import styles from '../index.less';
import { ClassType } from '../types';

interface SubjectViewProps {
  classType: ClassType;
}

const SubjectView = (props: SubjectViewProps) => {
  const { classType } = props;
  // 过滤获取启用的学科
  const enabledSubjects = classType.subjects.filter((subject) => subject.enable === 1);

  if (enabledSubjects.length === 0) {
    return <div className={styles.emptyText}>暂未设置</div>;
  }

  return (
    <div className={styles.subjectGrid}>
      <Row gutter={[24, 4]}>
        {enabledSubjects.map((subject) => (
          <Col span={8} key={subject.id}>
            <div className={styles.subjectItem}>
              <span className={styles.subjectName}>{subject.name}：</span>
              <span className={styles.textbookList}>
                {subject.materials.filter((m) => m.enable === 1).length > 0
                  ? subject.materials
                      .filter((m) => m.enable === 1)
                      .map((m) => m.name)
                      .join('、')
                  : '暂未设置'}
              </span>
            </div>
          </Col>
        ))}
      </Row>
    </div>
  );
};

export default SubjectView;
