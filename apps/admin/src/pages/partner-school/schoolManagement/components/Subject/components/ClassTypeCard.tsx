/**
 * 班型卡片
 */
import { <PERSON><PERSON>, Card, Checkbox, Modal } from 'antd';
import { useState } from 'react';
import { useSubjectContext } from '../contexts';
import styles from '../index.less';
import { ClassType, SubjectMaterialState } from '../types';

interface ClassTypeCardProps {
  classType: ClassType;
  isEditing: boolean;
  children: React.ReactNode;
  editData: SubjectMaterialState;
  setEditData: (editData: SubjectMaterialState) => void;
}
const ClassTypeCard = (props: ClassTypeCardProps) => {
  const { isEditing, editData, setEditData } = useSubjectContext();
  const { classType, children } = props;

  // 应用到其他班型相关状态
  const [applyModalVisibleClassType, setApplyModalVisibleClassType] = useState(false);
  const [currentClassTypeId, setCurrentClassTypeId] = useState<number>(0);
  const [selectedClassTypes, setSelectedClassTypes] = useState<number[]>([]);

  // 打开应用到其他班型的弹窗
  const handleApplyToOthersClassType = (classTypeId: number) => {
    setCurrentClassTypeId(classTypeId);
    setSelectedClassTypes([]);
    setApplyModalVisibleClassType(true);
  };

  // 检查班型所有学科是否已设置
  const checkClassTypeComplete = (classTypeId: number) => {
    const classType = editData?.classTypes.find((g) => g.id === classTypeId);
    if (!classType) return false;
    return classType.subjects.every((subject) => subject.enable === 1);
  };

  // 应用到其他班型
  const handleApply = () => {
    if (!currentClassTypeId) return;

    const sourceClass = editData?.classTypes.find((g) => g.id === currentClassTypeId);
    if (!sourceClass) return;
    // 创建新的班型数据，避免直接修改原数据
    const updatedClassTypes = editData?.classTypes.map((targetClass) => {
      // 只更新选中的目标班型
      if (!selectedClassTypes.includes(targetClass.id)) {
        return targetClass;
      }

      // 复制源班型的学科和教材设置到目标班型
      const updatedSubjects = targetClass.subjects.map((targetSubject) => {
        const sourceSubject = sourceClass.subjects.find((s) => s.id === targetSubject.id);
        if (sourceSubject) {
          return {
            ...sourceSubject,
            // enable: targetSubject.enable, // 保留目标班型原有的学科启用状态
          };
        }
        return targetSubject;
      });

      return {
        ...targetClass,
        subjects: updatedSubjects,
      };
    });

    setEditData({
      originalData: editData?.originalData || {},
      classTypes: updatedClassTypes || [],
    });

    setApplyModalVisibleClassType(false);
  };

  // 获取可应用的班型选项
  const getApplyOptions = () => {
    return editData?.classTypes
      .filter((grade) => grade.id !== currentClassTypeId)
      .map((grade) => ({
        label: grade.name,
        value: grade.id,
      }));
  };
  return (
    <>
      <Card
        title={classType.name}
        className={styles.classTypeCard}
        key={classType.id}
        extra={
          isEditing && (
            <Button
              type="link"
              onClick={() => handleApplyToOthersClassType(classType.id)}
              disabled={!checkClassTypeComplete(classType.id)}
            >
              应用到其他班型
            </Button>
          )
        }
      >
        {children}
      </Card>
      <Modal
        title="应用到其他年级"
        open={applyModalVisibleClassType}
        onOk={handleApply}
        onCancel={() => setApplyModalVisibleClassType(false)}
        okText="确认"
        cancelText="取消"
      >
        <p className="mb-4">请选择要应用到的年级：</p>
        <Checkbox.Group
          options={getApplyOptions()}
          value={selectedClassTypes}
          onChange={(values) => setSelectedClassTypes(values as number[])}
        />
      </Modal>
    </>
  );
};

export default ClassTypeCard;
