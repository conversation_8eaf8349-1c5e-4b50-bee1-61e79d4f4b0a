/**
 * 应用到其他年级
 */
import { post } from '@/services/fetcher';
import { FormOutlined } from '@ant-design/icons';
import { Button, Checkbox, Modal } from 'antd';
import { useState } from 'react';
import useSWRMutation from 'swr/mutation';
import { useSubjectContext } from '../contexts';

interface ApplyOthersGradeProps {
  buildSavePayload: () => any;
}

const ApplyOthersGrade = (props: ApplyOthersGradeProps) => {
  const { messageApi, schoolInfo, activeGrade, setIsEditing, setErrors } = useSubjectContext();
  const { buildSavePayload } = props;

  const { trigger: batchUpdateSubjectMaterial } = useSWRMutation(
    '/api/v1/subject_material/batchUpdate',
    post,
  );

  // 应用到其他年级相关状态
  const [applyModalVisibleGrade, setApplyModalVisibleGrade] = useState(false);
  const [selectedGrades, setSelectedGrades] = useState<number[]>([]);

  // 应用到其他年级
  const handleApplyToOthersGrade = (e: React.MouseEvent<HTMLButtonElement>) => {
    setSelectedGrades([]);
    setApplyModalVisibleGrade(true);
  };

  // 获取可应用的班型选项
  const getApplyOptions = () => {
    return schoolInfo?.phraseGrade
      ?.filter((grade) => grade.id !== parseInt(activeGrade) && grade.id !== 14) // 14 为高三复读
      .map((grade) => ({
        label: grade.name,
        value: grade.id,
      }));
  };

  // 应用到其他年级
  const handleApply = () => {
    if (!activeGrade) return;
    const payload = buildSavePayload();

    const params = {
      ...payload,
      subject_material_grades: [parseInt(activeGrade), ...selectedGrades],
    };
    delete params.subject_material_grade;
    batchUpdateSubjectMaterial(params, {
      onSuccess: () => {
        // setIsEditing(false);
        setErrors(new Set());
        messageApi.success('其他年级应用成功');
      },
      onError: () => {
        messageApi.error('其他年级应用成功失败');
      },
    });
    setApplyModalVisibleGrade(false);
  };
  return (
    <>
      <Button type="primary" icon={<FormOutlined />} onClick={handleApplyToOthersGrade}>
        应用到其他年级
      </Button>
      <Modal
        title="应用到其他年级"
        open={applyModalVisibleGrade}
        onOk={handleApply}
        onCancel={() => setApplyModalVisibleGrade(false)}
        okText="确认"
        cancelText="取消"
      >
        <p className="mb-4">请选择要应用到的年级：</p>
        <Checkbox.Group
          options={getApplyOptions()}
          value={selectedGrades}
          onChange={(values) => setSelectedGrades(values as number[])}
        />
      </Modal>
    </>
  );
};

export default ApplyOthersGrade;
