.container {
  width: 100%;
  height: 100%;
  padding: 16px;
  background-color: #fff;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.academicYear {
  margin: 0 !important;
  font-size: 16px;
  font-weight: 500;
  color: #000;
}

.editButtons {
  display: flex;
  gap: 8px;
}


.gradeContent {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.classTypeCard {
  margin-top: 16px;
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);

  &:last-child {
    margin-bottom: 0;
  }

  :global(.ant-card-head) {
    min-height: 48px;
    padding: 0 16px;
    background-color: #fafafa;
    border-bottom: 1px solid #f0f0f0;
  }

  :global(.ant-card-body) {
    padding: 16px;
  }
}

.subjectGrid {
  width: 100%;
}

.subjectItem {
  display: flex;
}

.subjectName {
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.textbookList {
  color: #666;
  font-size: 13px;
}

.emptyText {
  color: #999;
  font-style: italic;
}

.subjectEditItem {
  display: flex;
  flex-direction: column;
}

.subjectHeader {
  position: relative;
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.subjectCheckbox {
  font-weight: 500;
}

.errorCheckbox {
  color: #ff4d4f;

  :global(.ant-checkbox-wrapper) {
    color: #ff4d4f;
  }

  :global(.ant-checkbox-inner) {
    border-color: #ff4d4f;
  }
}

.errorIcon {
  color: #ff4d4f;
  margin-left: 8px;
  font-size: 14px;
}

.textbooksContainer {
  display: flex;
  flex-direction: column;
  padding-left: 24px;
}

.textbookCheckbox {
  margin-bottom: 6px;
  font-size: 13px;
}

:global {
  .ant-checkbox-wrapper {
    margin-right: 0;
    margin-bottom: 0;
  }

  .ant-checkbox-wrapper + .ant-checkbox-wrapper {
    margin-left: 0;
  }
}
