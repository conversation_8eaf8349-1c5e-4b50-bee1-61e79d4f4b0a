import fetcher from '@/services/fetcher';
import { Menu } from '@/types/menu';
import { UserOutlined } from '@ant-design/icons';
import { useModel } from '@umijs/max';
import { Form, message, Modal, Select, Spin, Tree, TreeProps } from 'antd';
import React, { Key, useEffect, useMemo, useState } from 'react';
import useSWR from 'swr';
import usePermissionData, { separateMenuNodes } from '../hooks/usePermissionData';

interface StaffModalProps {
  open: boolean;
  schoolId?: string | number;
  editingStaff?: { userId: number; roleIds: number[] };
  onCancel: () => void;
  onSuccess: () => void;
  existUserIds?: number[];
}

interface CheckedMenuIds {
  checked: Key[];
  halfChecked: Key[];
}

interface SchoolMenusData {
  list: Menu[];
}

const StaffModal: React.FC<StaffModalProps> = ({
  open,
  schoolId,
  editingStaff,
  onCancel,
  onSuccess,
  existUserIds = [],
}) => {
  const [form] = Form.useForm();

  // 使用自定义 hook 获取权限数据
  const {
    users,
    availableRoles,
    platformMenus,
    userMenus,
    isLoading,
    isMutating,
    loadBaseData,
    loadUserMenus,
    saveStaffPermission,
    getMenuTree,
  } = usePermissionData();
  const { initialState } = useModel('@@initialState');
  const currentUser = initialState?.currentUser;
  const { data: schoolMenusData } = useSWR(
    `/api/v1/user_school_menu/allMenus?userId=${Number(currentUser?.userId || 0)}&schoolId=${Number(schoolId)}`,
    fetcher<SchoolMenusData>,
    {
      revalidateOnFocus: false,
    },
  );

  // console.log('platformMenus: ', platformMenus, editingStaff, availableRoles);

  // const [checkedRoleIds, setCheckedRoleIds] = useState<number[]>([]);
  const [checkedMenuIds, setCheckedMenuIds] = useState<CheckedMenuIds>({
    checked: [],
    halfChecked: [],
  });

  // 计算学校的菜单
  // const underSchoolMenus = useMemo(
  //   () => platformMenus.filter((menu) => menu.isUnderSchool === 1),
  //   [platformMenus],
  // );

  // 计算最大权限菜单列表
  // const maxPermissionMenuList = useMemo(
  //   () => getMaxPermissionMenus(availableRoles, checkedRoleIds),
  //   [availableRoles, checkedRoleIds],
  // );

  // 生成菜单树
  const menuTree = useMemo(() => {
    if (!schoolMenusData || !Array.isArray(schoolMenusData.list)) return [];
    console.log('xxx: ', schoolMenusData);
    const treeIds = schoolMenusData.list.map((menu) => menu.menuId);
    return getMenuTree(treeIds);
  }, [getMenuTree, schoolMenusData]);

  // 初始数据加载
  useEffect(() => {
    if (open) {
      // 加载基础数据
      loadBaseData();

      // 编辑模式下加载用户菜单权限
      if (editingStaff && schoolId) {
        loadUserMenus(editingStaff.userId, schoolId);
      } else {
        // 新建模式下清空数据
        // setCheckedRoleIds([]);
        setCheckedMenuIds({
          checked: [],
          halfChecked: [],
        });
        form.resetFields();
      }
    }
    // 仅在弹窗状态改变、编辑对象改变或学校ID改变时执行
  }, [open, editingStaff, schoolId]);

  // 编辑模式数据加载
  useEffect(() => {
    if (!open || !editingStaff) return;
    if (!schoolMenusData || !Array.isArray(schoolMenusData.list)) return;

    // 只有当数据都加载完成后才设置表单值
    if (users.length > 0 && userMenus.length > 0 && schoolMenusData.list.length > 0) {
      const menuIds = userMenus.map((menu) => menu.menuId);

      form.setFieldsValue({
        uId: editingStaff.userId,
        roleIds: editingStaff.roleIds,
        menuIds,
      });

      // setCheckedRoleIds(editingStaff.roleIds);

      // 获取叶子节点和父节点
      const { leafNodes, parentNodes } = separateMenuNodes(schoolMenusData.list, menuIds);

      setCheckedMenuIds({
        checked: leafNodes,
        halfChecked: parentNodes,
      });
    }
  }, [open, editingStaff, users, userMenus, schoolMenusData, form]);

  // 表单提交
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      console.log('formvalue: ', values);

      if (!schoolId) {
        message.error('学校ID不能为空');
        return;
      }

      // 合并 checked 和 halfChecked 的菜单ID
      const allMenuIds = [...new Set([...checkedMenuIds.checked, ...checkedMenuIds.halfChecked])];

      await saveStaffPermission({
        userId: values.uId,
        roleIds: values.roleIds,
        schoolId: Number(schoolId),
        menuIds: allMenuIds,
      });

      message.success(editingStaff ? '编辑成功' : '添加成功');
      onSuccess();
    } catch (error: any) {
      if (error.errorFields) {
        return;
      }
      message.error(error.message || '操作失败');
    }
  };

  // 树形控件勾选处理
  const onCheck: TreeProps['onCheck'] = (checkedKeys, info) => {
    const checkedKeysArray = Array.isArray(checkedKeys) ? checkedKeys : checkedKeys.checked;
    const { halfCheckedKeys } = info;

    setCheckedMenuIds({
      checked: checkedKeysArray,
      halfChecked: halfCheckedKeys || [],
    });
    form.setFieldValue('menuIds', checkedKeysArray);
  };

  // 处理角色选择变化
  // const handleRoleChange = (checkedValues: any) => {
  //   const newValues = checkedValues as number[];
  //   setCheckedRoleIds(newValues);
  //   form.setFieldsValue({ roleIds: newValues });

  //   // 重置菜单选择
  //   setCheckedMenuIds({
  //     checked: [],
  //     halfChecked: [],
  //   });
  // };

  return (
    <Modal
      title={editingStaff ? '编辑学校管理员' : '添加学校管理员'}
      open={open}
      onCancel={onCancel}
      onOk={handleSubmit}
      confirmLoading={isMutating}
      okButtonProps={{
        disabled: isMutating || isLoading || menuTree.length === 0,
      }}
      width={600}
    >
      <Spin spinning={isLoading} tip="数据加载中...">
        <Form
          form={form}
          layout="vertical"
          initialValues={{ roleIds: [], menuIds: [] }}
          autoComplete="off"
        >
          <Form.Item
            name="uId"
            label="选择人员"
            rules={[{ required: true, message: '请选择人员' }]}
          >
            <Select
              showSearch
              placeholder="请输入姓名搜索"
              optionFilterProp="children"
              disabled={!!editingStaff}
              loading={isLoading}
              filterOption={(input, option) =>
                (option?.children?.toString() || '').toLowerCase().includes(input.toLowerCase())
              }
            >
              {users.map((user) => {
                const isExist = existUserIds?.includes(user.userId);
                return (
                  <Select.Option key={user.userId} value={user.userId} disabled={isExist}>
                    {isExist ? <UserOutlined /> : ''}{' '}
                    {`${user.userName} (${user.userPhoneNumber || '无手机号'}) `}
                    <span className="text-xs text-gray-500">
                      {user.roles?.map((role) => role.roleName).join(',') || '无角色'}
                    </span>
                  </Select.Option>
                );
              })}
            </Select>
          </Form.Item>

          {/* <Form.Item
            name="roleIds"
            label="角色"
            rules={[{ required: true, message: '请选择角色' }]}
          >
            <Checkbox.Group
              onChange={handleRoleChange}
              options={availableRoles.map((role) => ({
                label: role.roleName,
                value: role.roleId,
              }))}
            />
          </Form.Item> */}

          <Form.Item
            name="menuIds"
            label="权限范围"
            rules={[
              {
                required: true,
                message: '请选择权限范围',
                validator: (rule, value) => {
                  console.log('value: ', value);
                  if (value?.length === 0) {
                    // callback('请先选择角色');
                    return Promise.reject('请选择权限范围');
                  } else {
                    return Promise.resolve();
                    // callback();
                  }
                  // callback();
                },
              },
            ]}
          >
            {menuTree.length > 0 ? (
              <Tree
                defaultExpandAll
                showLine
                checkable
                selectable={false}
                checkedKeys={{
                  checked: checkedMenuIds.checked,
                  halfChecked: checkedMenuIds.halfChecked,
                }}
                treeData={menuTree}
                onCheck={onCheck}
              />
            ) : (
              <div className="text-gray-400 p-4 text-center border border-dashed rounded-md">
                没有可分配的权限
              </div>
            )}
          </Form.Item>
        </Form>
      </Spin>
    </Modal>
  );
};

export default StaffModal;
