import React, { useEffect, useState } from 'react';
import SimpleSchedule, { ScheduleItem } from '@/components/TimeSchedule';
import dayjs from 'dayjs';
import { useModel } from '@umijs/max';
import { Spin } from 'antd';
import { getTeacherSchedule } from '@/services/schedule-management';
import { TeacherSchedule } from '@/services/schedule-management/type';
type ScheduleParams = {
  teacher_id: number;
  school_id: number;
  school_year_id: number;
  start_date: string;
  end_date: string;
}

// 根据科目ID获取背景色
const getColorBySubject = (subjectId: number): string => {
  // 返回一个默认的背景色，普通配色
  const colorMap: Record<number, string> = {
    1: '#64b5f6', // 语文
    2: '#81c784', // 数学
    3: '#e57373', // 英语
    4: '#ffb74d', // 物理
    5: '#ba68c8', // 化学
    6: '#4db6ac', // 生物
    7: '#f06292', // 政治
    8: '#9575cd', // 历史
    9: '#4fc3f7', // 地理
  };
  return colorMap[subjectId] || '#a1a1a1';
};

// 根据科目ID获取边框色
const getBorderColorBySubject = (subjectId: number): string => {
  // 返回一个默认的边框色，普通配色
  const borderColorMap: Record<number, string> = {
    1: '#1565c0', // 语文
    2: '#2e7d32', // 数学
    3: '#c62828', // 英语
    4: '#ef6c00', // 物理
    5: '#7b1fa2', // 化学
    6: '#00796b', // 生物
    7: '#c2185b', // 政治
    8: '#5e35b1', // 历史
    9: '#0288d1', // 地理
  };
  return borderColorMap[subjectId] || '#757575';
};
const Schedule = ({ teacherId, title }: { teacherId: number, title: string }) => {
  console.log(teacherId, 'teacherId');

  const [loading, setLoading] = useState(false);
  const { schoolInfo } = useModel('schoolModel');
  const [params, setParams] = useState<{ teacher_id: number, school_id: number, school_year_id: number, start_date: string, end_date: string }>({
    teacher_id: teacherId,
    school_id: schoolInfo?.schoolId ?? 0,
    school_year_id: schoolInfo?.schoolYear?.school_year_id ?? 0,
    start_date: "",
    end_date: ""
  });
  const [scheduleData, setScheduleData] = useState<TeacherSchedule>();


  const querySchedule = async (_params?: ScheduleParams) => {
    if (!_params?.start_date || !_params.end_date) {
      return;
    }
    setLoading(true);
    const res = await getTeacherSchedule(_params ?? params);
    if (res.code === 0) {
      setScheduleData(res.data.schedule || {});
    }
    setLoading(false);
  }
  useEffect(() => {
    if (teacherId !== params.teacher_id) {
      setParams({
        ...params,
        teacher_id: teacherId,
      })
      querySchedule({
        ...params,
        teacher_id: teacherId,
      });
    }

  }, [teacherId]);


  const transformScheduleData = (data?: TeacherSchedule) => {
    const scheduleItems: ScheduleItem[] = [];
    if (!data) return scheduleItems;
    console.log('原始数据:', data);

    // 检查数据是否为空对象
    const hasData = Object.keys(data).some(key => Array.isArray(data[key as keyof TeacherSchedule]) && data[key as keyof TeacherSchedule].length > 0);
    if (!hasData) {
      console.warn('获取到的数据为空或格式不正确');
      return scheduleItems;
    }

    Object.keys(data).forEach((key) => {
      const weekday = Number(key);
      const items = data[key as keyof TeacherSchedule] || [];

      console.log(`处理第 ${weekday} 天的数据:`, items);

      if (!Array.isArray(items)) {
        console.warn(`第 ${weekday} 天的数据不是数组:`, items);
        return;
      }

      items.forEach((item, index) => {
        if (!item) {
          console.warn(`第 ${weekday} 天的第 ${index} 项数据为空`);
          return;
        }

        console.log(`处理第 ${weekday} 天的第 ${index} 项数据:`, item);

        // 生成唯一ID
        const id = `${weekday}_${item.schedule_id || index}`;

        const scheduleItem: ScheduleItem = {
          id: id,
          weekday: weekday,
          startTime: item.schedule_tpl_period_start_time,
          endTime: item.schedule_tpl_period_end_time,
          backgroundColor: getColorBySubject(item.class_schedule_course_id),
          borderColor: getBorderColorBySubject(item.class_schedule_course_id),
          ...item
        };

        console.log(`转换后的日程项:`, scheduleItem);
        scheduleItems.push(scheduleItem);
      });
    });

    console.log('转换后的所有日程项:', scheduleItems);
    return scheduleItems;
  }


  const renderScheduleCard = (eventInfo: any) => {
    console.log(eventInfo, 'eventInfo');

    const { event } = eventInfo;
    const data = event.extendedProps;

    if (!data) {
      console.error('事件没有extendedProps数据:', eventInfo);
      return <div>数据错误</div>;
    }

    return (
      <div style={{ padding: '4px 8px' }}>
        <div style={{ fontWeight: 'bold', fontSize: '14px' }}>
          {data.class_schedule_course || '未知课程'}
        </div>
        <div style={{ fontSize: '12px', marginTop: '3px' }}>
          {data.teacher_name || '未知教师'} | {data.grade_name || ''}
          {data.class_name || ''}
        </div>
        <div style={{ fontSize: '11px', marginTop: '2px', fontStyle: 'italic' }}>
          {data.class_schedule_study_type_name || ''}
        </div>
        {/* 时间 */}
        <div style={{ fontSize: '11px', marginTop: '2px', fontStyle: 'italic' }}>
          {data.startTime?.substring(0, 5) || ''} - {data.endTime?.substring(0, 5) || ''}
        </div>
      </div>
    );
  };

  return (
    <Spin spinning={loading}>
      <SimpleSchedule
        slotDuration='00:15:00'
        title={title}
        scheduleItems={transformScheduleData(scheduleData)}
        // scheduleItems={mockScheduleItems1}
        renderEventContent={renderScheduleCard}
        onDateRangeChange={(start, end) => {
          console.log("onDateRangeChange",dayjs(start).format('YYYY-MM-DD'), dayjs(end).format('YYYY-MM-DD'), 'start, end');
          console.log('原始结束日期:', end);
          console.log('减一天后的结束日期:', dayjs(end).subtract(1, 'day').format('YYYY-MM-DD'));

          // 准备新的参数
          const newParams = {
            ...params,
            start_date: dayjs(start).format('YYYY-MM-DD'),
            end_date: dayjs(end).subtract(1, 'day').format('YYYY-MM-DD')
          };

          // FullCalendar 返回的结束日期是排除的（exclusive），需要减去一天才是实际视图的结束日期
          setParams(newParams);

          // 直接使用新参数查询，避免状态更新延迟
          querySchedule(newParams);
        }}
        onEventClick={(info) => console.log('Clicked:', info.event.extendedProps)}
      />
    </Spin>
  );
};

export default Schedule;