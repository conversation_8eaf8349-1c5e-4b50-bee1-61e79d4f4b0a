import { post } from '@/services/fetcher';
import { UserOutlined } from '@ant-design/icons';
import { useModel } from '@umijs/max';
import { Empty, Input, message, Spin } from 'antd';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import useSWRMutation from 'swr/mutation';
import { TeacherInfo, TeacherListParams } from '../TeacherAccount/type';
import Schedule from './ScheduleData';

export interface GetScheduleParams {
  end_date?: string;
  school_id: number;
  school_year_id: number;
  start_date?: string;
  teacher_id?: number;
  [property: string]: any;
}

const TeacherScheduleManagement: React.FC = () => {
  // 状态管理
  const [selectedTeacher, setSelectedTeacher] = useState<TeacherInfo | null>(null);
  const [loading, setLoading] = useState<{ teachers: boolean; schedule: boolean }>({
    teachers: false,
    schedule: false,
  });
  const [searchValue, setSearchValue] = useState<string>('');
  const { schoolInfo } = useModel('schoolModel');
  const schoolId = schoolInfo?.schoolId || 0;
  const [params, setParams] = useState<GetScheduleParams>({
    school_id: Number(schoolId),
    school_year_id: Number(schoolInfo?.schoolYear?.school_year_id) || 1,
    start_date: '',
    end_date: '',
  });
  const {
    data: teacherListData,
    isMutating: isLoadingTeacherList,
    trigger: mutateTeacherList,
  } = useSWRMutation('/api/v1/userSchool/listTeachers', post<TeacherInfo[], TeacherListParams>);

  // 获取教师列表数据
  const fetchTeacherList = useCallback(async () => {
    setLoading((prev) => ({ ...prev, teachers: true }));
    try {
      await mutateTeacherList({
        schoolID: Number(schoolId),
      });
      setSelectedTeacher(teacherListData ? teacherListData[0] : null);
    } catch (error) {
      console.error('获取教师列表数据失败:', error);
      message.error('获取教师列表数据失败');
    } finally {
      setLoading((prev) => ({ ...prev, teachers: false }));
    }
  }, [schoolId, mutateTeacherList]);

  // 初始化加载
  useEffect(() => {
    fetchTeacherList();
  }, [fetchTeacherList]);

  // 当教师列表数据加载完成后，默认选中第一个教师
  useEffect(() => {
    if (teacherListData && teacherListData.length > 0 && !selectedTeacher) {
      const firstTeacher = teacherListData[0];
      setSelectedTeacher(firstTeacher);
      setParams({
        ...params,
        teacher_id: firstTeacher.userID,
      });
    }
  }, [teacherListData, selectedTeacher]);

  // 处理教师选择
  const handleTeacherSelect = (teacher: TeacherInfo) => {
    setSelectedTeacher(teacher);
  };

  // 处理搜索
  const handleSearch = (value: string) => {
    setSearchValue(value);
  };

  // 过滤教师列表 - 使用本地搜索
  const filteredTeacherList = useMemo(() => {
    console.log(teacherListData, 'teacherListData');
    if (!searchValue) return teacherListData || [];

    return teacherListData
      ? teacherListData.filter(
          (teacher) =>
            teacher.userName?.toLowerCase().includes(searchValue?.toLowerCase()) ||
            teacher.userPhone?.includes(searchValue),
        )
      : [];
  }, [teacherListData, searchValue]);

  // 渲染课表内容
  const renderScheduleContent = useCallback(() => {
    console.log(selectedTeacher, 'selectedTeacher');

    if (!selectedTeacher) {
      return (
        <div className="flex items-center justify-center h-full">
          <Empty description="请选择左侧教师" />
        </div>
      );
    }

    const { userName } = selectedTeacher;

    return (
      <div className="flex flex-col h-full">
        {/* 内容区域 */}
        <div className="flex-1 rounded">
          <Schedule teacherId={selectedTeacher?.userID} title={`${userName} 的课表`} />
        </div>
      </div>
    );
  }, [selectedTeacher]);

  const isTeacherSelected = (teacherId: number) => {
    return selectedTeacher?.userID === teacherId;
  };
  return (
    <div className="flex bg-transparent rounded overflow-hidden h-[calc(100vh-308px)]">
      {/* 左侧教师列表 */}
      <div className="w-60 bg-white overflow-hidden flex flex-col mr-3">
        <div className="text-xl font-medium px-5 pt-6 pb-4">教师课表</div>

        {/* 搜索框 */}
        <div className="px-5 pb-2">
          <Input
            placeholder="搜索教师"
            prefix={<span className="text-gray-400">🔍</span>}
            onChange={(e) => handleSearch(e.target.value)}
            className="rounded-lg"
            bordered={true}
          />
        </div>

        {/* 教师列表 */}
        <div className="flex-1 overflow-y-auto px-5 pb-2">
          <Spin spinning={isLoadingTeacherList || loading.teachers}>
            {filteredTeacherList.map((teacher) => (
              <div
                key={teacher.userID}
                className={`text-sm flex items-center my-2 p-2 cursor-pointer rounded-md space-x-2 ${
                  isTeacherSelected(teacher.userID)
                    ? 'bg-blue-50 text-blue-600'
                    : 'hover:bg-gray-50'
                }`}
                onClick={() => handleTeacherSelect(teacher)}
              >
                <UserOutlined className={`mr-3`} />
                {teacher.userName}
                {/* {teacher.teacherJobInfos[0]?.jobSubject &&
                  <span className={`${isTeacherSelected(teacher.teacherID) ? 'text-blue-600' : 'text-gray-500'}`}>
                    ({teacher.teacherJobInfos[0]?.jobSubject}教师)
                  </span>
                } */}
              </div>
            ))}
            {filteredTeacherList.length === 0 && (
              <div className="text-center py-6 text-gray-400">
                <Empty description={'暂无教师数据'} />
              </div>
            )}
          </Spin>
        </div>
      </div>

      {/* 右侧课表内容 */}
      <div className="h-full flex-1 overflow-y-auto bg-white">{renderScheduleContent()}</div>
    </div>
  );
};

export default TeacherScheduleManagement;
