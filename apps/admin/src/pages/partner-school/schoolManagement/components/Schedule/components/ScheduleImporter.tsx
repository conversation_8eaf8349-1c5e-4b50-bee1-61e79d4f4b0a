// components/ScheduleImporter.tsx
import UploadModal from '@/components/UploadModal';
import { downloadFile, post } from '@/services/fetcher';
import { PlusOutlined } from '@ant-design/icons';
import { request, useModel } from '@umijs/max';
import { Button, ButtonProps, message } from 'antd';
import dayjs from 'dayjs';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import useSWRMutation from 'swr/mutation';
import { NodeInfo, NodeType } from '../../../../../../components/GradeTree';

interface ScheduleImporterProps {
  type: 'grade' | 'class' | 'teacher';
  nodeType: NodeType;
  nodeInfo: NodeInfo<NodeType>;
  onSuccess: () => void;
  buttonProps?: ButtonProps & { children?: React.ReactNode };
  templateId?: number;
  templateName?: string;
}

const ScheduleImporter: React.FC<ScheduleImporterProps> = ({
  templateId,
  type,
  nodeType,
  nodeInfo,
  onSuccess,
  buttonProps,
  templateName,
}) => {
  const { schoolInfo } = useModel('schoolModel');
  const [modalVisible, setModalVisible] = useState(false);
  const classId = useMemo(
    () =>
      nodeType === NodeType.Class ? (nodeInfo as NodeInfo<NodeType.Class>).class_id : undefined,
    [nodeType, nodeInfo],
  );
  useEffect(() => {
    console.log('ScheduleImporter nodeInfo', nodeInfo, classId);
  }, [nodeInfo, classId]);
  // 导入课表数据
  const { trigger: importSchedule, error } = useSWRMutation('/api/v1/schedule/import', post, {
    onSuccess: () => {
      message.success('正在导入数据，请稍候...');
    },
  });

  const downloadTemplate = useCallback(() => {
    console.log('下载模板', templateId, classId);

    request(
      '/api/v1/grade_schedule_tpl/download/gradeScheduleTpl?schedule_tpl_id=' +
        templateId +
        (classId ? '&class_id=' + classId : ''),
      {
        method: 'GET',
      },
    ).then((res) => {
      // 将 base64 转换为 Blob
      const byteCharacters = atob(res.data.file_content);
      const byteNumbers = new Array(byteCharacters.length);

      for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i);
      }

      const byteArray = new Uint8Array(byteNumbers);
      const blob = new Blob([byteArray], { type: 'application/vnd.ms-excel' });
      downloadFile(blob, res.data.file_name);
    });
  }, [templateName, classId]);

  // 打开上传模态框
  const handleOpenModal = useCallback(() => {
    setModalVisible(true);
  }, []);

  // 关闭上传模态框
  const handleCloseModal = useCallback(() => {
    setModalVisible(false);
  }, []);

  // 导入成功回调
  const handleImportSuccess = useCallback(() => {
    // setModalVisible(false);
    onSuccess();
  }, [onSuccess]);

  // 获取模板文件名
  const getTemplateFileName = useCallback(() => {
    switch (type) {
      case 'grade':
        return '批量导入年级课表模板.xlsx';
      case 'class':
        return '批量导入班级课表模板.xlsx';
      case 'teacher':
        return '批量导入教师课表模板.xlsx';
      default:
        return '导入模板.xlsx';
    }
  }, [type]);

  // 获取默认按钮属性
  const defaultButtonProps = {
    type: 'link' as 'link',
    icon: <PlusOutlined />,
    onClick: handleOpenModal,
    children: type === 'grade' ? '批量导入课表' : '导入班级课表',
  };

  // 合并按钮属性
  const mergedButtonProps = {
    ...defaultButtonProps,
    ...buttonProps,
    onClick: (e: React.MouseEvent<HTMLElement>) => {
      handleOpenModal();
      buttonProps?.onClick?.(e);
    },
  };

  // 处理导入数据
  const handleImportData = useCallback(
    async (fileId: string) => {
      try {
        const params: any = {
          obj_key: fileId,
          schedule_tpl_id: templateId, // 根据实际情况设置
          school_id: schoolInfo?.schoolId,
          school_year_id: schoolInfo?.schoolYear.school_year_id,
          effective_date: dayjs().format('YYYY-MM-DD'),
          grade:
            nodeType === NodeType.Class
              ? (nodeInfo as NodeInfo<NodeType.Class>).gradeInfo.grade_id
              : (nodeInfo as NodeInfo<NodeType.Grade>).grade_id,
        };
        if (classId) {
          params.class_id = classId;
        }

        await importSchedule(params);

        if (!error) {
          return {
            success: true,
          };
        }

        return {
          success: false,
          error: '导入失败',
        };
      } catch (error) {
        console.error('导入数据失败:', error);
        return {
          success: false,
          error: '导入失败',
        };
      }
    },
    [importSchedule, nodeInfo, nodeType],
  );

  return (
    <>
      <Button {...mergedButtonProps} />

      <UploadModal
        acceptFileTypes={['.xlsx']}
        visible={modalVisible}
        onClose={handleCloseModal}
        onSuccess={handleImportSuccess}
        title={`批量导入${type === 'grade' ? '年级' : type === 'class' ? '班级' : '教师'}课表`}
        templateFileName={getTemplateFileName()}
        importData={handleImportData}
        downloadTemplate={downloadTemplate}
      />
    </>
  );
};

export default ScheduleImporter;
