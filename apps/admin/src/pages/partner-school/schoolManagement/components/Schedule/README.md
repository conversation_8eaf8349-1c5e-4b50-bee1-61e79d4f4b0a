# 学期周选择器集成示例

本文档展示如何在课表管理组件(ScheduleManagement)中集成学期周选择器(SemesterWeekSelector)。

## 集成步骤

在现有课表管理组件中，按照以下步骤集成学期周选择器：

1. 导入 SemesterWeekSelector 组件
2. 添加状态管理周信息
3. 集成到头部区域
4. 处理周变更事件

## 代码示例

```tsx
import React, { useState } from 'react';
import { Button } from 'antd';
import { SemesterWeekSelector, SemesterInfo, WeekInfo } from '@/components';
import ScheduleImporter from './components/ScheduleImporter';
// ... 其他导入 ...

const ScheduleManagement: React.FC<ScheduleManagementProps> = ({
  schoolId,
  // ... 其他属性 ...
}) => {
  // ... 现有状态 ...
  
  // 添加周信息状态
  const [currentWeekInfo, setCurrentWeekInfo] = useState<WeekInfo | null>(null);
  const [currentSemesterInfo, setCurrentSemesterInfo] = useState<SemesterInfo | null>(null);
  
  // 处理周变更事件
  const handleWeekChange = (weekInfo: WeekInfo, semesterInfo: SemesterInfo | null) => {
    setCurrentWeekInfo(weekInfo);
    setCurrentSemesterInfo(semesterInfo);
    
    // 根据需要加载该周的课表数据
    if (weekInfo) {
      // 使用 weekInfo.startDate 和 weekInfo.endDate 加载数据
      // 例如: refreshScheduleData({ weekStart: weekInfo.startDate, weekEnd: weekInfo.endDate });
    }
  };
  
  // ... 其他方法 ...
  
  return (
    <div className="schedule-management">
      {/* 头部标题和操作区 */}
      <div className="flex flex-col mb-4">
        {/* 学期周选择器 */}
        <div className="mb-4">
          <SemesterWeekSelector
            schoolId={schoolId}
            schoolYearId={1} // 根据实际情况设置
            grade={11} // 根据实际情况设置或从props获取
            onChange={handleWeekChange}
          />
        </div>
        
        <div className="flex justify-between items-center">
          <div className="text-lg font-medium">
            {isGradeView && scheduleData?.tpl_info?.template_name || scheduleName}
          </div>

          {!currentEditMode && (
            <div className="flex space-x-2">
              <ScheduleImporter
                type={isGradeView ? 'grade' : 'class'}
                nodeType={nodeType}
                nodeId={nodeId}
                onSuccess={handleImportSuccess}
              />
              <Button
                type='primary'
                onClick={() => setEditMode(isGradeView ? "grade" : "class")}
              >
                {isGradeView ? '编辑模版' : '编辑课表'}
              </Button>
            </div>
          )}
        </div>
      </div>
      
      {/* 课表内容区域 */}
      {/* ... 现有代码 ... */}
    </div>
  );
};

export default ScheduleManagement;
```

## 注意事项

1. 根据实际需求调整 `schoolYearId` 和 `grade` 参数
2. 在 `handleWeekChange` 回调中处理周信息变更，可以用于更新课表数据
3. 学期周选择器放置在头部标题之上，与现有UI分开，不影响原有布局
4. 使用 `weekInfo.startDate` 和 `weekInfo.endDate` 作为数据请求的参数 