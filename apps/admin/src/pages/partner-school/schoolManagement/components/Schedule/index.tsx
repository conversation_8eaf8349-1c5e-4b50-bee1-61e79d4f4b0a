import { getGradeScheduleTemplateDetail, getSchedule } from '@/services/schedule-management';
import { ScheduleData } from '@/services/schedule-management/type';
import { Semester } from '@/services/semester-management/type';
import { PlusOutlined, ScheduleOutlined } from '@ant-design/icons';
import { useModel } from '@umijs/max';
import { Button, Empty, message, Spin } from 'antd';
import dayjs from 'dayjs';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import GradeTree, { GradeTreeProps, NodeInfo, NodeType } from '../../../../../components/GradeTree';
import ScheduleImporter from './components/ScheduleImporter';
import ScheduleTable from './components/ScheduleTable';
import ScheduleTemplateEditor from './components/ScheduleTemplateEditor';
import useSemesterWeekSelector, {
  WeekInfo,
} from './components/SemesterWeekSelector/useSemesterWeekSelector';

// 节点信息类型
interface SelectedNodeInfo {
  nodeType: NodeType;
  nodeId: number;
  scheduleName: string;
  nodeInfo?: NodeInfo<NodeType>;
}

const getCurrentGrade = (nodeType: NodeType, nodeInfo?: NodeInfo<NodeType>) => {
  if (nodeType === NodeType.Grade) {
    return (nodeInfo as NodeInfo<NodeType.Grade>).grade_name;
  } else if (nodeType === NodeType.Class) {
    return (nodeInfo as NodeInfo<NodeType.Class>).gradeInfo.grade_name;
  }
  return '';
};

const ScheduleManagement: React.FC = () => {
  const { schoolInfo } = useModel('schoolModel');
  // 状态管理
  const [selectedNodeInfo, setSelectedNodeInfo] = useState<SelectedNodeInfo | null>(null);
  const [loading, setLoading] = useState<{ tree: boolean; schedule: boolean }>({
    tree: false,
    schedule: false,
  });
  const [scheduleData, setScheduleData] = useState<ScheduleData | null>(null);
  const [editMode, setEditMode] = useState<boolean | 'class' | 'grade'>(false);
  const [semesterInfo, setSemesterInfo] = useState<Semester | null>(null);
  const [weekInfo, setWeekInfo] = useState<WeekInfo | null>(null);
  const [currentGradeId, setCurrentGradeId] = useState<number | null>(null);
  const { SemesterWeekSelector, hasSemesterData, setStartDate } = useSemesterWeekSelector({
    editMode: !!editMode,
    grade: currentGradeId || 0,
    onChange: (weekInfo, semesterInfo) => {
      setWeekInfo(weekInfo);
      setSemesterInfo(semesterInfo);
    },
  });
  // 获取课表数据
  const fetchScheduleData = useCallback(
    async (nodeType: NodeType, nodeId: number) => {
      if (!weekInfo || !semesterInfo || !semesterInfo?.semester_id) {
        // message.error('请先选择学期和周');
        return;
      }
      console.log('fetchScheduleData', { nodeType, nodeId, semesterInfo });
      setLoading((prev) => ({ ...prev, schedule: true }));

      try {
        if (nodeType === NodeType.Grade) {
          const response = await getGradeScheduleTemplateDetail({
            grade: Number(nodeId),
            school_id: Number(schoolInfo?.schoolId),
            school_year_id: Number(schoolInfo?.schoolYear?.school_year_id) || 1,
            semester_id: semesterInfo?.semester_id,
            week_start_date: weekInfo?.startDate,
            week_end_date: weekInfo?.endDate,
            // semester_id: Number(schoolInfo?.semesterId) || 1
          });
          setScheduleData(response.data);
        } else if (nodeType === NodeType.Class) {
          const response = await getSchedule({
            school_id: Number(schoolInfo?.schoolId),
            school_year_id: Number(schoolInfo?.schoolYear?.school_year_id) || 1,
            semester_id: semesterInfo?.semester_id,
            start_date: weekInfo?.startDate,
            end_date: weekInfo?.endDate,
            type: 'class',
            class_id: Number(nodeId),
            week: 1,
          });
          setScheduleData(response.data);
        } else {
          setScheduleData(null);
        }
      } catch (error) {
        console.error('获取课表数据失败:', error);
        message.error('获取课表数据失败');
        setScheduleData(null);
      } finally {
        setLoading((prev) => ({ ...prev, schedule: false }));
      }
    },
    [schoolInfo?.schoolId, weekInfo, semesterInfo],
  );

  useEffect(() => {
    const selectedNodeType = selectedNodeInfo?.nodeType;
    console.log('selectedNodeType', selectedNodeType);
    if (selectedNodeType === NodeType.Grade || selectedNodeType === NodeType.Class) {
      fetchScheduleData(selectedNodeType, selectedNodeInfo?.nodeId || 0);
    }
  }, [selectedNodeInfo, semesterInfo, weekInfo]);

  // 处理树节点选择
  const handleTreeSelect = useCallback<GradeTreeProps['onNodeSelect']>(
    (selectedKey: React.Key, selectedNodeType, nodeInfo) => {
      console.log('handleTreeSelect', selectedKey, selectedNodeType, nodeInfo);

      if (!selectedKey) return;

      // 只有年级节点和班级节点可以选中
      if (selectedNodeType === NodeType.Grade || selectedNodeType === NodeType.Class) {
        // 查找节点名称
        let scheduleName = '';
        let nodeId = 0;

        if (selectedNodeType === NodeType.Grade && nodeInfo) {
          // 断言为年级节点类型
          const gradeInfo = nodeInfo as NodeInfo<NodeType.Grade>;
          scheduleName = gradeInfo.grade_name || '';
          nodeId = gradeInfo.grade_id;
          setCurrentGradeId(gradeInfo.grade_id);
        } else if (selectedNodeType === NodeType.Class && nodeInfo) {
          // 断言为班级节点类型
          const classInfo = nodeInfo as NodeInfo<NodeType.Class>;
          scheduleName = `${classInfo.gradeInfo.grade_name} ${classInfo.class_name}`;
          nodeId = classInfo.class_id;
          setCurrentGradeId(classInfo.gradeInfo.grade_id);
        }

        // 设置选中节点信息
        setSelectedNodeInfo({
          nodeType: selectedNodeType,
          nodeId,
          scheduleName,
          nodeInfo,
        });

        // 退出编辑模式
        setEditMode(false);
      }
    },
    [],
  );

  // 保存/取消处理
  const handleScheduleAction = useCallback(
    (action: 'save' | 'cancel') => {
      setEditMode(false);

      // 刷新课表数据
      if (selectedNodeInfo) {
        fetchScheduleData(selectedNodeInfo.nodeType, selectedNodeInfo.nodeId);
      }
    },
    [selectedNodeInfo, fetchScheduleData],
  );

  // 导入成功处理
  const handleImportSuccess = useCallback(() => {
    message.success('课表导入成功');

    // 刷新数据
    if (selectedNodeInfo) {
      fetchScheduleData(selectedNodeInfo.nodeType, selectedNodeInfo.nodeId);
    }
  }, [selectedNodeInfo, fetchScheduleData]);

  // 渲染空状态
  const renderEmptyState = useCallback(() => {
    if (!selectedNodeInfo) {
      return (
        <div className="flex items-center justify-center h-full">
          <Empty description="请选择左侧年级或班级" />
        </div>
      );
    }
    return null;
  }, [selectedNodeInfo]);

  // 渲染无数据状态
  const renderNoDataState = useCallback(() => {
    if (!selectedNodeInfo || scheduleData) return null;

    const { nodeType } = selectedNodeInfo;
    const isGradeView = nodeType === NodeType.Grade;
    // 选择时间后，是否在今天之前
    const isBeforeToday = dayjs(weekInfo?.endDate).isBefore(dayjs());

    // 创建年级课表按钮
    const renderGradeCreateButton = () =>
      isBeforeToday ? null : (
        <Button
          className="mt-6"
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => setEditMode('grade')}
        >
          创建年级课表模版
        </Button>
      );

    // 导入班级课表按钮
    const renderClassImportButton = () => (
      <div className="text-gray-400 mb-5">请先创建年级课表模板</div>
    );

    return (
      <div className="flex flex-col items-center justify-center h-full py-10 mt-10">
        <ScheduleOutlined className="text-5xl text-gray-300 mb-4" />
        <div className="text-gray-600">暂无{isGradeView ? '年级' : '班级'}课表数据</div>
        {isGradeView ? renderGradeCreateButton() : renderClassImportButton()}
      </div>
    );
  }, [selectedNodeInfo, scheduleData, handleImportSuccess]);

  // 渲染课表编辑器
  const renderScheduleEditor = useCallback(() => {
    if (!selectedNodeInfo || editMode !== 'grade') return null;

    return (
      <ScheduleTemplateEditor
        grade={Number(selectedNodeInfo.nodeId)}
        periods={scheduleData?.tpl_info?.period_info}
        templateInfo={scheduleData?.tpl_info}
        onCancel={() => handleScheduleAction('cancel')}
        onSave={({ startDate }) => {
          setStartDate(startDate);

          handleScheduleAction('save');
        }}
      />
    );
  }, [selectedNodeInfo, scheduleData, editMode, handleScheduleAction, weekInfo, semesterInfo]);

  // 渲染课表表格
  const renderScheduleTable = useCallback(() => {
    if (!selectedNodeInfo || !scheduleData || editMode === 'grade') return null;

    const { nodeType, nodeId, nodeInfo } = selectedNodeInfo;
    const isGradeView = nodeType === NodeType.Grade;
    const isEditingClass = editMode === 'class';
    const schoolId = Number(schoolInfo?.schoolId);
    let gradeId = undefined;
    let classId = undefined;
    if (nodeType === NodeType.Grade) {
      gradeId = Number(nodeId);
    } else if (nodeType === NodeType.Class) {
      classId = Number(nodeId);
      gradeId = Number((nodeInfo as NodeInfo<NodeType.Class>).gradeInfo.grade_id);
    }
    if (!weekInfo) return null;

    return (
      <Spin spinning={loading.schedule}>
        <ScheduleTable
          weekInfo={weekInfo}
          scheduleData={scheduleData}
          isGradeTemplate={isGradeView}
          isEditMode={isEditingClass}
          onSave={() => handleScheduleAction('save')}
          onCancel={() => handleScheduleAction('cancel')}
          schoolId={schoolId}
          gradeId={gradeId}
          classId={classId}
        />
      </Spin>
    );
  }, [
    selectedNodeInfo,
    scheduleData,
    editMode,
    loading.schedule,
    schoolInfo?.schoolId,
    handleScheduleAction,
  ]);

  // 渲染操作按钮
  const renderActionButtons = useCallback(() => {
    if (!selectedNodeInfo || !scheduleData) return null;

    const { nodeType, nodeInfo } = selectedNodeInfo;
    const isGradeView = nodeType === NodeType.Grade;
    const isClassView = nodeType === NodeType.Class;
    const isEditingTemplate = editMode === 'grade';
    const isEditingClass = editMode === 'class';
    const currentEditMode = isGradeView ? isEditingTemplate : isEditingClass;

    const templateName = `${schoolInfo?.schoolName}_${getCurrentGrade(nodeType, nodeInfo)}_${semesterInfo?.semester_name}_下载模板.xlsx`;
    if (!scheduleData && isClassView) return null;
    if (currentEditMode) return null;

    return (
      <div className="flex space-x-2">
        <ScheduleImporter
          templateName={templateName}
          templateId={scheduleData?.tpl_info?.template_id}
          type={isGradeView ? 'grade' : 'class'}
          nodeInfo={nodeInfo as NodeInfo<NodeType>}
          nodeType={nodeType}
          onSuccess={handleImportSuccess}
        />
        <Button type="primary" onClick={() => setEditMode(isGradeView ? 'grade' : 'class')}>
          {isGradeView ? '编辑模版' : '编辑课表'}
        </Button>
      </div>
    );
  }, [selectedNodeInfo, editMode, handleImportSuccess, scheduleData]);

  // 渲染课表内容
  const renderScheduleContent = useCallback(() => {
    const emptyState = renderEmptyState();
    if (emptyState) return emptyState;

    const { nodeType, scheduleName } = selectedNodeInfo!;
    const isGradeView = nodeType === NodeType.Grade;

    // 获取标题文本
    const titleText = (isGradeView && scheduleData?.tpl_info?.template_name) || scheduleName;

    return (
      <div className="flex flex-col h-full">
        {/* 头部标题和操作区 */}
        <div className="flex justify-between items-center">
          <div className="text-lg font-medium">{titleText}</div>
          {hasSemesterData ? renderActionButtons() : null}
        </div>

        <div className="mb-4">{SemesterWeekSelector}</div>

        {/* 内容区域 */}
        <div className="flex-1 rounded">
          {hasSemesterData ? (
            <Spin spinning={loading.schedule}>
              {renderScheduleEditor() || renderScheduleTable() || renderNoDataState()}
            </Spin>
          ) : null}
        </div>
      </div>
    );
  }, [
    selectedNodeInfo,
    scheduleData,
    renderEmptyState,
    renderActionButtons,
    renderScheduleEditor,
    renderScheduleTable,
    renderNoDataState,
  ]);

  const GradeTreeMemo = useMemo(() => {
    return <GradeTree disableSelection={!!editMode} onNodeSelect={handleTreeSelect} />;
  }, [editMode, handleTreeSelect]);

  return (
    <div className="flex bg-transparent rounded overflow-hidden h-[calc(100vh-308px)]">
      {/* 使用CSS过渡动画控制树组件的显示/隐藏 */}
      <div
        className={`transition-all duration-300 h-full overflow-hidden ${editMode === 'class' ? 'w-0' : 'w-60 mr-3'}`}
      >
        {GradeTreeMemo}
      </div>

      {/* 课表内容区域 */}
      <div className="h-full flex-1 p-4 overflow-y-auto bg-white">{renderScheduleContent()}</div>
    </div>
  );
};

export default ScheduleManagement;
