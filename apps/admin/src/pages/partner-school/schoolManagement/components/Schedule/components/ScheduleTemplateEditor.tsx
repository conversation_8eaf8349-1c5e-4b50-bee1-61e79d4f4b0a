import fetcher from '@/services/fetcher';
import {
  createGradeScheduleTemplate,
  updateGradeScheduleTemplate,
} from '@/services/schedule-management';
import {
  GradeScheduleTemplateParams,
  PeriodInfo,
  TplInfo,
} from '@/services/schedule-management/type';
import { SemesterData } from '@/services/semester-management/type';
import {
  DeleteOutlined,
  PlusOutlined,
  QuestionCircleOutlined,
  SettingOutlined,
} from '@ant-design/icons';
import { useModel } from '@umijs/max';
import { Button, DatePicker, InputNumber, message, Select, Space, TimePicker, Tooltip } from 'antd';
import type { Dayjs } from 'dayjs';
import dayjs from 'dayjs';
import { cloneDeep } from 'lodash-es';
import React, { useEffect, useMemo, useState } from 'react';
import useSWR from 'swr';
import { defaultPeriodGroups } from '../data';
import QuickSettingsModal, { QuickSettings } from './QuickSettingsModal';
import { findSemesterByDate } from './SemesterWeekSelector/useSemesterWeekSelector';

/**
 * 时间段分组信息
 */
interface TimeSpanGroup {
  spanType: number; // 1-上午, 2-下午, 3-晚上
  title: string;
  periods: PeriodInfo[];
}

interface ScheduleTemplateEditorProps {
  periods?: PeriodInfo[];
  templateInfo?: Partial<TplInfo>;
  grade: number;
  templateId?: number;
  onCancel: () => void;
  onSave: (params: { startDate: string }) => void;
}

const TIME_SPAN_MAPPING: Record<number, string> = {
  1: '上午',
  2: '下午',
  3: '晚上',
};

const ScheduleTemplateEditor: React.FC<ScheduleTemplateEditorProps> = ({
  periods: initialPeriods,
  templateInfo,
  grade,
  templateId,
  onCancel,
  onSave,
}) => {
  const { schoolInfo } = useModel('schoolModel');
  const schoolId = schoolInfo?.schoolId;
  const { semester_id, start_date, week_cycle } = templateInfo || {};
  // 获取学期列表
  const { data: semesterData } = useSWR(
    '/api/v1/semester/get?school_id=' +
      schoolInfo?.schoolId +
      '&school_year_id=' +
      (schoolInfo?.schoolYear?.school_year_id || 1),
    (key) => fetcher<SemesterData>(key),
    {
      revalidateOnFocus: false,
      revalidateOnReconnect: false,
      revalidateIfStale: false,
      revalidateOnMount: false,
    },
  );
  const semesterOptions = useMemo(() => {
    console.log('semesterData', semesterData, grade);
    return semesterData?.grade_settings
      ?.find((item) => item.grade_id === grade)
      ?.semesters?.map((item) => ({
        ...item,
        label: item.semester_name,
        value: item.semester_id,
      }));
  }, [semesterData, grade]);
  // 将初始期号数据按时间段分组
  const createPeriodGroups = (periods: PeriodInfo[]): TimeSpanGroup[] => {
    const grouped: Record<string, PeriodInfo[]> = {};

    // 根据 time_span 进行分组
    periods.forEach((period) => {
      const spanType =
        period.schedule_tpl_period_time_span === '上午'
          ? 1
          : period.schedule_tpl_period_time_span === '下午'
            ? 2
            : 3;

      if (!grouped[spanType]) {
        grouped[spanType] = [];
      }

      grouped[spanType].push({ ...period });
    });

    // 转换为数组格式，按时间段排序
    return Object.keys(grouped)
      .map(Number)
      .sort((a, b) => a - b)
      .map((spanType) => ({
        spanType,
        title: TIME_SPAN_MAPPING[spanType] || `时间段${spanType}`,
        periods: grouped[spanType].sort(
          (a, b) => a.schedule_tpl_period_period_no - b.schedule_tpl_period_period_no,
        ),
      }));
  };

  const [periodGroups, setPeriodGroups] = useState<TimeSpanGroup[]>(
    createPeriodGroups(initialPeriods || defaultPeriodGroups),
  );

  const [cycleWeeks, setCycleWeeks] = useState<number>(week_cycle || 1);
  const [startDate, setStartDate] = useState<string>(start_date || '');
  const [semester, setSemester] = useState<number>(semester_id || 1);
  const [quickSettingsVisible, setQuickSettingsVisible] = useState(false);

  useEffect(() => {
    if (templateInfo) return;
    const currentSemester = findSemesterByDate(semesterOptions || [], dayjs());
    console.log('currentSemester', currentSemester);

    setSemester(currentSemester?.semester_id || 1);
  }, [semesterOptions]);

  useEffect(() => {
    if (templateInfo) {
      setSemester(templateInfo?.semester_id || 1);
      setStartDate(templateInfo?.start_date || '');
      setCycleWeeks(templateInfo?.week_cycle || 1);
    }
  }, [templateInfo]);

  useEffect(() => {
    setPeriodGroups(createPeriodGroups(initialPeriods || defaultPeriodGroups));
    // if(!initialPeriods) {
    //   setStartDate("");
    // }
  }, [initialPeriods]);
  // 添加课程
  const handleAddPeriod = (groupIndex: number) => {
    console.log('添加新课时: ', groupIndex, cloneDeep(periodGroups));
    const newPeriodGroups = [...periodGroups];
    const group = newPeriodGroups[groupIndex];

    // 获取所有期号中的最大值
    // const maxPeriodNo = Math.max(
    //   ...periodGroups.flatMap((g) => g.periods.map((p) => p.schedule_tpl_period_period_no)),
    // );

    // 计算新课时的时间
    let newStartTime, newEndTime;

    if (group.periods.length > 0) {
      // 如果组内已有课时，基于最后一个课时的结束时间计算
      const [lastEndHour, lastEndMinute] = group.periods[
        group.periods.length - 1
      ].schedule_tpl_period_end_time
        .split(':')
        .map(Number);

      let newStartHour = lastEndHour;
      let newStartMinute = lastEndMinute + 10; // 休息10分钟

      if (newStartMinute >= 60) {
        newStartHour += 1;
        newStartMinute -= 60;
      }

      let newEndHour = newStartHour;
      let newEndMinute = newStartMinute + 45; // 课程45分钟

      if (newEndMinute >= 60) {
        newEndHour += 1;
        newEndMinute -= 60;
      }

      newStartTime = `${newStartHour.toString().padStart(2, '0')}:${newStartMinute.toString().padStart(2, '0')}`;
      newEndTime = `${newEndHour.toString().padStart(2, '0')}:${newEndMinute.toString().padStart(2, '0')}`;
    } else {
      // 如果是组内第一个课时，根据时间段设置默认时间
      switch (group.spanType) {
        case 1: // 上午
          newStartTime = '08:00';
          newEndTime = '08:45';
          break;
        case 2: // 下午
          newStartTime = '14:00';
          newEndTime = '14:45';
          break;
        case 3: // 晚上
          newStartTime = '18:00';
          newEndTime = '18:45';
          break;
        default:
          newStartTime = '08:00';
          newEndTime = '08:45';
      }
    }

    // 创建新课时
    const newPeriod: PeriodInfo = {
      schedule_tpl_period_id: Date.now(),
      schedule_tpl_period_period_no: 0,
      schedule_tpl_period_time_span: TIME_SPAN_MAPPING[group.spanType],
      schedule_tpl_period_start_time: newStartTime,
      schedule_tpl_period_end_time: newEndTime,
    };

    // 添加到组中
    group.periods.push(newPeriod);

    // 重新计算所有课时的序号
    newPeriodGroups
      .flatMap((group) => group.periods)
      .forEach((period, index) => {
        period.schedule_tpl_period_period_no = index + 1;
      });

    setPeriodGroups(newPeriodGroups);
  };

  // 删除课程
  const handleDeletePeriod = (groupIndex: number, periodIndex: number) => {
    const newPeriodGroups = [...periodGroups];
    const groupToUpdate = newPeriodGroups[groupIndex];

    // 删除指定课时
    groupToUpdate.periods.splice(periodIndex, 1);

    // 重新计算所有课时的序号
    let currentPeriodNo = 1;
    newPeriodGroups.forEach((group) => {
      group.periods.forEach((period) => {
        period.schedule_tpl_period_period_no = currentPeriodNo;
        period.schedule_tpl_period_id = currentPeriodNo;
        currentPeriodNo++;
      });
    });

    setPeriodGroups(newPeriodGroups);
  };

  // 更新课程时间
  const handleTimeChange = (
    groupIndex: number,
    periodIndex: number,
    type: 'start' | 'end',
    time: Dayjs | null,
  ) => {
    if (!time) return;

    const newPeriodGroups = [...periodGroups];
    const timeString = time.format('HH:mm');

    if (type === 'start') {
      newPeriodGroups[groupIndex].periods[periodIndex].schedule_tpl_period_start_time = timeString;
    } else {
      newPeriodGroups[groupIndex].periods[periodIndex].schedule_tpl_period_end_time = timeString;
    }

    setPeriodGroups(newPeriodGroups);
  };

  // 保存模板
  const handleSave = async () => {
    if (!startDate) {
      message.error('请选择开始生效日期');
      return;
    }

    // 将分组数据转换回平面数组
    const allPeriods = periodGroups.flatMap((group) => group.periods);

    const params: GradeScheduleTemplateParams = {
      school_year_id: schoolInfo?.schoolYear?.school_year_id || 1,
      school_id: schoolId || 0,
      grade: grade,
      semester_id: semester,
      start_date: startDate,
      week_cycle: cycleWeeks,
      schedule_periods: allPeriods.map((period) => ({
        time_span:
          period.schedule_tpl_period_time_span === '上午'
            ? 1
            : period.schedule_tpl_period_time_span === '下午'
              ? 2
              : 3,
        period_no: period.schedule_tpl_period_period_no,
        start_time: period.schedule_tpl_period_start_time,
        end_time: period.schedule_tpl_period_end_time,
        day_of_week: 1, // 默认值
      })),
    };

    console.log('保存年级课表模板', params);
    let res;
    if (templateId) {
      res = await updateGradeScheduleTemplate({
        ...params,
        id: templateId,
      });
    } else {
      res = await createGradeScheduleTemplate(params);
    }
    if (res.code === 0) {
      onSave({
        startDate: startDate,
      });
      message.success('课表模板保存成功');
    } else {
      message.error(res.message || '课表模板保存失败');
    }
  };

  // 格式化时间字符串为 dayjs 对象
  const formatTime = (timeString?: string): Dayjs | undefined => {
    if (!timeString) return undefined;
    const [hours, minutes] = timeString.split(':').map(Number);
    return dayjs().hour(hours).minute(minutes).second(0);
  };

  // 获取下周一的日期
  const getNextMonday = useMemo(() => {
    const today = dayjs();
    const dayOfWeek = today.day(); // 0是周日，1是周一，以此类推
    const daysUntilNextMonday = dayOfWeek === 0 ? 1 : 8 - dayOfWeek;
    return today.add(daysUntilNextMonday, 'day');
  }, []);

  // 日期选择器的禁用日期函数
  const disabledDate = (current: Dayjs, info?: { type: string }) => {
    if (!current) return false;

    // 对于年份和月份选择，禁用早于下周一所在年份/月份的选项
    if (info?.type === 'year') {
      return current.year() < getNextMonday.year();
    }

    if (info?.type === 'month') {
      const nextMondayYear = getNextMonday.year();
      const nextMondayMonth = getNextMonday.month();
      return (
        current.year() < nextMondayYear ||
        (current.year() === nextMondayYear && current.month() < nextMondayMonth)
      );
    }

    // 日期选择模式：禁用所有早于下周一的日期
    if (current.isBefore(getNextMonday, 'day')) {
      return true;
    }
    // 只允许选择周一
    return current.day() !== 1;
  };

  // 根据快捷设置生成课程时间
  const generatePeriodsFromSettings = (settings: QuickSettings): TimeSpanGroup[] => {
    const {
      morningStart,
      afternoonStart,
      eveningStart,
      periodDuration,
      breakDuration,
      periodsPerDay,
    } = settings;

    // 优先填满上午和下午
    let morningCount = 0;
    let afternoonCount = 0;
    let eveningCount = 0;

    // 根据课程数量分配时间段
    if (periodsPerDay <= 2) {
      // 1-2节课：全部放在上午
      morningCount = periodsPerDay;
      afternoonCount = 0;
      eveningCount = 0;
    } else if (periodsPerDay <= 4) {
      // 3-4节课：上午2节，下午1-2节
      morningCount = 2;
      afternoonCount = periodsPerDay - 2;
      eveningCount = 0;
    } else if (periodsPerDay <= 6) {
      // 5-6节课：上午3节，下午2-3节
      morningCount = 3;
      afternoonCount = periodsPerDay - 3;
      eveningCount = 0;
    } else {
      // 7节及以上：上午3节，下午3节，剩余放在晚上
      morningCount = 3;
      afternoonCount = 3;
      eveningCount = periodsPerDay - 6;
    }

    const generatePeriodsForSpan = (
      startTime: string,
      spanType: number,
      count: number,
      startPeriodNo: number,
    ): PeriodInfo[] => {
      if (count === 0) return []; // 如果数量为0，返回空数组

      const periods: PeriodInfo[] = [];
      let currentTime = dayjs(startTime, 'HH:mm');

      for (let i = 0; i < count; i++) {
        const startTimeStr = currentTime.format('HH:mm');
        currentTime = currentTime.add(periodDuration, 'minute');
        const endTimeStr = currentTime.format('HH:mm');
        currentTime = currentTime.add(breakDuration, 'minute');

        periods.push({
          schedule_tpl_period_id: startPeriodNo + i,
          schedule_tpl_period_period_no: startPeriodNo + i,
          schedule_tpl_period_time_span: TIME_SPAN_MAPPING[spanType],
          schedule_tpl_period_start_time: startTimeStr,
          schedule_tpl_period_end_time: endTimeStr,
        });
      }

      return periods;
    };

    let currentPeriodNo = 1;
    return [
      {
        spanType: 1,
        title: '上午',
        periods: generatePeriodsForSpan(morningStart, 1, morningCount, currentPeriodNo),
      },
      {
        spanType: 2,
        title: '下午',
        periods: generatePeriodsForSpan(
          afternoonStart,
          2,
          afternoonCount,
          currentPeriodNo + morningCount,
        ),
      },
      {
        spanType: 3,
        title: '晚上',
        periods: generatePeriodsForSpan(
          eveningStart,
          3,
          eveningCount,
          currentPeriodNo + morningCount + afternoonCount,
        ),
      },
    ];
  };

  const handleQuickSettingsOk = (settings: QuickSettings) => {
    const newPeriodGroups = generatePeriodsFromSettings(settings);
    setPeriodGroups(newPeriodGroups);
    setQuickSettingsVisible(false);
  };

  return (
    <div className="flex flex-col h-full p-0 overflow-hidden">
      <div className="flex items-center justify-between pb-4">
        <div className="text-lg font-medium">设置课表模板</div>
        <div className="flex space-x-2">
          <Button
            icon={<SettingOutlined />}
            onClick={() => setQuickSettingsVisible(true)}
            className="mr-2"
          >
            快捷设置
          </Button>
          <Button onClick={onCancel}>取消</Button>
          <Button type="primary" onClick={handleSave}>
            保存
          </Button>
        </div>
      </div>

      <div className="overflow-y-auto h-[calc(100vh-308px-100px-44px)]">
        <div className="flex items-center mb-4">
          <div className="mr-2">设置学期:</div>
          <Select
            value={semester}
            onChange={(value) => setSemester(value)}
            style={{ width: 120 }}
            className="mr-4"
            options={semesterOptions}
          ></Select>
        </div>

        <div className="flex flex-wrap items-center mb-6 whitespace-nowrap">
          <Space className="mr-4 mb-2">
            <span className="mr-2">开始生效日期:</span>
            <DatePicker
              value={startDate ? dayjs(startDate) : undefined}
              onChange={(date) => date && setStartDate(date.format('YYYY-MM-DD'))}
              disabledDate={disabledDate}
              placeholder="请选择周一"
            />
            <Tooltip
              title={`生效日期只能选择周一，最早可选下周一：${getNextMonday.format('MM月DD日')}`}
            >
              <QuestionCircleOutlined className="text-gray-400 cursor-help" />
            </Tooltip>
          </Space>
          <Space className="mb-2">
            <span className="mr-2">课表周期:</span>
            <span className="mr-2">每</span>
            <InputNumber
              min={1}
              max={52}
              value={cycleWeeks}
              onChange={(value) => setCycleWeeks(value || 1)}
            />
            <span>周循环一次</span>
          </Space>
        </div>

        {periodGroups.map((group, groupIndex) => (
          <div key={group.title} className="mb-6 border border-gray-200 rounded-lg p-4">
            <div className="text-base font-medium mb-4 flex items-center justify-between">
              {group.title}

              <Button
                type="link"
                className="mt-2"
                icon={<PlusOutlined />}
                onClick={() => handleAddPeriod(groupIndex)}
              >
                添加课程
              </Button>
            </div>

            {group.periods.map((period, periodIndex) => (
              <div key={period.schedule_tpl_period_id} className="flex items-center mb-3">
                <div className="w-24 mr-2">第{period.schedule_tpl_period_period_no}节</div>
                <div className="flex items-center">
                  <TimePicker
                    value={formatTime(period.schedule_tpl_period_start_time)}
                    onChange={(time) => handleTimeChange(groupIndex, periodIndex, 'start', time)}
                    format="HH:mm"
                    minuteStep={5}
                    className="w-24"
                    needConfirm={false}
                  />
                  <span className="mx-2">至</span>
                  <TimePicker
                    value={formatTime(period.schedule_tpl_period_end_time)}
                    onChange={(time) => handleTimeChange(groupIndex, periodIndex, 'end', time)}
                    format="HH:mm"
                    minuteStep={5}
                    className="w-24"
                    needConfirm={false}
                  />
                  <Button
                    type="text"
                    danger
                    className="ml-2"
                    icon={<DeleteOutlined />}
                    onClick={() => handleDeletePeriod(groupIndex, periodIndex)}
                  />
                </div>
              </div>
            ))}
          </div>
        ))}
      </div>

      <QuickSettingsModal
        visible={quickSettingsVisible}
        onCancel={() => setQuickSettingsVisible(false)}
        onOk={handleQuickSettingsOk}
      />
    </div>
  );
};

export default ScheduleTemplateEditor;
