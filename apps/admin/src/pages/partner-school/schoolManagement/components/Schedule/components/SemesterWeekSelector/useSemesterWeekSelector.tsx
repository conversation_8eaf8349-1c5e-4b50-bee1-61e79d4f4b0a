import fetcher from '@/services/fetcher';
import { Semester, SemesterData } from '@/services/semester-management/type';
import { CalendarOutlined, LeftOutlined, LoadingOutlined, RightOutlined } from '@ant-design/icons';
import { useModel } from '@umijs/max';
import { Button, DatePicker, Empty, message, Spin, Tooltip } from 'antd';
import dayjs from 'dayjs';
import { useEffect, useMemo, useState } from 'react';
import useSWR from 'swr';

// 周信息接口
export interface WeekInfo {
  weekNumber: number;
  startDate: string;
  endDate: string;
  fullDateRange: string;
}

// hooks参数接口
export interface UseSemesterWeekSelectorProps {
  grade?: number | string;
  onChange?: (weekInfo: WeekInfo, semesterInfo: Semester | null) => void;
  initialDate?: string; // 初始日期，默认为当前日期
  editMode?: boolean;
}

/**
 * 根据日期在学期列表中查找对应的学期
 */
export const findSemesterByDate = (semesters: Semester[], date: dayjs.Dayjs): Semester | null => {
  return (
    semesters.find((semester) => {
      if (!semester.start_date || !semester.end_date) return false;
      const startDate = dayjs(semester.start_date);
      const endDate = dayjs(semester.end_date);
      return (
        (date.isAfter(startDate) && date.isBefore(endDate)) ||
        date.isSame(startDate) ||
        date.isSame(endDate)
      );
    }) || null
  );
};

/**
 * 学期周选择器逻辑Hook
 * 管理学期和周信息，提供周切换功能
 */
const useSemesterWeekSelector = ({
  editMode = false,
  grade,
  onChange,
  initialDate: initialDateProp,
}: UseSemesterWeekSelectorProps) => {
  const { schoolInfo } = useModel('schoolModel');
  const defaultInitialDate = dayjs().format('YYYY-MM-DD');
  const initialDate = initialDateProp || defaultInitialDate;

  // 当前选中日期
  const [currentDate, setCurrentDate] = useState<string>(initialDate);
  const [currentSemester, setCurrentSemester] = useState<Semester | null>(null);
  const [hasSemesterData, setHasSemesterData] = useState<boolean>(true);

  // 使用 semester/get 接口获取学期数据
  const {
    data: semesterData,
    error: semesterError,
    isLoading,
  } = useSWR<SemesterData>(
    schoolInfo?.schoolId
      ? `/api/v1/semester/get?school_id=${schoolInfo.schoolId}&school_year_id=${schoolInfo?.schoolYear?.school_year_id || 1}`
      : null,
    fetcher,
  );

  useEffect(() => {
    if (semesterError) {
      message.error('获取学期数据失败');
    }
  }, [semesterError]);

  // 根据当前日期和年级，从获取的学期数据中找到对应的学期信息
  useEffect(() => {
    if (!semesterData) return;

    const currentDay = dayjs(currentDate);

    // 如果有指定年级，先查找该年级的学期设置
    if (grade) {
      const gradeSettings = semesterData.grade_settings.find((g) => g.grade_id === Number(grade));

      if (gradeSettings) {
        // 检查是否有有效的学期数据
        if (gradeSettings.semesters && gradeSettings.semesters.length > 0) {
          const hasValidSemester = gradeSettings.semesters.some((s) => s.start_date && s.end_date);

          if (!hasValidSemester) {
            setHasSemesterData(false);
            setCurrentSemester(null);
            return;
          }

          setHasSemesterData(true);
          setCurrentSemester(findSemesterByDate(gradeSettings.semesters, currentDay));
          return;
        } else {
          setHasSemesterData(false);
          setCurrentSemester(null);
          return;
        }
      }
    }

    // 如果没有指定年级或找不到指定年级的设置，尝试使用第一个年级的设置
    if (semesterData.grade_settings && semesterData.grade_settings.length > 0) {
      const firstGradeSetting = semesterData.grade_settings[0];

      // 检查是否有有效的学期数据
      if (firstGradeSetting.semesters && firstGradeSetting.semesters.length > 0) {
        const hasValidSemester = firstGradeSetting.semesters.some(
          (s) => s.start_date && s.end_date,
        );

        if (!hasValidSemester) {
          setHasSemesterData(false);
          setCurrentSemester(null);
          return;
        }

        setHasSemesterData(true);
        setCurrentSemester(findSemesterByDate(firstGradeSetting.semesters, currentDay));
        return;
      } else {
        setHasSemesterData(false);
        setCurrentSemester(null);
        return;
      }
    }

    // 如果没有找到任何学期设置，标记为没有学期数据
    setHasSemesterData(false);
    setCurrentSemester(null);
  }, [currentDate, grade, semesterData]);

  // 根据当前日期计算周信息
  const weekInfo = useMemo(() => {
    if (!currentSemester) return null;

    const currentDay = dayjs(currentDate);
    const semesterStart = dayjs(currentSemester.start_date);

    // 确保学期开始日期是周一
    const semesterStartDay = semesterStart.day();
    const adjustedSemesterStart =
      semesterStartDay === 1
        ? semesterStart
        : semesterStart.subtract(semesterStartDay === 0 ? 6 : semesterStartDay - 1, 'day');

    // 计算当前日期是学期开始后的第几周
    const diffDays = currentDay.diff(adjustedSemesterStart, 'day');
    const diffWeeks = Math.floor(diffDays / 7);
    const weekNumber = diffWeeks + 1; // 第一周从1开始

    // 计算本周的开始(周一)和结束日期(周日)
    const weekStart = adjustedSemesterStart.add(diffWeeks * 7, 'day');
    const weekEnd = weekStart.add(6, 'day');

    const weekStartStr = weekStart.format('YYYY.M.D');
    const weekEndStr = weekEnd.format('M.D');

    return {
      weekNumber,
      startDate: weekStart.format('YYYY-MM-DD'),
      endDate: weekEnd.format('YYYY-MM-DD'),
      fullDateRange: `${weekStartStr}～${weekEndStr}`,
    };
  }, [currentDate, currentSemester]);

  // 当周信息变化时触发回调
  useEffect(() => {
    if (weekInfo && onChange) {
      onChange(weekInfo, currentSemester);
    }
  }, [weekInfo, currentSemester, onChange]);

  // 切换到上一周
  const handlePrevWeek = () => {
    const prevWeekDate = dayjs(currentDate).subtract(7, 'day').format('YYYY-MM-DD');
    setCurrentDate(prevWeekDate);
  };

  // 切换到下一周
  const handleNextWeek = () => {
    const nextWeekDate = dayjs(currentDate).add(7, 'day').format('YYYY-MM-DD');
    setCurrentDate(nextWeekDate);
  };

  // 选择周
  const handleWeekSelect = (date: any) => {
    if (date) {
      // 获取所选日期的星期几（0是周日，1是周一，...）
      const dayOfWeek = date.day();
      // 调整到周一
      const weekStart =
        dayOfWeek === 1 ? date : date.subtract(dayOfWeek === 0 ? 6 : dayOfWeek - 1, 'day');

      setCurrentDate(weekStart.format('YYYY-MM-DD'));
    }
  };
  const setStartDate = (date: string) => {
    setCurrentDate(date);
  };

  const SemesterWeekSelector = useMemo(() => {
    // 获取周选择器的显示文本
    const getWeekPickerText = () => {
      return '';
    };

    if (semesterError) {
      return <div className="text-red-500">获取学期信息失败</div>;
    }

    if (!hasSemesterData && semesterData) {
      return <Empty description="请先设置学期数据" className="mt-20" />;
    }

    // if (!currentSemester && semesterData && hasSemesterData) {
    //   return <Empty description="当前日期不在任何学期内" className="mt-4" />;
    // }

    return (
      <Spin spinning={isLoading} indicator={<LoadingOutlined spin />}>
        <div className="flex items-center text-gray-500">
          <div className="text-xs mr-1">
            {currentSemester?.semester_name || '暂无学期'}
            {weekInfo ? ` 第 ${weekInfo.weekNumber} 周（${weekInfo.fullDateRange}）` : ''}
          </div>
          <div className="flex items-center space-x-1">
            <Tooltip title="上一周">
              <Button
                disabled={editMode}
                icon={<LeftOutlined />}
                onClick={handlePrevWeek}
                className="flex items-center justify-center"
                type="text"
                size="small"
              />
            </Tooltip>
            <Button
              disabled={editMode}
              className="flex items-center justify-center !pr-2 !pl-1"
              type="default"
              size="small"
            >
              <DatePicker
                disabled={editMode}
                placeholder=""
                picker="week"
                value={dayjs(currentDate)}
                onChange={handleWeekSelect}
                format={getWeekPickerText}
                allowClear={false}
                suffixIcon={<CalendarOutlined />}
                bordered={false}
                style={{ width: '20px', margin: '0 2px 0 0', padding: '0', cursor: 'pointer' }}
                popupClassName="week-picker-dropdown"
                size="small"
              />
            </Button>
            <Tooltip title="下一周">
              <Button
                disabled={editMode}
                icon={<RightOutlined />}
                onClick={handleNextWeek}
                className="flex items-center justify-center"
                type="text"
                size="small"
              />
            </Tooltip>
          </div>
        </div>
      </Spin>
    );
  }, [currentDate, currentSemester, weekInfo, handlePrevWeek, handleNextWeek, handleWeekSelect]);

  return {
    setStartDate,
    currentDate,
    setCurrentDate,
    currentSemester,
    weekInfo,
    hasSemesterData,
    semesterData,
    semesterError,
    isLoading,
    handlePrevWeek,
    handleNextWeek,
    handleWeekSelect,
    SemesterWeekSelector,
  };
};

export default useSemesterWeekSelector;
