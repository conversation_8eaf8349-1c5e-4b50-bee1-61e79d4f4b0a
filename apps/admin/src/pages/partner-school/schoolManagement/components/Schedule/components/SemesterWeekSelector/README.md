# 学期周选择器组件 (SemesterWeekSelector)

一个用于显示和选择学期周信息的组件，可在课表、课程安排等场景中使用。

## 功能特性

- 显示当前学期信息和周信息
- 提供多种周切换方式:
  - 上一周/下一周快捷按钮
  - 周选择器（点击中间的周数可打开日历选择特定周）
  - 返回当前周按钮（非当前周显示）
- 通过回调函数通知周信息变更
- 使用 SWR 自动管理数据请求和缓存
- 支持Hook方式使用，更灵活地集成到各种场景

## React Hook使用方式

```tsx
import { useSemesterWeekSelector } from '@/components/SemesterWeekSelector';

const MyComponent = () => {
  const {
    currentDate,
    currentSemester,
    weekInfo,
    hasSemesterData,
    semesterData,
    semesterError,
    isLoading,
    handlePrevWeek,
    handleNextWeek,
    handleWeekSelect
  } = useSemesterWeekSelector({
    grade: 1,
    onChange: (weekInfo, semesterInfo) => {
      console.log('周信息变更:', weekInfo);
      console.log('学期信息:', semesterInfo);
    }
  });

  // 在自定义组件中使用数据和函数
  return (
    <div>
      {isLoading ? (
        <p>加载中...</p>
      ) : weekInfo ? (
        <div>
          <h2>{currentSemester?.semester_name} 第 {weekInfo.weekNumber} 周</h2>
          <p>日期范围: {weekInfo.fullDateRange}</p>
          <button onClick={handlePrevWeek}>上一周</button>
          <button onClick={handleNextWeek}>下一周</button>
        </div>
      ) : (
        <p>没有可用的周信息</p>
      )}
    </div>
  );
};
```

## 组件使用方式

```tsx
import React from 'react';
import SemesterWeekSelector from '@/components/SemesterWeekSelector';

const ScheduleComponent = () => {
  const handleWeekChange = (weekInfo, semesterInfo) => {
    console.log('周信息变更:', weekInfo);
    console.log('学期信息:', semesterInfo);
    // 根据周信息加载相关数据...
  };

  return (
    <div>
      <SemesterWeekSelector 
        grade={1}
        onChange={handleWeekChange}
        initialDate="2024-04-01"
      />
      {/* 其他内容... */}
    </div>
  );
};
```

## 接口定义

### 组件属性 (Props)

```typescript
interface SemesterWeekSelectorProps {
  grade?: number | string;         // 年级（可选）
  onChange?: (weekInfo: WeekInfo, semesterInfo: SemesterInfo | null) => void;  // 周信息变更回调
  initialDate?: string;            // 初始日期，默认为当前日期
  editMode?: boolean;              // 是否为编辑模式（禁用选择器）
}
```

### Hook参数

```typescript
interface UseSemesterWeekSelectorProps {
  grade?: number | string;         // 年级（可选）
  onChange?: (weekInfo: WeekInfo, semesterInfo: SemesterInfo | null) => void;  // 周信息变更回调
  initialDate?: string;            // 初始日期，默认为当前日期
}
```

### 学期信息 (SemesterInfo)

```typescript
interface SemesterInfo {
  semester_id: number;        // 学期ID
  semester_type_id: number;   // 学期类型ID
  semester_name: string;      // 学期名称（如"第一学期"）
  start_date: string;         // 学期开始日期
  end_date: string;           // 学期结束日期
}
```

### 周信息 (WeekInfo)

```typescript
interface WeekInfo {
  weekNumber: number;     // 周序号
  startDate: string;      // 周开始日期（周一）
  endDate: string;        // 周结束日期（周日）
  fullDateRange: string;  // 格式化后的日期范围（如"2024.2.24～3.2"）
}
```

### Hook返回值

```typescript
{
  currentDate: string;                      // 当前选择的日期
  setCurrentDate: (date: string) => void;   // 设置当前日期
  currentSemester: SemesterInfo | null;     // 当前学期信息
  weekInfo: WeekInfo | null;                // 当前周信息
  hasSemesterData: boolean;                 // 是否有学期数据
  semesterData: SemesterData | undefined;   // 获取的学期数据
  semesterError: Error | undefined;         // 获取学期数据错误
  isLoading: boolean;                       // 是否正在加载数据
  handlePrevWeek: () => void;               // 切换到上一周
  handleNextWeek: () => void;               // 切换到下一周
  handleWeekSelect: (date: any) => void;    // 选择指定周
}
```

## 使用示例

```tsx
import React from 'react';
import SemesterWeekSelector from '../components/SemesterWeekSelector';

const ScheduleManagement = () => {
  const handleWeekChange = (weekInfo, semesterInfo) => {
    console.log('当前选中周:', weekInfo);
    console.log('当前学期信息:', semesterInfo);
    
    // 用选中周的开始和结束日期加载课表数据
    loadScheduleData(weekInfo.startDate, weekInfo.endDate);
  };

  return (
    <div>
      <SemesterWeekSelector
        grade={11}
        onChange={handleWeekChange}
      />
      
      {/* 课表内容 */}
    </div>
  );
};
```

## 周选择器交互说明

1. **文本显示**: 组件左侧显示当前学期名称、周次和日期范围
2. **周选择方式**:
   - 点击左右箭头按钮切换到上一周/下一周
   - 点击中间的"第X周"文本打开周选择日历，可选择特定周
   - 选择后，组件会将当前日期设置为所选周的第一天（周一）
3. **返回当前周**: 当不在当前周时，会显示"当前"按钮，点击可快速返回当前周

## 依赖说明

- 使用 `dayjs` 进行日期处理
- 使用 `useSWR` 获取和缓存学期数据
- 使用 `useModel('schoolModel')` 获取学校信息

## API 依赖

组件依赖以下后端 API:

```
GET /api/v1/semester/getByDate
参数：
  - school_id: 学校ID
  - school_year_id: 学年ID
  - grade: 年级
  - date: 日期（YYYY-MM-DD格式）
```

## 注意事项

1. 组件会根据当前日期自动计算所在周次
2. 使用 dayjs 处理日期计算，确保兼容性
3. 返回的 `weekInfo` 包含了完整的周信息，可直接用于后续组件的数据请求 