.container {
  display: flex;
  height: 100%;
  background-color: #fff;
  border-radius: 4px;
  overflow: hidden;
}

.gradeTreeContainer {
  width: 320px;
  border-right: 1px solid #f0f0f0;
  padding: 16px 0;
  overflow-y: auto;
  background-color: #fff;
}

.academicYear {
  font-size: 16px;
  font-weight: 500;
  padding: 0 16px 16px;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 16px;
}

.gradeTree {
  padding: 0 8px;

  :global {
    .ant-tree-treenode {
      padding: 4px 8px !important;
      border-radius: 4px;

      &:hover {
        background-color: #f5f5f5;
      }

      &.ant-tree-treenode-selected {
        background-color: #e6f7ff;
      }
    }

    .ant-tree-node-content-wrapper {
      flex: 1;
    }
  }
}

.loadingContainer {
  display: flex;
  justify-content: center;
  padding: 40px 0;
  width: 100%;
  height: 300px;
}

.scheduleContentContainer {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.scheduleContainer {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.title {
  font-size: 18px;
  font-weight: 500;
}

.scheduleTable {
  flex: 1;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  overflow: auto;
  position: relative;
}

.emptyContainer {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  padding: 40px 0;
}

.emptyIcon {
  font-size: 48px;
  color: #d9d9d9;
  margin-bottom: 16px;
}

.emptyText {
  color: #999;
  margin-bottom: 24px;
}

// 课表表格样式
.scheduleTableWrapper {
  padding: 16px;
}

.scheduleHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.semesterInfo {
  font-size: 14px;
  font-weight: 500;
}

.scheduleActions {
  display: flex;
  gap: 8px;
}

.scheduleGrid {
  width: 100%;
  border-collapse: collapse;

  th, td {
    border: 1px solid #f0f0f0;
    padding: 8px;
    text-align: center;
    min-width: 120px;
    height: 60px;
    vertical-align: middle;
  }

  th {
    background-color: #fafafa;
    font-weight: 500;
  }

  .timeColumn {
    width: 100px;
    background-color: #fafafa;
  }

  .periodCell {
    display: flex;
    flex-direction: column;

    .periodTime {
      font-size: 12px;
      color: #999;
      margin-top: 4px;
    }
  }

  .courseCell {
    background-color: #e6f7ff;
    border-radius: 2px;
    padding: 4px;

    .courseName {
      font-weight: 500;
      margin-bottom: 2px;
    }

    .teacherName {
      font-size: 12px;
      color: #666;
    }
  }
}
