import { useState, useEffect, useMemo } from 'react';
import { message } from 'antd';
import useSWR from 'swr';
import { useModel } from '@umijs/max';
import { GradeInfo } from '@/services/schedule-management/type';
import fetcher, { post } from '@/services/fetcher';
import useSWRMutation from 'swr/mutation';
import { TeacherInfo, TeacherListParams } from '../type';
import { ActionType } from '@ant-design/pro-components';
import { DictTypeEnum } from '@/services/dict';
// 职务类型枚举
export enum JobType {
  PRINCIPAL = 1, // 校长
  GRADE_DIRECTOR = 2, // 年级主任
  SUBJECT_LEADER = 3, // 学科组长
  SUBJECT_TEACHER = 4, // 学科教师
  CLASS_TEACHER = 5, // 班主任
}

export const jobTypeOptions = [
  { label: '校长', value: JobType.PRINCIPAL },
  { label: '年级主任', value: JobType.GRADE_DIRECTOR },
  { label: '学科组长', value: JobType.SUBJECT_LEADER },
  { label: '学科教师', value: JobType.SUBJECT_TEACHER },
  { label: '班主任', value: JobType.CLASS_TEACHER },
]

// TreeSelect 数据节点类型
export interface TreeNode {
  title: string;
  value: string;
  key: string;
  isLeaf?: boolean;
  selectable?: boolean;
  children?: TreeNode[];
  gradeId?: number;
  classId?: number;
  disabled?: boolean;
  checkable?: boolean;
}
// 配置 TreeNode 的 checkable 属性
const buildTreeData = (data: GradeInfo[]): TreeNode[] => {
  return data.map(grade => {
    // 检查是否有实际班级
    // let hasRealClasses = false;
    // if (grade.children && grade.children.length > 0) {
    //   for (const group of grade.children) {
    //     if (group.children && group.children.length > 0) {
    //       hasRealClasses = true;
    //       break;
    //     }
    //   }
    // }

    const gradeNode: TreeNode = {
      title: grade.grade_name,
      value: `grade-${grade.grade_id}`,
      key: `grade-${grade.grade_id}`,
      gradeId: grade.grade_id,
      children: [],
      isLeaf: false,
      disabled: false, // 如果没有实际班级，则禁用年级节点
      checkable: false, // 没有实际班级的节点不显示 checkbox
    };

    // 处理班级分组和班级
    if (grade.children && grade.children.length > 0) {
      grade.children.forEach(group => {
        // 创建班级分组节点
        const groupNode: TreeNode = {
          title: group.group_name,
          value: `group-${grade.grade_id}-${group.is_test}`,
          key: `group-${grade.grade_id}-${group.is_test}`,
          selectable: true,
          children: [],
          checkable: false,
        };

        // 添加班级节点
        if (group.children && group.children.length > 0) {
          groupNode.children = group.children.map(cls => {
            // 构建包含年级、班号和类型的班级标题
            const classType = group.is_test === 1 ? '（真实）' : '（测试）';
            const fullTitle = `${grade.grade_name} ${cls.class_name}${classType}`;

            return {
              title: fullTitle, // 完整班级名称，包含年级、班级名和类型
              value: `class-${grade.grade_id}-${cls.class_id}`,
              key: `class-${grade.grade_id}-${cls.class_id}`,
              classId: cls.class_id,
              gradeId: grade.grade_id,
              isLeaf: true,
              classType: group.is_test === 1 ? '真实' : '测试',
            };
          });
        }

        // 只有当分组有班级时才添加
        gradeNode.children?.push(groupNode);
      });
    }

    return gradeNode;
  });
};

export function useTeacherManagement(actionRef?: React.MutableRefObject<ActionType | undefined>) {
  const { dictCache, } = useModel('dictModels');
  const { schoolInfo } = useModel('schoolModel');
  const getGradeTreeUrl = useMemo(() => schoolInfo ? `/api/v1/class/tree?class_school_id=${schoolInfo?.schoolId}&class_school_year_id=${schoolInfo?.schoolYear?.school_year_id}` : '', [schoolInfo]);
  const { data: gradeTreeData, isLoading: loading } = useSWR(getGradeTreeUrl, fetcher<{
    total: number;
    list: GradeInfo[];
  }>);

  const [queryParams, setQueryParams] = useState<TeacherListParams>({
    schoolID: schoolInfo?.schoolId || 0,
    userName: '',
    userPhone: '',
    jobSubject: 0,
    teacherEmploymentStatus: 0,
    jobGrade: 0,
    jobClass: 0,
  });
  const { data: teacherListData, trigger: mutateTeacherList } = useSWRMutation('/api/v1/userSchool/listTeachers', post<TeacherInfo[], TeacherListParams>);
  const { trigger: createTeacher } = useSWRMutation('/api/v1/userSchool/createTeacher', post);
  const { trigger: updateTeacher } = useSWRMutation('/api/v1/userSchool/updateTeacher', post);
  // 基础数据

  const getSubjectOptions = () => dictCache[DictTypeEnum.SUBJECT].map(subject => ({ label: subject.meta_dict_name, value: Number(subject.meta_dict_key) }));
  const [treeData, setTreeData] = useState<TreeNode[]>([]);
  const [gradeOnlyTreeData, setGradeOnlyTreeData] = useState<TreeNode[]>([]);




  const isNeedSubject = (jobType: number) => jobType === JobType.SUBJECT_LEADER || jobType === JobType.SUBJECT_TEACHER;
  const isNeedClasses = (jobType: number) => jobType === JobType.SUBJECT_TEACHER || jobType === JobType.CLASS_TEACHER;
  const isNeedGradesOnly = (jobType: number) => jobType === JobType.GRADE_DIRECTOR || jobType === JobType.PRINCIPAL || jobType === JobType.SUBJECT_LEADER;
  // 获取年级树数据（不包含班级）
  const getGradeOnlyTreeData = (): TreeNode[] => {
    if (!gradeTreeData?.list) return [];

    return gradeTreeData.list.map(grade => {
      // 检查是否有实际班级
      // let hasRealClasses = false;
      // if (grade.children && grade.children.length > 0) {
      //   for (const group of grade.children) {
      //     if (group.children && group.children.length > 0) {
      //       hasRealClasses = true;
      //       break;
      //     }
      //   }
      // }

      return {
        title: grade.grade_name,
        value: `grade-${grade.grade_id}`,
        key: `grade-${grade.grade_id}`,
        gradeId: grade.grade_id,
        isLeaf: true,
        // disabled: !hasRealClasses, // 如果没有实际班级，则禁用年级节点
        // checkable: hasRealClasses, // 没有实际班级的节点不显示 checkbox
      };
    });
  };


  useEffect(() => {
    setTreeData(buildTreeData(gradeTreeData?.list || []));
    setGradeOnlyTreeData(getGradeOnlyTreeData());
  }, [gradeTreeData]);





  // 渲染选中的年级和班级
  const renderSelectedGradesAndClasses = (selectedOptions: any[], needClasses: boolean) => {
    if (!selectedOptions || selectedOptions.length === 0) return null;

    if (!needClasses) {
      // 只有年级时的显示
      const gradeLabels = selectedOptions.map(option => option[0].label).join('、');
      return {
        type: 'grade-only',
        content: gradeLabels
      };
    } else {
      // 有年级和班级时的显示
      const gradeMap = new Map<string, string[]>();

      selectedOptions.forEach(option => {
        const gradeName = option[0].label;
        const className = option[1]?.label;

        if (className) {
          if (!gradeMap.has(gradeName)) {
            gradeMap.set(gradeName, []);
          }
          gradeMap.get(gradeName)?.push(className);
        }
      });

      const formattedContent = Array.from(gradeMap.entries()).map(([gradeName, classNames]) => {
        return `${gradeName}：${classNames.join('、')}`;
      });

      return {
        type: 'grade-class',
        content: formattedContent
      };
    }
  };

  // 处理在职状态变更
  const handleEmploymentStatusChange = async (record: TeacherInfo, newStatus: number) => {
    try {
      const result = await updateTeacher({
        ...record,
        schoolID: schoolInfo?.schoolId,
        userID: record.userID,
        teacherEmploymentStatus: newStatus
      });
      if (result) {
        message.success(`教师${newStatus === 1 ? '复职' : '离职'}操作成功`);
      }
    } catch (error) {
      message.error(`教师${newStatus === 1 ? '复职' : '离职'}操作失败`);
    }
    actionRef?.current?.reload();
  };

  return {
    queryParams,
    setQueryParams,
    teacherListData,
    treeData,
    gradeOnlyTreeData,
    getSubjectOptions,
    createTeacher,
    updateTeacher,
    getGradeOnlyTreeData,
    loading,
    renderSelectedGradesAndClasses,
    handleEmploymentStatusChange,
    isNeedSubject,
    isNeedClasses,
    isNeedGradesOnly,
    mutateTeacherList
  };
}
