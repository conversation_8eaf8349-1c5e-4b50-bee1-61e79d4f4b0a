import { useModel } from '@umijs/max';
import { TeacherInfo } from '../type';
import { JobType, useTeacherManagement } from './useTeacherManagement';

// 自定义教师职务接口 - 适用于表单
export interface FormTeacherJobInfo {
  jobType: number;
  jobInfos?: string[] | null;
  jobSubject?: number | string;
}

// 教师信息 - 适用于表单
export interface FormTeacherInfo extends Omit<TeacherInfo, 'teacherJobInfos' | 'userPhone' | 'userIsTest'> {
  name?: string;
  userPhone?: string;
  userIsTest?: number;
  teacherJobInfos?: FormTeacherJobInfo[];
}

// 提交到后端的数据格式
export interface TeacherSubmitData {
  schoolID: number;
  userPhone: string;
  userStatus?: number;
  userIsTest: number;
  userName: string;
  teacherJobInfos: Array<{
    jobType: number;
    jobInfos: Array<{
      jobGrade: number;
      jobClass?: number[];
    }>;
    jobSubject?: number;
    _originalIndex?: number; // 用于排序，保持原有顺序
  }>;
}

/**
 * 教师数据转换 Hook
 * 提供教师数据格式化和转换功能
 */
export function useTeacherDataTransform() {
  const { schoolInfo } = useModel('schoolModel');
  const { isNeedSubject, isNeedClasses, isNeedGradesOnly } = useTeacherManagement();

  /**
   * 格式化教师数据，用于表单编辑
   * @param teacherData 原始教师数据
   * @returns 格式化后的表单教师数据
   */
  const formatTeacherDataForForm = (teacherData: TeacherInfo): FormTeacherInfo | undefined => {
    if (!teacherData) return;

    const formattedData: FormTeacherInfo = {
      ...teacherData,
      teacherJobInfos: []
    };

    if (teacherData.teacherJobInfos && teacherData.teacherJobInfos.length > 0) {
      const formattedJobs: FormTeacherJobInfo[] = teacherData.teacherJobInfos.map(jobInfo => {
        const { jobType, jobInfos, jobSubject } = jobInfo;

        // 基础职务信息
        const formattedJob: FormTeacherJobInfo = {
          jobType,
          jobSubject: jobSubject ? Number(jobSubject) : undefined
        };

        // 处理年级和班级信息
        if (jobInfos && jobInfos.length > 0) {
          // 根据职务类型处理数据
          if (jobType === JobType.GRADE_DIRECTOR || jobType === JobType.SUBJECT_LEADER) {
            // 年级主任和学科组长只需要年级，不需要班级
            const gradeKeys: string[] = [];
            jobInfos.forEach(info => {
              if (info.jobGrade) {
                gradeKeys.push(`grade-${info.jobGrade}`);
              }
            });
            formattedJob.jobInfos = gradeKeys;
          } else if (jobType === JobType.SUBJECT_TEACHER || jobType === JobType.CLASS_TEACHER) {
            // 学科教师和班主任需要年级和班级
            const selectedKeys: string[] = [];

            jobInfos.forEach(info => {
              const { jobGrade, jobClass } = info;

              // 如果没有选择具体班级，只添加年级
              if (!jobClass || jobClass.length === 0) {
                selectedKeys.push(`grade-${jobGrade}`);
              } else {
                // 如果选择了具体班级，添加班级节点
                jobClass.forEach(classId => {
                  selectedKeys.push(`class-${jobGrade}-${classId}`);
                });
              }
            });

            formattedJob.jobInfos = selectedKeys;
          }
        }

        return formattedJob;
      });

      formattedData.teacherJobInfos = formattedJobs;
    }

    return formattedData;
  };

  /**
   * 将表单数据转换为提交格式，并进行合并和排序
   * @param values 表单提交的原始数据
   * @returns 转换后的提交数据和是否需要合并的标志
   */
  const transFormSubmitData = (values: any): { data: TeacherSubmitData; needMerge: boolean } => {
    console.log(`transFormSubmitData values`, values);

    // 构建提交数据
    const submitData: TeacherSubmitData = {
      ...values,
      schoolID: schoolInfo!.schoolId || 0,
      teacherJobInfos: []
    };

    // 用于合并的 Map，key 为主键
    // 对于学科组长和学科教师，使用 jobType+jobSubject 作为主键，允许多个
    // 对于其他职务类型，只使用 jobType 作为键
    const mergedJobInfosMap = new Map<string, any>();
    let needMerge = false; // 是否需要合并的标志
    
    // 职务类型排序的辅助数组，保存每种职务类型的首次出现顺序
    const jobTypeOrder: number[] = [];
    
    // 记录每个职务原始的索引位置，用于后续排序保持原始顺序
    const originalIndexMap = new Map<string, number>();

    // 1. 遍历表单数据，进行初步转换和合并
    values.teacherJobInfos?.forEach((item: any, index: number) => {
      if (!item.jobType) return;

      const jobType = Number(item.jobType);
      let mapKey: string;
      
      // 记录职务类型首次出现的顺序
      if (!jobTypeOrder.includes(jobType)) {
        jobTypeOrder.push(jobType);
      }
      
      // 对于学科组长和学科教师，根据不同学科，可以有多个职务
      if (jobType === JobType.SUBJECT_LEADER || jobType === JobType.SUBJECT_TEACHER) {
        // 使用 jobType+jobSubject 作为键，允许多个相同类型的职务
        mapKey = `${jobType}-${item.jobSubject || 'default'}-${index}`;
        // 记录原始索引
        originalIndexMap.set(mapKey, index);
      } else {
        // 其他职务类型仍然只使用 jobType 作为键
        mapKey = `${jobType}`;
        // 如果已经存在，不更新索引，保留第一次出现的位置
        if (!originalIndexMap.has(mapKey)) {
          originalIndexMap.set(mapKey, index);
        }
      }

      let teacherJobInfo: any = mergedJobInfosMap.get(mapKey);

      if (!teacherJobInfo) {
        teacherJobInfo = {
          jobType,
          jobInfos: [],
          _originalIndex: index // 记录原始索引位置
        };
        mergedJobInfosMap.set(mapKey, teacherJobInfo);
      } else if (jobType !== JobType.SUBJECT_LEADER && jobType !== JobType.SUBJECT_TEACHER) {
        // 只有非学科组长和非学科教师的职务需要标记为需要合并
        needMerge = true;
      }

      // 添加学科信息（如果有）
      if (item.jobSubject && (jobType === JobType.SUBJECT_LEADER || jobType === JobType.SUBJECT_TEACHER)) {
        teacherJobInfo.jobSubject = Number(item.jobSubject);
      }

      if (isNeedClasses(jobType)) {
        // 处理需要班级的情况
        const gradeClassMap = new Map<number, number[]>();

        item.jobInfos?.forEach?.((jobInfo: string | { value: string }) => {
          let parts;
          if (typeof jobInfo === 'string') {
            parts = jobInfo.split('-');
          } else {
            parts = jobInfo.value.split('-');
          }
          if (!parts?.length || parts.length < 2) return;

          const gradeId = Number(parts[1]);
          const classId = parts.length > 2 ? Number(parts[2]) : 0;

          if (!gradeClassMap.has(gradeId)) {
            gradeClassMap.set(gradeId, []);
          }

          if (classId && parts[0] === 'class') {
            gradeClassMap.get(gradeId)?.push(classId);
          }
        });

        // 转换为需要的格式 { "jobGrade" : 3, "jobClass" : [1,2,3] }
        const jobInfos = Array.from(gradeClassMap.entries()).map(([gradeId, classIds]) => {
          return {
            jobGrade: gradeId,
            jobClass: classIds.length > 0 ? classIds : []
          };
        });

        // 合并 jobInfos
        teacherJobInfo.jobInfos.push(...jobInfos);
      } else if (isNeedGradesOnly(jobType)) {
        // 处理只需要年级的情况
        const gradeSet = new Set<number>();

        item.jobInfos?.forEach((jobGradeInfo: { value: string } | string) => {
          console.log(` jobGradeInfo`, jobGradeInfo);
          let parts;
          if (typeof jobGradeInfo === 'string') {
            parts = jobGradeInfo.split('-');
          } else {
            parts = jobGradeInfo.value.split('-');
          }
          if (parts.length < 2 || parts[0] !== 'grade') return;

          const gradeId = Number(parts[1]);
          gradeSet.add(gradeId);
        });

        const jobInfos = Array.from(gradeSet).map(gradeId => ({
          jobGrade: gradeId
        }));
        // 合并 jobInfos
        teacherJobInfo.jobInfos.push(...jobInfos);
      }
    });

    // 2. 将 Map 转换为数组，并进行排序
    const jobInfoArray = Array.from(mergedJobInfosMap.values());
    
    // 先按照原始的顺序排序
    jobInfoArray.sort((a, b) => {
      return a._originalIndex - b._originalIndex;
    });
    
    // 处理每个职务的年级和班级信息
    submitData.teacherJobInfos = jobInfoArray.map(teacherJobInfo => {
      // 对每个 teacherJobInfo 的 jobInfos 进行合并和排序
      const mergedGrades = new Map<number, number[] | undefined>();

      teacherJobInfo.jobInfos.forEach((jobInfo: { jobGrade: number; jobClass?: number[] }) => {
        const { jobGrade, jobClass } = jobInfo;
        if (!mergedGrades.has(jobGrade)) {
          mergedGrades.set(jobGrade, jobClass);
        } else if (jobClass) {
          const existingClasses = mergedGrades.get(jobGrade) || [];
          const newClasses = [...existingClasses, ...jobClass];
          // 去重班级
          const uniqueClasses = [...new Set(newClasses)];
          mergedGrades.set(jobGrade, uniqueClasses);
          // 对于学科组长和学科教师以外的职务，标记为需要合并
          if (teacherJobInfo.jobType !== JobType.SUBJECT_LEADER && teacherJobInfo.jobType !== JobType.SUBJECT_TEACHER) {
            needMerge = true;
          }
        }
      });

      // 转换为数组并排序
      teacherJobInfo.jobInfos = Array.from(mergedGrades.entries())
        .map(([jobGrade, jobClass]) => ({
          jobGrade,
          jobClass: jobClass ? [...jobClass].sort((a, b) => a - b) : undefined // 班级排序
        }))
        .sort((a, b) => a.jobGrade - b.jobGrade); // 年级排序

      // 删除辅助的原始索引字段，不需要提交到后端
      delete teacherJobInfo._originalIndex;
      
      return teacherJobInfo;
    });

    return { data: submitData, needMerge };
  };

  return {
    formatTeacherDataForForm,
    transFormSubmitData,
    isNeedSubject,
    isNeedClasses,
    isNeedGradesOnly
  };
}
