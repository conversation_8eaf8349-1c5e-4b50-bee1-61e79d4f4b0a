import React, { useState, useEffect, useMemo } from 'react';
import { ProTable } from '@ant-design/pro-components';
import type { ActionType, ProColumns, ProFormInstance } from '@ant-design/pro-components';
import { Button, message, Popconfirm } from 'antd';

import { TeacherInfo, TeacherListParams } from './type';
import TeacherForm from './TeacherForm';
import { Typography } from 'antd';
import { useTeacherManagement, jobTypeOptions } from './hooks/useTeacherManagement';
import { useTeacherDataTransform, FormTeacherInfo } from './hooks/useTeacherDataTransform';
import { request, useModel, useSearchParams } from '@umijs/max';
import './index.less';
import { useRenderJonInfos } from './hooks/useRenderJonInfos';
import { UploadOutlined } from '@ant-design/icons';
import UploadModal from '@/components/UploadModal';
import { downloadFile, post } from '@/services/fetcher';
import useSWRMutation from 'swr/mutation';

const { Title } = Typography;

// 渲染操作指引
const renderOperationGuide = () => {
  return (
    <div className="bg-blue-50 py-2 px-4 rounded-lg mb-3 border border-blue-200">
      <Title level={5} className="text-blue-700 mb-2">操作指引</Title>
      <ol className="list-decimal pl-5 text-blue-700">
        <li>请先点击年级，完成班级创建</li>
        <li>请批量上传教师信息，完成账号创建</li>
      </ol>
    </div>
  );
};

const TeacherManagement: React.FC = () => {
  const [searchParams] = useSearchParams();
  const schoolIdParam = searchParams.get('id');
  const { schoolInfo } = useModel('schoolModel');
  const schoolId = schoolIdParam ? Number(schoolIdParam) : (schoolInfo?.schoolId || 0);
  const actionRef = React.useRef<ActionType>(undefined);
  const formRef = React.useRef<ProFormInstance>(undefined);
  const [modalVisible, setModalVisible] = useState(false);
  const [modalTitle, setModalTitle] = useState('添加教师');
  const [currentTeacher, setCurrentTeacher] = useState<FormTeacherInfo | undefined>();
  const [selectedGrade, setSelectedGrade] = useState<number | undefined>(undefined);
  const { formatTeacherDataForForm } = useTeacherDataTransform();
  const [isUploadModalVisible, setIsUploadModalVisible] = useState(false);
  const { renderJobInfos, getClassOptions } = useRenderJonInfos();
  const { trigger: batchCreateTeacher, error: batchCreateTeacherError } = useSWRMutation('/api/v1/userSchool/batchCreateTeacher', post<{
    allCount: number,
    successCount: number,
    failedCount: number,
    success: null,
    failed: null
  }, {
    objectKey: string,
    schoolId: number,
    schoolYear: number
  }>);


  // 使用自定义 hook 替代 Provider
  const {
    handleEmploymentStatusChange,
    mutateTeacherList,
    gradeOnlyTreeData,
    getSubjectOptions,
    setQueryParams
  } = useTeacherManagement(actionRef);

  useEffect(() => {
    if (batchCreateTeacherError) {
      message.error(batchCreateTeacherError.message);
    }
  }, [batchCreateTeacherError]);

  // 当年级选择变化时重置班级选择
  useEffect(() => {
    if (formRef.current) {
      formRef.current.setFieldValue('classId', undefined);
    }
  }, [selectedGrade]);



  // 处理添加教师
  const handleAdd = () => {
    setModalTitle('添加教师');
    setCurrentTeacher(undefined);
    setModalVisible(true);
  };

  // 处理编辑教师
  const handleEdit = (record: TeacherInfo) => {
    setModalTitle('编辑教师');

    // 格式化教师数据，使表单可用
    const formattedTeacher = formatTeacherDataForForm(record);
    console.log("formattedTeacher", formattedTeacher);

    setCurrentTeacher(formattedTeacher);
    setModalVisible(true);
  };

  // 处理表单提交
  const handleFinish = async () => {
    setModalVisible(false);
    actionRef.current?.reload();
  };

  const columns: ProColumns<TeacherInfo>[] = [
    {
      title: '姓名',
      dataIndex: 'userName',
      width: 60,
      fixed: 'left',
      search: {
        transform: (value) => ({ keyword: value }),
      },
    },
    {
      title: '教师ID',
      dataIndex: 'userID',
      width: 80,
      fixed: 'left',
      search: false,
    },
    {
      title: '手机号',
      dataIndex: 'userPhone',
      width: 120,
    },
    {
      title: '职务信息',
      dataIndex: 'teacherJobInfos',
      width: 240,
      ellipsis: true,
      search: false,
      render: renderJobInfos,
    },
    {
      title: '测试状态',
      dataIndex: 'userIsTest',
      width: 100,
      valueEnum: {
        1: { text: '正式', status: 'Success' },
        2: { text: '测试', status: 'Warning' },
      },
    },
    {
      title: '在职状态',
      dataIndex: 'teacherEmploymentStatus',
      width: 100,
      valueEnum: {
        1: { text: '在职', status: 'Success' },
        2: { text: '离职', status: 'Error' },
      },
    },
    {
      title: '年级',
      hideInTable: true,
      dataIndex: 'jobGrade',
      valueType: 'select',
      fieldProps: {
        options: gradeOnlyTreeData?.map(grade => ({
          label: grade.title,
          value: grade.gradeId
        })),
        onChange: (value: number) => {
          setSelectedGrade(value);
        }
      },
    },
    {
      title: '班级',
      hideInTable: true,
      dataIndex: 'jobClass',
      valueType: 'select',
      fieldProps: {
        options: getClassOptions(selectedGrade),
        disabled: !selectedGrade,
      },
    },
    {
      title: '学科',
      hideInTable: true,
      dataIndex: 'jobSubject',
      valueType: 'select',
      fieldProps: {
        options: getSubjectOptions(),
      },
    },
    {
      title: '职务类型',
      hideInTable: true,
      dataIndex: 'jobType',
      valueType: 'select',
      fieldProps: {
        options: jobTypeOptions,
      },
    },
    {
      fixed: 'right',
      title: '操作',
      width: 100,
      valueType: 'option',
      render: (_: any, record: TeacherInfo) => {
        // 1 为在职 2 为离职
        const actionText = record.teacherEmploymentStatus === 1 ? '离职' : '复职';
        const newStatus = record.teacherEmploymentStatus === 1 ? 2 : 1;
        return [
          <Button key="edit" type="link" onClick={() => handleEdit(record)} size='small'>
            编辑
          </Button>,
          <Popconfirm
            key="status"
            title="确认操作"
            description={`确定要将该教师设置为${actionText}状态吗？`}
            onConfirm={() => handleEmploymentStatusChange(record, newStatus)}
            okText="确认"
            cancelText="取消"
          >
            <Button key="status" type="link" size='small'>
              {actionText}
            </Button>
          </Popconfirm>
        ]
      },
    },
  ];


  const UploadModalMemo = useMemo(() => {
    const downloadTemplate = async () => {
      try {
        const res = await request('/api/v1/userSchool/teacherTpl', {
          method: 'GET',
        });

        // 将 base64 转换为 Blob
        const byteCharacters = atob(res.data.file_content);
        const byteNumbers = new Array(byteCharacters.length);
        for (let i = 0; i < byteCharacters.length; i++) {
          byteNumbers[i] = byteCharacters.charCodeAt(i);
        }
        const byteArray = new Uint8Array(byteNumbers);
        const blob = new Blob([byteArray], { type: 'application/vnd.ms-excel' });
        downloadFile(blob, res.data.file_name);
      } catch (error) {
        message.error('下载模板失败');
      }
    };

    const importData = async (fileId: string) => {
      await batchCreateTeacher({
        objectKey: fileId,
        schoolId: schoolId,
        schoolYear: schoolInfo?.schoolYear.school_year_id || 0
      });
      if (batchCreateTeacherError) {
        return {
          success: false,
          error: batchCreateTeacherError.message
        }
      }

      return {
        success: true,
      }
    }
    return (
      <UploadModal
        importData={importData}
        title="批量导入教师"
        visible={isUploadModalVisible}
        onClose={() => setIsUploadModalVisible(false)}
        downloadTemplate={downloadTemplate}
      />
    )
  }, [isUploadModalVisible]);
  return (
    <>
      {renderOperationGuide()}
      <ProTable<TeacherInfo, TeacherListParams>
        scroll={{
          y: 'calc(100vh - 200px)',
          x: 'calc(100vw - 200px)',
        }}
        columns={columns}
        actionRef={actionRef}
        formRef={formRef}
        cardBordered
        request={async (params) => {
          console.log("params", params);

          const _params: TeacherListParams = {
            jobType: Number(params.jobType) || 0,
            schoolID: schoolId,
            userName: params.keyword || "",
            userPhone: params.userPhone || "",
            jobSubject: Number(params.jobSubject) || 0,
            teacherEmploymentStatus: Number(params.teacherEmploymentStatus) || 0,
            jobGrade: params.jobGrade || 0,
            jobClass: params.jobClass || 0,
            userIsTest: Number(params.userIsTest) || 0,
          }
          setQueryParams(_params);

          const list = await mutateTeacherList(_params)

          return {
            data: list || [],
            success: true,
            total: list?.length || 0,
          };
        }}
        rowKey="userID"
        search={{
          labelWidth: 'auto',
          defaultCollapsed: false,
          span: 6,
        }}
        pagination={{
          showSizeChanger: true,
          pageSizeOptions: [10, 20, 50, 100],
          showTotal: (total) => (
            <span>
              共 <a style={{ color: '#1890ff' }}>{total}</a> 条数据
            </span>
          ),
        }}
        dateFormatter="string"
        headerTitle="教师列表"
        toolBarRender={() => [
          <Button
            key="add"
            type="primary"
            onClick={handleAdd}
          >
            添加单个教师
          </Button>,
          <Button
            type="primary"

            key="batch"
            onClick={() => {
              setIsUploadModalVisible(true);
            }}
            icon={<UploadOutlined />}
          >
            批量添加教师
          </Button>,
        ]}
      />

      {
        modalVisible && <TeacherForm
          visible={modalVisible}
          title={modalTitle}
          initialValues={currentTeacher}
          onFinish={handleFinish}
          onCancel={() => setModalVisible(false)}
        />
      }
      {UploadModalMemo}
    </>
  );
};

export default TeacherManagement;
