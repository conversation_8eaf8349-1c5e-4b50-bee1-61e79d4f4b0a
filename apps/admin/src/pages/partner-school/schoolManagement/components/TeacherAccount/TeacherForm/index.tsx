import React, { useRef, useState } from 'react';
import { <PERSON>ton, Space, Drawer, FormInstance, TreeSelect, message, Modal, Tooltip } from 'antd';
import { PlusOutlined, ArrowUpOutlined, ArrowDownOutlined } from '@ant-design/icons';
import { JobType, jobTypeOptions, useTeacherManagement } from '../hooks/useTeacherManagement';
import { useTeacherDataTransform } from '../hooks/useTeacherDataTransform';
import { ProForm, ProFormText, ProFormRadio, ProFormSelect, ProFormList, ProFormDependency, ProFormItem } from '@ant-design/pro-components';
import type { FormListActionType } from '@ant-design/pro-components';
import { TeacherFormProps, TeacherInfo } from '../type';

// 添加CSS动画样式到head
if (typeof document !== 'undefined') {
  const style = document.createElement('style');
  style.innerHTML = `
    @keyframes flash-border {
      0% { outline: 2px solid transparent; }
      25% { outline: 2px solid #1890ff; }
      50% { outline: 2px solid transparent; }
      75% { outline: 2px solid #1890ff; }
      100% { outline: 2px solid transparent; }
    }
    .flash-highlight {
      animation: flash-border 1.5s ease-in-out;
    }
  `;
  document.head.appendChild(style);
}

const TeacherForm: React.FC<TeacherFormProps> = ({
  onFinish,
  onCancel,
  initialValues,
  visible,
  title,
}) => {
  const formRef = useRef<FormInstance>(null);
  const actionRef = useRef<FormListActionType>(undefined);
  // 存储错误信息的索引和目标跳转索引
  const [errorInfo, setErrorInfo] = useState<{
    errorIndex: number | null;
    targetIndex: number | null;
    jobType: number | null;
  }>({ errorIndex: null, targetIndex: null, jobType: null });
  // 用于追踪哪个元素需要闪烁高亮
  const [flashingIndex, setFlashingIndex] = useState<number | null>(null);

  const {
    getSubjectOptions,
    createTeacher,
    gradeOnlyTreeData,
    loading,
    treeData,
    isNeedSubject,
    isNeedClasses,
    isNeedGradesOnly,
    updateTeacher,
  } = useTeacherManagement();
  const { transFormSubmitData } = useTeacherDataTransform();

  console.log(` initialValues`, initialValues)

  const handleFormFinish = async (values: any) => {
    const { data: submitData, needMerge } = transFormSubmitData({
      ...initialValues,
      ...values,
    });
    console.log(` submitData`, { submitData, needMerge })
    const handleSubmit = async () => {
      try {
        if (initialValues) {
          await updateTeacher({ ...submitData });
          message.success('教师更新成功');
        } else {
          await createTeacher({ ...submitData });
          message.success('教师添加成功');
        }
        formRef.current?.resetFields();
        return await onFinish(submitData);
      } catch (error) {
        message.error('教师添加失败');
        return false;
      }
    }

    if (needMerge) {
      // 弹窗提示
      Modal.confirm({
        title: '提示',
        content: '存在需要合并的年级/班级，请确认是否继续',
        onOk: handleSubmit
      })
    } else {
      handleSubmit()
    }

    console.log(` submitData`, submitData)


  };

  const handleCancel = () => {
    onCancel();
    formRef.current?.resetFields();
  }

  const handleAddJob = () => {
    // 手动保存当前表单的值
    const currentValues = formRef.current?.getFieldsValue();

    // 使用 actionRef 添加一个空对象
    if (actionRef.current) {
      actionRef.current.add({});
    }

    // 稍后重新设置表单值，确保之前的数据不会丢失
    setTimeout(() => {
      formRef.current?.setFieldsValue(currentValues);
    }, 0);
  };

  // 处理跳转到对应表单项并删除当前重复职位
  const handleDuplicateJobFix = () => {
    const { targetIndex, errorIndex } = errorInfo;

    if (targetIndex !== null && errorIndex !== null && actionRef.current) {
      // 先跳转到目标位置
      const formGroup = document.querySelectorAll('.job-form-group')[targetIndex];
      if (formGroup) {
        formGroup.scrollIntoView({ behavior: 'smooth', block: 'center' });

        // 等待滚动完成后再触发闪烁高亮
        // 一般smooth滚动大约需要300-500ms完成
        setTimeout(() => {
          // 设置闪烁高亮
          setFlashingIndex(targetIndex);

          // 1.5秒后移除高亮
          setTimeout(() => {
            setFlashingIndex(null);
          }, 1500);
        }, 500); // 等待滚动完成
      }

      // 删除当前错误项
      actionRef.current.remove(errorIndex);

      // 清除错误状态
      setErrorInfo({ errorIndex: null, targetIndex: null, jobType: null });

      // 重新验证表单
      setTimeout(() => {
        formRef.current?.validateFields(['teacherJobInfos']);
      }, 100);
    }
  };

  // 自定义校验函数，用于校验校长、年级主任、班主任职务不能重复选择
  const validateLimitedRoles = (_: any, value: any[]) => {
    if (!value || value.length === 0) {
      return Promise.resolve();
    }

    const limitedRoles = [JobType.PRINCIPAL, JobType.GRADE_DIRECTOR, JobType.CLASS_TEACHER];

    const roleCount: Record<number, number> = {};
    const roleFirstIndex: Record<number, number> = {}; // 记录每个角色第一次出现的索引

    limitedRoles.forEach(role => {
      roleCount[role] = 0;
      roleFirstIndex[role] = -1;
    });

    // 找出每个角色的出现次数和第一次出现的位置
    value.forEach((job, index) => {
      if (job && job.jobType && limitedRoles.includes(job.jobType)) {
        // 记录首次出现的位置
        if (roleCount[job.jobType] === 0) {
          roleFirstIndex[job.jobType] = index;
        }
        roleCount[job.jobType]++;
      }
    });

    // 检查是否有角色出现多次
    for (const role of limitedRoles) {
      if (roleCount[role] > 1) {
        const roleName = jobTypeOptions.find(option => option.value === role)?.label || '该职务';

        // 找出最后一个出现该角色的位置
        let lastIndex = -1;
        for (let i = value.length - 1; i >= 0; i--) {
          if (value[i] && value[i].jobType === role) {
            lastIndex = i;
            break;
          }
        }

        // 只标记最后添加的为错误项，并记录应该跳转到的目标索引（第一个出现的位置）
        setErrorInfo({
          errorIndex: lastIndex,
          targetIndex: roleFirstIndex[role],
          jobType: role
        });

        return Promise.reject(`${roleName}只能选择一次`);
      }
    }

    // 清空错误状态
    setErrorInfo({ errorIndex: null, targetIndex: null, jobType: null });
    return Promise.resolve();
  };

  return (
    <Drawer
      title={title}
      open={visible}
      onClose={handleCancel}
      width={600}
      bodyStyle={{ padding: '24px', paddingBottom: 12 }}
    >
      <ProForm<TeacherInfo>
        id="teacherForm"
        layout="vertical"
        onFinish={handleFormFinish}
        initialValues={{
          ...initialValues,
          teacherJobInfos: initialValues?.teacherJobInfos,
        }}
        submitter={{
          render: () => {
            return (
              <div className="text-right">
                <Space>
                  <Button onClick={handleCancel}>取消</Button>
                  <Button
                    type="primary"
                    form="teacherForm"
                    htmlType="submit"
                    onClick={() => {
                      formRef.current?.validateFields();
                    }}
                  >
                    确定
                  </Button>
                </Space>
              </div>
            )
          }
        }}
        formRef={formRef}
      >
        <div className="text-base font-bold mb-4">基本信息</div>
        <ProFormText
          name="userName"
          label="老师姓名"
          rules={[{ required: true, message: '请输入姓名' }]}
          placeholder="请输入老师姓名"
          fieldProps={{
            maxLength: 20,
            showCount: true,
            className: "h-10 rounded"
          }}
        />

        <ProFormText
          name="userPhone"
          label="手机号"
          rules={[
            { required: true, message: '请输入手机号' },
            { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号' }
          ]}
          placeholder="请输入手机号"
          fieldProps={{
            className: "h-10 rounded"
          }}
        />

        <ProFormRadio.Group
          name="userIsTest"
          label="真实/测试"
          rules={[{ required: true, message: '请选择账号类型' }]}
          options={[
            { label: '真实教师', value: 1 },
            { label: '测试教师', value: 2 }
          ]}
          fieldProps={{
            className: "flex gap-6"
          }}
        />

        <div className="h-px bg-gray-100 my-6"></div>

        <div className="text-base font-bold mb-4">职务信息</div>
        <ProFormList
          name="teacherJobInfos"
          initialValue={[{}]}
          actionRef={actionRef}
          rules={[
            {
              validator: validateLimitedRoles
            }
          ]}
          creatorButtonProps={{
            creatorButtonText: '添加职务',
            icon: <PlusOutlined />,
            type: 'link',
            className: 'text-left justify-start mt-2',
            style: {
              width: '120px',
            },
            onClick: handleAddJob,
          }}
          itemRender={({ listDom, action }, { index, field, fields }) => {
            // 判断是否是当前错误项
            const isError = errorInfo.errorIndex === index;
            // 判断箭头方向：如果目标索引小于当前索引，显示上箭头，否则显示下箭头
            const showUpArrow = errorInfo.targetIndex !== null && errorInfo.targetIndex < index;
            // 判断是否需要闪烁高亮
            const shouldFlash = flashingIndex === index;

            return (
              <div
                key={field.key}
                className={`job-form-group ${index === fields.length - 1 ? 'mb-0' : 'mb-6'} p-4 rounded-lg border ${isError ? 'border-red-500' : 'border-gray-200'} ${shouldFlash ? 'flash-highlight' : ''}`}
              >
                <div className="flex items-center justify-between mb-5 mt-2 flex-1 w-full relative">
                  <div className="flex flex-1">
                    <div className="before:content-['*'] before:text-red-500 before:mr-1 font-bold flex items-center">选取职务{index + 1}</div>
                    <div className="flex items-center">
                      {isError && (
                        <div className="ml-3 flex items-center text-red-500">
                          <span className="text-sm">该职务只能选择一次</span>
                          <Tooltip title="去修改">
                            {showUpArrow ? (
                              <div className="ml-1 p-1 rounded-md hover:bg-gray-100 flex items-center justify-center">
                                <ArrowUpOutlined
                                  className="cursor-pointer text-blue-500 text-lg "
                                  onClick={handleDuplicateJobFix}
                                />
                              </div>
                            ) : (
                              <div className="ml-1 p-1 rounded-md hover:bg-gray-100 flex items-center justify-center">
ƒ                                <ArrowDownOutlined
                                  className="ƒcursor-pointer text-blue-500 text-lg "
                                  onClick={handleDuplicateJobFix}
                                />
                              </div>
                            )}
                          </Tooltip>
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="absolute right-[6px] top-[-10px] ">
                    {action}
                  </div>
                </div>
                {listDom}
              </div>
            );
          }}
        >
          {(meta, groupIndex) => {
            return (
              <>
                <ProFormRadio.Group
                  name="jobType"
                  label={false}
                  rules={[{ required: true, message: '请选择职务' }]}
                  options={jobTypeOptions}
                  fieldProps={{
                    onChange: (e) => {
                      // 当职务类型变更时，清空年级班级选择
                      const index = groupIndex
                      if (index !== -1) {
                        formRef.current?.setFieldValue(['teacherJobInfos', index, 'jobInfos'], undefined);
                        // 如果是需要学科的职务，也清空学科选择
                        if (isNeedSubject(e.target.value)) {
                          formRef.current?.setFieldValue(['teacherJobInfos', index, 'jobSubject'], undefined);
                        }
                        // 触发表单整体校验
                        setTimeout(() => {
                          formRef.current?.validateFields(['teacherJobInfos']);
                        }, 0);
                      }
                    }
                  }}
                />

                <ProFormDependency name={['jobType']}>
                  {({ jobType }) => {
                    const needSubject = isNeedSubject(jobType);
                    const needClasses = isNeedClasses(jobType);
                    const needGradesOnly = isNeedGradesOnly(jobType);

                    if (!jobType || jobType === JobType.PRINCIPAL) {
                      return null;
                    }

                    return (
                      <>
                        {needSubject && (
                          <ProFormSelect
                            name="jobSubject"
                            label="选取学科"
                            rules={[{ required: true, message: '请选择学科' }]}
                            options={getSubjectOptions()}
                          />
                        )}

                        {(needClasses || needGradesOnly) && (
                          <ProFormDependency name={['jobInfos']}>
                            {() => {

                              return (
                                <>
                                  <ProFormItem
                                    name="jobInfos"
                                    label={needGradesOnly ? "选取年级" : "选取年级/班级"}
                                    rules={[{ required: true, message: '请选择年级/班级' }]}
                                    validateTrigger={['onChange', 'onBlur']}
                                  >
                                    <TreeSelect
                                      treeData={needGradesOnly ? gradeOnlyTreeData : treeData}
                                      loading={loading}
                                      treeCheckable={true}
                                      treeCheckStrictly={true}
                                      showCheckedStrategy={TreeSelect.SHOW_CHILD}
                                      placeholder={needClasses ? "请选择年级和班级" : "请选择年级"}
                                      className="w-full"
                                      maxTagCount="responsive"
                                      allowClear
                                      treeDefaultExpandAll
                                      virtual={false}
                                      treeLine={needGradesOnly ? false : true}
                                      treeNodeFilterProp="title"
                                      dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
                                      treeExpandAction="click"
                                    />
                                  </ProFormItem>

                                </>
                              );
                            }}
                          </ProFormDependency>
                        )}
                      </>
                    );
                  }}
                </ProFormDependency>
              </>

            )

          }}


        </ProFormList>
      </ProForm>
    </Drawer>
  );
};

export default TeacherForm;