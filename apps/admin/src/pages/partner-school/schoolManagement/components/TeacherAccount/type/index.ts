
export interface TeacherInfo {
  userID: number;
  schoolID: number;
  userName: string;
  userPhone: string;
  teacherEmploymentStatus: number;
  userIsTest: number;
    teacherJobInfos: TeacherJobInfo[];
  }

  export interface TeacherJobInfo {
    jobType: number;
    jobInfos?: JobInfo[]  | null;
    jobSubject: number;
  }


  export interface JobInfo {
    jobGrade: number;
    jobClass: null | number[]
  }
  export interface TeacherFormProps {
    onFinish: (values: any) => void;
    onCancel: () => void;
    initialValues?: any;
    visible: boolean;
    title: string;
    schoolId?: number;
  }


export type CreateTeacherParams = Omit<TeacherInfo, 'userID' | 'createrID'>;


// 更新教师参数
export interface UpdateTeacherParams extends CreateTeacherParams {
  userID: number;
}

// 查询参数
export interface TeacherListParams {
  jobType?: number;
  schoolID: number;
  userName?: string;
  userPhone?: string;
  jobSubject?: number;
  teacherEmploymentStatus?: number;
  jobGrade?: number;
  jobClass?: number;
  userIsTest?: number;
}
