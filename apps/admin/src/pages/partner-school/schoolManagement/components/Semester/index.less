.container {
  width: 100%;
  height: 100%;
  padding: 16px;
  background-color: #fff;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.academicYear {
  margin: 0 !important;
  color: #000;
  font-weight: 500;
  font-size: 16px;
}

.editButtons {
  display: flex;
  gap: 8px;
}

.content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.gradeCard {
  margin-bottom: 16px;
  overflow: hidden;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);

  &:last-child {
    margin-bottom: 0;
  }

  :global(.ant-card-head) {
    min-height: 48px;
    padding: 0 16px;
    background-color: #fafafa;
    border-bottom: 1px solid #f0f0f0;
  }

  :global(.ant-card-body) {
    padding: 16px;
  }
}

.semestersRow {
  margin-bottom: 0 !important;
}

.semesterItem {
  height: 100%;
  padding: 12px;
  text-align: left;
  background-color: #fff;
  transition: all 0.3s;
}

.semesterTitle {
  margin-bottom: 12px;
  color: #6b7280;
  font-weight: 500;
  font-size: 14px;
  font-family: Roboto;
  line-height: 20px;
  font-feature-settings: 'kern' on;
}

.semesterDate {
  font-family: Roboto;
  font-size: 16px;
  font-weight: normal;
  line-height: 24px;
  letter-spacing: 0px;

  font-feature-settings: "kern" on;
  color: #000000;
}

.datePickerContainer {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.datePicker {
  width: 100%;
}

:global {
  .ant-picker {
    width: 100%;
  }

  .ant-checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .ant-card-head-title {
    font-weight: 500;
    font-size: 15px;
  }

  .ant-card-extra {
    padding: 0;
  }
}
