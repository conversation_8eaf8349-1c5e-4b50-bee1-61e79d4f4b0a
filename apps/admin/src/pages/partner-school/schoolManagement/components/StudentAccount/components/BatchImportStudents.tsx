import React, { useState } from 'react';
import { Modal, Upload, Button, message, Table, Radio, Form } from 'antd';
import { UploadOutlined, InboxOutlined } from '@ant-design/icons';
import type { UploadFile, UploadProps } from 'antd/es/upload/interface';
import { batchImportStudents } from '@/services/student-management';

interface BatchImportStudentsProps {
  visible: boolean;
  onCancel: () => void;
  onSuccess: () => void;
  schoolId: number;
  gradeId: number;
  classId: number;
}

interface StudentData {
  key: string;
  userName: string;
  userNumber?: string;
  userIsTest: number;
}

const BatchImportStudents: React.FC<BatchImportStudentsProps> = ({
  visible,
  onCancel,
  onSuccess,
  schoolId,
  gradeId,
  classId,
}) => {
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [previewData, setPreviewData] = useState<StudentData[]>([]);
  const [uploading, setUploading] = useState<boolean>(false);
  const [defaultIsTest, setDefaultIsTest] = useState<number>(0);

  // 处理文件上传
  const handleUpload: UploadProps['customRequest'] = (options) => {
    // 通知上传进度
    if (options.onProgress) {
      options.onProgress({ percent: 0 });
    }

    // 这里应该是解析Excel文件的逻辑
    // 为了演示，我们模拟一些数据
    setTimeout(() => {
      // 完成上传
      if (options.onSuccess) {
        options.onSuccess('ok');
      }

      const mockData: StudentData[] = [
        { key: '1', userName: '张三', userNumber: '20240001', userIsTest: defaultIsTest },
        { key: '2', userName: '李四', userNumber: '20240002', userIsTest: defaultIsTest },
        { key: '3', userName: '王五', userNumber: '20240003', userIsTest: defaultIsTest },
      ];

      setPreviewData(mockData);
      message.success('文件解析成功');
    }, 1000);
  };

  // 处理文件列表变化
  const handleFileChange: UploadProps['onChange'] = (info) => {
    let newFileList = [...info.fileList];

    // 只保留最后一个文件
    newFileList = newFileList.slice(-1);

    setFileList(newFileList);
  };

  // 处理导入
  const handleImport = async () => {
    if (previewData.length === 0) {
      message.warning('请先上传并解析文件');
      return;
    }

    setUploading(true);

    try {
      await batchImportStudents({
        schoolId,
        gradeId,
        classId,
        students: previewData.map(item => ({
          userName: item.userName,
          userNumber: item.userNumber,
          userIsTest: item.userIsTest,
        })),
      });

      message.success('批量导入成功');
      onSuccess();
    } catch (error) {
      console.error('批量导入失败:', error);
      message.error('批量导入失败');
    } finally {
      setUploading(false);
    }
  };

  // 表格列定义
  const columns = [
    {
      title: '姓名',
      dataIndex: 'userName',
      key: 'userName',
    },
    {
      title: '学号',
      dataIndex: 'userNumber',
      key: 'userNumber',
    },
    {
      title: '是否测试',
      dataIndex: 'userIsTest',
      key: 'userIsTest',
      render: (isTest: number) => (isTest === 1 ? '是' : '否'),
    },
  ];

  return (
    <Modal
      title="批量导入学生"
      open={visible}
      onCancel={onCancel}
      width={700}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          取消
        </Button>,
        <Button
          key="import"
          type="primary"
          loading={uploading}
          onClick={handleImport}
          disabled={previewData.length === 0}
        >
          导入
        </Button>,
      ]}
    >
      <div className="mb-4">
        <Form layout="vertical">
          <Form.Item label="默认账号类型">
            <Radio.Group
              value={defaultIsTest}
              onChange={(e) => setDefaultIsTest(e.target.value)}
            >
              <Radio value={0}>正式账号</Radio>
              <Radio value={1}>测试账号</Radio>
            </Radio.Group>
          </Form.Item>
        </Form>
      </div>

      <Upload.Dragger
        fileList={fileList}
        customRequest={handleUpload}
        onChange={handleFileChange}
        accept=".xlsx,.xls"
        maxCount={1}
        className="mb-4"
      >
        <p className="ant-upload-drag-icon">
          <InboxOutlined />
        </p>
        <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
        <p className="ant-upload-hint">
          支持 .xlsx, .xls 格式的Excel文件
        </p>
        <Button icon={<UploadOutlined />} className="mt-3">
          选择文件
        </Button>
      </Upload.Dragger>

      {previewData.length > 0 && (
        <div>
          <h3 className="mb-2">数据预览</h3>
          <Table
            columns={columns}
            dataSource={previewData}
            pagination={false}
            size="small"
            scroll={{ y: 240 }}
          />
        </div>
      )}
    </Modal>
  );
};

export default BatchImportStudents;
