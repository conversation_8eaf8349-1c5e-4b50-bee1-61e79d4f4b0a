import { NodeType, NodeInfo } from '../../../../../components/GradeTree';
import { StudentInfo } from '@/services/student-management/type';

// 选中节点信息类型
export interface SelectedNodeInfo {
  nodeType: NodeType;
  nodeId: string;
  nodeName: string;
  nodeInfo?: NodeInfo<NodeType>;
}

// 统计数据类型
export interface SchoolStats {
  totalCount: number;
  paidCount: number;
  trialCount: number;
  stoppedCount: number;
}

// 筛选参数类型
export interface FilterParams {
  searchKeyword: string;
  userIsTest?: number;
  userStatus?: number;
  currentPage: number;
  pageSize: number;
}

// 协作模态框状态
export interface CooperationModalState {
  visible: boolean;
  defaultActiveKey: string;
}

// 学生列表响应类型
export interface StudentListResponse {
  list: StudentInfo[];
  total: number;
}

// 学生表单属性
export interface StudentFormProps {
  schoolId: string;
  gradeId: string;
  classId: string;
  visible: boolean;
  onCancel: () => void;
  onSuccess: () => void;
}

// 转班表单属性
export interface TransferClassFormProps {
  schoolId: string;
  studentList: StudentInfo[];
  visible: boolean;
  onCancel: () => void;
  onSuccess: () => void;
}

// 学生管理组件属性
export interface StudentManagementProps {
  schoolId: string;
  schoolName: string;
} 