import React, { useState } from 'react';
import { But<PERSON>, Modal, message } from 'antd';
import { DownloadOutlined } from '@ant-design/icons';
import useSWRMutation from 'swr/mutation';
import { downloadFile, post } from '@/services/fetcher';
import { StudentInfo } from '@/services/student-management/type';

interface ExportParams {
  studentIDs: string;
  gradeID: number;
  classID: number;
  schoolID: number;
}

interface ExportStudentsProps {
  gradeId: number;
  classId: number;
  schoolId: number;
  gradeName: string;
  className: string;
  schoolName: string;
  selectedStudentList: StudentInfo[];
}

const ExportStudents: React.FC<ExportStudentsProps> = ({
  gradeId,
  classId,
  schoolId,
  gradeName,
  className,
  schoolName,
  selectedStudentList,
}) => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const { trigger: exportStudents, isMutating: isExporting } = useSWRMutation(
    '/api/v1/userSchool/exportAllStudents',
    post<{
      file_name: string;
      file_content: string;
    }, ExportParams>,
    {
      onSuccess: (data) => {
        // 将 base64 转换为 Blob
        const byteCharacters = atob(data?.file_content || '');
        const byteNumbers = new Array(byteCharacters.length);

        for (let i = 0; i < byteCharacters.length; i++) {
          byteNumbers[i] = byteCharacters.charCodeAt(i);
        }

        const byteArray = new Uint8Array(byteNumbers);
        const blob = new Blob([byteArray], { type: 'application/vnd.ms-excel' });
        downloadFile(blob, data?.file_name || '');
        message.success('导出成功');
        setIsModalVisible(false);
      },
      onError: (error) => {
        message.error(error.message || '导出失败，请重试');
      }
    }
  );

  return (
    <>
      <Button
        size="small"
        type="default"
        icon={<DownloadOutlined />}
        onClick={() => setIsModalVisible(true)}
        className="flex items-center export-students-button"
      >
        导出学生
      </Button>
      <Modal
        title={<span className="text-lg font-medium">导出学生信息</span>}
        open={isModalVisible}
        onOk={async () => {
          const studentIDs = selectedStudentList.map(student => student.userID).join(',');
          await exportStudents({
            studentIDs,
            gradeID: gradeId,
            classID: classId,
            schoolID: schoolId,
          })
        }}
        confirmLoading={isExporting}
        onCancel={() => setIsModalVisible(false)}
        okText="确认导出"
        cancelText="取消"
        maskClosable={false}
        className="export-students-modal"
      >
        <div className="space-y-3 p-2">
          <p className="font-bold text-gray-800">您正在导出以下信息的学生：</p>
          <div className="bg-gray-50 p-3 rounded-md">
            <p className="mb-2"><span className="text-gray-600 inline-block w-12">学校：</span>{schoolName}</p>
            <p className="mb-2"><span className="text-gray-600 inline-block w-12">年级：</span>{gradeName || '全部'}</p>
            <p className="mb-2"><span className="text-gray-600 inline-block w-12">班级：</span>{className || '全部'}</p>
            <p className="mb-0"><span className="text-gray-600 inline-block w-12">学生：</span>
              {selectedStudentList.length > 0 ? `已选择 ${selectedStudentList.length} 名学生` : '全部学生'}
            </p>
          </div>
          {selectedStudentList.length > 0 && (
            <div className="bg-blue-50 p-3 rounded-md max-h-32 overflow-y-auto">
              <p className="text-sm text-gray-600 mb-2">选中的学生：</p>
              <div className="text-sm space-y-1">
                {selectedStudentList.map((student, index) => (
                  <div key={student.userID} className="text-gray-700">
                    {index + 1}. {student.userName} {student.userNumber ? `(${student.userNumber})` : ''}
                  </div>
                ))}
              </div>
            </div>
          )}
          <p className="text-gray-500 text-sm mt-4 bg-blue-50 p-2 rounded border-l-2 border-blue-400">
            导出文件将包含{selectedStudentList.length > 0 ? '选中' : '所有'}学生的详细信息，请妥善保管学生数据
          </p>
        </div>
      </Modal>
    </>
  );
};

export default ExportStudents; 