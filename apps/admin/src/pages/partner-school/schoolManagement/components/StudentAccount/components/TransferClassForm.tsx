import React, { useState, useEffect } from 'react';
import { Form, Select, Modal, message, Spin, Button } from 'antd';
import { transferStudent } from '@/services/student-management';
import { getClassTree } from '@/services/schedule-management';
import { GradeInfo } from '@/services/schedule-management/type';
import { CloseOutlined } from '@ant-design/icons';
import { StudentInfo } from '@/services/student-management/type';

interface TransferClassFormProps {
  visible: boolean;
  onCancel: () => void;
  onSuccess: () => void;
  studentList: StudentInfo[];
  schoolId: number;
}

const TransferClassForm: React.FC<TransferClassFormProps> = ({
  visible,
  onCancel,
  onSuccess,
  studentList,
  schoolId,
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState<boolean>(false);
  const [gradeList, setGradeList] = useState<GradeInfo[]>([]);
  const [classList, setClassList] = useState<{ classId: string; className: string }[]>([]);
  const [selectedGradeId, setSelectedGradeId] = useState<number | undefined>();
  const [confirmLoading, setConfirmLoading] = useState<boolean>(false);
  const [selectedStudentList, setSelectedStudentList] = useState<StudentInfo[]>(studentList);
  useEffect(() => {
    setSelectedStudentList(studentList);
    
  }, [studentList]);
  // 处理年级变化
  const handleGradeChange = (gradeId: number, gradeData = gradeList) => {
    setSelectedGradeId(gradeId);
    form.setFieldValue('classId', undefined);

    // 提取该年级下的所有班级
    const selectedGrade = gradeData.find(grade => grade.grade_id === gradeId);
    if (selectedGrade) {
      const allClasses: { classId: string; className: string }[] = [];
      selectedGrade.children?.forEach(group => {
        group.children?.forEach(classItem => {
          allClasses.push({
            classId: String(classItem.class_id),
            className: classItem.class_name,
          });
        });
      });

      setClassList(allClasses);
    } else {
      setClassList([]);
    }
  };
  // 获取年级和班级数据
  const fetchGradeAndClassData = async () => {
    setLoading(true);
    try {
      const response = await getClassTree({
        class_school_id: schoolId,
        class_school_year_id: 1, // 默认使用当前学年
      });

      if (response?.data?.list) {
        setGradeList(response.data.list);

        // 如果只有一个学生，尝试自动选择当前年级
        if (studentList.length === 1 && response.data.list.length > 0) {
          const currentGrade = response.data.list.find(grade =>
            grade.grade_id === studentList[0].userGrade
          );

          if (currentGrade) {
            setSelectedGradeId(currentGrade.grade_id);
            form.setFieldValue('gradeId', currentGrade.grade_id);
            handleGradeChange(currentGrade.grade_id, response.data.list);
          }
        }
      }
    } catch (error) {
      console.error('获取年级班级数据失败:', error);
      message.error('获取年级班级数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 当对话框打开时获取年级和班级数据
  useEffect(() => {
    if (visible) {
      fetchGradeAndClassData();
      console.log("studentList",studentList,studentList[0]?.userGrade);
      
      setSelectedGradeId(studentList[0]?.userGrade);
      // 重置表单
      form.resetFields();
    }
  }, [visible]);



  // 处理表单提交
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setConfirmLoading(true);

      // 检查是否存在学生已经在目标班级
      const sameClassStudents = studentList.filter(s =>
        String(s.userGrade) === values.gradeId && String(s.userClass) === values.classId
      );

      if (sameClassStudents.length > 0 && studentList.length === 1) {
        message.warning('所选班级与当前班级相同，无需转班');
        setConfirmLoading(false);
        return;
      }

      // 如果是多个学生，只转未在该班级的学生
      const studentsToTransfer = studentList.filter(s =>
        !(String(s.userGrade) === values.gradeId && String(s.userClass) === values.classId)
      );

      if (studentsToTransfer.length === 0) {
        message.warning('所有学生都已在目标班级，无需转班');
        setConfirmLoading(false);
        return;
      }

      console.log("transfer",{studentsToTransfer});
      
   const res = await transferStudent({
        studentIDs: studentsToTransfer.map(s => s.userID.toString()).join(','),
        gradeID: Number(values.gradeId),
        classID: Number(values.classId),
        schoolID: schoolId,
      })

      if (res.status === 200) {
        message.success(`成功转班 ${studentsToTransfer.length} 名学生`);
        setConfirmLoading(false);
        onSuccess();
      } else {
        message.error(res.message);
      }

    } catch (error) {
      console.error('转班失败:', error);
      setConfirmLoading(false);
      message.error('转班失败');
    }
  };

  // 渲染已选择的学生列表
  const renderSelectedStudents = () => {
    return (
      <div className="mt-2 mb-4">
        <div className="text-gray-700 font-medium mb-2">已选学生</div>
        <div className="max-h-48 overflow-y-auto border border-gray-200 rounded">
          {selectedStudentList.map((s, index) => (
            <div key={s.userID} className="flex justify-between items-center py-2 px-3 border-b last:border-b-0">
              <span>{s.userName} - {s.userNumber || `学号${index + 1}`}</span>
              <CloseOutlined
                className="text-gray-400 hover:bg-gray-100 cursor-pointer hover:text-red-500 p-1 rounded-sm"
                onClick={() => {
                  setSelectedStudentList(selectedStudentList.filter(c => c.userID !== s.userID));
                }}
              />
            </div>
          ))}
        </div>
      </div>
    );
  };

  return (
    <Modal
      title="转班"
      open={visible}
      onCancel={onCancel}
      width={520}
      maskClosable={false}
      destroyOnClose
      footer={[
        <Button key="cancel" onClick={onCancel}>
          取消
        </Button>,
        <Button
          key="submit"
          type="primary"
          loading={confirmLoading}
          onClick={handleSubmit}
        >
          确认转班
        </Button>
      ]}
    >
      <Spin spinning={loading}>
        <div className="mb-4">
          <h3 className="text-lg font-medium mb-2">选择转入班级</h3>
          <Form
            form={form}
            layout="vertical"
          >
            <div className="flex gap-4">
              <Form.Item
                name="gradeId"
                label="选择年级"
                className="flex-1"
                rules={[{ required: true, message: '请选择年级' }]}
              >
                <Select
                  options={gradeList}
                  fieldNames={{label: 'grade_name', value: 'grade_id'}}
                  value={selectedGradeId}
                  placeholder="选择年级"
                  onChange={(value) => handleGradeChange(value)}
                  suffixIcon={<span className="text-gray-400">▼</span>}
                >
                </Select>
              </Form.Item>

              <Form.Item
                name="classId"
                label="选择班级"
                className="flex-1"
                rules={[{ required: true, message: '请选择班级' }]}
              >
                <Select
                  placeholder="选择班级"
                  disabled={!selectedGradeId}
                  suffixIcon={<span className="text-gray-400">▼</span>}
                >
                  {classList.map(classItem => (
                    <Select.Option key={classItem.classId} value={classItem.classId}>
                      {classItem.className}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </div>
          </Form>
        </div>

        {/* 已选学生列表 */}
        {renderSelectedStudents()}
      </Spin>
    </Modal>
  );
};

export default TransferClassForm;