export interface Student {
  className: string;
  gradName: string;
  userAccount: string;
  userClass: number;
  userGrade: number;
  userID: number;
  userIsTest: number;
  userName: string;
  userNumber: string;
  userStatus: number;
}

export interface StudentInfo {
  userID: number;
  userName: string;
  userNumber: string;
  userGrade: number;
  userIsTest: number;
  userStatus: number;
}

export interface ClassData {
  className: string;
  classID: number;
  studentList: Student[];
  classIsTest: number;
}

export interface GradeData {
  gradeID: number;
  gradeName: string;
  classList: ClassData[];
}

export interface SchoolData {
  schoolID: number;
  schoolName: string;
  gradeList?: GradeData[];
}
