import { DictTypeEnum } from '@/services/dict';
import { post } from '@/services/fetcher';
import { getSchoolDetail } from '@/services/partner-school';
import type { SchoolListParams } from '@/services/partner-school/types';
import { SchoolItem } from '@/types/school';
import { PlusOutlined } from '@ant-design/icons';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import { useAccess, useModel } from '@umijs/max';
import { Button, message } from 'antd';
import dayjs from 'dayjs';
import { useRef, useState } from 'react';
import useSWRMutation from 'swr/mutation';
import { useSchoolTabNavigation } from '../schoolManagement/hooks/useSchoolTabNavigation';
import CooperationModal from './components/CooperationModal';
import SchoolForm from './components/SchoolForm';

export default () => {
  const { getDictValueEnum } = useModel('dictModels');
  const { initialState } = useModel('@@initialState');
  const { allProvinces = [] } = initialState || {};
  const access = useAccess();
  const actionRef = useRef<ActionType>(undefined);
  const [modalVisible, setModalVisible] = useState(false);
  const [currentSchool, setCurrentSchool] = useState<SchoolItem>();
  const [modalTitle, setModalTitle] = useState('创建新学校');
  const [cooperationVisible, setCooperationVisible] = useState(false);
  const { fetchSchoolInfo } = useModel('schoolModel');
  // const { data: allProvinces } = useSWR<RegionItem[]>('/api/v1/dict/listAllProvinces', fetcher);
  const url = access.canReadAllSchool
    ? '/api/v1/school/listSchools'
    : '/api/v1/school/listMySchools';
  const { trigger: mutateSchoolList, isMutating } = useSWRMutation(
    url,
    post<API.TableData<SchoolItem>, SchoolListParams>,
  );

  // 使用导航 hook
  const { navigateToSchool } = useSchoolTabNavigation();

  const handleEdit = async (record: SchoolItem) => {
    try {
      const response = await getSchoolDetail(record.schoolId);
      if (response.status === 200) {
        setCurrentSchool(response.data);
        setModalTitle('编辑学校');
        setModalVisible(true);
      }
    } catch (error: any) {
      message.error(error.message || '获取学校详情失败');
    }
  };

  const handleVisibleChange = (visible: boolean) => {
    setCurrentSchool(undefined);
    setModalTitle('创建新学校');
    setModalVisible(visible);
  };

  const handleView = async (record: SchoolItem) => {
    try {
      await fetchSchoolInfo(record.schoolId);
      await navigateToSchool(record.schoolId);
    } catch (error) {
      console.error('Navigation error:', error);
      message.error('跳转失败，请重试');
    }
  };

  const columns: ProColumns<SchoolItem>[] = [
    {
      title: '学校编号',
      dataIndex: 'schoolNumber',
      width: 120,
      search: false,
    },
    {
      title: '学校名称',
      dataIndex: 'schoolName',
      width: 200,
      search: {
        transform: (value: string) => ({ schoolKey: value }),
      },
      fieldProps: {
        placeholder: '请输入学校名称 或 ID',
      },
    },
    {
      title: '所在地区',
      dataIndex: 'schoolRegionId',
      width: 240,
      valueType: 'cascader',
      fieldProps: {
        options: allProvinces,
        showSearch: true,
        placeholder: '请选择所在地区',
        changeOnSelect: true,
        fieldNames: {
          label: 'regionDictName',
          value: 'regionDictCode',
          children: 'subList',
        },
      },
      search: {
        transform: (value: string[]) => {
          if (value && value.length > 0) {
            return { schoolRegionId: parseInt(value[value.length - 1]) };
          }
          return {};
        },
      },
      render: (_, record) => {
        return record?.regionInfos?.map((item) => item.regionDictName).join(' / ');
      },
    },
    {
      title: '合作状态',
      dataIndex: 'schoolStatus',
      width: 120,
      valueEnum: getDictValueEnum(DictTypeEnum.SCHOOL_CP),
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 220,
      valueType: 'dateTime',
      search: false,
      render: (_, record) =>
        record.createTime ? dayjs(record.createTime).format('YYYY-MM-DD HH:mm') : '-',
    },
    {
      title: '创建人',
      dataIndex: 'creatorName',
      width: 120,
      search: false,
    },
    {
      title: '操作',
      width: access.canReadAllSchool ? 200 : 120,
      valueType: 'option',
      search: false,
      render: (_, record) => [
        <Button type="link" size="small" key="view" onClick={() => handleView(record)}>
          查看
        </Button>,
        <Button type="link" size="small" key="edit" onClick={() => handleEdit(record)}>
          编辑
        </Button>,
        access.canReadAllSchool && (
          <Button
            type="link"
            size="small"
            key="cooperation"
            onClick={() => {
              setCurrentSchool(record);
              setCooperationVisible(true);
            }}
          >
            变更合作状态
          </Button>
        ),
      ],
    },
  ];

  return (
    <>
      <ProTable<SchoolItem>
        columns={columns}
        actionRef={actionRef}
        loading={isMutating}
        cardBordered
        request={async (params) => {
          const { current, pageSize, ...rest } = params;
          console.log('params', params);

          try {
            const requestParams: SchoolListParams = {
              ...rest,
              page: current || 1,
              pageSize: pageSize || 10,
              schoolStatus: Number(rest.schoolStatus),
            };
            if (!access.canReadAllSchool) {
              requestParams.userID = initialState?.currentUser?.userId || 0;
            }

            let response;
            response = await mutateSchoolList(requestParams);

            return {
              data: response?.list || [],
              success: true,
              total: response?.total || 0,
            };
          } catch (error: any) {
            message.error(error.message || '获取数据失败');
            return {
              data: [],
              success: false,
              total: 0,
            };
          }
        }}
        rowKey="schoolId"
        search={{
          labelWidth: 'auto',
          defaultCollapsed: false,
          span: 8,
          layout: 'vertical',
        }}
        pagination={{
          showTotal: (total) => (
            <span>
              共 <a style={{ color: '#1890ff' }}>{total}</a> 条数据
            </span>
          ),
        }}
        dateFormatter="string"
        headerTitle={access.canReadAllSchool ? '合作校列表' : '我的合作校列表'}
        toolBarRender={() => [
          access.canReadAllSchool && (
            <Button
              key="button"
              icon={<PlusOutlined />}
              type="primary"
              onClick={() => handleVisibleChange(true)}
            >
              创建新学校
            </Button>
          ),
        ]}
      />

      {modalVisible && (
        <SchoolForm
          visible={modalVisible}
          title={modalTitle}
          initialValues={currentSchool}
          onFinish={() => {
            actionRef.current?.reload();
            setModalVisible(false);
          }}
          onVisibleChange={handleVisibleChange}
        />
      )}

      {currentSchool && (
        <CooperationModal
          visible={cooperationVisible}
          school={currentSchool}
          onVisibleChange={setCooperationVisible}
        />
      )}
    </>
  );
};
