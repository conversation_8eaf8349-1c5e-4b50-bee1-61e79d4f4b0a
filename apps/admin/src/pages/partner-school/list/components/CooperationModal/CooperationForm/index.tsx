import { CooperationFormData, CooperationType } from '@/types/cooperation';
import {
  ProForm,
  ProFormDatePicker,
  ProFormDateRangePicker,
  ProFormDependency,
  ProFormDigit,
  ProFormSelect,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { Space, Typography } from 'antd';
import dayjs from 'dayjs';
import React, { useState } from 'react';
import { isDisableType, isPaymentType, isTrialType } from '../../../../utils/cooperationUtils';
import { useCooperationSubmit } from './hooks/useCooperationSubmit';
import StudentSelector from './StudentSelector';

const { Text } = Typography;

interface CooperationFormProps {
  onCancel: () => void;
  schoolId: number;
  schoolName: string;
}

const CooperationForm: React.FC<CooperationFormProps> = ({ onCancel, schoolId, schoolName }) => {
  const [selectedStudentIds, setSelectedStudentIds] = useState<number[]>([]);
  const { submit, isSubmitting } = useCooperationSubmit();
  const [cooperationType, setCooperationType] = useState<CooperationType>(CooperationType.TRIAL);

  // Handle type change
  const handleTypeChange = (value: CooperationType) => {
    setCooperationType(value);
  };

  // 禁用今天之前的日期
  const disabledDate = (current: dayjs.Dayjs) => {
    return current && current < dayjs().startOf('day');
  };

  const endTimeDisabledDate = (current: dayjs.Dayjs, startTime: dayjs.Dayjs) => {
    console.log('结束日期 ===>: ', current, startTime);
    return current && current < startTime;
  };

  const handleCancel = () => {
    onCancel();
  };

  const onFinish = async (values: CooperationFormData) => {
    console.log('onFinish', values);
    // 将选中的学生ID添加到表单数据中
    values.selectedStudents = selectedStudentIds.map((id) => String(id));
    values.studentCount = selectedStudentIds.length;
    if (values.dateRange) {
      values.startTime = values.dateRange[0];
      values.endTime = values.dateRange[1];
    }

    console.log('提交审批values', values);
    const result = await submit(values, { schoolName: schoolName, schoolID: schoolId });
    onCancel();
    console.log(result);
  };

  // 处理学生选择变化
  const handleStudentChange = (studentIds: number[]) => {
    setSelectedStudentIds(studentIds);
  };

  // 计算结束日期
  const calculateEndDate = (startDate: dayjs.Dayjs | null, days: number | null) => {
    if (!startDate || !days) return null;
    return startDate.add(days, 'day').format('YYYY-MM-DD');
  };

  const calculateDays = (start, end) => {
    if (!start || !end) return null;
    return end.diff(start, 'day');
  };

  return (
    <ProForm<CooperationFormData>
      layout="horizontal"
      submitter={{
        searchConfig: {
          submitText: '确认',
          resetText: '取消',
        },
        submitButtonProps: {
          loading: isSubmitting,
        },
        onReset: handleCancel,
        render: (props, dom) => {
          return <Space style={{ float: 'right' }}>{dom}</Space>;
        },
      }}
      onFinish={onFinish}
      labelCol={{ span: 4 }}
      wrapperCol={{ span: 20 }}
      initialValues={{
        cooperationType: cooperationType,
        studentType: 'all',
      }}
      style={{
        paddingTop: '12px',
      }}
    >
      {/* 合并的发起流程选择器 */}
      <ProFormSelect
        name="cooperationType"
        label="发起流程"
        valueEnum={{
          [CooperationType.TRIAL]: '试用',
          [CooperationType.TRIAL_EXTENSION]: '试用延期（仅限试用中学生）',
          [CooperationType.PAYMENT]: '付费',
          [CooperationType.DISABLE]: '停用',
        }}
        onChange={handleTypeChange}
        width="md"
      />

      <ProFormDependency name={['cooperationType']}>
        {({ cooperationType: formType }) => {
          // 使用表单中的值，如果没有则使用props传入的值
          const currentType = formType || cooperationType;

          return (
            <>
              {isTrialType(currentType) && (
                <>
                  <ProFormDateRangePicker
                    name="dateRange"
                    rules={[
                      { required: true, message: '请选择试用时间' },
                      {
                        validator: (_, value) => {
                          if (value) {
                            const days = calculateDays(value[0], value[1]);
                            if (days) {
                              return Promise.resolve();
                            }
                            return Promise.reject('结束日期必须大于开始日期');
                          }
                          return Promise.resolve();
                        },
                      },
                    ]}
                    label="试用日期"
                    width="md"
                    fieldProps={{
                      disabledDate: disabledDate,
                    }}
                    placeholder={['开始试用日期', '结束试用日期']}
                  />

                  <ProFormDependency name={['dateRange']}>
                    {({ dateRange }) => {
                      if (!dateRange) {
                        return null;
                      }
                      const totalDays = dateRange
                        ? calculateDays(dayjs(dateRange[0]), dayjs(dateRange[1]))
                        : 0;
                      console.log('totalDays', totalDays);

                      if (totalDays === 0) {
                        return null;
                      }

                      return (
                        <ProFormDigit
                          label="开通天数"
                          name="totalDays"
                          min={1}
                          max={1825}
                          value={totalDays}
                          readonly
                          width="md"
                        />
                      );
                    }}
                  </ProFormDependency>

                  {currentType === CooperationType.TRIAL && (
                    <ProForm.Item
                      label="选择学生"
                      name="studentSelection"
                      rules={[{ required: true, message: '请选择学生' }]}
                    >
                      <StudentSelector
                        value={selectedStudentIds}
                        onChange={handleStudentChange}
                        schoolId={schoolId}
                        cooperationType={currentType}
                      />
                    </ProForm.Item>
                  )}

                  {currentType === CooperationType.TRIAL_EXTENSION && (
                    <ProForm.Item
                      label="选择学生"
                      name="studentSelection"
                      rules={[{ required: true, message: '请选择试用中学生' }]}
                      extra="仅可选择当前处于试用中的学生进行延期"
                    >
                      <StudentSelector
                        value={selectedStudentIds}
                        onChange={handleStudentChange}
                        schoolId={schoolId}
                        cooperationType={currentType}
                      />
                    </ProForm.Item>
                  )}
                </>
              )}

              {isPaymentType(currentType) && (
                <>
                  <ProFormDateRangePicker
                    name="dateRange"
                    rules={[
                      { required: true, message: '请选择计费时间' },
                      {
                        validator: (_, value) => {
                          if (value) {
                            const days = calculateDays(value[0], value[1]);
                            if (days) {
                              return Promise.resolve();
                            }
                            return Promise.reject('结束日期必须大于开始日期');
                          }
                          return Promise.resolve();
                        },
                      },
                    ]}
                    label="计费日期"
                    width="md"
                    fieldProps={{
                      disabledDate: disabledDate,
                    }}
                    placeholder={['开始计费日期', '结束计费日期']}
                  />
                  <ProFormDependency name={['dateRange']}>
                    {({ dateRange }) => {
                      if (!dateRange) {
                        return null;
                      }
                      const totalDays = dateRange
                        ? calculateDays(dayjs(dateRange[0]), dayjs(dateRange[1]))
                        : 0;
                      console.log('totalDays', totalDays);

                      if (totalDays === 0) {
                        return null;
                      }

                      return (
                        <ProFormDigit
                          label="开通天数"
                          name="totalDays"
                          min={1}
                          max={1825}
                          value={totalDays}
                          readonly
                          width="md"
                        />
                      );
                    }}
                  </ProFormDependency>

                  <ProForm.Item
                    label="选择学生"
                    name="studentSelection"
                    rules={[{ required: true, message: '请选择学生' }]}
                  >
                    <StudentSelector
                      value={selectedStudentIds}
                      onChange={handleStudentChange}
                      schoolId={schoolId}
                      cooperationType={currentType}
                    />
                  </ProForm.Item>
                </>
              )}

              {isDisableType(currentType) && (
                <>
                  <ProFormDatePicker
                    label="停用时间"
                    name="startTime"
                    rules={[{ required: true, message: '请选择停用时间' }]}
                    width="md"
                    fieldProps={{
                      disabledDate: disabledDate,
                    }}
                  />

                  <ProForm.Item
                    label="选择账号"
                    name="studentSelection"
                    rules={[{ required: true, message: '请选择要停用的账号' }]}
                  >
                    <StudentSelector
                      value={selectedStudentIds}
                      onChange={handleStudentChange}
                      schoolId={schoolId}
                      cooperationType={currentType}
                    />
                  </ProForm.Item>

                  <ProFormTextArea
                    label="停用原因"
                    name="reason"
                    placeholder="请输入停用原因"
                    rules={[{ required: true, message: '请输入停用原因' }]}
                  />
                </>
              )}

              <ProFormTextArea label="备注" name="remark" placeholder="请输入备注信息（选填）" />
            </>
          );
        }}
      </ProFormDependency>
    </ProForm>
  );
};

export default CooperationForm;
