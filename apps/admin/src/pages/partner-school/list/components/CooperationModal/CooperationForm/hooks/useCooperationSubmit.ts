import { post } from '@/services/fetcher';
import { CooperationFormData } from '@/types/cooperation';
import { message } from 'antd';
import dayjs from 'dayjs';
import { useEffect } from 'react';
import useSWRMutation from 'swr/mutation';

interface ExecuteParams {
  type: number;
  schoolName: string;
  schoolID: number;
  desc: string;
  startTime: number;
  periodTime: number;
  endTime: number;
  studentIds: string;
}

interface SubmitParams {
  schoolID: number;
  type: number;
  execute_params: ExecuteParams;
}

interface SubmitResponse {
  code: number;
  message: string;
  data?: any;
}

/**
 * 合作申请提交Hook
 * @returns {Object} 包含提交方法和状态的对象
 */
export const useCooperationSubmit = () => {
  const { trigger, isMutating, error, data } = useSWRMutation(
    '/api/v1/commonApproval/create',
    post<SubmitResponse, SubmitParams>,
  );

  useEffect(() => {
    if (error) {
      message.error(error.message || '提交失败');
    }
  }, [error]);
  /**
   * 提交合作申请
   * @param params 合作申请参数
   */
  const submit = async (
    params: CooperationFormData,
    { schoolName, schoolID }: { schoolName: string; schoolID: number },
  ) => {
    const {
      startTime: startDate,
      endTime: endDate,
      studentSelection,
      cooperationType,
      remark,
    } = params;
    const startTime = startDate ? Math.floor(dayjs(startDate).valueOf() / 1000) : 0;
    const endTime = endDate ? Math.floor(dayjs(endDate).valueOf() / 1000) : 0;
    const periodTime = endTime ? endTime - startTime : 0;

    const submitData: SubmitParams = {
      schoolID: schoolID,
      type: Number(cooperationType),
      execute_params: {
        type: Number(cooperationType),
        schoolName: schoolName,
        schoolID: schoolID,
        desc: remark || '',
        // 转为时间戳
        startTime,
        periodTime,
        endTime,
        studentIds: studentSelection.join(','),
      },
    };
    console.log('submitData', submitData);
    const result = await trigger(submitData);
    console.log('submitData2', result, data, error);

    if (!error) {
      message.success('提交成功！');
      return {
        code: 0,
        message: '提交成功',
      };
    } else {
      message.error('提交失败');
      throw new Error('提交失败');
    }
  };

  return {
    submit,
    isSubmitting: isMutating,
    error,
    submitResult: data,
  };
};
