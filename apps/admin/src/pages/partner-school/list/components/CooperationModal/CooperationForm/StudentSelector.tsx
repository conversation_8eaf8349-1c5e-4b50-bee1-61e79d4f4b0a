import { ClassData, GradeData, SchoolData, Student } from '@/pages/partner-school/types';
import { filterStudentsByCooperationType } from '@/pages/partner-school/utils/student';
import { post } from '@/services/fetcher';
import { CooperationType } from '@/types/cooperation';
import { StudentIsTest, StudentStatus } from '@/types/students';
import { CheckOutlined, DeleteOutlined, SearchOutlined, UserOutlined } from '@ant-design/icons';
import { ProFormDependency, ProFormRadio, ProFormText } from '@ant-design/pro-components';
import { Button, Card, Empty, Flex, Space, Spin, Tag, Tree, Typography } from 'antd';
import cloneDeep from 'lodash-es/cloneDeep';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import useSWRMutation from 'swr/mutation';

const { Text, Title } = Typography;

interface StudentSelectorProps {
  value?: number[];
  onChange?: (value: number[]) => void;
  schoolId?: number;
  cooperationType?: CooperationType;
}

interface EventInfo {
  node: {
    isLeaf?: boolean;
    studentInfo?: Student;
  };
}

const StudentSelector: React.FC<StudentSelectorProps> = ({
  value = [],
  onChange,
  schoolId,
  cooperationType = CooperationType.TRIAL,
}) => {
  const [selectedIds, setSelectedIds] = useState<number[]>(value);
  const [search, setSearch] = useState<string>('');
  const [studentType, setStudentType] = useState<'all' | 'real' | 'test'>('all');
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);
  const [autoExpand, setAutoExpand] = useState<boolean>(true);

  // Data fetching with SWR
  const { data, error, isMutating, trigger } = useSWRMutation(
    schoolId ? `/api/v1/userSchool/listAllStudent` : null,
    post<SchoolData, { schoolID: number; status: number }>,
  );

  useEffect(() => {
    if (schoolId) trigger({ schoolID: schoolId, status: 0 } as any);
  }, [schoolId]);

  // Sync external value only when it's actually different
  useEffect(() => {
    if (value && JSON.stringify([...value].sort()) !== JSON.stringify([...selectedIds].sort())) {
      setSelectedIds(value);
    }
  }, [value]);

  // Update selected IDs and notify parent
  const updateSelectedIds = useCallback(
    (newIds: number[]) => {
      setSelectedIds(newIds);
      // Only notify parent if there's an actual change
      if (
        onChange &&
        JSON.stringify([...newIds].sort()) !== JSON.stringify([...(value || [])].sort())
      ) {
        onChange(newIds);
      }
    },
    [onChange, value],
  );

  // Transform data to tree format
  const treeData = useMemo(() => {
    console.log('学生接口返回的data', cloneDeep(data));
    if (!data) return [];

    const filterStudents = (students: Student[]) => {
      // 根据合作类型筛选学生
      const cooperationStudents = filterStudentsByCooperationType(students, cooperationType);
      // 根据搜索内容筛选学生
      const searchStudents = cooperationStudents.filter((student) => {
        if (!search) return true;
        return (
          student.userName.includes(search) ||
          student.userNumber.includes(search) ||
          student.userAccount.includes(search)
        );
      });

      return searchStudents.map((student) => ({
        title: `${student.userName} (${student.userNumber})`,
        key: `student-${student.userID}`,
        icon: <UserOutlined />,
        isLeaf: true,
        selectable: true,
        studentInfo: student,
      }));
    };

    const formatClasses = (grade: GradeData) => {
      const list = Array.isArray(grade.classList) ? grade.classList : [];
      return list.map((classItem: ClassData) => {
        console.log(`班级 == ${grade.gradeID} ==>`, classItem);
        return {
          title: classItem.className ?? `班级-${classItem.classID}`,
          key: `grade-${grade.gradeID}-class-${classItem.classID}`,
          selectable: false,
          isTest: classItem.classIsTest === 2,
          children: filterStudents(classItem.studentList),
        };
      });
    };

    const formatGrades = (school: SchoolData) => {
      const list = Array.isArray(school.gradeList) ? school.gradeList : [];
      return list.map((grade) => {
        console.log('年级 ==>', grade);
        return {
          title: grade.gradeName ?? `年级-${grade.gradeID}`,
          key: `grade-${grade.gradeID}`,
          selectable: false,
          children: formatClasses(grade),
        };
      });
    };

    return formatGrades(data);
  }, [data, search, studentType, cooperationType]);

  // Find selected students' information
  const selectedStudents = useMemo(() => {
    if (!data) return [];

    const allGrades: GradeData[] = [...(data.gradeList || [])];

    const result: Student[] = [];
    allGrades.forEach((grade) => {
      grade.classList.forEach((classItem) => {
        classItem.studentList.forEach((student) => {
          if (selectedIds.includes(student.userID)) {
            result.push(student);
          }
        });
      });
    });

    return result;
  }, [data, selectedIds]);

  // 获取节点下所有学生ID的函数
  const getStudentIdsFromNode = (nodeKey: string, treeData: any[]): number[] => {
    const studentIds: number[] = [];

    const findNode = (nodes: any[], targetKey: string): any => {
      for (const node of nodes) {
        if (node.key === targetKey) {
          return node;
        }
        if (node.children) {
          const found = findNode(node.children, targetKey);
          if (found) return found;
        }
      }
      return null;
    };

    const collectStudentIds = (node: any) => {
      if (node.isLeaf && node.studentInfo) {
        studentIds.push(node.studentInfo.userID);
      } else if (node.children) {
        node.children.forEach(collectStudentIds);
      }
    };

    const targetNode = findNode(treeData, nodeKey);
    if (targetNode) {
      collectStudentIds(targetNode);
    }

    return studentIds;
  };

  // 处理全选逻辑
  const handleSelectAll = (nodeKey: string) => {
    const nodeStudentIds = getStudentIdsFromNode(nodeKey, treeData);

    // 检查是否所有学生都已选中
    const allSelected = nodeStudentIds.every((id) => selectedIds.includes(id));

    if (allSelected) {
      // 如果都已选中，则取消选中
      const newIds = selectedIds.filter((id) => !nodeStudentIds.includes(id));
      updateSelectedIds(newIds);
    } else {
      // 否则选中所有学生
      const newIds = [...selectedIds];
      nodeStudentIds.forEach((id) => {
        if (!newIds.includes(id)) {
          newIds.push(id);
        }
      });
      updateSelectedIds(newIds);
    }
  };

  // Handle tree node selection
  const handleSelect = (_: React.Key[], info: EventInfo) => {
    if (info.node.isLeaf && info.node.studentInfo) {
      const studentId = info.node.studentInfo.userID;
      const newIds = selectedIds.includes(studentId)
        ? selectedIds.filter((id) => id !== studentId)
        : [...selectedIds, studentId];
      updateSelectedIds(newIds);
    }
  };

  // Handle search input change
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearch(value);

    if (value && data) {
      // Expand all nodes when searching
      const keys: React.Key[] = [];
      const allGrades = [...(data.gradeList || [])];

      allGrades.forEach((grade) => {
        keys.push(`grade-${grade.gradeID}`);
        grade.classList.forEach((classItem) => {
          keys.push(`grade-${grade.gradeID}-class-${classItem.classID}`);
        });
      });

      setExpandedKeys(keys);
      setAutoExpand(true);
    } else {
      setExpandedKeys([]);
    }
  };

  return (
    <div className="w-full student-selector">
      <ProFormDependency name={['search']}>
        {() => {
          return (
            <Spin spinning={isMutating}>
              <Space direction="vertical" className="w-full" size="middle">
                {/* Search and filter toolbar */}
                <div className="flex justify-between items-center gap-4 flex-wrap">
                  <ProFormText
                    width="md"
                    placeholder="搜索姓名或学号..."
                    fieldProps={{
                      onChange: handleSearch,
                      value: search,
                      prefix: <SearchOutlined />,
                      allowClear: true,
                      className: 'min-w-[250px] flex-1 rounded-lg border-gray-300',
                    }}
                  />

                  <ProFormRadio.Group
                    name="studentType"
                    options={[
                      { label: '全部学生', value: 'all' },
                      { label: '正式学生', value: 'real' },
                      { label: '测试学生', value: 'test' },
                    ]}
                    fieldProps={{
                      onChange: (e) => {
                        setStudentType(e.target.value);
                        // form.setFieldsValue({
                        //   studentType: e.target.value
                        // })
                      },
                      optionType: 'button',
                      buttonStyle: 'solid',
                    }}
                  ></ProFormRadio.Group>
                </div>

                {/* Main content area */}
                <div className="flex gap-5 md:flex-row flex-col">
                  {/* Left: Tree selection */}
                  <Card
                    styles={{
                      body: {
                        padding: 10,
                      },
                    }}
                    className="flex-1 min-h-[320px] max-h-[500px] overflow-auto shadow-sm rounded-lg"
                    title={
                      <div className="flex items-center justify-between">
                        <Title level={5} className="m-0">
                          班级学生列表
                        </Title>
                        {treeData.length > 0 && (
                          <Text type="secondary" className="text-xs">
                            点击选择学生
                          </Text>
                        )}
                      </div>
                    }
                  >
                    {error ? (
                      <Empty description="加载失败，请重试" />
                    ) : treeData.length > 0 ? (
                      <Tree
                        showLine={{ showLeafIcon: false }}
                        treeData={treeData}
                        onSelect={handleSelect}
                        expandedKeys={expandedKeys}
                        autoExpandParent={autoExpand}
                        onExpand={(keys) => {
                          setExpandedKeys(keys);
                          setAutoExpand(false);
                        }}
                        blockNode={true}
                        className="clean-tree"
                        titleRender={(nodeData: any) => {
                          if (nodeData.isLeaf && nodeData.studentInfo) {
                            const student = nodeData.studentInfo;
                            const isSelected = selectedIds.includes(student.userID);
                            return (
                              <div
                                className={`flex items-center py-1 px-1.5 rounded transition-all ${isSelected ? 'bg-blue-50' : ''}`}
                              >
                                {isSelected ? (
                                  <CheckOutlined className="mr-1.5 text-blue-500 text-xs" />
                                ) : (
                                  <UserOutlined className="mr-1.5 text-gray-400 text-xs" />
                                )}
                                <span className={`${isSelected ? 'text-blue-500' : ''}`}>
                                  {student.userName}
                                </span>
                                {/* <span className="text-gray-400 mx-1 text-xs">{student.userNumber}</span> */}
                                {student.userIsTest === StudentIsTest.YES && (
                                  <Tag color="blue" className="compact-tag ml-2">
                                    测试
                                  </Tag>
                                )}
                                {student.userStatus === StudentStatus.TRIAL && (
                                  <Tag color="orange" className="compact-tag">
                                    试用中
                                  </Tag>
                                )}
                                {student.userStatus === StudentStatus.PAID && (
                                  <Tag color="purple" className="compact-tag">
                                    付费
                                  </Tag>
                                )}
                                {student.userStatus === StudentStatus.DISABLED && (
                                  <Tag color="red" className="compact-tag">
                                    停用
                                  </Tag>
                                )}
                              </div>
                            );
                          }
                          // 非叶子节点（年级或班级）
                          const nodeStudentIds = getStudentIdsFromNode(nodeData.key, treeData);
                          // console.log('nodeStudentIds', nodeData, nodeStudentIds);
                          const allSelected =
                            nodeStudentIds.length > 0 &&
                            nodeStudentIds.every((id) => selectedIds.includes(id));
                          const hasStudents = nodeStudentIds.length > 0;

                          return (
                            <div className="text-gray-700 flex items-center gap-2 justify-between w-full">
                              <span>{nodeData.title}</span>
                              <div className="flex items-center gap-2">
                                {nodeData.isTest && (
                                  <Tag
                                    color="blue"
                                    className="compact-tag"
                                    style={{
                                      width: '60px',
                                      display: 'inline-block',
                                      textAlign: 'center',
                                    }}
                                  >
                                    测试
                                  </Tag>
                                )}
                                {hasStudents && (
                                  <Button
                                    size="small"
                                    type="text"
                                    className={`text-xs px-2 py-0 h-6 ${
                                      allSelected
                                        ? 'text-orange-600 hover:text-orange-700 hover:bg-orange-50'
                                        : 'text-blue-600 hover:text-blue-700 hover:bg-blue-50'
                                    }`}
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleSelectAll(nodeData.key);
                                    }}
                                  >
                                    {allSelected ? '取消全选' : '全选'}
                                  </Button>
                                )}
                              </div>
                            </div>
                          );
                        }}
                      />
                    ) : (
                      <Empty description="没有找到符合条件的学生" />
                    )}
                  </Card>

                  {/* Right: Selected students */}
                  <Card
                    styles={{
                      body: {
                        padding: 10,
                      },
                    }}
                    className="w-full md:w-[320px] min-h-[320px] max-h-[500px] overflow-auto shadow-sm rounded-lg"
                    title={
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <Title level={5} className="!m-0">
                            已选学生
                          </Title>
                          <span className="ml-2 bg-green-500 text-white rounded-full h-5 min-w-[20px] inline-flex items-center justify-center text-xs px-1">
                            {selectedIds.length}
                          </span>
                        </div>
                        {selectedIds.length > 0 && (
                          <Button
                            type="text"
                            icon={<DeleteOutlined />}
                            onClick={() => updateSelectedIds([])}
                            className="text-red-500 hover:bg-red-50 hover:text-red-600 text-xs px-2"
                          >
                            清空
                          </Button>
                        )}
                      </div>
                    }
                  >
                    {selectedStudents.length > 0 ? (
                      <div className="grid grid-cols-1 gap-2 py-1">
                        {selectedStudents.map((student) => (
                          <div
                            key={student.userID}
                            className="flex items-center px-3 py-1 bg-gray-50 hover:bg-gray-100 rounded transition-all group"
                          >
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center">
                                <Space direction="vertical" className="flex-1">
                                  <Flex align="center" gap={10}>
                                    <Text strong className="truncate flex">
                                      {student.userName}
                                    </Text>
                                    {student.userIsTest === 2 && (
                                      <Tag color="blue" className="ml-1 px-1 h-4 leading-4 text-xs">
                                        测试
                                      </Tag>
                                    )}
                                  </Flex>

                                  <Text type="secondary" className="text-xs block">
                                    ID：{student.userNumber}
                                  </Text>
                                </Space>
                              </div>
                            </div>
                            <Button
                              type="text"
                              size="small"
                              icon={<DeleteOutlined />}
                              onClick={() =>
                                updateSelectedIds(selectedIds.filter((id) => id !== student.userID))
                              }
                              className="text-red-500 opacity-0 group-hover:opacity-100 hover:bg-red-50 p-0 h-6 w-6 flex items-center justify-center"
                            />
                          </div>
                        ))}
                      </div>
                    ) : (
                      <Empty
                        image={Empty.PRESENTED_IMAGE_SIMPLE}
                        description="请从左侧选择学生"
                        className="mt-12"
                      />
                    )}
                  </Card>
                </div>
              </Space>
            </Spin>
          );
        }}
      </ProFormDependency>
    </div>
  );
};

export default StudentSelector;
