import { Outlet, useNavigate } from '@umijs/max';
import { Tabs, TabsProps } from 'antd';
import { useEffect } from 'react';

const SystemPermissionManagement: React.FC = () => {
  const navigate = useNavigate();
  const items: TabsProps['items'] = [
    {
      key: 'user',
      label: '运营后台账号管理',
    },
    {
      key: 'role',
      label: '角色权限',
    },
  ];

  useEffect(() => {
    if (location.pathname === '/system-permission-management') {
      navigate('./user');
    }
    if (location.pathname === '/system-permission-management/role') {
      navigate('./role');
    }
  }, [navigate, location.pathname]);

  return (
    <>
      <Tabs defaultActiveKey="user" activeKey={location.pathname.split('/').pop()} items={items} onChange={(key) => navigate(`./${key}`)} />
      <Outlet />
    </>
  );
};

export default SystemPermissionManagement;
