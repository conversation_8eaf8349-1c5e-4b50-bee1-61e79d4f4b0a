import fetcher from '@/services/fetcher';
import { Menu, PlatformMenus } from '@/types/menu';
import {  RoleMultiPlatformMenu } from '@/types/role';
import { TreeDataNode } from 'antd';
import { createContext, useContext, useEffect, useState } from 'react';
import useSWR from 'swr';
import type { DataNode } from 'antd/es/tree';

interface MenuTree {
  platformName: string;
  menuTree: DataNode[];
  selectedKeys?: string[];
  expandedKeys?: string[];
  menuId?: string;
}

type RoleManagementContextType = {
  platformsMenuTree: MenuTree[];
  platformsMenuList: PlatformMenus[];
  roleList: RoleMultiPlatformMenu[];
  refreshRoleList: () => void;
  selectedRole?: RoleMultiPlatformMenu | null;
  setSelectedRole: (role: RoleMultiPlatformMenu | null | undefined) => void;
  handleMenuSelect: (selectedKeys: string[], info: any) => void;
  handleMenuExpand: (expandedKeys: string[], info: any) => void;
  handleMenuTreeSelect: (menuId: string) => void;
};

export const convertMenusToTreeData = (menus: Menu[]): TreeDataNode[] => {
  if (!menus || menus.length === 0) {
    return [];
  }

  // 创建 id 到菜单项的映射，方便查找
  const menuMap = new Map<number, TreeDataNode>();

  // 第一次遍历：创建所有节点的基本结构
  menus.forEach((menu) => {
    menuMap.set(menu.menuId, {
      key: menu.menuId,
      title: menu.menuName,
      children: [],
      isLeaf: true, // 默认所有节点都是叶子节点
    });
  });

  // 第二次遍历：构建树形结构
  const result: TreeDataNode[] = [];

  menus.forEach((menu) => {
    const currentNode = menuMap.get(menu.menuId);

    if (menu.menuParentId === 0 || !menuMap.has(menu.menuParentId)) {
      // 如果是根节点或父节点不存在，则加入结果数组
      result.push(currentNode!);
    } else {
      // 否则，将当前节点加入到父节点的 children 中
      const parentNode = menuMap.get(menu.menuParentId);
      if (parentNode && parentNode.children) {
        parentNode.children.push(currentNode!);
        // 如果一个节点有子节点，那么它不是叶子节点
        parentNode.isLeaf = false;
      }
    }
  });

  return result;
};

const RoleManagementContext = createContext<RoleManagementContextType>({
  platformsMenuTree: [],
  platformsMenuList: [],
  roleList: [],
  refreshRoleList: () => {},
  selectedRole: null,
  setSelectedRole: () => {},
  handleMenuSelect: () => {},
  handleMenuExpand: () => {},
  handleMenuTreeSelect: () => {},
});

const useRoleManagementContext = () => useContext(RoleManagementContext);

const RoleManagementProvider = ({ children }: { children: React.ReactNode }) => {
  // 获取菜单列表
  const { data: {list:menus} = {list:[]} } = useSWR<API.TableData<PlatformMenus>>('/api/v1/menu/list', fetcher);
  // 获取角色列表
  const { data: {list:roleList} = {list:[]}, mutate: refreshRoleList } = useSWR<API.TableData<RoleMultiPlatformMenu>>('/api/v1/role/list', fetcher);

  const [selectedRole, setSelectedRole] = useState<RoleMultiPlatformMenu | null | undefined>(null);
  // const [loading, setLoading] = useState<boolean>(false);
  const [platformsMenuTree, setPlatformsMenuTree] = useState<MenuTree[]>([]);
  const [platformsMenuList, setPlatformsMenuList] = useState<PlatformMenus[]>([]);

  // console.log({ data, error });
  useEffect(() => {
    console.log('menus', menus);
    
    if (menus) {
      const data = menus as PlatformMenus[];
      const treeData = data?.map((item) => ({
        platformId: item.platformId,
        platformName: item.platformName,
        menuTree: convertMenusToTreeData(item.menus),
      }));
      console.log('treeData', treeData);
      setPlatformsMenuList(data);
      setPlatformsMenuTree(treeData.map(item => ({
        platformName: item.platformName,
        menuTree: item.menuTree,
        selectedKeys: [],
        expandedKeys: [],
      })));
    }
  }, [menus]);

  // 处理菜单选择
  const handleMenuSelect = (selectedKeys: string[], info: any) => {
    const { node } = info;
    const menuId = node.key;
    const isChecked = node.checked;

    // 获取所有菜单树的选择结果
    const allSelectedKeys = platformsMenuTree.map(tree => tree.selectedKeys || []).flat();
    
    // 根据当前操作更新选择结果
    let newSelectedKeys: string[];
    if (isChecked) {
      // 选中时，添加当前节点ID
      newSelectedKeys = [...allSelectedKeys, menuId];
    } else {
      // 取消选中时，移除当前节点ID
      newSelectedKeys = allSelectedKeys.filter(key => key !== menuId);
    }

    // 更新所有菜单树的选择状态
    const updatedMenuTrees = platformsMenuTree.map(tree => ({
      ...tree,
      selectedKeys: newSelectedKeys
    }));

    setPlatformsMenuTree(updatedMenuTrees);
  };

  // 处理菜单展开
  const handleMenuExpand = (expandedKeys: string[], info: any) => {
    const { node } = info;
    const menuId = node.key;

    // 更新所有菜单树的展开状态
    const updatedMenuTrees = platformsMenuTree.map(tree => {
      if (tree.menuId === menuId) {
        return {
          ...tree,
          expandedKeys
        };
      }
      return tree;
    });

    setPlatformsMenuTree(updatedMenuTrees);
  };

  // 处理菜单树选择
  const handleMenuTreeSelect = (menuId: string) => {
    // 更新所有菜单树的选中状态
    const updatedMenuTrees = platformsMenuTree.map(tree => ({
      ...tree,
      selectedKeys: tree.menuId === menuId ? tree.selectedKeys : []
    }));

    setPlatformsMenuTree(updatedMenuTrees);
  };

  const value = {
    platformsMenuList,
    platformsMenuTree,
    roleList: roleList || [],
    refreshRoleList,
    selectedRole,
    setSelectedRole,
    handleMenuSelect,
    handleMenuExpand,
    handleMenuTreeSelect,
  };

  return <RoleManagementContext.Provider value={value}>{children}</RoleManagementContext.Provider>;
};

export { RoleManagementProvider, useRoleManagementContext };

export default RoleManagementContext;
