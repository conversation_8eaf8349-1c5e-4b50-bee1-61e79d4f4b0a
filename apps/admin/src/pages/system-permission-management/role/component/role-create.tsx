import { post } from '@/services/fetcher';
import { Role } from '@/types/role';
import { Form, Input, message, Spin } from 'antd';
import { useCallback, useEffect } from 'react';
import useSWRMutation from 'swr/mutation';
import SubmitButton from '../../component/submit-button';
import { useRoleManagementContext } from '../context';
import EditableMenuTree from './editable-menu-tree';

const RoleCreate: React.FC<{ onSuccess: () => void }> = ({ onSuccess }) => {
  const [messageApi, contextHolder] = message.useMessage();

  const { refreshRoleList } = useRoleManagementContext();
  const { trigger, isMutating, error } = useSWRMutation('/api/v1/role/create', post);

  const [form] = Form.useForm<Role>();

  const submit = useCallback(async () => {
    await trigger(form.getFieldsValue());
    refreshRoleList();
    onSuccess();
  }, [form, onSuccess, trigger]);

  useEffect(() => {
    if (error) {
      messageApi.error(error.message);
    }
  }, [error, messageApi]);

  return (
    <Spin spinning={isMutating} tip="创建中...">
      {contextHolder}
      <Form<Role> layout="vertical" form={form} onFinish={submit}>
        <Form.Item
          label="角色名称"
          name="roleName"
          rules={[{ required: true, message: '请输入角色名称' }]}
        >
          <Input />
        </Form.Item>
        <Form.Item
          label="菜单权限"
          name="menuIds"
          rules={[{ required: true, message: '请选择菜单权限' }]}
        >
          <EditableMenuTree />
        </Form.Item>
        <Form.Item>
          <SubmitButton form={form}>创建</SubmitButton>
        </Form.Item>
      </Form>
    </Spin>
  );
};

export default RoleCreate;
