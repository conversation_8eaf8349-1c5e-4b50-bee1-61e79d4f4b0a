import { post } from '@/services/fetcher';
import { useCallback } from 'react';
import useSWRMutation from 'swr/mutation';
import InfoItem from '../../component/info-item';
import { useRoleManagementContext } from '../context';
import MenuTree from './menu-tree';

const RoleDetail: React.FC<{ isChangeStatus?: boolean; onSuccess?: () => void }> = ({
  isChangeStatus = false,
  onSuccess,
}) => {
  const { selectedRole, refreshRoleList } = useRoleManagementContext();

  const { trigger, isMutating } = useSWRMutation('/api/v1/role/update', post);

  const submit = useCallback(async () => {
    trigger(
      {
        ...selectedRole,
        roleStatus: selectedRole?.roleStatus === 1 ? 0 : 1,
      },
      {
        onSuccess: () => {
          onSuccess?.();
          refreshRoleList();
        },
      },
    );
  }, [selectedRole, trigger]);

  if (!selectedRole) {
    return null;
  }
  return (
    <div className="flex flex-col gap-2">
      <InfoItem label="角色名称">{selectedRole.roleName}</InfoItem>
      <InfoItem label="角色状态">{selectedRole.roleStatus === 1 ? '启用' : '禁用'}</InfoItem>
      <InfoItem label="菜单权限">
        <MenuTree platformCheckedKeys={selectedRole.menuIds} />
      </InfoItem>
      <InfoItem label="创建时间">{selectedRole.createTime}</InfoItem>
      <InfoItem label="更新时间">{selectedRole.updateTime}</InfoItem>

    </div>
  );
};

export default RoleDetail;
