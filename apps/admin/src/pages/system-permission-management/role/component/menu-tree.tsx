import { Tree, TreeProps } from 'antd';
import { useRoleManagementContext } from '../context';
import { PlatformMenu } from '@/types/role';
import { useState, useEffect } from 'react';
import type { Key } from 'react';
import { separateMenuNodes } from '@/pages/partner-school/schoolManagement/components/Permission/hooks/usePermissionData';

interface MenuTreeProps {
  onChange?: (checkedKeys: Key[]) => void;
  defaultExpandAll?: boolean;
  platformCheckedKeys?: PlatformMenu[];
}

const MenuTree: React.FC<MenuTreeProps> = ({
  onChange,
  defaultExpandAll = true,
  platformCheckedKeys,
}) => {
  const { platformsMenuTree, platformsMenuList } = useRoleManagementContext();
  const [platformTreeStates, setPlatformTreeStates] = useState<Record<string, Key[]>>({});

  console.log('platformCheckedKeys',platformCheckedKeys);
  
  // 初始化平台树状态
  useEffect(() => {
    const initialStates: Record<string, Key[]> = {};
    platformCheckedKeys?.forEach(platform => {
      const menuList = platformsMenuList.find(p => p.platformName === platform.platformName)?.menus || [];
      const menuIds = platform.menuIds;
      const { leafNodes } = separateMenuNodes(menuList, menuIds);
      initialStates[platform.platformName] = leafNodes;
    });
    setPlatformTreeStates(initialStates);
    console.log('initialStates',initialStates);
    
  }, [platformsMenuTree, platformsMenuList, platformCheckedKeys]);

  // 处理选中状态变化
  const onCheck: TreeProps['onCheck'] = (_, info) => {
    const { checkedNodes } = info;
    const leafNodeKeys = checkedNodes.filter((node) => node.isLeaf).map((node) => node.key);
    onChange?.(leafNodeKeys);
  };

  return (
    <div className="flex flex-row gap-8">
      {platformsMenuTree.map((platform, index) => {
        const platformState = platformTreeStates[platform.platformName] || [];
        console.log(`platformState${platform.platformName}-${index}`,{ platformState });
        return (
          <div key={`${platform.platformName}-${index}`}>
            <h3>{platform.platformName}</h3>
            <Tree
              showLine
              showIcon={false}
              defaultExpandAll={defaultExpandAll}
              checkable
              checkedKeys={platformState}
              treeData={platform.menuTree}
              onCheck={onCheck}
              className="permission-tree"
            />
          </div>
        );
      })}
    </div>
  );
};

export default MenuTree;
