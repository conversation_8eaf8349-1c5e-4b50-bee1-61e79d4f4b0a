import { Tree } from 'antd';
import { useRoleManagementContext } from '../context';
import { PlatformMenu } from '@/types/role';
import { useState, useEffect } from 'react';
import type { Key } from 'react';
import { separateMenuNodes } from '@/pages/partner-school/schoolManagement/components/Permission/hooks/usePermissionData';

interface EditableMenuTreeProps {
  platformCheckedKeys?: PlatformMenu[];
  onChange?: (checkedKeys: Key[]) => void;
  disabled?: boolean;
}

interface PlatformTreeState {
  [platformName: string]: {
    checked: Key[];
    halfChecked: Key[];
  };
}

const EditableMenuTree: React.FC<EditableMenuTreeProps> = ({
  disabled = false,
  platformCheckedKeys,
  onChange,
}) => {
  const { platformsMenuTree, platformsMenuList } = useRoleManagementContext();
  const [platformTreeStates, setPlatformTreeStates] = useState<PlatformTreeState>({});


  const onTreeChange = (newPlatformTreeStates: PlatformTreeState) => {
    const allCheckedKeys = Object.values(newPlatformTreeStates).reduce<Key[]>((acc, curr) => {
      return [...acc, ...curr.checked, ...curr.halfChecked];
    }, []);
    console.log(`allCheckedKeys`, allCheckedKeys, newPlatformTreeStates);

    // 通知父组件
    if (onChange) {
      onChange([...new Set(allCheckedKeys)]);
    }
  }
  // 初始化平台树状态
  useEffect(() => {
    const initialStates: PlatformTreeState = {};
    platformCheckedKeys?.forEach(platform => {
      const menuList = platformsMenuList.find(p => p.platformName === platform.platformName)?.menus || [];
      const menuIds = platform.menuIds
      const { leafNodes, parentNodes } = separateMenuNodes(menuList, menuIds);

      initialStates[platform.platformName] = {
        checked: leafNodes,
        halfChecked: parentNodes
      };
    });
    setPlatformTreeStates(initialStates);
    onTreeChange(initialStates)
    
    // 组件卸载时清空状态
    return () => {
      setPlatformTreeStates({});
    };
  }, [platformsMenuTree, platformsMenuList, platformCheckedKeys]);


  const onCheck = (checkedKeys: Key[] | { checked: Key[]; halfChecked: Key[] }, info: any, platformName: string) => {
    const checkedKeysArray = Array.isArray(checkedKeys)
      ? checkedKeys
      : checkedKeys.checked;
    const { halfCheckedKeys } = info

    const newPlatformTreeStates = {
      ...platformTreeStates,
      [platformName]: {
        checked: checkedKeysArray,
        halfChecked: halfCheckedKeys
      }
    }
    console.log(` checkedKeysArray`, {checkedKeysArray, info,newPlatformTreeStates} )

    // 更新当前平台的状态
    setPlatformTreeStates(newPlatformTreeStates)
    onTreeChange(newPlatformTreeStates)

  };

  return (
    <div className="flex flex-row gap-8">
      {platformsMenuTree.map((platform, index) => {
        const platformState = platformTreeStates[platform.platformName] || {
          checked: [],
          halfChecked: []
        };

        return (
          <div key={`${platform.platformName}-${index}`} className="flex flex-col gap-2">
            <h4 className="pl-1">{platform.platformName}</h4>
            <Tree
              defaultExpandAll
              showLine
              checkable
              selectable={false}
              disabled={disabled}
              checkedKeys={platformState}
              treeData={platform.menuTree}
              onCheck={(checkedKeys, info) => onCheck(checkedKeys, info, platform.platformName)}
            />
          </div>
        );
      })}
    </div>
  );
};

export default EditableMenuTree;
