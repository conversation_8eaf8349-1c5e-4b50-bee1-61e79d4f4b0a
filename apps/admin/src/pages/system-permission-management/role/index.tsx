import {  RoleMultiPlatformMenu } from '@/types/role';
import { <PERSON><PERSON>, Drawer, Space, Table, TableColumnsType } from 'antd';
import { FC, useCallback, useMemo, useState } from 'react';
import MenuTree from './component/menu-tree';
import RoleCreate from './component/role-create';
import RoleDetail from './component/role-detail';
import RoleEdit from './component/role-edit';
import { RoleManagementProvider, useRoleManagementContext } from './context';
import dayjs from 'dayjs';

enum ActionType {
  CREATE = 0,
  EDIT = 1,
  CHANGE_STATUS = 2,
  DETAIL = 3,
}

const RoleManagement: FC = () => {
  const { roleList, selectedRole, setSelectedRole } = useRoleManagementContext();

  const [openDrawer, setOpenDrawer] = useState(false);
  const [actionType, setActionType] = useState<ActionType>(ActionType.CREATE);
  // const showDrawer = () => {
  //   setOpenDrawer(true);
  // };

  const onCloseDrawer = useCallback(() => {
    setSelectedRole(null);
    setOpenDrawer(false);
  }, [setSelectedRole, setOpenDrawer]);

  const handleAction = useCallback(
    (type: ActionType, role?: RoleMultiPlatformMenu) => {

      setActionType(type);
      setOpenDrawer(true);
      setSelectedRole(role);

    },
    [setActionType, setOpenDrawer, setSelectedRole],
  );

  const drawerTitle = useMemo(() => {
    switch (actionType) {
      case ActionType.CREATE:
        return '创建新角色';
      case ActionType.EDIT:
        return '编辑角色';
      case ActionType.CHANGE_STATUS:
        return selectedRole?.roleStatus === 1 ? '启用角色' : '禁用角色';
      case ActionType.DETAIL:
        return '角色详情';
    }
  }, [actionType]);

  const drawerContent = useMemo(() => {
    switch (actionType) {
      case ActionType.CREATE:
        return <RoleCreate onSuccess={onCloseDrawer} />;
      case ActionType.EDIT:
      case ActionType.CHANGE_STATUS:
        return <RoleEdit onSuccess={onCloseDrawer} isChangeStatus={actionType === ActionType.CHANGE_STATUS} />;
      case ActionType.DETAIL:
        return <RoleDetail />;
    }
  }, [actionType, selectedRole]);

  const columns: TableColumnsType<RoleMultiPlatformMenu> = [
    {
      title: '角色名称',
      dataIndex: 'roleName',
      key: 'roleName',
    },
    {
      title: '启用状态',
      dataIndex: 'roleStatus',
      key: 'roleStatus',
      render: (status) => (status === 1 ? '启用' : '禁用'),
    },
    {
      title: '可见页面 & 功能权限',
      dataIndex: 'menuIds',
      key: 'menuList',
      render: (menuIds) => <MenuTree defaultExpandAll={false} platformCheckedKeys={menuIds} />,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      render: (createTime) => dayjs(createTime).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space direction="horizontal">
          <Button type="link" onClick={() => handleAction(ActionType.EDIT, record)}>
            编辑
          </Button>

          <Button type="link" onClick={() => handleAction(ActionType.CHANGE_STATUS, record)}>
            {record.roleStatus === 1 ? '禁用' : '启用'}
          </Button>

          <Button type="link" onClick={() => handleAction(ActionType.DETAIL, record)}>
            详情
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div className="flex flex-col gap-3 ">
      <div className="flex flex-row items-center justify-end px-6 py-3">
        <Button
          type="primary"
          onClick={() => {
            handleAction(ActionType.CREATE);
          }}
        >
          创建新角色
        </Button>
      </div>
      {drawerContent && (
        <Drawer title={drawerTitle} size="large" open={openDrawer} onClose={onCloseDrawer}>
          {drawerContent}
        </Drawer>
      )}
      <Table rowKey="roleId" dataSource={roleList} columns={columns} />
    </div>
  );
};
const RoleManagementPanel: React.FC = () => {
  return (
    <RoleManagementProvider>
      <RoleManagement />
    </RoleManagementProvider>
  );
};

export default RoleManagementPanel;
