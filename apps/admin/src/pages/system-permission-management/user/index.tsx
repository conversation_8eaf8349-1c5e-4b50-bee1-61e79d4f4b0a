import { Role } from '@/types/role';
import { ManagementUser } from '@/types/user';
import { <PERSON>ton, Drawer, Space, Table, TableColumnsType, Tag } from 'antd/lib';
import { useCallback, useMemo, useState } from 'react';
import UserSearch from './component/search';
import UserChangeStatus from './component/user-change-status';
import UserCreate from './component/user-create';
import UserEdit from './component/user-edit';
import UserManagementProvider, { useUserManagementContext } from './context';
import dayjs from 'dayjs';

enum ActionType {
  CREATE = 0,
  EDIT = 1,
  CHANGE_STATUS = 2,
}

const SystemUserManager: React.FC = () => {
  const {isLoading, userList, userQuery, setUserQuery, selectedUser, setSelectedUser,total } =
    useUserManagementContext();

  const [openDrawer, setOpenDrawer] = useState(false);
  const [actionType, setActionType] = useState<ActionType>(ActionType.CREATE);

  const onCloseDrawer = useCallback(() => {
    setOpenDrawer(false);
  }, [setOpenDrawer]);

  const handleAction = useCallback(
    (type: ActionType, user?: ManagementUser) => {
      setSelectedUser(user);

      setActionType(type);
      setOpenDrawer(true);
    },
    [setActionType, setOpenDrawer, setSelectedUser],
  );

  const drawerTitle = useMemo(() => {
    switch (actionType) {
      case ActionType.CREATE:
        return '添加运营后台新用户';
      case ActionType.EDIT:
        return '编辑运营后台用户';
      case ActionType.CHANGE_STATUS:
        return selectedUser?.userEmploymentStatus === 1 ? '设置离职' : '取消离职';
      default:
        return '';
    }
  }, [actionType]);

  const drawerContent = useMemo(() => {
    switch (actionType) {
      case ActionType.CREATE:
        return <UserCreate onSuccess={onCloseDrawer} />;
      case ActionType.EDIT:
        return <UserEdit onSuccess={onCloseDrawer} />;
      case ActionType.CHANGE_STATUS:
        return <UserChangeStatus onSuccess={onCloseDrawer} />;
    }
  }, [actionType]);

  const columns: TableColumnsType<ManagementUser> = [
    {
      title: '用户名',
      dataIndex: 'userName',
    },
    {
      title: '手机号',
      dataIndex: 'userPhoneNumber',
    },
    {
      title: '角色',
      dataIndex: 'roleList',
      render: (value: Role[], record: ManagementUser) => {
        return record.roles?.map((role) => <Tag key={role.roleId}>{role.roleName}</Tag>)
      },
    },
    {
      title: '状态',
      dataIndex: 'userEmploymentStatus',
      render: (userEmploymentStatus) => (userEmploymentStatus === 1 ? '在职' : '离职'),
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      render: (createTime) => dayjs(createTime).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '操作',
      dataIndex: 'action',
      render: (_, record) => (
        <Space direction="horizontal">
          <Button type="link" onClick={() => handleAction(ActionType.EDIT, record)}>
            编辑
          </Button>
          <Button type="link" onClick={() => handleAction(ActionType.CHANGE_STATUS, record)}>
            {record.userEmploymentStatus === 1 ? '设置离职' : '取消离职'}
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div className="flex flex-col gap-3">
      <UserSearch
        createBtn={
          <Button type="primary" onClick={() => handleAction(ActionType.CREATE)}>
            创建新用户
          </Button>
        }
      />

      {drawerContent && (
        <Drawer title={drawerTitle} open={openDrawer} onClose={onCloseDrawer}>
          {drawerContent}
        </Drawer>
      )}
      <Table
        loading={isLoading}
        rowKey="roleId"
        dataSource={userList}
        columns={columns}
        pagination={{
          total,
          showTotal: (total) => `共${total}条`,
          pageSize: userQuery.pageSize,
          current: userQuery.page,
          onChange: (page, pageSize) => {
            setUserQuery({ ...userQuery, page, pageSize });
          },
        }}
      />
    </div>
  );
};

const SystemUserManagerPanel: React.FC = () => {
  return (
    <UserManagementProvider>
      <SystemUserManager />
    </UserManagementProvider>
  );
};

export default SystemUserManagerPanel;
