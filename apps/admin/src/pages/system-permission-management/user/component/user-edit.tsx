import { post } from '@/services/fetcher';
import { Checkbox, Form, Input, Spin } from 'antd';
import { useCallback, useEffect, useMemo } from 'react';
import useSWRMutation from 'swr/mutation';
import SubmitButton from '../../component/submit-button';
import { useUserManagementContext } from '../context';

interface UserEditForm {
  userName: string;
  userPhoneNumber: string;
  roleIds: number[];
}

const UserEdit: React.FC<{ onSuccess?: () => void }> = ({ onSuccess }) => {
  const { selectedUser, refreshUserList, roleList, setSelectedUser } = useUserManagementContext();

  const { trigger, isMutating } = useSWRMutation('/api/v1/user/update', post);

  const [form] = Form.useForm<UserEditForm>();

  // 从 selectedUser 中提取角色 ID 数组
  const initialRoleIds = useMemo(() => {
    return selectedUser?.roles?.map(role => role.roleId) || [];
  }, [selectedUser]);

  // 当 selectedUser 变化时，重置表单数据
  useEffect(() => {
    if (selectedUser) {
      form.setFieldsValue({
        userName: selectedUser.userName,
        userPhoneNumber: selectedUser.userPhoneNumber,
        roleIds: initialRoleIds
      });
    }
  }, [selectedUser, form, initialRoleIds]);

  const roleOptions = useMemo(() => {
    return roleList.map((role) => ({
      label: role.roleName,
      value: role.roleId,
    }));
  }, [roleList]);

  const submit = useCallback(async () => {
    const formValues = form.getFieldsValue();
    trigger({
      ...selectedUser,
      userId: selectedUser?.userId,
      userName: formValues.userName,
      userPhoneNumber: formValues.userPhoneNumber,
      roleIds: formValues.roleIds
    }, {
      onSuccess: () => {
        setSelectedUser(null);
        onSuccess?.();
        refreshUserList();
      },
    });
  }, [form, onSuccess, refreshUserList, selectedUser, setSelectedUser, trigger]);

  if (!selectedUser) {
    return null;
  }

  return (
    <Spin spinning={isMutating} tip="保存中...">
      <h4 className="font-bold mb-3">基本信息</h4>
      <Form<UserEditForm>
        layout="vertical"
        form={form}
        onFinish={submit}
      >
        <Form.Item
          label="用户名"
          name="userName"
          rules={[{ required: true, message: '请输入用户名' }]}
        >
          <Input />
        </Form.Item>
        <Form.Item
          label="手机号"
          name="userPhoneNumber"
          rules={[{ required: true, message: '请输入手机号' }]}
        >
          <Input />
        </Form.Item>

        <h4 className="font-bold mb-3">权限信息</h4>
        <Form.Item
          label="选取角色"
          name="roleIds"
          rules={[{ required: true, message: '请选择角色' }]}
        >
          <Checkbox.Group options={roleOptions} />
        </Form.Item>
        <Form.Item>
          <SubmitButton form={form}>保存</SubmitButton>
        </Form.Item>
      </Form>
    </Spin>
  );
};

export default UserEdit;
