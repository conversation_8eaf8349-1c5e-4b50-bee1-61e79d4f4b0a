import { post } from '@/services/fetcher';
import { Checkbox, Form, Input, message, Spin } from 'antd';
import { useCallback, useEffect, useMemo } from 'react';
import useSWRMutation from 'swr/mutation';
import SubmitButton from '../../component/submit-button';
import { useUserManagementContext } from '../context';
// import { useRoleManagementContext } from '../context';

interface ManagementUserCreateForm {
  userName: string;
  userPhoneNumber: string;
  roleIds: number[];
}

const UserCreate: React.FC<{ onSuccess: () => void }> = ({ onSuccess }) => {
  //TODO: 移动到外层处理错误
  const [messageApi, contextHolder] = message.useMessage();
  const { roleList, refreshUserList } = useUserManagementContext();
  const { trigger, isMutating, error } = useSWRMutation('/api/v1/user/create', post);

  const [form] = Form.useForm<ManagementUserCreateForm>();

  const submit = useCallback(async () => {
    console.log(form.getFieldsValue());
    trigger(form.getFieldsValue(), {
      onSuccess: () => {
        onSuccess();
        refreshUserList();
      },
    });
  }, [form, onSuccess, trigger]);

  const roleOptions = useMemo(() => {
    return roleList.map((role) => ({
      label: role.roleName,
      value: role.roleId,
    }));
  }, [roleList]);

  useEffect(() => {
    console.log({ error });
    if (error) {
      console.log({ error });
      messageApi.error(error.message);
    }
  }, [error, messageApi]);

  return (
    <Spin spinning={isMutating} tip="创建中...">
      {contextHolder}
      <Form<ManagementUserCreateForm> layout="vertical" form={form} onFinish={submit}>
        <h4 className="font-bold mb-3">基本信息</h4>
        <Form.Item
          label="用户名"
          name="userName"
          rules={[{ required: true, message: '请输入用户名' }]}
        >
          <Input />
        </Form.Item>
        <Form.Item
          label="手机号"
          name="userPhoneNumber"
          rules={[
            { required: true, message: '请输入手机号' },
            { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号格式' }
          ]}
        >
          <Input />
        </Form.Item>

        <h4 className="font-bold mb-3">权限信息</h4>
        <Form.Item
          label="选取角色"
          name="roleIds"
          rules={[{ required: true, message: '请选择角色' }]}
        >
          <Checkbox.Group options={roleOptions} />
        </Form.Item>

        <Form.Item>
          <SubmitButton form={form}>创建</SubmitButton>
        </Form.Item>
      </Form>
    </Spin>
  );
};

export default UserCreate;
