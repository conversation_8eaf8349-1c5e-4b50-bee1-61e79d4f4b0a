import { Button, Form, Input, Select } from 'antd';
import { ReactNode, useCallback, useMemo } from 'react';
import { DefaultUserQuery, UserQuery, useUserManagementContext } from '../context';

const UserSearch: React.FC<{ createBtn: ReactNode }> = ({ createBtn }) => {
  const { roleList, userQuery, setUserQuery } = useUserManagementContext();

  const [form] = Form.useForm<UserQuery>();

  const search = useCallback(async () => {
    setUserQuery({ ...userQuery, ...form.getFieldsValue(), page: 1 });
  }, [form, setUserQuery, userQuery]);

  const roleOptions = useMemo(() => {
    return roleList.map((role) => ({ label: role.roleName, value: role.roleId }));
  }, [roleList]);

  return (
    <Form<UserQuery>
      className="flex flex-col gap-3 rounded-lg bg-white p-4"
      form={form}
      initialValues={DefaultUserQuery}
    >
      <div className="w-full flex flex-row items-center justify-center gap-4">
        <Form.Item
          className="w-1/4"
          label="用户名"
          name="userName"
          rules={[{ message: '请输入用户名', required: false }]}
        >
          <Input placeholder="请输入用户名" />
        </Form.Item>
        <Form.Item
          className="w-1/4"
          label="用户手机号"
          name="userPhoneNumber"
          rules={[{ message: '请输入用户手机号', required: false }]}
        >
          <Input placeholder="请输入用户手机号" />
        </Form.Item>
        <Form.Item
          className="w-1/4"
          label="角色"
          name="roleId"
          rules={[{ message: '请选择角色', required: false }]}
        >
          <Select placeholder="请选择角色" options={roleOptions} />
        </Form.Item>
        <Form.Item
          className="w-1/4"
          label="在职状态"
          name="userEmploymentStatus"
          rules={[{ message: '请选择在职状态', required: false }]}
        >
          <Select
            placeholder="请选择状态"
            options={[
              { label: '全部', value: 0 },
              { label: '在职', value: 1 },
              { label: '离职', value: 2 },
            ]}
          />
        </Form.Item>
      </div>

      <div className="flex flex-row items-center justify-end gap-4 py-3">
        <Button type="default" onClick={() => form.resetFields()}>
          重置
        </Button>

        <Button type="primary" onClick={search}>
          查询
        </Button>
        {createBtn}
      </div>
    </Form>
  );
};

export default UserSearch;
