import { post } from '@/services/fetcher';
import { Button } from 'antd';
import { useCallback } from 'react';
import useSWRMutation from 'swr/mutation';
import InfoItem from '../../component/info-item';
import { useUserManagementContext } from '../context';

const UserChangeStatus: React.FC<{ onSuccess: () => void }> = ({ onSuccess }) => {
  const { selectedUser, refreshUserList } = useUserManagementContext();

  const { trigger, isMutating } = useSWRMutation('/api/v1/user/update', post);

  const submit = useCallback(async () => {
    trigger(
      { ...selectedUser, userEmploymentStatus: selectedUser?.userEmploymentStatus === 1 ? 0 : 1 },
      {
        onSuccess: () => {
          onSuccess();
          refreshUserList();
        },
      },
    );
  }, [selectedUser, trigger]);

  if (!selectedUser) {
    return null;
  }

  return (
    <div className="flex flex-col gap-2">
      <InfoItem label="用户名">{selectedUser.userName}</InfoItem>
      <InfoItem label="手机号">{selectedUser.userPhoneNumber}</InfoItem>
      <InfoItem label="角色">
        {selectedUser.roles.map((role) => (
          <div key={role.roleId}>{role.roleName}</div>
        ))}
      </InfoItem>
      <InfoItem label="创建时间">{selectedUser.createTime}</InfoItem>
      <InfoItem label="更新时间">{selectedUser.updateTime}</InfoItem>

      <div className="flex w-full items-center justify-center">
        <Button
          loading={isMutating}
          type="primary"
          className="mt-6 w-[120px]"
          danger={selectedUser.userEmploymentStatus === 1}
          onClick={submit}
        >
          {selectedUser.userEmploymentStatus === 1 ? '禁用' : '启用'}
        </Button>
      </div>
    </div>
  );
};

export default UserChangeStatus;
