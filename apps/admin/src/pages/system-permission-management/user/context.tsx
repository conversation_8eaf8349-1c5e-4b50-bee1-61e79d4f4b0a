import fetcher, { search } from '@/services/fetcher';
import type { Role } from '@/types/role';
import { ManagementUser } from '@/types/user';
import { createContext, useContext, useState } from 'react';
import useSWR from 'swr';

export type UserQuery = {
  userName?: string;
  userPhoneNumber?: string;
  userEmploymentStatus?: number;
  roleIds?: number[];
  page?: number;
  pageSize?: number;
};

export const DefaultUserQuery: UserQuery = {
  pageSize: 20,
  page: 1,
};

type UserManagementContextType = {
  roleList: Role[];
  userQuery: UserQuery;
  setUserQuery: (params: UserQuery) => void;
  userList: ManagementUser[];
  refreshUserList: () => void;
  selectedUser: ManagementUser | null | undefined;
  setSelectedUser: (user: ManagementUser | null | undefined) => void;
  isLoading: boolean;
  total: number;
};

const UserManagementContext = createContext<UserManagementContextType>({
  roleList: [],
  userQuery: DefaultUserQuery,
  setUserQuery: () => {},
  userList: [],
  refreshUserList: () => {},
  selectedUser: null,
  setSelectedUser: () => { },
  isLoading: false,
  total: 0,
});

export const useUserManagementContext = () => {
  return useContext(UserManagementContext);
};

const UserManagementProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [userQuery, setUserQuery] = useState<UserQuery>({
    pageSize: 20,
    page: 1,
  });

  const { data: {list:roleList} = {list:[]} } = useSWR<API.TableData<Role>>('/api/v1/role/list', fetcher);

  const { data: {list:userList,total} = {list:[],total:0}, mutate: refreshUserList, isLoading } = useSWR<API.TableData<ManagementUser>>(
    { url: '/api/v1/user/list', query: userQuery },
    search,
  );
  const [selectedUser, setSelectedUser] = useState<ManagementUser | null | undefined>(null);

  const value = {
    roleList: roleList || [],
    userQuery,
    setUserQuery,
    userList: userList || [],
    refreshUserList,
    selectedUser,
    setSelectedUser,
    isLoading,
    total,
  };

  return <UserManagementContext.Provider value={value}>{children}</UserManagementContext.Provider>;
};

export default UserManagementProvider;
