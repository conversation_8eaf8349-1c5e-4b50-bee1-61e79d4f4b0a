import { Button, Form, FormInstance } from 'antd';
import { useEffect, useState } from 'react';

const SubmitButton: React.FC<{
  form: FormInstance;
  children: React.ReactNode;
}> = ({ form, children }) => {
  const [submittable, setSubmittable] = useState<boolean>(false);
  // Watch all values
  const values = Form.useWatch([], form);

  useEffect(() => {
    form
      .validateFields({ validateOnly: true })
      .then(() => setSubmittable(true))
      .catch(() => setSubmittable(false));
  }, [form, values]);

  return (
    <div className="flex justify-center w-full">
      <Button
        className="w-[120px]"
        type="primary"
        disabled={!submittable}
        onClick={() => {
          form.submit();
        }}
      >
        {children}
      </Button>
    </div>
  );
};

export default SubmitButton;
