import React, { useState } from 'react';
import { Modal, Breadcrumb } from 'antd';
import { ApprovalProvider } from './context';
import ApprovalInfo from './components/ApprovalInfo';
// import FlowInfo from './components/FlowInfo';
// import ApprovalAction from './components/ApprovalAction';
import AccountList from './components/AccountList';

const ApprovalDetail: React.FC = () => {
  const location = window.location;
  const searchParams = new URLSearchParams(location.search);
  const approvalID = searchParams.get('approvalID') || '';
  const schoolID = searchParams.get('schoolID') || '';
  
  const [accountModalVisible, setAccountModalVisible] = useState(false);

  // 查看账号详情
  const handleViewAccounts = () => {
    setAccountModalVisible(true);
  };

  // 面包屑配置
  const breadcrumbItems = [
    { title: '审批' },
    { title: '审批详情' }
  ];

  return (
    <div className='bg-gray-50 min-h-screen w-full'>
      {/* 面包屑 */}
      <Breadcrumb items={breadcrumbItems} className='p-3 px-6 text-gray-600' />
      <ApprovalProvider approvalId={Number(approvalID)} schoolID={Number(schoolID)}>
        <div className='flex flex-col gap-3 px-4 md:px-6 pb-6'>
          <div className='flex-1'>
            <ApprovalInfo onViewAccounts={handleViewAccounts} />
          </div>

          {/* 账号列表弹窗 */}
          <Modal
            title="账号详情"
            open={accountModalVisible}
            onCancel={() => setAccountModalVisible(false)}
            footer={null}
            width={window.innerWidth < 768 ? '95%' : 800}
            centered
            className="approval-detail-modal"
          >
            <AccountList />
          </Modal>
        </div>
      </ApprovalProvider>
    </div>
  );
};

export default ApprovalDetail; 