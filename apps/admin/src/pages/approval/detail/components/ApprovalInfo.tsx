// ApprovalInfo.tsx
import React from 'react';
import { useApproval } from '../context';
import dayjs from 'dayjs';

interface ApprovalInfoProps {
  onViewAccounts: () => void;
}

// Helper Components
const InfoGrid: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <div className="grid gap-4 rounded-xl bg-slate-50 p-4">{children}</div>
);

const InfoItem: React.FC<{ label: string; value: React.ReactNode }> = ({ label, value }) => (
  <div className="flex items-center justify-between">
    <span className="text-sm text-slate-500">{label}</span>
    <span className="font-medium text-slate-900">{value || '-'}</span>
  </div>
);

const CalendarIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
  </svg>
);

const TimeRange: React.FC<{ start: string; end: string }> = ({ start, end }) => (
  <div className="space-y-3 rounded-xl bg-slate-50 p-4">
    <h3 className="text-sm font-medium text-slate-500">有效期</h3>
    <div className="flex flex-col space-y-2 font-medium text-slate-900">
      <div className="flex items-center space-x-2">
        <CalendarIcon className="h-4 w-4 text-slate-400" />
        <span>{start || '未设置'}</span>
      </div>
      <div className="flex items-center space-x-2">
        <CalendarIcon className="h-4 w-4 text-slate-400" />
        <span>{end || '未设置'}</span>
      </div>
    </div>
  </div>
);

// Data Parsing
const getTypeLabel = (type: number) => {
  const typeMap: Record<number, string> = {
    1: '试用流程',
    2: '试用延期',
    3: '付费流程',
    4: '停用流程'
  };
  return typeMap[type] || '未知类型';
};

const parseParams = (params: string) => {
  try {
    const detail = JSON.parse(params);
    return {
      schoolName: detail.schoolName || '未知学校',
      typeLabel: getTypeLabel(detail.type),
      startTime: detail.startTime ? dayjs(detail.startTime*1000).format('YYYY-MM-DD') : '',
      endTime: detail.endTime ? dayjs(detail.endTime*1000).format('YYYY-MM-DD') : '',
      periodTime: detail.periodTime ? String(Math.floor(Number(detail.periodTime) / (24 * 60 * 60))) : '0',
      studentCount: detail.studentIds?.split(',').length || 0,
      desc: detail.desc || ''
    };
  } catch (error) {
    console.error('参数解析错误:', error);
    return {
      schoolName: '',
      typeLabel: '未知类型',
      startTime: '',
      endTime: '',
      periodTime: '0',
      studentCount: 0,
      desc: ''
    };
  }
};

const statusConfig = {
  0: { text: '待审批', className: 'bg-blue-100 text-blue-800 border border-blue-200 font-medium' },
  1: { text: '已通过', className: 'bg-green-100 text-green-800 border border-green-200 font-medium' },
  2: { text: '已驳回', className: 'bg-red-100 text-red-800 border border-red-200 font-medium' },
  3: { text: '已撤销', className: 'bg-slate-100 text-slate-800 border border-slate-200 font-medium' }
};

const ApprovalInfo: React.FC<ApprovalInfoProps> = ({ onViewAccounts }) => {
  const { approvalDetail, loading } = useApproval();

  if (loading) {
    return (
      <div className="flex h-48 items-center justify-center">
        <div className="h-8 w-8 animate-spin rounded-full border-2 border-slate-300 border-t-slate-600"/>
      </div>
    );
  }

  if (!approvalDetail) {
    return (
      <div className="flex h-48 flex-col items-center justify-center space-y-3 text-slate-400">
        <svg className="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"/>
        </svg>
        <span className="text-sm">暂无审批信息</span>
      </div>
    );
  }

  const { execute_params, approver_result } = approvalDetail;
  const detailData = parseParams(execute_params);

  return (
    <div className="overflow-hidden rounded-xl bg-white shadow-md ring-1 ring-slate-100">
      {/* Header */}
      <div className="flex items-center justify-between bg-white px-5 py-4">
        <h2 className="text-lg font-semibold text-slate-900">审批详情-{detailData.schoolName}</h2>
        <span className={`rounded-full px-3 py-1 text-sm ${statusConfig[approver_result as keyof typeof statusConfig]?.className}`}>
          {statusConfig[approver_result as keyof typeof statusConfig]?.text}
        </span>
      </div>

      {/* Content */}
      <div className="grid grid-cols-1 gap-6 p-5 md:grid-cols-2">
        <InfoGrid>
          <InfoItem label="合作类型" value={detailData.typeLabel} />
          <InfoItem label="持续时间" value={`${detailData.periodTime}天`} />
          <InfoItem label="覆盖账号" value={
            <button 
              type="button"
              onClick={onViewAccounts}
              className="group flex items-center space-x-1 text-blue-600 hover:text-blue-800"
            >
              <span>{detailData.studentCount}个账户</span>
              <svg className="h-4 w-4 transition-transform group-hover:translate-x-0.5" 
                fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7"/>
              </svg>
            </button>
          }/>
        </InfoGrid>

        <TimeRange start={detailData.startTime} end={detailData.endTime} />

        <div className="col-span-full space-y-3">
          <h3 className="text-sm font-medium text-slate-500">申请说明</h3>
          <p className="rounded-lg bg-slate-50 p-4 text-slate-700 leading-relaxed">
            {detailData.desc || '暂无附加说明'}
          </p>
        </div>
      </div>
    </div>
  );
};

export default ApprovalInfo;