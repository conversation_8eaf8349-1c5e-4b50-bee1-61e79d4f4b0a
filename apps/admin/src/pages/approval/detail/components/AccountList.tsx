import React from 'react';
import { useApproval } from '../context';

const AccountList: React.FC = () => {
  const { loading, accountList } = useApproval();

  if (loading) {
    return (
      <div className="flex justify-center items-center py-8">
        <div className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  if (!accountList?.length) {
    return (
      <div className="flex flex-col items-center justify-center py-10 text-slate-500">
        <svg className="w-16 h-16 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
        </svg>
        <span>暂无账号数据</span>
      </div>
    );
  }

  return (
    <div className="mt-4">
      {/* PC 视图保持不变 */}
      <div className="hidden md:block overflow-x-auto bg-white rounded-lg shadow-sm border border-slate-200">
        <table className="min-w-full divide-y divide-slate-200">
          <thead className="bg-slate-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">姓名</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">用户名</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">学号</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">年级</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">班级</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-slate-200">
            {accountList.map((account) => (
              <tr key={account.userID} className="hover:bg-slate-50">
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-slate-800">{account.userName}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-slate-600">{account.userAccount}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-slate-600">{account.userNumber}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-slate-600">{account.gradeName}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-slate-600">{account.className}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* 优化后的移动端视图 */}
      <div className="md:hidden space-y-4">
        {accountList.map((account) => (
          <div key={account.userID} className="bg-white rounded-lg overflow-hidden shadow-sm border border-slate-200">
            <div className="px-4 py-3 bg-slate-50 border-b border-slate-200">
              <div className="flex items-center justify-between">
                <span className="font-medium text-slate-900">{account.userName}</span>
                <span className="text-sm text-slate-500 bg-white px-3 py-1 rounded-full shadow-sm">
                 ID: {account.userNumber}
                </span>
              </div>
            </div>
            <div className="px-4 py-3 space-y-2">
              <div className="flex items-center text-sm">
                <span className="w-16 text-slate-500">用户名</span>
                <span className="text-slate-900">{account.userAccount}</span>
              </div>
              <div className="flex items-center text-sm">
                <span className="w-16 text-slate-500">年级</span>
                <span className="text-slate-900">{account.gradeName}</span>
              </div>
              <div className="flex items-center text-sm">
                <span className="w-16 text-slate-500">班级</span>
                <span className="text-slate-900">{account.className}</span>
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="mt-4 text-sm text-blue-600 text-center">
        共 <span className="font-medium">{accountList.length}</span> 条记录
      </div>
    </div>
  );
};

export default AccountList;