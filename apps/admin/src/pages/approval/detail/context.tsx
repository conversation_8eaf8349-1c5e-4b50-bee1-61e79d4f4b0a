import React, { createContext, useContext, useEffect, useMemo, useState } from 'react';
import useSWR from 'swr';
import useSWRMutation from 'swr/mutation';
import fetcher, { post } from '@/services/fetcher';

export interface ApprovalDetail {
  approve_id: number;
  approval_code: string;
  instance_code: string;
  approver_result: number;
  execute_params: string;
  approve_user: number;
  approval_time: string;
}

export interface AccountInfo {
  userNumber: string;
  userName: string;
  userIsTest: number;
  userGrade: number;
  gradeName: string;
  userClass: number;
  className: string;
  userStatus: number;
  userID: number;
  userAccount: string;
  userPassword: string;
}
interface ApprovalContextType {
  accountList: AccountInfo[];
  // 审批详情数据
  approvalDetail: ApprovalDetail;
  // 账号列表
  getStudentList: (params: { studentIDs: string, schoolID: number }) => Promise<AccountInfo[]>;
  // 加载状态
  loading: boolean;
}

const ApprovalContext = createContext<ApprovalContextType | undefined>(undefined);

export const ApprovalProvider: React.FC<{
  children: React.ReactNode;
  approvalId: number;
  schoolID: number;
}> = ({ children, approvalId, schoolID }) => {
  const [accountList, setAccountList] = useState<AccountInfo[]>([]);
  // 从路由中获取 idle
  // 获取审批详情
  const { data: approvalList, isLoading } = useSWR(
    approvalId ? `/api/v1/internalApi/list?approvalID=${approvalId}&schoolID=${schoolID}` : null,
    fetcher<ApprovalDetail[]>
  );
  const approvalDetail = useMemo(() => approvalList?.[0], [approvalList]) as ApprovalDetail;

  // 获取账号列表
  const { trigger: getStudentList } = useSWRMutation(
    approvalId ? `/api/v1/internalApi/listAllStudent?approvalId=${approvalId}` : null,
    post<AccountInfo[], { studentIDs: string, schoolID: number }>
  );

  useEffect(() => {
    if (approvalDetail?.execute_params) {
      const studentIds = JSON.parse(approvalDetail.execute_params).studentIds;
      getStudentList({ studentIDs: studentIds, schoolID: schoolID }).then((res) => {
        setAccountList(res || []);
      });
    }
  }, [approvalDetail, getStudentList]);



  const values = useMemo(() => ({
    approvalDetail,
    accountList,
    getStudentList,
    loading: isLoading,
  }), [approvalDetail, accountList, isLoading]);
  return (
    <ApprovalContext.Provider
      value={values}
    >
      {children}
    </ApprovalContext.Provider>
  );
};

export const useApproval = () => {
  const context = useContext(ApprovalContext);
  if (context === undefined) {
    throw new Error('useApproval must be used within an ApprovalProvider');
  }
  return context;
};