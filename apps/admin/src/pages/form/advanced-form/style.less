@import '~antd/es/style/themes/default.less';

.card {
  margin-bottom: 24px;

  :global {
    .ant-legacy-form-item .ant-legacy-form-item-control-wrapper {
      width: 100%;
    }
  }
}

.errorIcon {
  margin-right: 24px;
  color: @error-color;
  cursor: pointer;

  span.anticon {
    margin-right: 4px;
  }
}

.errorPopover {
  :global {
    .ant-popover-inner-content {
      min-width: 256px;
      max-height: 290px;
      padding: 0;
      overflow: auto;
    }
  }
}

.errorListItem {
  padding: 8px 16px;
  list-style: none;
  border-bottom: 1px solid @border-color-split;
  cursor: pointer;
  transition: all 0.3s;
  &:hover {
    background: @item-active-bg;
  }
  &:last-child {
    border: 0;
  }
  .errorIcon {
    float: left;
    margin-top: 4px;
    margin-right: 12px;
    padding-bottom: 22px;
    color: @error-color;
  }
  .errorField {
    margin-top: 2px;
    color: @text-color-secondary;
    font-size: 12px;
  }
}

.editable {
  td {
    padding-top: 13px !important;
    padding-bottom: 12.5px !important;
  }
}
