import { useState } from 'react';
import { message } from 'antd';
import type { RcFile, UploadFile } from 'antd/es/upload/interface';
import useSWRMutation from 'swr/mutation';
import { upload } from '@/services/fetcher';
import { UploadResult, UseUploadParams, UseUploadReturn } from '../types';
import { Upload } from 'antd';

const useUpload = (params?: UseUploadParams): UseUploadReturn => {
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadError, setUploadError] = useState(false);
  const [fileId, setFileId] = useState<string | null>(null);

  const { trigger: uploadTrigger } = useSWRMutation<UploadResult>(
    '/api/upload/oss?objectKey&userId&businessType&remark', 
    upload
  );

  // 处理文件上传
  const handleUpload = async (file: RcFile) => {
    setFileList([file]);
    setUploading(true);
    setUploadProgress(0);
    setUploadError(false);
    setFileId(null);

    // 模拟上传进度
    const progressInterval = setInterval(() => {
      setUploadProgress(prev => {
        const newProgress = prev + Math.floor(Math.random() * 10);
        return newProgress >= 90 ? 90 : newProgress;
      });
    }, 300);

    try {
      const formData = new FormData();
      formData.append('file', file);
      
      // 添加上传参数
      if (params?.uploadParams) {
        Object.entries(params.uploadParams).forEach(([key, value]) => {
          formData.append(key, value as string);
        });
      }
      
      const result = await uploadTrigger(formData as any);
      console.log('upload result:', result);

      clearInterval(progressInterval);

      if (result.objectKey) {
        setUploadProgress(100);
        setUploadError(false);
        setFileId(result.objectKey);
      } else {
        setUploadProgress(0);
        setUploadError(true);
        message.error('上传失败');
      }
    } catch (error) {
      clearInterval(progressInterval);
      setUploadProgress(0);
      setUploadError(true);
      message.error('上传失败');
      console.error('上传失败:', error);
    } finally {
      setUploading(false);
    }
  };

  // 文件上传前检查
  const beforeUpload = (file: RcFile, acceptFileTypes: string[]) => {
    const isAcceptedType = acceptFileTypes.some(type =>
      file.name.toLowerCase().endsWith(type.toLowerCase())
    );

    if (!isAcceptedType) {
      message.error(`只支持 ${acceptFileTypes.join(', ')} 格式的文件`);
      return Upload.LIST_IGNORE;
    }

    // 自定义上传逻辑
    handleUpload(file);
    return false;
  };

  // 删除文件
  const handleRemoveFile = () => {
    setFileList([]);
    setUploadProgress(0);
    setUploadError(false);
    setFileId(null);
  };

  // 重新上传
  const handleReupload = () => {
    if (fileList.length > 0) {
      const file = fileList[0];
      if (file) {
        // 直接重新上传当前文件，而不是清空让用户重选
        handleUpload(file as RcFile);
      }
    }
  };

  // 重置状态
  const resetUploadState = () => {
    setFileList([]);
    setUploading(false);
    setUploadProgress(0);
    setUploadError(false);
    setFileId(null);
  };

  return {
    fileList,
    uploading,
    uploadProgress,
    uploadError,
    fileId,
    handleUpload,
    handleRemoveFile,
    handleReupload,
    beforeUpload: (file: RcFile) => beforeUpload(file, params?.acceptFileTypes || ['.xls', '.xlsx', '.csv']),
    resetUploadState,
  };
};

export default useUpload; 