.uploadModal {
  :global {
    .ant-modal-body {
      padding: 0;
    }

    .ant-upload-drag {
      border: 1px dashed #d9d9d9;
      border-radius: 4px;
      background-color: #fafafa;
      transition: border-color 0.3s;

      &:hover {
        border-color: #1890ff;
      }
    }
  }


  .loadingWrapper {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10;
    border-radius: 4px;
  }

  .section {
    padding: 24px 0;
    border-bottom: 1px solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }
  }

  .sectionTitle {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 16px;
    color: #000;
  }

  .uploadArea {
    width: 100%;

    .uploadIcon {
      color: #1890ff;
      font-size: 48px;
      margin-bottom: 16px;
    }

    .uploadText {
      color: #333;
      font-size: 16px;
      margin-bottom: 8px;
    }

    .uploadHint {
      line-height: 1;
      color: #999;
      font-size: 13px;
    }
  }

  .fileItem {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    margin-top: 16px;
    border-radius: 4px;
    background-color: #f6ffed;
    border: 1px solid #b7eb8f;
    transition: all 0.3s;

    &.error {
      background-color: #fff2f0;
      border-color: #ffccc7;

      .fileIcon {
        color: #ff4d4f;
      }
    }

    .fileIcon {
      font-size: 24px;
      margin-right: 12px;
      color: #52c41a;
    }

    .fileInfo {
      flex: 1;
      overflow: hidden;

      .fileName {
        font-size: 14px;
        color: #333;
        margin-bottom: 4px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .fileSize {
        font-size: 12px;
        color: #999;
      }
    }

    .fileActions {
      display: flex;
      align-items: center;

      .actionButton {
        margin-left: 8px;
      }
    }
  }

  .progressBar {
    margin-top: 12px;
  }

  .importSection {
    .importStatus {
      margin-bottom: 16px;
      color: #666;
      font-size: 14px;
    }

    .importResult {
      margin-bottom: 12px;
      font-size: 14px;
      line-height: 1.5;

      .failCount {
        color: #ff4d4f;
        font-weight: 500;
      }
    }

    .downloadLink {
      color: #1890ff;
      cursor: pointer;
      font-size: 14px;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  .formItem {
    margin-bottom: 16px;

    .formLabel {
      margin-bottom: 8px;
      color: #333;
      font-size: 14px;
    }

    .formControl {
      display: flex;
      align-items: center;

      .inputNumber {
        width: 80px;
        margin: 0 8px;
      }

      .inputText {
        margin: 0 8px;
      }
    }
  }

  .downloadButton {
    margin-top: 16px;
  }
}
