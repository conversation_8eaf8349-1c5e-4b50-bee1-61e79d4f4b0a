import { DownloadOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, Spin } from 'antd';
import React from 'react';
import { ImportResult } from '../types';

interface ImportSectionProps {
  importResult: ImportResult;
  handleDownloadFailReasons: () => Promise<void>;
}

const ImportSection: React.FC<ImportSectionProps> = ({
  importResult,
  handleDownloadFailReasons,
}) => {
  // 是否显示下载失败原因链接 - 当有失败数据或者状态为失败时都显示
  const showDownloadLink =
    (importResult.failed !== undefined && importResult.failed > 0) ||
    (importResult.status === 'failed' && importResult.taskId);

  return (
    <div className="py-6 border-b border-gray-200 last:border-b-0">
      <div className="text-base font-medium mb-4 text-black">3. 导入数据</div>
      <div className="px-2  border-b border-gray-200 last:border-b-0">
        {importResult.status === 'idle' && (
          <div className="text-gray-500">点击导入按钮开始导入数据</div>
        )}

        {importResult.status === 'processing' && (
          <div className="text-gray-500" style={{ display: 'flex', alignItems: 'center', gap: 10 }}>
            <Spin />
            正在导入数据，请稍候...{' '}
          </div>
        )}

        {importResult.status === 'success' && (
          <>
            <div className="text-gray-500">
              表格共: <span className="font-bold text-blue-500">{importResult.total || 0}</span>{' '}
              条数据， 成功导入:{' '}
              <span className="font-bold text-green-500">{importResult.success || 0}</span> 条，
              失败: <span className="font-bold text-red-500">{importResult.failed || 0}</span> 条
            </div>
            {showDownloadLink && (
              // <div
              //   className="text-blue-500"
              //   onClick={handleDownloadFailReasons}
              // >
              //   下载失败原因
              // </div>
              <Button
                className="p-0 gap-0.5"
                type="link"
                icon={<DownloadOutlined />}
                onClick={handleDownloadFailReasons}
              >
                下载失败原因
              </Button>
            )}
          </>
        )}

        {importResult.status === 'failed' && (
          <div className="text-gray-500">
            <div className="text-red-500">导入失败</div>
            {importResult.total !== undefined && (
              <div className="text-gray-500">
                表格共: <span className="font-bold text-blue-500">{importResult.total || 0}</span>{' '}
                条数据， 成功导入:{' '}
                <span className="font-bold text-green-500">{importResult.success || 0}</span> 条，
                失败: <span className="font-bold text-red-500">{importResult.failed || 0}</span> 条
              </div>
            )}
            <div className="text-blue-500" onClick={handleDownloadFailReasons}>
              下载失败原因
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ImportSection;
