import React, { ReactNode } from 'react';
import { Button } from 'antd';
import { DownloadOutlined } from '@ant-design/icons';

interface DownloadSectionProps {
  customContent?: ReactNode;
  templateFileName?: string;
  downloadTemplate: () => void;
  disabled?: boolean;
}

const DownloadSection: React.FC<DownloadSectionProps> = ({
  customContent,
  templateFileName,
  downloadTemplate,
  disabled = false,
}) => {
  const _templateFileName = templateFileName || '导入模板';

  return (
    <div className="py-4 border-b border-gray-200 last:border-b-0">
      <div className="text-base font-medium mb-4 text-black">
        1. 下载模板
      </div>
      {customContent || (
        <div>
          {downloadTemplate ? (
            <Button
              type="link"
              icon={<DownloadOutlined />}
              onClick={downloadTemplate}
              disabled={disabled}
            >
              下载{_templateFileName}
            </Button>
          ) : (
            <div>请配置下载模板内容</div>
          )}
        </div>
      )}
    </div>
  );
};

export default DownloadSection; 