import { LogoutOutlined, SettingOutlined, UserOutlined } from '@ant-design/icons';
import { history, useModel } from '@umijs/max';
import { message, Spin } from 'antd';
import { createStyles } from 'antd-style';
import React, { useCallback } from 'react';
import HeaderDropdown from '../HeaderDropdown';
import { logout } from '@/services/user';
import { goLogin } from '@/pages/user/login';

export type GlobalHeaderRightProps = {
  menu?: boolean;
  children?: React.ReactNode;
};


export const AvatarName = () => {
  const { initialState } = useModel('@@initialState');
  const { currentUser } = initialState || {};
  return <span className="anticon">
    <UserOutlined className='mr-2' />
    {currentUser?.userName}</span>;
};

const useStyles = createStyles(({ token }) => {
  return {
    action: {
      display: 'flex',
      height: '48px',
      marginLeft: 'auto',
      overflow: 'hidden',
      alignItems: 'center',
      padding: '0 8px',
      cursor: 'pointer',
      borderRadius: token.borderRadius,
      '&:hover': {
        backgroundColor: token.colorBgTextHover,
      },
    },
  };
});

export const AvatarDropdown: React.FC<GlobalHeaderRightProps> = ({ menu, children }) => {
  const { styles } = useStyles();

  const { initialState, setInitialState } = useModel('@@initialState');
  /**
   * 退出登录，并且将当前的 url 保存
   */
  const loginOut = useCallback(async () => {
    try {
      // 优先使用 initialState 中的 userId，如果没有则使用本地存储的 userId
      const userId = initialState?.currentUser?.userId || Number(localStorage.getItem('userId'));

      if (!userId) {
        message.error('用户信息获取失败，请重新登录');
        goLogin();
        return;
      }

      const { status, message: msg } = await logout();

      // 清除状态和本地存储
      setInitialState((s) => ({ ...s, currentUser: undefined }));
      localStorage.clear();

      // 跳转到登录页
      goLogin();
      if (status !== 200) {
        message.error(msg);
        return;
      } else {
        message.success('退出成功');
      }


    } catch (error) {
      console.error('退出登录失败:', error);
      // message.error('退出失败，请重试');
      // 发生错误时也跳转到登录页
      goLogin();
    }
  }, [initialState, history, logout, setInitialState]);

  const onMenuClick = useCallback(
    async (event: any) => {
      const { key } = event;
      if (key === 'logout') {

        await loginOut();
        setInitialState((s) => ({ ...s, currentUser: undefined }));
        localStorage.clear();
        return;
      }
      history.push(`/account/${key}`);
    },
    [setInitialState],
  );

  const loading = (
    <span className={styles.action}>
      <Spin
        size="small"
        style={{
          marginLeft: 8,
          marginRight: 8,
        }}
      />
    </span>
  );

  if (!initialState) {
    return loading;
  }

  const { currentUser } = initialState;

  if (!currentUser || !currentUser.userName) {
    return loading;
  }

  const menuItems = [
    ...(menu
      ? [
        {
          key: 'center',
          icon: <UserOutlined />,
          label: '个人中心',
        },
        {
          key: 'settings',
          icon: <SettingOutlined />,
          label: '个人设置',
        },
        {
          type: 'divider' as const,
        },
      ]
      : []),
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
    },
  ];

  return (
    <HeaderDropdown
      menu={{
        selectedKeys: [],
        onClick: onMenuClick,
        items: menuItems,
      }}
    >
      {children}
    </HeaderDropdown>
  );
};
