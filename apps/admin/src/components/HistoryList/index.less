.timelineItem {
  margin-bottom: 24px;

  .header {
    margin-bottom: 8px;
  }

  .info {
    margin-bottom: 8px;
  }

  .content {
    padding: 12px;
    background-color: #f5f5f5;
    border-radius: 4px;

    > div {
      margin-bottom: 8px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

.historyList {
  padding: 4px 0;
  margin-top: 12px;
}

.historyCard {
  margin-bottom: 12px;
  border-radius: 6px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);

  :global(.ant-card-body) {
    padding: 12px 16px;
  }
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.contentWrapper {
  padding: 0 4px;
}

.infoItem {
  margin-bottom: 6px;
  line-height: 20px;
  font-size: 13px;

  &:last-child {
    margin-bottom: 0;
  }
}
