import React from 'react';
import { Card, Tag, Typography, Spin, Space, Empty } from 'antd';
import { CooperationType } from '@/types/cooperation';
import {
  getTypeColor,
  getTypeLabel,
} from '@/pages/partner-school/utils/cooperationUtils';
import styles from './index.less';
import dayjs from 'dayjs';
import { ApprovalDetail } from '@/pages/approval/detail/context';

const { Text } = Typography;

// 状态标签配置
const STATUS_CONFIG: Record<number, { color: string; text: string }> = {
  0: {
    color: 'orange',
    text: '待审批',
  },
  1: {
    color: 'green',
    text: '已通过',
  },
  2: {
    color: 'red',
    text: '已拒绝',
  },
};

interface HistoryListProps {
  dataSource: ApprovalDetail[];
  loading?: boolean;
}

const HistoryList: React.FC<HistoryListProps> = ({ dataSource, loading }) => {
  // 解析审批参数
  const parseParams = (params: string) => {
    try {
      const detail = JSON.parse(params);
      
      // 处理时间字段，检查是否为秒级时间戳
      const formatTime = (time: any) => {
        if (!time) return '';
        const timestamp = typeof time === 'string' ? parseInt(time) : time;
        // 如果是秒级时间戳（长度为10位），则转换为毫秒
        const milliseconds = timestamp.toString().length === 10 ? timestamp * 1000 : timestamp;
        return dayjs(milliseconds).format('YYYY-MM-DD');
      };
      
      // 处理持续时间，将秒转换为天
      const formatPeriodTime = (periodTime: any) => {
        if (!periodTime) return '0';
        const seconds = typeof periodTime === 'string' ? parseInt(periodTime) : periodTime;
        // 将秒转换为天（1天 = 24 * 60 * 60 = 86400秒）
        const days = Math.round(seconds / 86400);
        return days.toString();
      };
      
      return {
        schoolName: detail.schoolName || '未知学校',
        type: detail.type,
        typeLabel: getTypeLabel(detail.type),
        startTime: formatTime(detail.startTime),
        endTime: formatTime(detail.endTime),
        periodTime: formatPeriodTime(detail.periodTime),
        studentCount: detail.studentIds?.split(',').length || 0,
        desc: detail.desc || ''
      };
    } catch (error) {
      console.error('参数解析错误:', error);
      return {
        schoolName: '',
        type: 0,
        typeLabel: '未知类型',
        startTime: '',
        endTime: '',
        periodTime: '0',
        studentCount: 0,
        desc: ''
      };
    }
  };

  // 渲染历史记录内容
  const renderContent = (item: ApprovalDetail) => {
    const detailData = parseParams(item.execute_params);

    return (
      <div className={`${styles.contentWrapper} history_content_wrapper`}>
        {/* 学校名称 */}
        <div className={`${styles.infoItem} history_school_name_item`}>
          <Text type="secondary" style={{ fontSize: '13px' }}>学校名称：</Text>
          {detailData.schoolName}
        </div>

        {/* 合作类型 */}
        <div className={`${styles.infoItem} history_cooperation_type_item`}>
          <Text type="secondary" style={{ fontSize: '13px' }}>合作类型：</Text>
          {detailData.typeLabel}
        </div>

        {/* 持续时间 */}
        <div className={`${styles.infoItem} history_duration_item`}>
          <Text type="secondary" style={{ fontSize: '13px' }}>持续时间：</Text>
          {detailData.periodTime}天
        </div>

        {/* 覆盖账号 */}
        <div className={`${styles.infoItem} history_account_count_item`}>
          <Text type="secondary" style={{ fontSize: '13px' }}>覆盖账号：</Text>
          {detailData.studentCount}个账户
        </div>

        {/* 时间信息 */}
        {(detailData.startTime || detailData.endTime) && (
          <div className={`${styles.infoItem} history_time_range_item`}>
            <Space size={16}>
              {detailData.startTime && (
                <span className="history_start_time">
                  <Text type="secondary" style={{ fontSize: '13px' }}>开始时间：</Text>
                  {detailData.startTime}
                </span>
              )}
              {detailData.endTime && (
                <span className="history_end_time">
                  <Text type="secondary" style={{ fontSize: '13px' }}>到期时间：</Text>
                  {detailData.endTime}
                </span>
              )}
            </Space>
          </div>
        )}

        {/* 申请说明 */}
        {detailData.desc && (
          <div className={`${styles.infoItem} history_description_item`}>
            <Text type="secondary" style={{ fontSize: '13px' }}>申请说明：</Text>
            {detailData.desc}
          </div>
        )}
      </div>
    );
  };

  return (
    <Spin spinning={loading}>
      {dataSource.length === 0 && (
        <div className="my-10 history_list_empty">
          <Empty description="暂无历史记录" />
        </div>
      )}
      <div className={`${styles.historyList} history_list_container`} style={{ maxHeight: '500px', overflowY: 'auto' }}>
        {dataSource.map((item) => {
          const params = parseParams(item.execute_params);
          const type = params.type as CooperationType;
          return (
            <Card key={item.approve_id} className={`${styles.historyCard} history_card_item`} bodyStyle={{ padding: '12px 16px' }}>
              <div className={`${styles.header} history_card_header`} style={{ marginBottom: 8 }}>
                <Tag color={getTypeColor(type)} bordered={false} style={{ fontSize: '12px', padding: '0 6px', marginRight: 0 }} className="history_type_tag">
                  {getTypeLabel(type)}
                </Tag>
                <Text type="secondary" style={{ fontSize: '12px' }} className="history_status_time">
                  <span style={{ color: "#3146A7", marginRight: 8 }} className="history_status_text">{STATUS_CONFIG[item.approver_result]?.text}</span>
                  {dayjs(item.approval_time).format('YYYY-MM-DD HH:mm:ss')}
                </Text>
              </div>
              {renderContent(item)}
            </Card>
          );
        })}
      </div>
    </Spin>
  );
};

export default HistoryList;
