// @import '~antd/es/style/themes/default.less';

.calendarContainer {
  height: 700px;
  
  :global {
    .fc-theme-standard .fc-scrollgrid {
      border-radius: 4px;
      overflow: hidden;
      border: 1px solid @border-color-split;
    }
    
    .fc-col-header-cell {
      background-color: @background-color-light;
      padding: 12px 0 !important;
    }
    
    .fc-col-header-cell-cushion {
      padding: 8px 4px;
      font-weight: 500;
      text-decoration: none !important;
      color: @text-color !important;
    }
    
    .fc-timegrid-slot {
      height: 40px !important;
    }
    
    .fc-timegrid-slot-lane {
      border-bottom: 1px solid @border-color-split;
    }
    
    .fc-timegrid-now-indicator-line {
      border-color: @error-color;
    }
    
    .fc-day-today {
      background-color: lighten(@primary-1, 5%) !important;
    }
    
    .fc-event {
      border-radius: 4px !important;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    
    .fc-toolbar-chunk .fc-button {
      background-color: #fff;
      color: @text-color;
      border: 1px solid @border-color-base;
      box-shadow: none;
      height: 32px;
      padding: 4px 15px;
      font-size: 14px;
    }
    
    .fc-toolbar-chunk .fc-button:hover {
      background-color: @background-color-light;
      border-color: @primary-color;
    }
    
    .fc-toolbar-chunk .fc-button-primary:not(:disabled).fc-button-active {
      background-color: @primary-color;
      border-color: @primary-color;
      color: #fff;
    }
    
    .fc-toolbar-title {
      font-size: 16px !important;
      font-weight: 500;
    }
  }
}

.fullscreenMode {
  height: 100vh !important;
  width: 100vw !important;
  display: flex;
  flex-direction: column;
  overflow: auto;
  padding: 16px;
  background-color: white;
  
  :global {
    .fc {
      height: calc(100vh - 80px) !important;
      flex: 1;
    }
    
    .fc-view-harness {
      height: calc(100vh - 120px) !important;
      overflow: auto;
    }
    
    .fc-scrollgrid {
      border-radius: 4px;
      overflow: hidden;
    }
  }
}