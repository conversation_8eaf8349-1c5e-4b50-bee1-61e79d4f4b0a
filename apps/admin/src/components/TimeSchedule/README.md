# 课程表组件 (Schedule)

## 概述

一个功能完善的课程表组件，支持自定义渲染和数据驱动，适用于学校课程排班、教师日程安排等场景。

## 特性

- **数据驱动**: 完全由外部数据驱动，无需直接调用API
- **自定义渲染**: 支持自定义事件内容渲染
- **周/日视图**: 支持周视图和日视图切换
- **学期周选择器**: 显示"第二学期 第5周（2025.3.24～3.30）"格式
- **全屏模式**: 支持全屏显示课程表
- **响应式设计**: 适配各种屏幕尺寸
- **TypeScript支持**: 完整的类型定义

## 安装依赖

```bash
pnpm add @fullcalendar/react @fullcalendar/timegrid @fullcalendar/interaction @fullcalendar/core dayjs
```

## 基本用法

```tsx
import React from 'react';
import SimpleSchedule, { SemesterInfo } from '@/components/Schedule';

// 定义日程数据
const scheduleItems = [
  {
    id: 1,
    weekday: 1, // 周一
    startTime: "08:00:00",
    endTime: "08:45:00",
    backgroundColor: '#64b5f6',
    borderColor: '#1565c0',
    class_schedule_course: "语文",
    teacher_name: "张老师",
  }
];

// 定义学期信息
const semester: SemesterInfo = {
  semesterId: 1,
  semesterName: '第二学期',
  startDate: '2025-02-17',
  endDate: '2025-07-12',
};

const App: React.FC = () => (
  <SimpleSchedule 
    scheduleItems={scheduleItems}
    renderEventContent={renderScheduleCard}
    initialSemester={semester}
    initialDate="2025-03-24"
    title="课程表"
  />
);

// 自定义渲染事件卡片
const renderScheduleCard = (eventInfo) => {
  const { event } = eventInfo;
  const data = event.extendedProps;
  
  return (
    <div>
      <div>{data.class_schedule_course}</div>
      <div>{data.teacher_name}</div>
    </div>
  );
};

export default App;
```

## 学期周选择器

显示当前学期和周信息，格式为：`第二学期 第5周（2025.3.24～3.30）`

功能：
- 上一周/下一周导航
- 日期选择器
- 周视图/日视图切换
- 自动计算周信息

```tsx
// 使用学期周选择器
<SimpleSchedule 
  initialSemester={semester}
  initialDate="2025-03-24" // 第5周的周一
/>
```

## 自定义事件渲染

```tsx
import SimpleSchedule, { RenderEventContentFn } from '@/components/Schedule';

const customRenderEvent: RenderEventContentFn = (eventInfo) => {
  const { event } = eventInfo;
  const data = event.extendedProps;
  
  return (
    <div style={{ padding: '4px' }}>
      <div style={{ fontWeight: 'bold' }}>
        {data.class_schedule_course}
      </div>
      <div>
        教师: {data.teacher_name}
      </div>
      <div>
        {data.startTime.substring(0, 5)} - {data.endTime.substring(0, 5)}
      </div>
    </div>
  );
};
```

## 全屏模式

组件支持全屏显示，点击右上角的全屏按钮可切换。

## API参考

### 属性

| 属性名 | 类型 | 默认值 | 说明 |
|-------|------|-------|------|
| scheduleItems | ScheduleItem[] | `[]` | 日程数据项 |
| loading | boolean | `false` | 加载状态 |
| title | string | `'日程表'` | 标题 |
| renderEventContent | RenderEventContentFn | - | 自定义事件渲染函数（必须） |
| calendarOptions | object | `{}` | FullCalendar的自定义配置 |
| onEventClick | (info: any) => void | - | 事件点击回调 |
| onDateRangeChange | (startDate: Date, endDate: Date) => void | - | 日期范围变化回调 |
| initialSemester | SemesterInfo | - | 初始学期信息 |
| initialDate | string | 当前日期 | 初始日期(YYYY-MM-DD) |
| slotDuration | string | '00:15:00' | 时间槽间隔 |

### 类型定义

```tsx
// 基础日程项
export interface ScheduleItem {
  id: number | string;
  startTime: string; // "HH:MM:SS"
  endTime: string;   // "HH:MM:SS"
  weekday: number;   // 1-7，周一到周日
  backgroundColor?: string;
  borderColor?: string;
  [key: string]: any; // 允许自定义属性
}

// 学期信息
export interface SemesterInfo {
  semesterId: number;
  semesterName: string;
  startDate: string; // YYYY-MM-DD
  endDate: string;   // YYYY-MM-DD
}

// 周信息
export interface WeekInfo {
  weekNumber: number;
  startDate: string;  // YYYY-MM-DD
  endDate: string;    // YYYY-MM-DD
  fullDateRange: string; // "2025.3.24～3.30"
}

// 事件渲染函数
export type RenderEventContentFn = (eventInfo: {
  event: {
    title: string;
    start: Date;
    end: Date;
    extendedProps: any;
  };
}) => ReactNode;
```

## 进阶用法

### 监听日期范围变化

```tsx
const handleDateRangeChange = (startDate: Date, endDate: Date) => {
  console.log('当前日期范围:', 
    dayjs(startDate).format('YYYY-MM-DD'), 
    '至', 
    dayjs(endDate).format('YYYY-MM-DD')
  );
  
  // 可在此加载特定日期范围的数据
  // loadScheduleData(startDate, endDate);
};

<SimpleSchedule 
  onDateRangeChange={handleDateRangeChange}
/>
```

### 处理事件点击

```tsx
const handleEventClick = (info: any) => {
  const eventData = info.event.extendedProps;
  console.log('点击了课程:', eventData.class_schedule_course);
  
  // 显示详情弹窗
  // showEventDetails(eventData);
};

<SimpleSchedule 
  onEventClick={handleEventClick}
/>
```

### 自定义时间槽间隔

```tsx
// 设置30分钟的时间槽
<SimpleSchedule 
  slotDuration="00:30:00"
/>
```

## 注意事项

1. 组件依赖 FullCalendar 和 dayjs，请确保安装
2. 默认使用中文本地化，支持周一至周日显示
3. 日期以YYYY-MM-DD格式传入
4. 周几编号：1表示周一，7表示周日
5. 全屏模式下支持滚动 