import React, { useState, useEffect, useRef, ReactNode } from 'react';
import { Card, Spin, Button } from 'antd';
import FullCalendar from '@fullcalendar/react';
import timeGridPlugin from '@fullcalendar/timegrid';
import interactionPlugin from '@fullcalendar/interaction';
import zhCnLocale from '@fullcalendar/core/locales/zh-cn';
import { EventInput } from '@fullcalendar/core';
import styles from './index.less';
import { FullscreenOutlined, FullscreenExitOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';

// 基础日程项
export interface ScheduleItem {
  id: number | string;
  startTime: string; // 格式为 "HH:MM:SS"
  endTime: string;   // 格式为 "HH:MM:SS"
  weekday: number;   // 1-7，表示周一到周日
  backgroundColor?: string;
  borderColor?: string;
  [key: string]: any; // 允许任意额外属性
}

// 事件渲染函数类型
export type RenderEventContentFn = (eventInfo: {
  event: {
    title: string;
    start: Date;
    end: Date;
    extendedProps: any;
  };
}) => ReactNode;

// 组件属性
export interface SimpleScheduleProps {
  slotDuration?: string;
  scheduleItems?: ScheduleItem[];
  loading?: boolean;
  title?: string;
  renderEventContent: RenderEventContentFn;
  calendarOptions?: any; // 允许自定义 FullCalendar 的任何选项
  onEventClick?: (info: any) => void;
  onDateRangeChange?: (startDate: Date, endDate: Date) => void;
}

const SimpleSchedule: React.FC<SimpleScheduleProps> = ({
  slotDuration = '00:15:00',
  scheduleItems = [],
  loading = false,
  title = '日程表',
  renderEventContent,
  calendarOptions = {},
  onEventClick,
  onDateRangeChange,
}) => {
  // 状态定义
  const [calendarEvents, setCalendarEvents] = useState<EventInput[]>([]);
  const calendarRef = useRef<any>(null);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const [currentDate, setCurrentDate] = useState(new Date()); // 当前显示的日期范围中心点
  
  // 记录上一次的日期范围，避免重复触发 onDateRangeChange
  const lastDateRangeRef = useRef<{start?: string, end?: string}>({});

  // 获取本周一的日期
  const getMondayOfWeek = (date: Date): Date => {
    const day = date.getDay() || 7; // 如果是周日，getDay()返回0，我们需要7
    const result = new Date(date);
    if (day !== 1) {
      result.setDate(result.getDate() - (day - 1));
    }
    // 设置为当天0点
    result.setHours(0, 0, 0, 0);
    return result;
  };

  // 将日程数据转换为FullCalendar事件格式
  const transformScheduleToEvents = (items: ScheduleItem[], baseDate: Date): EventInput[] => {
    console.log("开始转换数据:", items, "基准日期:", baseDate);
    const events: EventInput[] = [];
    const mondayOfWeek = getMondayOfWeek(baseDate);

    items.forEach(item => {
      try {
        const dayDate = new Date(mondayOfWeek);
        const weekday = item.weekday;

        // 确保weekday在有效范围内 (1-7)
        if (weekday < 1 || weekday > 7) {
          console.warn("无效的weekday:", weekday);
          return;
        }

        // 计算当天日期 (从周一开始，weekday 1 = 周一)
        dayDate.setDate(mondayOfWeek.getDate() + weekday - 1);

        // 创建开始和结束时间
        const startParts = item.startTime.split(':').map(Number);
        const endParts = item.endTime.split(':').map(Number);

        if (startParts.length < 2 || endParts.length < 2) {
          console.warn("无效的时间格式:", item.startTime, item.endTime);
          return;
        }

        const startTime = new Date(dayDate);
        startTime.setHours(startParts[0], startParts[1], startParts[2] || 0);

        const endTime = new Date(dayDate);
        endTime.setHours(endParts[0], endParts[1], endParts[2] || 0);

        console.log(`事件 ${item.id}: ${dayDate.toDateString()}, ${startTime.toTimeString()} - ${endTime.toTimeString()}`);

        // 创建事件对象
        events.push({
          id: String(item.id),
          title: item.title || '',
          start: startTime,
          end: endTime,
          backgroundColor: item.backgroundColor,
          borderColor: item.borderColor,
          extendedProps: {
            ...item,
            originalWeekday: weekday, // 保存原始的星期信息
          }
        });
      } catch (err) {
        console.error("处理项目时出错:", item, err);
      }
    });

    console.log("转换后的事件:", events);
    return events;
  };

  // 更新日历事件
  const updateCalendarEvents = () => {
    if (scheduleItems && scheduleItems.length > 0) {
      const transformedEvents = transformScheduleToEvents(scheduleItems, currentDate);
      setCalendarEvents(transformedEvents);

      // 直接应用到日历
      if (calendarRef.current) {
        const calendarApi = calendarRef.current.getApi();
        calendarApi.removeAllEvents();
        calendarApi.addEventSource(transformedEvents);
      }
    } else {
      setCalendarEvents([]);
      if (calendarRef.current) {
        const calendarApi = calendarRef.current.getApi();
        calendarApi.removeAllEvents();
      }
    }
  };

  // 当scheduleItems变化时更新事件
  useEffect(() => {
    updateCalendarEvents();
  }, [scheduleItems, JSON.stringify(currentDate)]);

  // 当currentDate变化时处理日期变更
  useEffect(() => {
    // 如果日历已经初始化，跳转到当前日期
    if (calendarRef.current) {
      const calendarApi = calendarRef.current.getApi();
      calendarApi.gotoDate(currentDate);
    }
  }, [currentDate]);

  // 处理日历视图变化
  const handleDatesSet = (dateInfo: {
    start: Date;
    end: Date;
    view: {
      currentStart: Date;
      currentEnd: Date;
    }
  }) => {
    // 获取当前视图的中心日期
    const viewCenter = new Date(
      (dateInfo.view.currentStart.getTime() + dateInfo.view.currentEnd.getTime()) / 2
    );

    console.log("日期范围变化:",
      dayjs(dateInfo.view.currentStart).format('YYYY-MM-DD'),
      "到",
      dayjs(dateInfo.view.currentEnd).format('YYYY-MM-DD'),
      "中心点:",
      dayjs(viewCenter).format('YYYY-MM-DD')
    );

    // 只有当中心日期变化超过1天时才更新
    if (Math.abs(viewCenter.getTime() - currentDate.getTime()) > 24 * 60 * 60 * 1000) {
      setCurrentDate(viewCenter);
    }
    
    // 转换为字符串格式进行比较
    const currentStartStr = dayjs(dateInfo.start).format('YYYY-MM-DD');
    const currentEndStr = dayjs(dateInfo.end).format('YYYY-MM-DD');
    
    // 检查日期范围是否发生变化
    const isDifferentRange = currentStartStr !== lastDateRangeRef.current.start || 
                             currentEndStr !== lastDateRangeRef.current.end;
    
    console.log('日期范围比较:', {
      当前范围: `${currentStartStr} 至 ${currentEndStr}`,
      上次范围: `${lastDateRangeRef.current.start || '无'} 至 ${lastDateRangeRef.current.end || '无'}`,
      是否变化: isDifferentRange,
      是否回调: isDifferentRange && !!onDateRangeChange
    });
    
    // 只有当日期范围发生变化时才调用回调函数
    if (onDateRangeChange && isDifferentRange) {
      // 更新上一次的日期范围
      lastDateRangeRef.current = {
        start: currentStartStr,
        end: currentEndStr
      };
      
      console.log('触发 onDateRangeChange 回调');
      onDateRangeChange(dateInfo.start, dateInfo.end);
    }
  };

  // 切换全屏模式
  const toggleFullscreen = () => {
    if (!isFullscreen) {
      if (containerRef.current?.requestFullscreen) {
        containerRef.current.requestFullscreen();
      }
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen();
      }
    }
  };

  // 监听全屏状态变化
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
    };
  }, []);

  return (
    <Spin spinning={loading}>
      <div ref={containerRef} className={`${styles.calendarContainer} ${isFullscreen ? styles.fullscreenMode : ''}`} style={{ minWidth: '780px' }}>
        <Card
          title={title}
          bordered={false}
          extra={
            <Button
              type="text"
              icon={isFullscreen ? <FullscreenExitOutlined /> : <FullscreenOutlined />}
              onClick={toggleFullscreen}
            />
          }
        >
          <div>
            <FullCalendar
              ref={calendarRef}
              plugins={[timeGridPlugin, interactionPlugin]}
              headerToolbar={{
                left: 'prev,next today',
                center: 'title',
                right: 'timeGridWeek,timeGridDay'
              }}
              initialView="timeGridWeek"
              initialDate={currentDate}
              locale={zhCnLocale}
              allDaySlot={false}
              slotMinTime="06:00:00"
              slotMaxTime="22:00:00"
              slotDuration={slotDuration || '00:15:00'}
              slotLabelInterval="01:00:00"
              slotLabelFormat={{
                hour: '2-digit',
                minute: '2-digit',
                hour12: false
              }}
              dayHeaderFormat={{ weekday: 'short', month: 'numeric', day: 'numeric', omitCommas: true }}
              nowIndicator={true}
              height={isFullscreen ? 'auto' : 700}
              events={calendarEvents}
              eventContent={renderEventContent}
              eventClick={(info) => {
                if (onEventClick) {
                  onEventClick(info);
                }
              }}
              datesSet={handleDatesSet}
              buttonText={{
                today: '今天',
                week: '周视图',
                day: '日视图'
              }}
              firstDay={1}  // 周一开始
              {...calendarOptions} // 允许自定义覆盖任何默认配置
            />
          </div>
        </Card>
      </div>
    </Spin>
  );
};

export default SimpleSchedule;