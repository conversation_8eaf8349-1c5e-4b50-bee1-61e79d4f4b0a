import React from 'react';
import SimpleSchedule from './index';

const mockScheduleItems = [
  {
    id: 1,
    weekday: 1, // 周一
    startTime: "08:00:00",
    endTime: "08:45:00",
    backgroundColor: '#64b5f6',
    borderColor: '#1565c0',
    class_schedule_course: "语文",
    teacher_name: "张老师",
    grade_name: "高一",
    class_name: "(1)班",
    class_schedule_study_type: 1,
    class_schedule_study_type_name: "课程"
  },
  {
    id: 2,
    weekday: 2, // 周二
    startTime: "09:00:00",
    endTime: "09:45:00",
    backgroundColor: '#81c784',
    borderColor: '#2e7d32',
    class_schedule_course: "数学",
    teacher_name: "李老师",
    grade_name: "高一",
    class_name: "(2)班",
    class_schedule_study_type: 2,
    class_schedule_study_type_name: "自习"
  },
  // 添加更多示例数据...
];

const DemoPage = () => {
  const renderScheduleCard = (eventInfo) => {
    const { event } = eventInfo;
    const data = event.extendedProps;
    
    return (
      <div style={{ padding: '4px 8px' }}>
        <div style={{ fontWeight: 'bold', fontSize: '14px' }}>
          {data.class_schedule_course}
        </div>
        <div style={{ fontSize: '12px', marginTop: '3px' }}>
          {data.teacher_name} | {data.grade_name}{data.class_name}
        </div>
        <div style={{ fontSize: '11px', marginTop: '2px', fontStyle: 'italic' }}>
          {data.class_schedule_study_type_name}
        </div>
        {/* 时间 */}
        <div style={{ fontSize: '11px', marginTop: '2px', fontStyle: 'italic' }}>
          {data.startTime} - {data.endTime}
        </div>
      </div>
    );
  };

  return (
    <SimpleSchedule
      title="课程表演示"
      scheduleItems={mockScheduleItems}
      renderEventContent={renderScheduleCard}
      onEventClick={(info) => console.log('Clicked:', info.event.extendedProps)}
    />
  );
};

export default DemoPage;