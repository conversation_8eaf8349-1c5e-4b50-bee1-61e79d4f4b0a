import { getClassTree } from '@/services/schedule-management';
import { ClassGroupInfo, ClassInfo, GradeInfo } from '@/services/schedule-management/type';
import { DownOutlined, RightOutlined } from '@ant-design/icons';
import { useModel } from '@umijs/max';
import { Button, message, Spin, Tooltip } from 'antd';
import React, {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';
import ClassFormDrawer, { ClassFormDrawerRef } from './ClassFormDrawer';

export enum NodeType {
  School = 0,
  Grade = 1,
  ClassType = 2,
  Class = 3,
}

// 更具体的类型定义，提高类型安全性
export type NodeInfo<T extends NodeType> = T extends NodeType.Grade
  ? GradeInfo
  : T extends NodeType.Class
    ? ClassInfo & { gradeInfo: GradeInfo }
    : T extends NodeType.School
      ? {
          class_school_id: number;
          school_name: string;
        }
      : ClassGroupInfo;

export interface GradeTreeProps {
  editableClass?: boolean;
  disableSelection?: boolean;
  className?: string;
  style?: React.CSSProperties;
  /** 外部控制的加载状态 */
  loading?: boolean;
  /** 外部提供的树数据（用于受控模式） */
  treeData?: GradeInfo[];
  /** 节点选择回调 */
  onNodeSelect: <T extends NodeType>(
    selectedKey: React.Key,
    nodeType: T,
    nodeInfo?: NodeInfo<T>,
  ) => void;
  /** 数据更新回调 */
  onDataUpdated?: (data: GradeInfo[]) => void;
  /** 默认选中学校节点，在账号管理-学生管理下需要默认选中学校节点 */
  canSelectedSchool?: boolean;
  /** 协作状态 - 班级创建使用 */
  collaborationStatus?: number;
  /** 学科使用状态 - 班级创建使用 */
  subjectUsageStatus?: number;
}

export interface GradeTreeRef {
  refreshData: () => Promise<void>;
}

const GradeTree = forwardRef<GradeTreeRef, GradeTreeProps>((props, ref) => {
  const {
    editableClass = false,
    disableSelection = false,
    className = '',
    style,
    loading: externalLoading,
    treeData: externalTreeData,
    onNodeSelect,
    onDataUpdated,
    canSelectedSchool = false,
    collaborationStatus = 1,
    subjectUsageStatus = 1,
  } = props;
  const { schoolInfo } = useModel('schoolModel');
  console.log('GradeTreeschoolInfo', schoolInfo);

  // 从 schoolInfo 中提取必要属性
  const schoolId = schoolInfo?.schoolId || 0;
  const schoolYearId = schoolInfo?.schoolYear?.school_year_id || 1;
  const schoolName = schoolInfo?.schoolName || '';

  // 状态管理
  const [selectedNode, setSelectedNode] = useState<string>('');
  const [internalTreeData, setInternalTreeData] = useState<GradeInfo[]>([]);
  const [internalLoading, setInternalLoading] = useState(false);
  const [expandedNodes, setExpandedNodes] = useState<Record<string, boolean>>({});

  // 引用班级表单抽屉组件
  const classFormRef = useRef<ClassFormDrawerRef>(null);

  // 判断是否为受控模式
  const isControlled = Boolean(externalTreeData);
  const finalTreeData = isControlled ? externalTreeData || [] : internalTreeData;
  const finalLoading = isControlled ? Boolean(externalLoading) : internalLoading;

  // 生成一致的节点键值的辅助函数
  const getNodeKey = useCallback(
    (nodeType: NodeType, nodeInfo: any): string => {
      switch (nodeType) {
        case NodeType.Grade:
          return `${nodeType}-${nodeInfo.grade_id}`;
        case NodeType.Class:
          return `${nodeType}-${nodeInfo.class_id}`;
        case NodeType.ClassType:
          return `${nodeType}-${nodeInfo.is_test}`;
        case NodeType.School:
          return `${nodeType}-${schoolInfo?.schoolId}`;
        default:
          return '';
      }
    },
    [schoolInfo?.schoolId],
  );

  // 数据获取函数
  const fetchGradeTreeData = useCallback(async () => {
    if (!schoolId || !schoolYearId) return;

    setInternalLoading(true);
    try {
      const response = await getClassTree({
        class_school_id: schoolId,
        class_school_year_id: schoolYearId,
      });

      setInternalTreeData(response.data.list || []);

      // 如果提供了学校名称，则自动选择学校节点
      if (canSelectedSchool) {
        const schoolNodeKey = getNodeKey(NodeType.School, { class_school_id: schoolId });
        setSelectedNode(schoolNodeKey);
        onNodeSelect(schoolNodeKey, NodeType.School, {
          class_school_id: schoolId,
          school_name: schoolName,
        });
        return;
      }

      // 否则，选择第一个年级（如果有）
      if (response.data.list?.length > 0) {
        const firstGrade = response.data.list[0];
        const nodeType = NodeType.Grade;
        const nodeKey = getNodeKey(nodeType, firstGrade);
        setSelectedNode(nodeKey);
        onNodeSelect(nodeKey, nodeType, firstGrade);
      }
    } catch (error) {
      console.error('获取年级树数据失败:', error);
      message.error('获取年级树数据失败');
    } finally {
      setInternalLoading(false);
    }
  }, [schoolId, schoolYearId, schoolName, getNodeKey, onNodeSelect]);

  // 在组件挂载或依赖项变化时初始化数据
  useEffect(() => {
    console.log('collaborationStatus', collaborationStatus);
    console.log('subjectUsageStatus', subjectUsageStatus);
    console.log('schoolId', schoolId);
    console.log('schoolYearId', schoolYearId);

    if (!isControlled && schoolId && schoolYearId) {
      fetchGradeTreeData();
    }
  }, [isControlled, schoolId, schoolYearId, fetchGradeTreeData]);

  // 向父组件暴露刷新方法
  useImperativeHandle(
    ref,
    () => ({
      refreshData: async () => {
        if (!isControlled) {
          await fetchGradeTreeData();
        }
        return; // 显式返回以提高清晰度
      },
    }),
    [isControlled, fetchGradeTreeData],
  );

  // 切换树节点展开状态
  const handleNodeToggle = useCallback((nodeKey: string) => {
    setExpandedNodes((prev) => ({
      ...prev,
      [nodeKey]: !prev[nodeKey],
    }));
  }, []);

  // 处理节点点击事件
  const handleNodeClick = useCallback(
    (nodeKey: string, nodeType: NodeType, nodeInfo?: any) => {
      // 如果某些节点类型被禁用选择，则阻止选择
      if (disableSelection && [NodeType.Grade, NodeType.ClassType].includes(nodeType)) {
        return message.warning('请先完成当前编辑操作');
      }

      setSelectedNode(nodeKey);
      onNodeSelect(nodeKey, nodeType, nodeInfo);

      // 为容器节点切换展开状态
      if (nodeType === NodeType.Grade || nodeType === NodeType.ClassType) {
        handleNodeToggle(nodeKey);
      }
    },
    [disableSelection, onNodeSelect, handleNodeToggle],
  );

  // 处理数据刷新（用于表单成功提交后）
  const handleDataRefresh = useCallback(async () => {
    if (!isControlled) {
      await fetchGradeTreeData();
    } else if (onDataUpdated) {
      onDataUpdated(finalTreeData);
    }
  }, [isControlled, fetchGradeTreeData, onDataUpdated, finalTreeData]);

  // 创建新班级
  const handleCreateClass = useCallback(
    (gradeId: string, gradeName: string) => {
      classFormRef.current?.openCreate({
        grade: gradeId,
        gradeName,
        schoolId,
        schoolYearId,
        collaborationStatus,
        subjectUsageStatus,
      });
    },
    [schoolId, schoolYearId, collaborationStatus, subjectUsageStatus],
  );

  // 编辑现有班级
  const handleEditClass = useCallback(
    (classId: string, grade: string, gradeName: string) => {
      classFormRef.current?.openEdit({
        id: classId,
        grade,
        gradeName,
        schoolId,
        schoolYearId,
      });
    },
    [schoolId, schoolYearId],
  );

  // 处理学校节点选择
  const handleSchoolSelect = useCallback(() => {
    const schoolNodeKey = getNodeKey(NodeType.School, { class_school_id: schoolId });
    setSelectedNode(schoolNodeKey);
    onNodeSelect(schoolNodeKey, NodeType.School, {
      class_school_id: schoolId,
      school_name: schoolName,
    });
  }, [schoolId, schoolName, getNodeKey, onNodeSelect]);

  // 渲染班级类型节点（组）
  const renderClassTypeNode = useCallback(
    (classGroup: ClassGroupInfo, parentGrade: GradeInfo) => {
      const nodeKey = getNodeKey(NodeType.ClassType, classGroup);
      const isExpanded = expandedNodes[nodeKey];
      const hasChildren = Array.isArray(classGroup.children) && classGroup.children.length > 0;

      return (
        <div key={nodeKey} className="mb-1 mt-1">
          <div
            className="py-2 px-4 w-full text-gray-500 font-medium flex items-center cursor-pointer hover:bg-gray-50"
            onClick={() => handleNodeClick(nodeKey, NodeType.ClassType, classGroup)}
          >
            {hasChildren && (
              <span className="mr-1">{isExpanded ? <DownOutlined /> : <RightOutlined />}</span>
            )}
            <span>{classGroup.group_name}</span>
          </div>
          {hasChildren && isExpanded && (
            <div className="pl-4">
              {classGroup.children.map((classInfo) => {
                const classNodeKey = getNodeKey(NodeType.Class, classInfo);
                const isClassSelected = selectedNode === classNodeKey;

                return (
                  <div key={classNodeKey} className="mb-1">
                    <div
                      className={`py-2 px-4 w-full rounded cursor-pointer flex items-center justify-between group ${isClassSelected ? 'bg-blue-50 text-blue-600 font-medium' : 'hover:bg-gray-50'}`}
                    >
                      <div
                        className="flex-grow"
                        onClick={() =>
                          handleNodeClick(classNodeKey, NodeType.Class, {
                            ...classInfo,
                            gradeInfo: parentGrade,
                          })
                        }
                      >
                        {classInfo.class_name}
                      </div>
                      {editableClass && (
                        <div className="invisible group-hover:visible flex items-center h-6">
                          <Tooltip title="编辑班级">
                            <Button
                              type="link"
                              size="small"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleEditClass(
                                  String(classInfo.class_id),
                                  String(classInfo.class_grade),
                                  parentGrade.grade_name,
                                );
                              }}
                              className="p-0 h-6 hover:text-blue-500"
                            >
                              编辑
                            </Button>
                          </Tooltip>
                        </div>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>
      );
    },
    [expandedNodes, selectedNode, editableClass, getNodeKey, handleNodeClick, handleEditClass],
  );

  // 渲染年级节点
  const renderTreeNode = useCallback(
    (grade: GradeInfo) => {
      const nodeKey = getNodeKey(NodeType.Grade, grade);
      const isSelected = selectedNode === nodeKey;
      const isExpanded = expandedNodes[nodeKey];
      const hasChildren = Array.isArray(grade.children) && grade.children.length > 0;

      return (
        <div key={nodeKey} className="mb-1">
          <div
            className={`py-2 px-4 w-full rounded cursor-pointer flex items-center justify-between group ${isSelected ? 'bg-blue-50 text-blue-600 font-medium' : 'hover:bg-gray-50'}`}
          >
            <div
              className="flex items-center flex-grow"
              onClick={() => handleNodeClick(nodeKey, NodeType.Grade, grade)}
            >
              {hasChildren && (
                <span className="mr-1">{isExpanded ? <DownOutlined /> : <RightOutlined />}</span>
              )}
              <span>{grade.grade_name}</span>
            </div>
            {editableClass && (
              <div className="invisible group-hover:visible flex items-center h-6">
                <Tooltip title="新建班级">
                  <Button
                    type="link"
                    size="small"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleCreateClass(String(grade.grade_id), grade.grade_name);
                    }}
                    className="p-0 h-6 hover:text-blue-500"
                  >
                    添加
                  </Button>
                </Tooltip>
              </div>
            )}
          </div>
          {hasChildren && isExpanded && (
            <div className="pl-4">
              {grade.children.map((classGroup) => renderClassTypeNode(classGroup, grade))}
            </div>
          )}
        </div>
      );
    },
    [
      selectedNode,
      expandedNodes,
      editableClass,
      getNodeKey,
      handleNodeClick,
      handleCreateClass,
      renderClassTypeNode,
    ],
  );

  return (
    <>
      <div
        className={`h-full w-60 flex flex-col bg-white rounded shadow-sm ${className}`}
        style={style}
      >
        {/* 学校头部和学年 */}
        {canSelectedSchool && (
          <div
            className="p-3 border-b border-gray-200 flex justify-between hover:bg-blue-50 cursor-pointer hover:border-blue-200 items-center"
            onClick={handleSchoolSelect}
          >
            <div
              className={`text-lg font-medium cursor-pointer hover:text-blue-500 ${selectedNode === getNodeKey(NodeType.School, { class_school_id: schoolId }) ? 'text-blue-500' : ''}`}
            >
              {schoolInfo?.schoolName}
            </div>
            <div className="text-sm text-gray-500">ID: {schoolId}</div>
          </div>
        )}
        <div className="text-base font-medium px-3 py-2 h-10">
          {schoolInfo?.schoolYear?.school_year || '学年'}
        </div>

        {/* 树内容 */}
        {finalLoading ? (
          <div className="flex justify-center py-10">
            <Spin />
          </div>
        ) : (
          <div className="flex-1 overflow-y-auto">
            <div className="p-2">{finalTreeData.map(renderTreeNode)}</div>
          </div>
        )}
      </div>

      {/* 使用抽离出的班级表单组件 */}
      {editableClass && <ClassFormDrawer ref={classFormRef} onSuccess={handleDataRefresh} />}
    </>
  );
});

export default GradeTree;
