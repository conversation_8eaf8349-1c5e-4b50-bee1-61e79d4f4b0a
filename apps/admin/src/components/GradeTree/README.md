# GradeTree 组件使用文档

## 概述

GradeTree 是一个用于教育管理系统的树形结构导航组件，专为展示和管理学校年级-班级层级结构而设计。它以直观的方式呈现学校的组织架构，支持节点选择、层级展开/折叠以及班级管理等核心功能。

该组件采用多级嵌套的结构来展示复杂的教育机构组织关系：学校 → 年级 → 班级类型(真实班/测试班) → 具体班级。通过这种结构化的导航，用户可以轻松定位并管理特定班级的信息。

## 功能特性

- **多层级组织结构展示**：清晰呈现学校-年级-班级的层级关系
- **交互式节点管理**：支持节点选择、展开/折叠操作
- **班级增删改功能**：内置班级创建和编辑功能
- **灵活的数据管理**：支持自动数据获取和外部数据控制两种模式
- **响应式交互**：节点选中高亮、悬停显示操作按钮
- **权限控制**：可配置是否允许编辑班级信息
- **自定义样式**：支持通过 className 和 style 属性定制外观

## 安装与引入

```jsx
import GradeTree, { GradeTreeRef, NodeType } from '@/components/GradeTree';
import { useRef } from 'react';

// 使用组件
function MyComponent() {
  const treeRef = useRef<GradeTreeRef>(null);
  
  return (
    <GradeTree
      schoolId={1001}
      schoolYearId={2023}
      schoolName="示范中学"
      disableSelection={false}
      onNodeSelect={(key, type, info) => {
        // 处理节点选择
      }}
    />
  );
}
```

## 核心属性

| 属性名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| `schoolId` | number | 是 | 学校ID，用于数据获取和班级创建 |
| `schoolYearId` | number | 是 | 学年ID，用于获取对应学年的班级信息 |
| `disableSelection` | boolean | 是 | 是否禁用节点选择功能 |
| `onNodeSelect` | function | 是 | 节点选择回调，参数：(key, nodeType, nodeInfo) |
| `schoolName` | string | 否 | 学校名称，显示在树的顶部 |
| `currentAcademicYear` | string | 否 | 当前学年信息，如"2023-2024学年" |
| `editableClass` | boolean | 否 | 是否显示班级添加/编辑按钮 |
| `className` | string | 否 | 自定义CSS类名 |
| `style` | object | 否 | 自定义内联样式 |
| `loading` | boolean | 否 | 控制组件加载状态（受控模式） |
| `treeData` | array | 否 | 外部提供的树形数据（受控模式） |
| `onDataUpdated` | function | 否 | 数据更新回调（受控模式） |

## 节点类型

组件使用 `NodeType` 枚举来区分不同类型的节点：

```typescript
enum NodeType {
  School = 0,  // 学校节点
  Grade = 1,   // 年级节点
  ClassType = 2, // 班级类型节点（真实班/测试班分组）
  Class = 3    // 具体班级节点
}
```

## 使用示例

### 基础用法

```jsx
<GradeTree
  schoolId={1001}
  schoolYearId={2023}
  schoolName="实验中学"
  currentAcademicYear="2023-2024学年"
  disableSelection={false}
  editableClass={true}
  onNodeSelect={(key, type, info) => {
    if (type === NodeType.Class) {
      console.log('选中班级:', info.class_name);
      // 加载班级信息
    }
  }}
/>
```

### 受控模式

```jsx
const [gradeData, setGradeData] = useState([]);
const [loading, setLoading] = useState(false);

// 自定义数据加载逻辑
const loadData = async () => {
  setLoading(true);
  try {
    const response = await fetchGradeData(schoolId);
    setGradeData(response.data);
  } finally {
    setLoading(false);
  }
};

// 组件渲染
<GradeTree
  schoolId={schoolId}
  schoolYearId={yearId}
  treeData={gradeData}
  loading={loading}
  onDataUpdated={setGradeData}
  onNodeSelect={handleNodeSelect}
  disableSelection={false}
/>
```

### 使用 Ref 手动刷新

```jsx
const treeRef = useRef<GradeTreeRef>(null);

const handleRefresh = () => {
  // 手动触发数据刷新
  treeRef.current?.refreshData();
};

<>
  <Button onClick={handleRefresh}>刷新数据</Button>
  <GradeTree
    ref={treeRef}
    schoolId={schoolId}
    schoolYearId={yearId}
    disableSelection={false}
    onNodeSelect={handleSelect}
  />
</>
```

## 班级表单组件

GradeTree 内部集成了班级创建和编辑功能，使用 `ClassFormDrawer` 组件处理班级表单提交。该组件也可单独使用：

```jsx
import ClassFormDrawer, { ClassFormDrawerRef } from '@/components/GradeTree/ClassFormDrawer';
import { useRef } from 'react';

function ClassManager() {
  const formRef = useRef<ClassFormDrawerRef>(null);
  
  const handleAddClass = () => {
    formRef.current?.openCreate({
      grade: '1',
      gradeName: '一年级',
      schoolId: 1001,
      schoolYearId: 2023,
    });
  };
  
  return (
    <>
      <Button onClick={handleAddClass}>添加班级</Button>
      <ClassFormDrawer
        ref={formRef}
        onSuccess={() => console.log('操作成功')}
      />
    </>
  );
}
```

## 班级表单字段说明

| 字段 | 说明 | 可选值 |
|------|------|--------|
| `class_name` | 班级名称 | 文本，最多10个字符 |
| `subject_type` | 文理班类型 | 1=不分文理，2=理科班，3=文科班 |
| `class_is_test` | 是否测试班 | 0=真实班，1=测试班 |
| `class_study_type` | 教学模式 | 1=平板班，2=传统班 |

## 最佳实践

1. **权限控制**：根据用户权限设置 `editableClass` 属性，控制用户是否有班级管理权限
2. **组合使用**：将 GradeTree 与具体内容展示区结合，实现"导航+内容"的布局
3. **数据刷新**：在关键操作后使用 `refreshData()` 保持数据最新状态
4. **状态同步**：受控模式下确保通过 `onDataUpdated` 同步外部状态

## 注意事项

1. **性能考虑**：大量数据时考虑使用虚拟滚动
2. **表单扩展**：实际使用时可能需要扩展班级表单字段
3. **班级编辑优化**：当编辑班级时，应先获取班级详情再填充表单
4. **样式自定义**：组件默认宽度为240px，可通过样式属性调整

## 拓展方向

- 添加搜索筛选功能
- 支持拖拽排序
- 实现批量操作功能
- 添加更详细的班级统计信息
- 集成权限系统

---

通过合理使用 GradeTree 组件，可以大幅提升教育管理系统中班级结构的可视化效果和操作效率，为用户提供直观、高效的管理体验。