// components/ClassFormDrawer.tsx
import { DrawerForm, ProFormInstance, ProFormRadio, ProFormText } from '@ant-design/pro-components';
import { message } from 'antd';
import { forwardRef, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';

import fetcher, { post } from '@/services/fetcher';
import { ClassDetail } from '@/types/class';
import useSWR from 'swr';
import useSWRMutation from 'swr/mutation';

interface ClassCreateParams {
  class_school_id: number;
  class_grade: number;
  class_school_year_id: number;
  class_type: number;
  class_study_type: number;
  class_is_test: number;
  class_name: string;
  subject_type: number;
  collaboration_status: number;
  subject_usage_status: number;
}
export type ClassFormValues = ClassCreateParams;

export interface ClassFormData {
  id?: string;
  grade: string;
  gradeName: string;
  schoolId: number;
  schoolYearId: number;
  collaborationStatus?: number; // 协作状态
  subjectUsageStatus?: number; // 学科使用状态
  initialValues?: Partial<ClassFormValues>;
}

export interface ClassFormDrawerProps {
  onSuccess?: () => Promise<void>;
}

export interface ClassFormDrawerRef {
  openCreate: (data: ClassFormData) => void;
  openEdit: (data: ClassFormData & { id: string }) => void;
}

// 表单初始值
const FORM_INIT_VALUES = {
  class_name: '',
  class_is_test: 1,
  class_study_type: 1,
  subject_type: 1,
};

const ClassFormDrawer = forwardRef<ClassFormDrawerRef, ClassFormDrawerProps>((props, ref) => {
  const { onSuccess } = props;
  const formRef = useRef<ProFormInstance<ClassDetail>>(undefined);
  const [visible, setVisible] = useState(false);
  const [title, setTitle] = useState('');
  const [currentData, setCurrentData] = useState<ClassFormData | null>(null);
  const [isEdit, setIsEdit] = useState(false);
  const [classId, setClassId] = useState<string | null>(null);
  // 使用 SWR mutation 创建班级
  const { trigger: createClass, isMutating: isCreating } = useSWRMutation(
    '/api/v1/class/create',
    post,
  );
  const { trigger: updateClass } = useSWRMutation('/api/v1/class/update', post);
  const { data: classDetail } = useSWR(
    classId ? `/api/v1/class/detail?class_id=${classId}` : null,
    fetcher<ClassDetail>,
  );

  const initFieldsValue = useMemo(() => {
    // 没有formRef 或者没有currentData，返回初始值
    if (!formRef.current || !currentData) return { ...FORM_INIT_VALUES };
    // 创建模式，返回表单初始值
    if (!isEdit)
      return {
        ...FORM_INIT_VALUES,
        ...currentData,
      };
    // 编辑模式，返回表单初始值 + 详情数据
    return {
      ...FORM_INIT_VALUES,
      ...currentData,
      ...classDetail,
    };
  }, [formRef.current, currentData, isEdit, classDetail]);

  useEffect(() => {
    if (formRef.current) {
      formRef.current.setFieldsValue(initFieldsValue);
    }
  }, [formRef.current, initFieldsValue]);

  useEffect(() => {
    if (classDetail) {
      formRef.current?.setFieldsValue({
        ...classDetail,
        class_name: classDetail.class_name,
        class_is_test: classDetail.class_is_test,
        class_study_type: classDetail.class_study_type,
        subject_type: classDetail.subject_type,
      });
    }
  }, [classDetail]);

  // 暴露方法给父组件
  useImperativeHandle(ref, () => ({
    openCreate: (data: ClassFormData) => {
      setTitle(`${data.gradeName}：新增班级`);
      setCurrentData(data);
      console.log('currentData => ', data, classDetail);
      setIsEdit(false);
      setVisible(true);
    },
    openEdit: (data: ClassFormData & { id: string }) => {
      setClassId(data.id);
      setTitle(`${data.gradeName}：编辑班级`);
      setCurrentData(data);
      console.log('currentData ===> ', data);
      setIsEdit(true);
      setVisible(true);
    },
  }));

  // 提交表单
  const handleSubmit = async (values: ClassFormValues) => {
    if (!currentData) return false;

    try {
      if (isEdit && currentData.id) {
        // 更新班级
        await updateClass({
          class_id: Number(currentData.id),
          ...values,
          class_grade: Number(currentData.grade),
          class_school_id: currentData.schoolId,
          class_school_year_id: currentData.schoolYearId,
        });
        message.success('更新班级成功');
      } else {
        // 创建班级
        await createClass({
          ...values,
          class_grade: Number(currentData.grade),
          class_school_id: currentData.schoolId,
          class_school_year_id: currentData.schoolYearId,
        });
        message.success('创建班级成功');
        setVisible(false);
      }

      // 执行成功回调
      if (onSuccess) {
        await onSuccess();
      }

      return true;
    } catch (error) {
      message.error(isEdit ? '更新班级失败' : '创建班级失败');
      console.error(error);
      return false;
    }
  };

  return (
    <DrawerForm
      title={title}
      formRef={formRef}
      open={visible}
      onOpenChange={(visible) => {
        if (!visible) {
          setCurrentData(null);
          setIsEdit(false);
        }
        setVisible(visible);
      }}
      width={400}
      onFinish={handleSubmit}
      submitter={{
        searchConfig: {
          resetText: '取消',
          submitText: '确定',
        },
        submitButtonProps: {
          loading: isCreating,
        },
      }}
    >
      <ProFormText
        name="class_name"
        label="班级名称"
        placeholder="请输入班级名称"
        rules={[{ required: true, message: '请输入班级名称' }]}
        fieldProps={{
          maxLength: 10,
          showCount: true,
        }}
      />

      <ProFormRadio.Group
        name="subject_type"
        label="文理班"
        rules={[{ required: true, message: '请选择文理类型' }]}
        options={[
          { label: '不分文理', value: 1 },
          { label: '理科班', value: 2 },
          { label: '文科班', value: 3 },
        ]}
      />

      <ProFormRadio.Group
        name="class_is_test"
        label="班级属性"
        rules={[{ required: true, message: '请选择班级属性' }]}
        options={[
          { label: '真实班', value: 1 },
          { label: '测试班', value: 2 },
        ]}
      />

      <ProFormRadio.Group
        name="class_study_type"
        label="班级类型"
        rules={[{ required: true, message: '请选择班级类型' }]}
        options={[
          { label: '平板班', value: 1 },
          { label: '传统班', value: 2 },
        ]}
      />
    </DrawerForm>
  );
});

export default ClassFormDrawer;
