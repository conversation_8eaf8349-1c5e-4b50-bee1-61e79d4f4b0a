// @ts-ignore
/* eslint-disable */

declare namespace API {

  // 通用响应类型
  type ResponseBody<T> = {
    status: number;
    code: number;
    message: string;
    response_time: number;
    data: T;
  };

  type TableData<T> = {
    list: T[];
    total: number;
    page: number;
    pageSize: number;
  }

  type LoginResult = {
    status: number;
    code: number;
    message: string;
    response_time: number;
    data: LoginResultData;

  };

  type LoginResultData = {
    token: string;
    user: UserInfo;
    isAlreadyLogin?: boolean;
  };

  interface UserInfo {
  userId: number;
  userName: string;
  userPhoneNumber: string;
  userEmploymentStatus: number;
  userStatus: number;
  remark: string;
  createTime: string;
  updateTime: string;
  menus: Menu[];
  roles: Role[];
  schools: null;
}

interface Role {
  createrId: number;
  createTime: string;
  deleted?: number;
  remark?: string;
  roleId: number;
  roleName: string;
  menuIds: number[];
  roleSort?: number;
  roleStatus: number;
  updaterId?: number;
  updateTime?: string;
}


interface Menu {
  menuId: number;
  menuName: string;
  menuParentId: number;
  menuOrderNum: number;
  menuPath: string;
  menuComponent: string;
  menuIsCache: number;
  menuType: number;
  menuVisible: number;
  menuPerms: string;
  menuIcon: string;
  menuStatus: number;
  menuPlatformId: number;
  remark: string;
  createrId: number;
  updaterId: number;
  deleted: number;
  createTime: string;
  updateTime: string;
}
  type PageParams = {
    current?: number;
    pageSize?: number;
  };

  type RuleListItem = {
    key?: number;
    disabled?: boolean;
    href?: string;
    avatar?: string;
    name?: string;
    owner?: string;
    desc?: string;
    callNo?: number;
    status?: number;
    updatedAt?: string;
    createdAt?: string;
    progress?: number;
  };

  type RuleList = {
    data?: RuleListItem[];
    /** 列表的内容总数 */
    total?: number;
    success?: boolean;
  };

  type FakeCaptcha = {
    code?: number;
    status?: string;
  };

  type LoginParams = {
    username?: string;
    password?: string;
    autoLogin?: boolean;
    type?: string;
    platForm?: number;
  };

  type ErrorResponse = {
    /** 业务约定的错误码 */
    errorCode: string;
    /** 业务上的错误信息 */
    errorMessage?: string;
    /** 业务上的请求是否成功 */
    success?: boolean;
  };

  type NoticeIconList = {
    data?: NoticeIconItem[];
    /** 列表的内容总数 */
    total?: number;
    success?: boolean;
  };

  type NoticeIconItemType = 'notification' | 'message' | 'event';

  type NoticeIconItem = {
    id?: string;
    extra?: string;
    key?: string;
    read?: boolean;
    avatar?: string;
    title?: string;
    status?: string;
    datetime?: string;
    description?: string;
    type?: NoticeIconItemType;
  };
}
