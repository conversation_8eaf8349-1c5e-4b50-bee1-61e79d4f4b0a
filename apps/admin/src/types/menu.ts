export type Menu = {
  createTime?: Date;
  createrId?: number;
  deleted?: number;
  menuComponent?: string;
  menuIcon?: string;
  menuId: number;
  menuIsCache?: number;
  menuName: string;
  menuOrderNum?: number;
  menuParentId: number;
  menuPath: string;
  menuPerms?: string;
  menuStatus?: number;
  menuType?: number;
  menuVisible?: number;
  remark?: string;
  updateTime?: Date;
  updaterId?: number;
  isUnderSchool?: number;
};

export type PlatformMenus = {
  platformId?: number;
  platformName: string;
  menus: Menu[];
};
