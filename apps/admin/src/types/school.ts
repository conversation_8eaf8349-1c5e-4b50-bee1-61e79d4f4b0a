// 学校合作状态枚举
export enum SchoolStatus {
  TRIAL = 'TRIAL', // 试用中
  PAID = 'PAID', // 付费中
  EXPIRED = 'EXPIRED', // 已过期
  TERMINATED = 'TERMINATED', // 已终止
}

interface PhraseGrade {
  id: number;
  name: string;
}
export interface RegionInfo {
  regionDictName: string;
  regionDictCode: number;
  regionDictParentCode: number;
}
interface SchoolYear {
  school_year: number;
  school_year_id: number;
  school_year_start: string;
  school_year_end: string;
  school_year_status: number;
}
// 学校完整信息
export interface SchoolItem {
  userIDs: number[];
  schoolYear: SchoolYear;
  schoolId: number;
  schoolNumber: string;
  schoolName: string;
  regionInfos: RegionInfo[];
  schoolAddress: string;
  schoolEduLevel: number;
  schoolEduSystem: number;
  schoolNature: number;
  schoolTag: string;
  schoolFeature: number;
  schoolIsTest: number;
  schoolStatus: number;
  remark: string;
  createrID: number;
  creatorName: string;
  updaterID: number;
  deleted: number;
  createTime: string;
  updateTime: string;
  phraseGrade: PhraseGrade[];
}
