import { CooperationType } from '@/types/cooperation';

// 历史记录状态枚举
export enum HistoryStatus {
  PENDING = 'pending',   // 待审批
  APPROVED = 'approved', // 已通过
  REJECTED = 'rejected', // 已拒绝
}

// 历史记录项接口
export interface HistoryItem {
  id: string;                   // 记录ID
  type: CooperationType;        // 记录类型
  status: HistoryStatus;        // 记录状态
  title: string;                // 记录标题
  operator: string;             // 操作人
  operateTime: string;          // 操作时间
  content?: {                   // 记录内容
    studentIds?: string;        // 学生ID列表，逗号分隔
    startTime?: string;         // 开始时间
    duration?: number;          // 持续天数
    policy?: string;            // 政策类型
    price?: number;             // 单价
    reason?: string;            // 原因
    desc?: string;              // 备注
  };
}
