export enum StudentStatus {
  /**
   * 尚未启用
   */
  DEFAULT = 1,
  /**
   * 试用
   */
  TRIAL = 2,
  /**
   * 付费
   */
  PAID = 3,
  /**
   * 停用
   */
  DISABLED = 4,
  /**
   * 尚未付费(试用过期，付费过期)
   */
  NOT_PAID = 5,
}

export const StudentStatusMap = {
  [StudentStatus.DEFAULT]: '尚未启用',
  [StudentStatus.TRIAL]: '试用',
  [StudentStatus.PAID]: '付费',
  [StudentStatus.DISABLED]: '停用',
  [StudentStatus.NOT_PAID]: '尚未付费',
}

// 学生是否测试
export enum StudentIsTest {
  /**
   * 否
   */
  NO = 1,
  /**
   * 是
   */
  YES = 2,
}

export const StudentIsTestMap = {
  [StudentIsTest.NO]: '否',
  [StudentIsTest.YES]: '是',
}
