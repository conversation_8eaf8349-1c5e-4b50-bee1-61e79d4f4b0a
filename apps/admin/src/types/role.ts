export interface Role {
  createrId: number;
  createTime: string;
  deleted?: number;
  remark?: string;
  roleId: number;
  roleName: string;
  menuIds: number[];
  roleSort?: number;
  roleStatus: number;
  updaterId?: number;
  updateTime?: string;
}
export interface RoleMultiPlatformMenu extends Omit<Role, 'menuIds'> {
  menuIds: PlatformMenu[];
}
export interface PlatformMenu {
  platformName: string;
  platformId: number;
  menuIds: number[];
}
interface Menu {
  menuId: number;
  menuName: string;
  menuParentId: number;
  menuOrderNum: number;
  menuPath: string;
  menuComponent: string;
  menuIsCache: number;
  menuType: number;
  menuVisible: number;
  menuPerms: string;
  menuIcon: string;
  menuStatus: number;
  menuPlatformId: number;
  remark: string;
  createrId: number;
  updaterId: number;
  deleted: number;
  createTime: string;
  updateTime: string;
}