// src/hooks/useRegions.ts
import { getProvinceList, getTopProvinceList } from '@/services/partner-school';
import { RegionItem } from '@/services/partner-school/types';
import { message } from 'antd';
import { useCallback, useEffect, useRef, useState } from 'react';

// 区域数据类型定义
export interface RegionItemOption extends RegionItem {
  children?: RegionItemOption[];
  isLeaf?: boolean;
  value: string | number; // 级联选择器需要的字段
  label: string; // 级联选择器需要的字段
  loading?: boolean;
}

  // Helper function to find an option by its region code
  const findOptionByCode = (
    options: RegionItemOption[], 
    code: number | string,
    currentPath: number[] = [],
    resultPath: number[] = []
  ): RegionItemOption | null => {
    for (let i = 0; i < options.length; i++) {
      const option = options[i];
      const optionCode = option.regionDictCode || option.value;
      
      // Check if this is the one we're looking for
      if (String(optionCode) === String(code)) {
        resultPath.push(...currentPath, i);
        return option;
      }
      
      // Check children recursively
      if (option.children && option.children.length > 0) {
        const found = findOptionByCode(
          option.children, 
          code, 
          [...currentPath, i],
          resultPath
        );
        if (found) return found;
      }
    }
    
    return null;
  };

  // Helper function to update an option at a specific path
  const updateOptionAtPath = (
    options: RegionItemOption[], 
    path: number[], 
    newValue: RegionItemOption
  ) => {
    if (path.length === 0) return;
    
    let current = options;
    for (let i = 0; i < path.length - 1; i++) {
      current = current[path[i]].children || [];
    }
    
    current[path[path.length - 1]] = newValue;
  };
/**
 * 区域级联数据管理 Hook
 */
export function useRegions() {
  const [regionOptions, setRegionOptions] = useState<RegionItemOption[]>([]);
  const [loading, setLoading] = useState(false);
  const isLoadingRef = useRef(false);
  
  // Cache for region data to prevent duplicate requests
  const regionCache = useRef<Record<string, RegionItemOption[]>>({});
  const initializingPaths = useRef<Set<string>>(new Set());

  // 获取顶级省份
  const fetchTopProvinces = useCallback(async () => {
    if (regionOptions.length > 0 || isLoadingRef.current) {
      return regionOptions;
    }

    isLoadingRef.current = true;
    setLoading(true);

    try {
      // Check cache first
      if (regionCache.current['top']) {
        setRegionOptions(regionCache.current['top']);
        return regionCache.current['top'];
      }

      const response = await getTopProvinceList();

      if (response && response.code === 0 && Array.isArray(response.data)) {
        const provinces = response.data.map((item) => ({
          ...item,
          isLeaf: false,
          value: item.regionDictCode,
          label: item.regionDictName,
        }));

        // Update cache
        regionCache.current['top'] = provinces;
        setRegionOptions(provinces);
        return provinces;
      } else {
        message.error(response?.message || '获取省份列表失败');
        return [];
      }
    } catch (error) {
      console.error('获取省份列表失败:', error);
      message.error('获取省份列表失败');
      return [];
    } finally {
      setLoading(false);
      isLoadingRef.current = false;
    }
  }, [regionOptions.length]);

  // 加载下级区域
  const fetchRegionChildren = useCallback(async (regionCode: number): Promise<RegionItemOption[]> => {
    // Check cache first
    const cacheKey = `region_${regionCode}`;
    if (regionCache.current[cacheKey]) {
      return regionCache.current[cacheKey];
    }

    try {
      const response = await getProvinceList(regionCode);
      
      if (response && response.code === 0 && Array.isArray(response.data)) {
        const children = response.data.map((item) => ({
          ...item,
          isLeaf: false,
          value: item.regionDictCode,
          label: item.regionDictName,
        }));
        
        // Update cache
        regionCache.current[cacheKey] = children;
        return children;
      }
      return [];
    } catch (error) {
      console.error(`获取区域 ${regionCode} 的子级失败:`, error);
      return [];
    }
  }, []);

  // For Ant Design Cascade component
  const loadRegionData = useCallback(async (selectedOptions: RegionItemOption[]) => {
    if (!selectedOptions || selectedOptions.length === 0) return;

    const targetOption = selectedOptions[selectedOptions.length - 1];
    if (!targetOption) return;

    targetOption.loading = true;
    setRegionOptions([...regionOptions]);

    try {
      const children = await fetchRegionChildren(Number(targetOption.value || targetOption.regionDictCode));
      
      if (children.length === 0) {
        targetOption.isLeaf = true;
      } else {
        targetOption.children = children;
      }
    } catch (error) {
      targetOption.isLeaf = false;
      targetOption.children = undefined;
    } finally {
      targetOption.loading = false;
      setRegionOptions([...regionOptions]);
    }
  }, [fetchRegionChildren, regionOptions]);

  // Completely rewritten loadRegionDataInit
  const loadRegionDataInit = useCallback(async (regions?: RegionItem[]) => {
    if (!regions || regions.length === 0) return;
    
    // Create a unique key for this region path
    const pathKey = regions.map(r => r.regionDictCode).join('-');
    
    // Skip if we're already initializing this path
    if (initializingPaths.current.has(pathKey)) return;
    initializingPaths.current.add(pathKey);
    
    setLoading(true);
    
    try {
      // Make sure we have top-level data
      let currentOptions = regionOptions.length > 0 ? 
        regionOptions : 
        await fetchTopProvinces();
        
      if (currentOptions.length === 0) {
        throw new Error('Failed to load province data');
      }
      
      // We'll build a deep clone of the options to modify
      let optionsCopy = JSON.parse(JSON.stringify(currentOptions));
      let hasUpdates = false;
      
      // For each level in the region path
      for (let i = 0; i < regions.length - 1; i++) {
        const currentRegion = regions[i];
        const nextRegion = regions[i + 1];
        
        // Find the current region in our options
        let currentPath: number[] = [];
        let currentTarget = findOptionByCode(
          optionsCopy, 
          currentRegion.regionDictCode, 
          [], 
          currentPath
        );
        
        if (!currentTarget) {
          console.warn(`Could not find region with code ${currentRegion.regionDictCode}`);
          break;
        }
        
        // Load its children if needed
        if (!currentTarget.children || currentTarget.children.length === 0) {
          const children = await fetchRegionChildren(Number(currentRegion.regionDictCode));
          
          // Update our copy with the new children
          if (children.length > 0) {
            // Use the path to update the correct node
            updateOptionAtPath(optionsCopy, currentPath, {
              ...currentTarget,
              children,
              loading: false
            });
            hasUpdates = true;
            
            // Continue searching at the next level
            currentTarget.children = children;
          } else {
            // No children, can't go deeper
            break;
          }
        }
        
        // Find the next region among the children
        let found = false;
        if (currentTarget.children) {
          // The next region we're looking for is among these children
          for (let child of currentTarget.children) {
            if (child.regionDictCode === nextRegion.regionDictCode) {
              found = true;
              break;
            }
          }
        }
        
        if (!found) {
          console.warn(`Could not find child region with code ${nextRegion.regionDictCode}`);
          break;
        }
      }
      
      // If we've made changes, update the state
      if (hasUpdates) {
        setRegionOptions(optionsCopy);
      }
    } catch (error) {
      console.error('Failed to initialize region data:', error);
    } finally {
      setLoading(false);
      // Remove from tracking since we're done with this path
      initializingPaths.current.delete(pathKey);
    }
  }, [fetchTopProvinces, fetchRegionChildren, regionOptions]);



  // 在组件挂载时获取顶级省份
  useEffect(() => {
    fetchTopProvinces();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  return {
    regionOptions,
    loading,
    loadRegionData,
    fetchTopProvinces,
    loadRegionDataInit
  };
}

export default useRegions;