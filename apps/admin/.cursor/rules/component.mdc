---
description: 
globs: 
alwaysApply: true
---
## Component Encapsulation
- **Form Scenarios**: Prioritize using components like ProForm, ModalForm, and StepsForm. Use Ant Design Form components only when they do not meet custom requirements.
- **List Scenarios**: Prioritize using ProTable. Use Ant Design Table components only when they do not meet custom requirements.
- **Component Size**: No component should exceed 600 lines of code. If necessary, split the component or encapsulate logic into custom hooks.
- **Table Total Display**: All tables must display the total number of items. The text `Total {total} items` should be placed on the left side of the pagination, with the `total` value displayed in blue font.
- **Color Usage**: Use Ant Design's color palette for all color-related styling.
- **Form Layout**:
  - For forms located above tables, place labels above the input fields.
  - In all other scenarios, place labels to the left of the input fields.
  - Buttons in modal forms should be aligned to the right.
- **Mock Data**: Newly developed components should include mock data to allow users to validate business logic easily. Add `TODO` comments to indicate areas for future improvement or updates.