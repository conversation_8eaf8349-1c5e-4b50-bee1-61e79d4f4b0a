---
description: 
globs: 
alwaysApply: false
---
# Rules for User-Backend Joint Debugging

During the backend joint debugging phase, users have typically completed the majority of front-end component development. The primary task at this stage is to align data structures and fields with the backend. Avoid modifying existing functionality unless absolutely necessary. If adjustments are required, make them sparingly.

If a component is highly complete but the backend interface fields do not match, prioritize writing data transformation functions to convert the data format. This approach minimizes disruption to the front-end component logic.

[fetcher.ts](mdc:src/services/fetcher.ts)
When using SWR to call APIs, there is no need to encapsulate API functions separately. Directly use SWR within the component to make the calls.

- **Global Type Definitions**: Place globally used type definitions under `src/types`.
- **Component-Specific Type Definitions**: Place non-global type definitions in a `type.ts` file within the current component directory.
- All interface calls use swr, useRequest and request are prohibited

---

### SWR Example

```tsx
// Fetch menu list
const { data: menus } = useSWR<PlatformMenus[]>('/api/v1/menu/list', fetcher);

// Manual API call
const { trigger, isMutating, error } = useSWRMutation('/api/v1/role/create', post);

const [form] = Form.useForm<Role>();

const submit = useCallback(async () => {
  try {
    await trigger(form.getFieldsValue());
    refreshRoleList();
    onSuccess();
  } catch (err) {
    console.error('Submission failed:', err);
  }
}, [form, onSuccess, trigger]);
```


By following these guidelines, you can ensure a smooth integration with the backend while maintaining clean, maintainable, and user-friendly front-end code.