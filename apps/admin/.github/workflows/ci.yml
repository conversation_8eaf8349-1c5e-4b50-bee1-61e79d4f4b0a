name: Node CI

on: [push, pull_request]

permissions:
  contents: read

jobs:
  build:
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        node_version: [16.x, 14.x]
        os: [ubuntu-latest, windows-latest, macOS-latest]
    steps:
      - uses: actions/checkout@v1
      - name: Use Node.js ${{ matrix.node_version }}
        uses: actions/setup-node@v1
        with:
          node-version: ${{ matrix.node_version }}
      - run: echo ${{github.ref}}
      - run: npm install --legacy-peer-deps
      - run: yarn run lint
      - run: yarn run tsc
      - run: yarn run build
        env:
          CI: true
          PROGRESS: none
          NODE_ENV: test
          NODE_OPTIONS: --max_old_space_size=4096
