# React 19 升级指南

## 🎯 **官方推荐解决方案：使用 Ant Design React 19 兼容性补丁**

### ✅ **最佳实践（已完成）**

Ant Design 官方为 React 19 提供了专门的兼容性补丁包，这是最简单、最可靠的解决方案：

#### 1. 升级 Ant Design 到最新版本
```bash
pnpm add antd@latest
```
当前项目已升级到：`antd@5.25.3`

#### 2. 安装官方 React 19 兼容性补丁
```bash
pnpm add @ant-design/v5-patch-for-react-19 --save
```

#### 3. 在应用入口导入补丁包
在 `src/app.tsx` 文件顶部添加：
```tsx
import '@ant-design/v5-patch-for-react-19';
```

#### 4. 升级 Pro Components 到兼容版本
```bash
pnpm add @ant-design/pro-components@latest
```
当前项目已升级到：`@ant-design/pro-components@2.8.7`

### 🔧 **补丁包解决的问题**

这个官方补丁包自动解决了以下 React 19 兼容性问题：
- ✅ Modal.confirm、message.success 等静态方法调用警告
- ✅ ref 属性访问问题
- ✅ 组件渲染和生命周期兼容性
- ✅ 主题上下文消费问题
- ✅ Cascader 组件的 onDropdownVisibleChange 废弃警告

### 📊 **升级前后对比**

| 项目 | 升级前 | 升级后 | 状态 |
|------|--------|--------|------|
| React | 19.1.0 | 19.1.0 | ✅ 保持 |
| Ant Design | 5.12.7 | 5.25.3 | ✅ 已升级 |
| Pro Components | 2.6.44 | 2.8.7 | ✅ 已升级 |
| 兼容性补丁 | 无 | 1.0.3 | ✅ 已安装 |

### 🚀 **验证结果**

- ✅ 开发服务器启动成功
- ✅ 无 React 19 兼容性警告
- ✅ Modal.confirm 正常工作
- ✅ message 静态方法正常工作
- ✅ Cascader onDropdownVisibleChange 警告已消除

---

## 🔍 **备用解决方案（仅供参考）**

如果官方补丁包不能解决所有问题，可以参考以下手动解决方案：

### 解决 Ant Design 静态方法调用警告

#### 问题描述
升级到 React 19 后，您可能会遇到以下警告：
```
Warning: [antd: Modal] Static function can not consume context like dynamic theme. Please use 'App' component instead.
```

#### 手动解决方案

##### 1. 应用入口配置 App 组件
在 `src/app.tsx` 中的 `childrenRender` 函数中添加了 `App` 组件包装：

```tsx
import { App } from 'antd';

export const layout: RunTimeLayoutConfig = ({ initialState, setInitialState }) => {
  return {
    // ... 其他配置
    childrenRender: (children) => {
      return (
        <App className="admin-app-wrapper">
          {children}
          {/* 其他内容 */}
        </App>
      );
    },
  };
};
```

##### 2. 创建自定义 Hooks
创建了 `src/hooks/useMessage.ts` 来替代静态方法调用：

```tsx
import { App } from 'antd';

export const useMessage = () => {
  const { message } = App.useApp();
  return message;
};

export const useModal = () => {
  const { modal } = App.useApp();
  return modal;
};

export const useNotification = () => {
  const { notification } = App.useApp();
  return notification;
};
```

##### 3. 在组件中使用新的 Hooks

**❌ 旧的写法（会产生警告）：**
```tsx
import { message, Modal } from 'antd';

// 在组件外部或组件内部直接调用
message.success('操作成功');
Modal.confirm({ title: '确认删除？' });
```

**✅ 新的写法（推荐）：**
```tsx
import { useMessage, useModal } from '@/hooks/useMessage';

const MyComponent = () => {
  const message = useMessage();
  const modal = useModal();
  
  const handleSuccess = () => {
    message.success('操作成功');
  };
  
  const handleDelete = () => {
    modal.confirm({
      title: '确认删除？',
      onOk: () => {
        // 删除逻辑
      }
    });
  };
  
  return (
    // 组件内容
  );
};
```

### 迁移清单

需要将以下静态方法调用替换为 Hook 调用：

- [ ] `message.success()` → `useMessage().success()`
- [ ] `message.error()` → `useMessage().error()`
- [ ] `message.warning()` → `useMessage().warning()`
- [ ] `message.info()` → `useMessage().info()`
- [ ] `message.loading()` → `useMessage().loading()`
- [ ] `Modal.confirm()` → `useModal().confirm()`
- [ ] `Modal.info()` → `useModal().info()`
- [ ] `notification.open()` → `useNotification().open()`

### 注意事项

1. **函数组件内使用**：只能在 React 函数组件内部使用这些 hooks
2. **移动函数到组件内部**：如果有在组件外部定义的函数使用了静态方法，需要将这些函数移动到组件内部
3. **依赖数组**：在 `useEffect`、`useCallback` 等 Hook 中使用时，可能需要添加到依赖数组中

### 示例：完整的迁移案例

参考 `src/pages/list/table-list/index.tsx` 文件，已完成从静态方法到 Hook 的迁移。

### 常见问题

**Q: 为什么不能在组件外部使用这些 hooks？**
A: React Hooks 只能在函数组件内部或自定义 Hook 内部调用，这是 React 的规则。

**Q: 遇到 "modal.confirm is not a function" 错误怎么办？**
A: 确保：
1. 应用已被 `<App>` 组件包装
2. 在函数组件内部使用 hooks
3. 检查 Ant Design 版本是否支持 App.useApp()

**Q: 如果我有很多文件需要迁移，有自动化工具吗？**
A: 可以使用以下命令查找需要迁移的文件：
```bash
# 查找使用 message 静态方法的文件
grep -r "message\.\(success\|error\|warning\|info\|loading\)" src/

# 查找使用 Modal 静态方法的文件  
grep -r "Modal\.\(confirm\|info\|success\|error\|warning\)" src/
```

然后手动进行迁移。

### 临时解决方案

如果遇到 `modal.confirm is not a function` 错误，可以使用以下临时解决方案：

#### 方案一：直接使用 Modal.confirm（推荐）
```tsx
import { Modal, message } from 'antd';
import { useMessage } from '@/hooks/useMessage';

const MyComponent = () => {
  const message = useMessage(); // 使用 hooks 版本的 message
  
  const handleConfirm = () => {
    // 对于 Modal.confirm，暂时使用静态方法
    Modal.confirm({
      title: '确认',
      content: '确认要执行此操作吗？',
      onOk: () => {
        message.success('操作成功'); // message 使用 hooks 版本
      }
    });
  };
  
  return <Button onClick={handleConfirm}>确认</Button>;
};
```

#### 方案二：检查 modal 对象（调试用）
```tsx
const modal = useModal();
console.log('modal 对象:', modal);
console.log('modal 的方法:', Object.keys(modal || {}));

// 如果 modal.confirm 不存在，使用 Modal.confirm
const showConfirm = () => {
  if (modal && modal.confirm) {
    modal.confirm({ title: '确认' });
  } else {
    Modal.confirm({ title: '确认' });
  }
};
```

#### 方案三：升级 Ant Design 版本
考虑升级到最新版本的 Ant Design，可能已经修复了这个问题：
```bash
npm update antd
``` 