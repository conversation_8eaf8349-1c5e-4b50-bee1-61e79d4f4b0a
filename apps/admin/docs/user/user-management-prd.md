# 用户管理模块 PRD

## 产品角度

### 1. 功能概述
用户管理模块用于管理系统中的用户账号，支持用户信息的维护、角色分配和状态管理。

### 2. 核心功能
1. **用户列表**
   - 展示所有用户信息
   - 支持用户状态（在职/离职）的快速切换
   - 显示用户基本信息、角色信息等

2. **用户创建**
   - 设置用户基本信息（姓名、手机号等）
   - 分配用户角色
   - 设置用户状态
   - 可选设置备注信息

3. **用户编辑**
   - 修改用户基本信息
   - 调整用户角色
   - 更新用户状态
   - 修改备注信息

4. **用户详情**
   - 查看用户基本信息
   - 展示已分配的角色
   - 显示用户状态变更历史

### 3. 用户交互
1. **列表页**
   - 表格展示用户信息
   - 支持多条件搜索（姓名、手机号、角色等）
   - 操作列包含编辑、查看详情、状态切换等
   - 支持批量操作

2. **编辑页**
   - 表单形式编辑用户信息
   - 角色多选
   - 状态切换
   - 表单验证

3. **详情页**
   - 清晰展示用户信息
   - 展示已分配角色
   - 支持快速状态切换

### 4. 业务规则
1. 手机号不可重复
2. 用户名不可重复
3. 状态变更需要二次确认
4. 离职用户不可登录系统
5. 支持用户复职操作

## 技术角度

### 1. 技术架构
1. **前端架构**
   - 基于 React + TypeScript
   - 使用 Ant Design 组件库
   - 采用 Context API 管理状态
   - 使用 SWR 处理数据请求

2. **数据流设计**
   - 使用 Context 管理用户相关状态
   - 采用 SWR 处理数据获取和缓存
   - 实现乐观更新提升用户体验

### 2. 核心组件
1. **UserList**
   - 用户列表展示
   - 搜索条件处理
   - 状态管理
   - 操作处理

2. **UserEdit**
   - 表单处理
   - 角色选择
   - 数据验证
   - 提交处理

3. **UserDetail**
   - 信息展示
   - 角色展示
   - 状态管理

### 3. 数据结构
```typescript
// 用户基础信息
interface User {
  userId: number;
  userName: string;
  userPhone: string;
  userStatus: number;
  roleIds: number[];
  // ...其他基础字段
}

// 用户列表查询参数
interface UserListParams {
  schoolID: number;
  userName: string;
  userPhone: string;
  jobSubject: number;
  teacherEmployStatus: number;
  jobGrade: number;
  jobClass: number;
}

// 教师信息
interface TeacherInfo extends User {
  jobType: number;
  jobSubject?: number;
  jobGrade?: number;
  jobClass?: number;
  teacherEmploymentStatus: number;
}
```

### 4. 性能优化
1. **数据缓存**
   - 使用 SWR 缓存用户列表
   - 实现角色数据的本地缓存
   - 优化重复请求

2. **组件优化**
   - 使用 useMemo 优化计算
   - 实现组件懒加载
   - 优化重渲染逻辑
   - 分页加载优化

### 5. 安全考虑
1. 权限验证
   - 接口权限控制
   - 操作权限校验
   - 数据访问控制

2. 数据安全
   - 敏感信息加密
   - 操作日志记录
   - 数据备份机制

### 6. 扩展性设计
1. 支持自定义用户属性
2. 预留角色分配扩展接口
3. 支持多语言配置
4. 预留主题定制接口
5. 支持用户导入导出 