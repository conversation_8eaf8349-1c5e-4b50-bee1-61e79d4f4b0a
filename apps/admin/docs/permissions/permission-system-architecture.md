# 权限系统架构设计

## 概述

本系统采用两级权限管理机制，分为全局级别和学校级别，确保不同角色的用户只能访问和操作其权限范围内的资源。权限系统基于 RBAC (Role-Based Access Control) 模型，通过角色分配和菜单权限控制用户访问。

## 权限级别结构

### 1. 全局级别权限
- **适用对象**: 总部管理人员
- **权限范围**:
  - 查看所有合作学校信息
  - 管理所有学校的运营人员
  - 审批学校合作状态变更
  - 查看全局数据统计
- **访问路径**: `/partner-school/list`

### 2. 学校级别权限
- **适用对象**: 学校管理员
- **权限范围**:
  - 仅查看和管理自己所在学校的信息
  - 管理本校的教职工账号
  - 申请合作状态变更
  - 查看本校数据统计
- **访问路径**: `/my-partner-school/list`

## 角色定义

### 1. 总部运营人员
- **职责**:
  - 管理所有合作学校
  - 审批学校合作状态变更
  - 分配学校顶级管理员
- **权限特点**:
  - 全局视角
  - 最高权限级别
  - 可跨学校操作

### 2. 学校顶级管理员
- **职责**:
  - 管理本校运营
  - 设置本校管理员
  - 申请合作状态变更
- **权限特点**:
  - 仅限于本校范围
  - 可设置下级管理员
  - 权限受总部运营人员控制

### 3. 学校普通管理员
- **职责**:
  - 管理本校日常运营
  - 维护本校数据
- **权限特点**:
  - 权限由学校顶级管理员分配
  - 仅限于特定功能模块
  - 无法进行系统级设置

## 前端权限控制

### 1. 路由权限控制

系统使用多层次的权限控制机制确保用户只能访问其有权限的页面和功能：

#### 1.1 路由配置中的权限控制

在 `routes.ts` 文件中，通过 `access` 属性定义路由的访问权限：

```typescript
export default [
  {
    path: '/partner-school',
    routes: [
      {
        path: '/partner-school/list',
        component: './partner-school/list',
        access: 'partnerSchoolList', // 权限点标识
      },
      {
        path: '/partner-school/management',
        routes: [
          {
            path: '/partner-school/management/academic/semester',
            component: './partner-school/schoolManagement/components/Academic/SemesterManagement',
            access: '/partner-school/management/academic/semester', // 使用路径作为权限标识
          },
          // 更多子路由...
        ],
      },
    ],
  },
  // 更多路由...
];
```

#### 1.2 访问权限验证

在 `access.ts` 文件中定义权限验证逻辑：

```typescript
export default function access(initialState: { currentUser?: API.CurrentUser }) {
  const { currentUser } = initialState || {};
  const menuPaths = currentUser?.menus?.map(menu => menu.menuPath) || [];
  
  // 将菜单路径转换为权限点
  const accessMap = menuPaths.reduce((acc, path) => {
    acc[path] = true;
    return acc;
  }, {} as Record<string, boolean>);

  return {
    // 通用权限检查函数
    canAccess: (route: string) => !!accessMap[route],
    
    // 特定路由权限
    partnerSchoolList: !!accessMap['/partner-school/list'],
    myPartnerSchoolList: !!accessMap['/my-partner-school/list'],
    // 更多权限点...
  };
}
```

### 2. 组件级权限控制

在组件内部使用 `useAccess` hook 进行权限检查：

```typescript
import { useAccess } from '@umijs/max';

const YourComponent = () => {
  const access = useAccess();
  
  // 检查是否有特定操作权限
  if (access.canAccess('/partner-school/management/permission')) {
    // 显示权限管理功能
  }
  
  return (
    // 组件渲染逻辑
  );
};
```

### 3. 初始路由访问控制

系统根据用户的权限自动导航到相应的页面：

```typescript
// app.tsx 中的路由控制
onPageChange: () => {
  const { location } = history;
  const { currentUser } = initialState || {}
  const menus = currentUser?.menus || []

  if (location.pathname === '/') {
    // 判断是否有全局级别权限
    const hasPartnerSchoolList = menus.some(menu => menu.menuPath === '/partner-school/list')
    if (hasPartnerSchoolList) {
      // 总部运营人员导航到合作校列表
      history.push('/partner-school/list')
    } else {
      // 学校管理员导航到我的合作校列表
      history.push('/my-partner-school/list')
    }
  }
  
  // 如果没有登录，重定向到 login
  if (!initialState?.currentUser && location.pathname !== loginPath) {
    history.push(loginPath);
  }
}
```

### 4. 多级权限模型

系统实现了多级权限模型，包括：

1. **平台级权限**：控制用户可访问的平台功能，如合作学校管理、系统管理等
2. **学校级权限**：控制用户在特定学校内的权限，如学期管理、课表管理等
3. **功能级权限**：控制用户在特定功能模块内的操作权限，如查看、编辑、删除等

#### 4.1 平台权限与学校权限分离

系统区分了两类用户路径：
- `/partner-school/*`：总部运营人员路径
- `/my-partner-school/*`：学校管理员路径

#### 4.2 学校超级管理员特权

对于学校顶级管理员，系统提供特殊权限处理：

```typescript
// 判断是否为学校顶级管理员
const isSchoolSuperAdmin = useMemo(() => {
  if (!isMyPartnerSchool) return true;
  
  return Array.isArray((schoolInfo)?.userIDs) &&
    schoolInfo?.userIDs?.includes(currentUser?.userId || 0);
}, [schoolInfo, currentUser, isMyPartnerSchool]);

// 超级管理员权限检查逻辑
const hasPermission = (path: string) => {
  if (isSchoolSuperAdmin) {
    return access[path];
  }
  
  // 普通用户权限检查
  // ...
};
```

## 权限控制规则

### 1. 访问控制
1. **全局访问**:
   - 仅总部运营人员可访问`/partner-school/list`
   - 可查看所有学校信息
   - 可进行全局设置

2. **学校访问**:
   - 学校管理员只能访问`/my-partner-school/list`
   - 仅能查看和管理自己所在学校
   - 权限范围受上级管理员控制

### 2. 操作控制
1. **人员管理**:
   - 总部运营人员可设置学校顶级管理员
   - 学校顶级管理员可设置本校普通管理员
   - 下级管理员权限不得超过上级

2. **合作状态变更**:
   - 学校管理员可申请变更合作状态
   - 总部运营人员负责审批
   - 变更记录需存档

3. **数据访问**:
   - 总部运营人员可查看全局数据
   - 学校管理员仅能查看本校数据
   - 数据访问需记录日志

## 权限检查实现

在页面组件中，根据权限动态显示/隐藏功能：

```typescript
const SchoolManagement = () => {
  const access = useAccess();
  
  // 检查用户是否有权限访问指定路径
  const hasPermission = (path: string) => {
    if (isSchoolSuperAdmin) {
      return access[path];
    }

    // 如果权限数据正在加载，默认不允许访问
    if (loadingPermissions || !schoolPermissions?.list) {
      return false;
    }

    // 检查用户是否有该路径的权限
    return schoolPermissions.list.some((menu) => menu.menuPath === path);
  };
  
  // 在渲染或导航逻辑中使用权限检查
  const handleTabChange = (tab: string) => {
    const targetPath = `${routePrefix}/management/${tab}`;
    
    if (!hasPermission(targetPath)) {
      message.error('您没有权限访问此页面');
      return;
    }
    
    navigate(targetPath);
  };
  
  return (
    // 页面渲染逻辑
  );
};
```

## 后端权限控制

### 1. 接口权限验证

所有接口请求都会进行权限验证，主要包括：

1. **Token 验证**：验证用户身份
2. **接口权限验证**：验证用户是否有权调用特定接口
3. **数据权限验证**：验证用户是否有权操作特定数据

### 2. 权限数据结构

```typescript
// 菜单权限结构
interface Menu {
  menuId: number;
  menuName: string;
  menuParentId: number;
  menuPath: string;
  menuComponent: string;
  menuType: number;
  menuVisible: number;
  menuPerms: string;
  menuStatus: number;
}

// 角色权限结构
interface Role {
  roleId: number;
  roleName: string;
  menuIds: number[] | PlatformMenu[];
  roleStatus: number;
}

// 多平台菜单权限
interface PlatformMenu {
  platformName: string;
  platformId: number;
  menuIds: number[];
}
```

### 3. 权限数据获取

系统在以下情况下加载权限数据：

1. **用户登录时**：加载用户的基本权限
2. **进入学校管理页面时**：加载用户在特定学校的权限

```typescript
// 获取用户在当前学校的权限
const { data: schoolPermissions } = useSWR<SchoolPermissionResponse>(
  schoolId ? `/api/v1/user_school_menu/list?userId=${currentUser?.userId}&schoolId=${schoolId}` : null,
  fetcher
);
```

## 安全机制

### 1. 权限验证
- 每次操作前验证用户权限
- 记录操作日志
- 异常操作报警

### 2. 数据保护
- 敏感信息加密存储
- 数据访问权限控制
- 操作记录可追溯

### 3. 审计机制
- 定期审查权限分配
- 监控异常操作
- 自动生成审计报告

## 合作管理中的权限

### 1. 合作模式

系统支持学校合作管理，主要包括以下权限控制点：

1. **合作创建权限**：控制谁可以发起合作（总部运营人员）
2. **合作审批权限**：控制谁可以审批合作请求（总部运营人员）
3. **合作管理权限**：控制谁可以管理合作关系

### 2. 合作流程权限控制

在 `CooperationModal` 组件中：

```typescript
// 根据用户权限和合作状态控制可见操作
const getActionButtons = () => {
  if (userHasApprovePermission && status === 'pending') {
    return (
      <>
        <Button onClick={handleApprove}>批准</Button>
        <Button onClick={handleReject}>拒绝</Button>
      </>
    );
  }
  
  if (userHasEditPermission) {
    return <Button onClick={handleEdit}>编辑</Button>;
  }
  
  return null;
};
```

## 权限分配流程

### 1. 总部运营人员
1. 登录系统
2. 进入"合作校管理"
3. 选择目标学校
4. 设置学校顶级管理员
5. 分配相应权限

### 2. 学校顶级管理员
1. 登录系统
2. 进入"我的合作校"
3. 选择"权限管理"
4. 添加本校管理员
5. 分配具体权限

## 权限扩展性设计

### 1. 动态权限配置

系统支持通过后台配置动态调整权限，无需修改代码：

1. 新增菜单项
2. 配置菜单权限
3. 分配给相应角色

### 2. 多维度权限控制

系统支持多维度的权限控制：

1. **功能维度**：控制用户可用的功能模块
2. **数据维度**：控制用户可访问的数据范围
3. **操作维度**：控制用户可执行的具体操作

### 3. 权限继承机制

权限支持继承机制，简化权限配置：

1. 子菜单权限继承父菜单权限
2. 特定操作权限继承查看权限
3. 下级管理员权限不得超过上级

## 异常处理

### 1. 权限不足
- 提示具体原因
- 记录访问尝试
- 通知系统管理员

### 2. 数据访问异常
- 终止异常访问
- 记录异常信息
- 触发安全警报

```typescript
// 处理无权限访问的情况
if (!hasPermission(targetPath)) {
  message.error('您没有权限访问此页面');
  logger.warn(`用户 ${currentUser?.userId} 尝试访问无权限页面: ${targetPath}`);
  return;
}
```

## 最佳实践

### 1. 权限检查规范

1. 在路由配置中使用 `access` 属性进行粗粒度控制
2. 在组件内使用 `useAccess` 进行细粒度控制
3. 对关键操作进行显式权限检查

### 2. 权限设计原则

1. **最小权限原则**：用户只被授予完成工作所需的最小权限集
2. **职责分离**：关键操作需要多人协作完成
3. **易于理解**：权限设计应直观、易于理解和管理

### 3. 权限调试技巧

1. 使用浏览器开发工具查看当前用户权限
2. 检查接口返回的权限数据
3. 测试边界情况（如权限刚被授予或刚被移除）

## 维护与更新

### 1. 权限维护
- 定期审查权限分配
- 及时调整权限设置
- 清理过期权限

### 2. 系统更新
- 更新前备份权限配置
- 测试新权限功能
- 更新后验证权限有效性 