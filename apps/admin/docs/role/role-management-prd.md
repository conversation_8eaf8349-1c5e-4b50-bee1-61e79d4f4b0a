# 角色管理模块 PRD

## 产品角度

### 1. 功能概述
角色管理模块用于管理系统中的角色权限，支持多平台菜单权限的分配和管理。

### 2. 核心功能
1. **角色列表**
   - 展示所有角色信息
   - 支持角色状态（启用/禁用）的快速切换
   - 显示角色创建时间、更新时间等基本信息

2. **角色创建**
   - 设置角色名称
   - 分配菜单权限（支持多平台）
   - 可选设置角色备注

3. **角色编辑**
   - 修改角色名称
   - 调整菜单权限
   - 更新角色备注

4. **角色详情**
   - 查看角色基本信息
   - 展示已分配的菜单权限
   - 显示角色状态变更历史

### 3. 用户交互
1. **列表页**
   - 表格展示角色信息
   - 支持角色搜索
   - 操作列包含编辑、查看详情、状态切换等

2. **编辑页**
   - 表单形式编辑角色信息
   - 树形结构展示菜单权限
   - 支持多平台菜单的批量选择

3. **详情页**
   - 清晰展示角色信息
   - 树形结构展示已分配权限
   - 支持快速状态切换

### 4. 业务规则
1. 角色名称不可重复
2. 系统默认角色不可删除
3. 角色状态变更需要二次确认
4. 菜单权限支持多平台独立配置

## 技术角度

### 1. 技术架构
1. **前端架构**
   - 基于 React + TypeScript
   - 使用 Ant Design 组件库
   - 采用 Context API 管理状态
   - 使用 SWR 处理数据请求

2. **数据流设计**
   - 使用 Context 管理角色相关状态
   - 采用 SWR 处理数据获取和缓存
   - 实现乐观更新提升用户体验

### 2. 核心组件
1. **RoleList**
   - 角色列表展示
   - 状态管理
   - 操作处理

2. **RoleEdit**
   - 表单处理
   - 权限树组件
   - 数据提交

3. **MenuTree**
   - 多平台菜单树
   - 权限选择逻辑
   - 状态同步

### 3. 数据结构
```typescript
// 角色基础信息
interface Role {
  roleId: number;
  roleName: string;
  roleStatus: number;
  menuIds: number[];
  // ...其他基础字段
}

// 多平台菜单角色
interface RoleMultiPlatformMenu extends Omit<Role, 'menuIds'> {
  menuIds: PlatformMenu[];
}

// 平台菜单
interface PlatformMenu {
  platformName: string;
  platformId: number;
  menuIds: number[];
}
```

### 4. 性能优化
1. **数据缓存**
   - 使用 SWR 缓存角色列表
   - 实现菜单树的本地缓存
   - 优化重复请求

2. **组件优化**
   - 使用 useMemo 优化计算
   - 实现组件懒加载
   - 优化重渲染逻辑

### 5. 安全考虑
1. 权限验证
   - 接口权限控制
   - 操作权限校验
   - 数据访问控制

2. 数据安全
   - 敏感信息加密
   - 操作日志记录
   - 数据备份机制

### 6. 扩展性设计
1. 支持自定义角色属性
2. 预留权限规则扩展接口
3. 支持多语言配置
4. 预留主题定制接口 