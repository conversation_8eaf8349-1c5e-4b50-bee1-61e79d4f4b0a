import { Request, Response } from 'express';
import Mock from 'mockjs';

const getUploadHistoryList = (req: Request, res: Response) => {
  // 生成25条数据
  const list = Array.from({ length: 25 }, (_, index) => {
    const id = index + 1;
    const taskId = `TASK_${Mock.Random.guid().slice(0, 8)}`;
    const taskType = Mock.Random.pick(['题目录入审核', '选题审核']);
    const fileName = `${Mock.Random.word(5, 10)}_${Mock.Random.word(5, 10)}_${Mock.Random.word(5, 10)}.docx`;
    const fileUrl = `https://example.com/files/${fileName}`;
    const uploadTime = Mock.Random.integer(1704067200000, 1704153600000); // 2024-01-01 到 2024-01-02 的时间戳
    const status = Mock.Random.pick([
      '后台自动识别中',
      '识别失败重新上传',
      '审核中',
      '审核不通过',
      '数据已入库',
    ]);

    return {
      id,
      taskId,
      taskType,
      fileName,
      fileUrl,
      uploadTime,
      status,
    };
  });

  res.json({
    code: 200,
    message: 'success',
    data: {
      list,
      total: 25,
      page: 1,
      pageSize: 25,
    },
  });
};

export default {
  'GET /api/v1/word/upload/list': getUploadHistoryList,
};
