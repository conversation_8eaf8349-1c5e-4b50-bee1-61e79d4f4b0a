import { Request, Response } from 'express';
import Mock from 'mockjs';

export default {
  'GET /api/upload/policy': (req: Request, res: Response) => {
    const params = req.query as unknown as API.UploadPolicyRequestParams;
    const result: API.UploadPolicyResponse = {
      code: 0,
      msg: 'success',
      data: {
        paperId: '120',
        presignUrl:
          'https://gil-test.oss-cn-beijing.aliyuncs.com/paper/2023-2024%E5%AD%A6%E5%B9%B4%E5%8C%97%E4%BA%AC%E5%B8%82%E5%A4%A7%E5%85%B4%E5%8C%BA%E9%AB%98%E4%B8%80%EF%BC%88%E4%B8%8A%EF%BC%89%E6%9C%9F%E6%9C%AB%E6%95%B0%E5%AD%A6%E8%AF%95%E5%8D%B7_1742461815.doc?x-oss-credential=LTAI5tPsnnDoL3Fh1uPdMDwe%2F20250320%2Fcn-beijing%2Foss%2Faliyun_v4_request&x-oss-date=20250320T091015Z&x-oss-expires=186400&x-oss-signature=eb2ae13371322d0a77e963439ebb194cebd1854ecfce9f298e6d8458c2501317&x-oss-signature-version=OSS4-HMAC-SHA256',
      },
    };
    res.json(result);
  },
  'POST /api/v1/resource/notice/upload/result': (req: Request, res: Response) => {
    const params = req.body as API.NoticeUploadResultRequestParams;
    const result: API.Common.CommonResponse = {
      code: 0,
      message: 'success',
      data: {
        paperId: params.paperId,
        uploadStatus: params.result.uploadStatus,
        uploadTime: new Date().getTime(),
      },
    };
    res.json(result);
  },
  'GET /api/v1/resource/get/upload/paper/list': (req: Request, res: Response) => {
    const params = req.query as unknown as API.Common.CommonListRequestParams;
    const { page = 1, pageSize = 10 } = params;

    // 生成55条数据
    const list = Array.from({ length: 55 }, (_, index) => {
      const id = index + 1;
      const paperId = `PAPER_${Mock.Random.guid().slice(0, 8)}`;
      const paperName = `${Mock.Random.ctitle(5, 30)}.docx`;
      const createTime = Mock.Random.integer(1704067200000, 1704153600000); // 2024-01-01 到 2024-01-02 的时间戳
      const updateTime = createTime + Mock.Random.integer(1000, 3600000); // 创建时间后1秒到1小时
      const paperStatus = Mock.Random.integer(1, 7);
      const paperStatusText = Mock.Random.pick([
        '待处理',
        '处理中',
        '处理完成',
        '处理失败',
        '已删除',
      ]);
      const auditTaskId = Mock.Random.integer(1000, 9999);

      return {
        createTime,
        paperId,
        paperName,
        paperStatus,
        paperStatusText,
        updateTime,
        auditTaskId,
      };
    });

    // 计算分页
    const start = (page - 1) * pageSize;
    const end = start + pageSize;
    const pageList = list.slice(start, end);

    const result: API.UploadWordListResponse = {
      code: 0,
      message: 'success',
      data: {
        list: pageList,
        total: 55,
        page: page,
        pageSize: pageSize,
      },
    };
    res.json(result);
  },
};
