import { Request, Response } from 'express';
const questionFilterEnumData: API.QuestionFilterEnumResponse = {
  code: 0,
  message: 'success',
  data: {
    searchTypes: [
      { label: '题目ID', value: 'questionId' },
      { label: '题目', value: 'question' },
      { label: '答案', value: 'answer' },
      { label: '解析', value: 'analysis' },
      { label: '知识点', value: 'knowledge' },
    ],
    filters: [
      {
        name: '类型',
        field: 'questionTypeEnums',
        options: [
          { label: '单选', value: 'single' },
          { label: '多选', value: 'multiple' },
          { label: '判断', value: 'judge' },
          { label: '填空', value: 'fill' },
        ],
      },
      {
        name: '难度',
        field: 'questionDifficultEnums',
        options: [
          { label: '简单', value: 'easy' },
          { label: '中等', value: 'medium' },
          { label: '困难', value: 'hard' },
        ],
      },
      // {
      //     name: '用途',
      //     field: 'questionPurposeEnums',
      //     options: [
      //         { label: '练习', value: 'practice' },
      //         { label: '考试', value: 'exam' },
      //         { label: '测试', value: 'test' }
      //     ]
      // },
      {
        name: '年份',
        field: 'questionYears',
        options: Array.from({ length: 15 }, (_, index) => {
          const year = 2025 - index;
          return {
            label: String(year),
            value: String(year),
          };
        }),
      },
      // {
      //     name: '来源',
      //     field: 'questionSourceEnums',
      //     options: [
      //         { label: '教材', value: 'textbook' },
      //         { label: '历年真题', value: 'historical' },
      //         { label: '模拟题', value: 'mock' },
      //         { label: '其他', value: 'other' }
      //     ]
      // },
      // {
      //     name: '场景',
      //     field: 'sceneCategoryEnums',
      //     options: [
      //         { label: 'AI课', value: 'choice' },
      //         { label: '巩固练习', value: 'practice' },
      //         { label: '个性化练习', value: 'personalized_practice' }
      //     ]
      // },
      {
        name: '省份',
        field: 'provinceEnums',
        options: [
          { label: '北京', value: 'beijing' },
          { label: '上海', value: 'shanghai' },
          { label: '广东', value: 'guangdong' },
          { label: '江苏', value: 'jiangsu' },
          { label: '浙江', value: 'zhejiang' },
          { label: '山东', value: 'shandong' },
          { label: '河南', value: 'henan' },
          { label: '四川', value: 'sichuan' },
          { label: '湖北', value: 'hubei' },
          { label: '湖南', value: 'hunan' },
          { label: '河北', value: 'hebei' },
          { label: '福建', value: 'fujian' },
          { label: '陕西', value: 'shaanxi' },
          { label: '安徽', value: 'anhui' },
          { label: '重庆', value: 'chongqing' },
          { label: '天津', value: 'tianjin' },
          { label: '江西', value: 'jiangxi' },
          { label: '云南', value: 'yunnan' },
          { label: '辽宁', value: 'liaoning' },
          { label: '广西', value: 'guangxi' },
          { label: '山西', value: 'shanxi' },
          { label: '内蒙古', value: 'neimenggu' },
          { label: '贵州', value: 'guizhou' },
          { label: '甘肃', value: 'gansu' },
          { label: '海南', value: 'hainan' },
          { label: '青海', value: 'qinghai' },
          { label: '宁夏', value: 'ningxia' },
          { label: '新疆', value: 'xinjiang' },
          { label: '西藏', value: 'xizang' },
        ],
      },
    ],
    sortType: [
      { label: '最新题目', value: 'latest' },
      { label: '使用次数', value: 'usage_count' },
      { label: '难易程度', value: 'difficulty' },
    ],
  },
};

function fetchQuestionFilterEnumHandler(req: Request, res: Response) {
  return res.json(questionFilterEnumData);
}

export default {
  'GET /api/v1/question/filter/enum': fetchQuestionFilterEnumHandler,
};
