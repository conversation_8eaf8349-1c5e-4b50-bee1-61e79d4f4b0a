import { Request, Response } from 'express';
import Mock from 'mockjs';

// 生成问题选项
const createQuestionOptions = () => {
  const options = ['A', 'B', 'C', 'D'];
  return options.map((option) => ({
    key: option,
    content: Mock.Random.cparagraph(1, 2),
  }));
};

// 生成单个问题项
const createQuestionItem = (index: number): API.QuestionItemType => {
  const questionId = Mock.Random.guid();
  const questionYear = Mock.Random.integer(2020, 2024);
  const questionType = Mock.Random.integer(1, 5); // 1-5 表示不同的题目类型
  const questionDifficult = Mock.Random.integer(1, 5); // 1-5 表示不同的难度等级
  const phase = Mock.Random.integer(1, 3); // 1: 小学, 2: 初中, 3: 高中
  const subject = Mock.Random.integer(1, 6); // 1: 语文, 2: 数学, 3: 英语, 4: 物理, 5: 化学, 6: 生物
  const provinceCode = Mock.Random.integer(110000, 820000); // 省份编码
  const cityCode = Mock.Random.integer(110100, 820100); // 城市编码
  const areaCode = Mock.Random.integer(110101, 820101); // 区县编码
  const baseTreeId = Mock.Random.integer(10000, 99999);
  const baseTreeNodeIds = new Array(Mock.Random.integer(1, 3))
    .fill(0)
    .map(() => Mock.Random.integer(10000, 99999));

  return {
    questionId,
    questionYear,
    questionContent: Mock.Random.cparagraph(1, 3),
    questionType,
    questionAnswer: Mock.Random.pick(['A', 'B', 'C', 'D']),
    questionDifficult,
    questionExplanation: Mock.Random.cparagraph(1, 2),
    aiScene: Mock.Random.pick([0, 1]),
    areaCode,
    baseTreeId,
    baseTreeNodeIds,
    cityCode,
    consolidateScene: Mock.Random.pick([0, 1]),
    phase,
    provinceCode,
    questionExtra: Mock.Random.cparagraph(1, 2),
    questionSource: Mock.Random.integer(1, 5),
    questionTopic: Mock.Random.ctitle(3, 5),
    subject,
    createTime: Mock.Random.datetime(),
    updateTime: Mock.Random.datetime(),
    optionsLayout: Mock.Random.integer(1, 2), // 1: 横向, 2: 纵向
    options: createQuestionOptions(),
  };
};

// 生成问题列表响应
const createQuestionListResponse = (
  params: API.QuestionListRequestParams,
): API.QuestionListResponse => {
  const page = params.page ?? 1;
  const pageSize = params.pageSize ?? 10;
  const total = 100; // 总数据量

  // 生成问题列表
  const list = new Array(total).fill(0).map((_, index) => createQuestionItem(index));

  // 根据分页参数返回数据
  const startIndex = (page - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const pagedList = list.slice(startIndex, endIndex);

  return {
    code: 200,
    message: 'success',
    data: {
      total,
      page,
      pageSize,
      list: pagedList,
    },
  };
};

// 处理请求
const getQuestionList = (req: Request, res: Response) => {
  const list = Array.from({ length: 25 }, (_, index) => {
    const questionId = Mock.Random.string('number', 8);
    const questionYear = Mock.Random.integer(2020, 2024);
    const questionType = Mock.Random.integer(1, 6); // 1-6 代表不同题型
    const questionDifficult = Mock.Random.integer(1, 5); // 1-5 代表不同难度
    const phase = Mock.Random.integer(1, 3); // 1-3 代表不同学段
    const subject = Mock.Random.integer(1, 6); // 1-6 代表不同学科

    // 生成题目内容的 JSON 字符串
    const questionContent = JSON.stringify({
      question_order: Mock.Random.integer(1, 20),
      question_score: Mock.Random.integer(3, 10),
      question_origin_name: `${questionYear}${Mock.Random.pick(['春', '秋'])}•${Mock.Random.city()}${Mock.Random.pick(['期中', '期末'])}`,
      question_stem: Mock.Random.pick([
        '下列哪个是我国四大发明之一？',
        '下列诗句出自哪位诗人？',
        '以下哪个是可再生能源？',
        '下列历史事件发生的时间最早的是？',
        '以下哪个说法是正确的？',
      ]),
      question_option_list: [
        {
          option_key: 'A',
          option_val: Mock.Random.word(4, 8),
        },
        {
          option_key: 'B',
          option_val: Mock.Random.word(4, 8),
        },
        {
          option_key: 'C',
          option_val: Mock.Random.word(4, 8),
        },
        {
          option_key: 'D',
          option_val: Mock.Random.word(4, 8),
        },
      ],
    });

    return {
      questionId,
      questionYear,
      questionContent,
      questionType,
      questionAnswer: Mock.Random.pick(['A', 'B', 'C', 'D']),
      questionDifficult,
      questionExplanation: Mock.Random.paragraph(1, 3),
      aiScene: Mock.Random.integer(0, 1),
      areaCode: Mock.Random.integer(100000, 999999),
      baseTreeId: Mock.Random.integer(1, 100),
      baseTreeNodeIds: Array.from({ length: Mock.Random.integer(1, 3) }, () =>
        Mock.Random.integer(1, 100),
      ),
      cityCode: Mock.Random.integer(100000, 999999),
      consolidateScene: Mock.Random.integer(0, 1),
      phase,
      provinceCode: Mock.Random.integer(100000, 999999),
      questionExtra: JSON.stringify({
        tags: Array.from({ length: Mock.Random.integer(1, 3) }, () => Mock.Random.word(3, 6)),
      }),
      questionSource: Mock.Random.integer(1, 4),
      questionTopic: Mock.Random.word(5, 10),
      subject,
      optionsLayout: Mock.Random.integer(1, 2), // 1: 横排, 2: 竖排
      options: [
        { key: 'A', content: Mock.Random.word(4, 8) },
        { key: 'B', content: Mock.Random.word(4, 8) },
        { key: 'C', content: Mock.Random.word(4, 8) },
        { key: 'D', content: Mock.Random.word(4, 8) },
      ],
    };
  });

  res.json({
    code: 200,
    message: 'success',
    data: {
      list,
      total: 100,
      page: 1,
      pageSize: 25,
    },
  });
};

export default {
  'POST /api/v1/resource/get/question/list': getQuestionList,
};
