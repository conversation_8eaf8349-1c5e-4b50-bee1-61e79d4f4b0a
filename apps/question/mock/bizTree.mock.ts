import { Request, Response } from 'express';
import Mock from 'mockjs';

const createBizTreeListRes = (params: API.BizTreeListRequestParams) => {
  const page = params.page ?? 1;
  const pageSize = params.pageSize ?? 10;
  const total = 20;
  const list = new Array(total).fill(0).map((_, index) => {
    const bizTreeId = Mock.Random.integer(10000, 99999);
    const baseTreeId = Mock.Random.integer(10000, 99999);
    return {
      bizTreeId,
      bizTreeName: Mock.Random.ctitle(5, 10) + '业务知识点体系',
      bizTreeVersion: `v${Mock.Random.float(1, 3, 1, 1)}`,
      baseTreeId,
      phaseEnum: Mock.Random.pick([1, 2, 3]), // 1: 小学, 2: 初中, 3: 高中
      subjectEnum: Mock.Random.pick([1, 2, 3, 4, 5, 6]), // 1: 语文, 2: 数学, 3: 英语, 4: 物理, 5: 化学, 6: 生物
      createTime: Mock.Random.datetime(),
      updateTime: Mock.Random.datetime(),
      creatorId: Mock.Random.integer(1000, 9999),
      updaterId: Mock.Random.integer(1000, 9999),
      status: Mock.Random.pick([0, 1]), // 0: 禁用, 1: 启用
      remark: Mock.Random.cparagraph(1, 2),
    };
  });

  // 根据分页参数返回数据
  const startIndex = (page - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const pagedList = list.slice(startIndex, endIndex);

  return {
    code: 200,
    message: 'success',
    data: {
      list: pagedList,
      page,
      pageSize,
      total,
    },
  };
};

// 生成业务树节点的辅助函数
const createBizTreeNode = (
  level: number,
  parentId?: number,
  siblingOrder: number = 1,
): API.BizTreeNode => {
  const nodeId = Mock.Random.integer(10000, 99999);
  const nodeName =
    level === 1
      ? Mock.Random.ctitle(3, 5) + '章'
      : level === 2
        ? Mock.Random.ctitle(3, 5) + '节'
        : Mock.Random.ctitle(3, 5) + '知识点';

  // 根据层级决定是否生成子节点
  let children: API.BizTreeNode[] = [];
  if (level < 3) {
    const childCount = Mock.Random.integer(2, 4); // 每个节点2-4个子节点
    children = new Array(childCount)
      .fill(0)
      .map((_, index) => createBizTreeNode(level + 1, nodeId, index + 1));
  }

  // 生成基础树节点ID数组
  const baseTreeNodeIds = new Array(Mock.Random.integer(1, 3))
    .fill(0)
    .map(() => Mock.Random.integer(10000, 99999));

  // 计算上架叶子节点统计
  const totalLeafNodes =
    level === 3
      ? 1
      : children.reduce((acc, node) => acc + Number(node.shelfOnLeafNodeStat.split('/')[1]), 0);
  const shelfOnLeafNodes =
    level === 3
      ? Mock.Random.pick([0, 1])
      : children.reduce((acc, node) => acc + Number(node.shelfOnLeafNodeStat.split('/')[0]), 0);

  return {
    bizTreeNodeId: nodeId,
    bizTreeNodeName: nodeName,
    bizTreeNodeLevel: level,
    bizTreeNodeSiblingOrder: siblingOrder,
    bizTreeParentNodeId: parentId,
    bizTreeNodeChildren: children,
    baseTreeNodeIds,
    shelfOnLeafNodeStat: `${shelfOnLeafNodes}/${totalLeafNodes}`,
    shelfStatus: level === 3 ? Mock.Random.pick([0, 1]) : 0,
  };
};

const createBizTreeDetailRes = (params: API.BizTreeDetailRequestParams) => {
  const { bizTreeId, showShelfStatus = 0 } = params;

  // 生成根节点
  const rootNode = createBizTreeNode(1);

  return {
    code: 200,
    message: 'success',
    data: {
      bizTreeId,
      baseTreeId: String(Mock.Random.integer(10000, 99999)),
      bizTreeName: Mock.Random.ctitle(5, 10) + '业务知识点体系',
      bizTreeVersion: `v${Mock.Random.float(1, 3, 1, 1)}`,
      phase: Mock.Random.pick([1, 2, 3]), // 1: 小学, 2: 初中, 3: 高中
      subject: Mock.Random.pick([1, 2, 3, 4, 5, 6]), // 1: 语文, 2: 数学, 3: 英语, 4: 物理, 5: 化学, 6: 生物
      bizTreeDetail: rootNode,
    },
  };
};

const fetchBizTreeListHandler = (req: Request, res: Response) => {
  const params = req.query as unknown as API.BizTreeListRequestParams;
  const result = createBizTreeListRes(params);
  return res.json(result);
};

const fetchBizTreeDetailHandler = (req: Request, res: Response) => {
  const params = req.query as unknown as API.BizTreeDetailRequestParams;
  const result = createBizTreeDetailRes(params);
  return res.json(result);
};

export default {
  'GET /api/v1/base/data/biz/tree/list': fetchBizTreeListHandler,
  'GET /api/v1/base/data/biz/tree/detail': fetchBizTreeDetailHandler,
};
