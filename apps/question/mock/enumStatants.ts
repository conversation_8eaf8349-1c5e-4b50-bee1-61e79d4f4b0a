import { Request, Response } from 'express';

const getEnumConstants = (req: Request, res: Response) => {
  res.json({
    code: 200,
    message: 'success',
    data: {
      // 审核状态列表
      auditsStatusList: [
        { nameEn: 'PENDING', nameZh: '待审核', value: 1 },
        { nameEn: 'APPROVED', nameZh: '已通过', value: 2 },
        { nameEn: 'REJECTED', nameZh: '已驳回', value: 3 },
      ],
      // 试卷类型列表
      paperTypeList: [
        { nameEn: 'MIDTERM', nameZh: '期中考试', value: 1 },
        { nameEn: 'FINAL', nameZh: '期末考试', value: 2 },
        { nameEn: 'MOCK', nameZh: '模拟考试', value: 3 },
        { nameEn: 'ENTRANCE', nameZh: '入学考试', value: 4 },
      ],
      // 学段列表
      phaseList: [
        { nameEn: 'PRIMARY', nameZh: '小学', value: 1 },
        { nameEn: 'JUNIOR', nameZh: '初中', value: 2 },
        { nameEn: 'SENIOR', nameZh: '高中', value: 3 },
      ],
      // 学段科目关系
      phaseSubjectRelation: [
        {
          nameEn: 'PRIMARY',
          nameZh: '小学',
          value: 1,
          subjectList: [
            { nameEn: 'CHINESE', nameZh: '语文', value: 1 },
            { nameEn: 'MATH', nameZh: '数学', value: 12 },
            { nameEn: 'ENGLISH', nameZh: '英语', value: 13 },
          ],
        },
        {
          nameEn: 'JUNIOR',
          nameZh: '初中',
          value: 2,
          subjectList: [
            { nameEn: 'CHINESE', nameZh: '语文', value: 21 },
            { nameEn: 'MATH', nameZh: '数学', value: 22 },
            { nameEn: 'ENGLISH', nameZh: '英语', value: 23 },
            { nameEn: 'PHYSICS', nameZh: '物理', value: 24 },
            { nameEn: 'CHEMISTRY', nameZh: '化学', value: 25 },
          ],
        },
        {
          nameEn: 'SENIOR',
          nameZh: '高中',
          value: 3,
          subjectList: [
            { nameEn: 'CHINESE', nameZh: '语文', value: 31 },
            { nameEn: 'MATH', nameZh: '数学', value: 32 },
            { nameEn: 'ENGLISH', nameZh: '英语', value: 33 },
            { nameEn: 'PHYSICS', nameZh: '物理', value: 34 },
            { nameEn: 'CHEMISTRY', nameZh: '化学', value: 35 },
            { nameEn: 'BIOLOGY', nameZh: '生物', value: 36 },
          ],
        },
      ],
      // 省份列表
      provinceList: [
        { nameEn: 'BEIJING', nameZh: '北京', value: 1 },
        { nameEn: 'SHANGHAI', nameZh: '上海', value: 2 },
        { nameEn: 'GUANGDONG', nameZh: '广东', value: 3 },
        { nameEn: 'JIANGSU', nameZh: '江苏', value: 4 },
        { nameEn: 'ZHEJIANG', nameZh: '浙江', value: 5 },
      ],
      // 年份列表
      yearList: Array.from({ length: 15 }, (_, index) => {
        const year = 2025 - index;
        return {
          nameEn: `YEAR_${year}`,
          nameZh: String(year),
          value: year,
        };
      }),
      // 题目难度列表
      questionDifficultList: [
        { nameEn: 'EASY', nameZh: '简单', value: 1 },
        { nameEn: 'RELATIVELY_EASY', nameZh: '较易', value: 2 },
        { nameEn: 'MEDIUM', nameZh: '中等', value: 3 },
        { nameEn: 'RELATIVELY_HARD', nameZh: '较难', value: 4 },
        { nameEn: 'HARD', nameZh: '困难', value: 5 },
      ],
      // 题目来源列表
      questionSourceList: [
        { nameEn: 'ORIGINAL', nameZh: '原创', value: 1 },
        { nameEn: 'TEXTBOOK', nameZh: '教材', value: 2 },
        { nameEn: 'EXAM', nameZh: '考试', value: 3 },
        { nameEn: 'REFERENCE', nameZh: '参考书', value: 4 },
      ],
      // 题目类型列表
      questionTypeList: [
        { nameEn: 'SINGLE_CHOICE', nameZh: '单选题', value: 1 },
        { nameEn: 'MULTIPLE_CHOICE', nameZh: '多选题', value: 2 },
        { nameEn: 'TRUE_FALSE', nameZh: '判断题', value: 3 },
        { nameEn: 'FILL_BLANK', nameZh: '填空题', value: 4 },
        { nameEn: 'SHORT_ANSWER', nameZh: '简答题', value: 5 },
        { nameEn: 'ESSAY', nameZh: '论述题', value: 6 },
      ],
      // 上架状态列表
      shelfStatusList: [
        { nameEn: 'DRAFT', nameZh: '草稿', value: 1 },
        { nameEn: 'PUBLISHED', nameZh: '已发布', value: 2 },
        { nameEn: 'OFFLINE', nameZh: '已下架', value: 3 },
      ],
      // 科目列表
      subjectList: [
        { nameEn: 'CHINESE', nameZh: '语文', value: 1 },
        { nameEn: 'MATH', nameZh: '数学', value: 2 },
        { nameEn: 'ENGLISH', nameZh: '英语', value: 3 },
        { nameEn: 'PHYSICS', nameZh: '物理', value: 4 },
        { nameEn: 'CHEMISTRY', nameZh: '化学', value: 5 },
        { nameEn: 'BIOLOGY', nameZh: '生物', value: 6 },
      ],
    },
  });
};

export default {
  'GET /api/v1/enums/get/consts': getEnumConstants,
};
