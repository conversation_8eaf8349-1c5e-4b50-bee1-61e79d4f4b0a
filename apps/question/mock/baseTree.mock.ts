import { Request, Response } from 'express';
import Mock from 'mockjs';

const createBaseTreeListRes = (params: API.BaseTreeListRequestParams) => {
  const { page = 1, pageSize = 10 } = params;
  const total = 20;
  const list = new Array(total).fill(0).map((_, index) => {
    const baseTreeId = Mock.Random.integer(10000, 99999);
    return {
      baseTreeId,
      baseTreeName: Mock.Random.ctitle(5, 10) + '知识点体系',
      baseTreeVersion: `v${Mock.Random.float(1, 3, 1, 1)}`,
      phaseEnum: Mock.Random.pick([1, 2, 3]), // 1: 小学, 2: 初中, 3: 高中
      subjectEnum: Mock.Random.pick([1, 2, 3, 4, 5, 6]), // 1: 语文, 2: 数学, 3: 英语, 4: 物理, 5: 化学, 6: 生物
      createTime: Mock.Random.datetime(),
      updateTime: Mock.Random.datetime(),
      creatorId: Mock.Random.integer(1000, 9999),
      updaterId: Mock.Random.integer(1000, 9999),
      status: Mock.Random.pick([0, 1]), // 0: 禁用, 1: 启用
      remark: Mock.Random.cparagraph(1, 2),
    };
  });

  // 根据分页参数返回数据
  const startIndex = (page - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const pagedList = list.slice(startIndex, endIndex);

  return {
    code: 200,
    message: 'success',
    data: {
      list: pagedList,
      page,
      pageSize,
      total,
    },
  };
};

// 生成树节点的辅助函数
const createTreeNode = (
  level: number,
  parentId?: number,
  siblingOrder: number = 1,
): API.BaseTreeNode => {
  const nodeId = Mock.Random.integer(10000, 99999);
  const nodeName =
    level === 1
      ? Mock.Random.ctitle(3, 5) + '章'
      : level === 2
        ? Mock.Random.ctitle(3, 5) + '节'
        : Mock.Random.ctitle(3, 5) + '知识点';

  // 根据层级决定是否生成子节点
  let children: API.BaseTreeNode[] = [];
  if (level < 3) {
    const childCount = Mock.Random.integer(2, 4); // 每个节点2-4个子节点
    children = new Array(childCount)
      .fill(0)
      .map((_, index) => createTreeNode(level + 1, nodeId, index + 1));
  }

  return {
    baseTreeNodeId: nodeId,
    baseTreeNodeName: nodeName,
    baseTreeNodeLevel: level,
    baseTreeNodeSiblingOrder: siblingOrder,
    baseTreeParentNodeId: parentId,
    baseTreeNodeChildren: children,
  };
};

const createBaseTreeDetailRes = (params: API.BaseTreeDetailRequestParams) => {
  const { baseTreeId } = params;

  // 生成根节点
  const rootNode = createTreeNode(1);

  return {
    code: 200,
    message: 'success',
    data: {
      baseTreeId,
      baseTreeName: Mock.Random.ctitle(5, 10) + '知识点体系',
      baseTreeVersion: `v${Mock.Random.float(1, 3, 1, 1)}`,
      phase: Mock.Random.pick([1, 2, 3]), // 1: 小学, 2: 初中, 3: 高中
      subject: Mock.Random.pick([1, 2, 3, 4, 5, 6]), // 1: 语文, 2: 数学, 3: 英语, 4: 物理, 5: 化学, 6: 生物
      baseTreeDetail: rootNode,
    },
  };
};

const fetchBaseTreeListHandler = (req: Request, res: Response) => {
  const params = req.query as unknown as API.BaseTreeListRequestParams;
  const result = createBaseTreeListRes(params);
  return res.json(result);
};

const fetchBaseTreeDetailHandler = (req: Request, res: Response) => {
  const params = req.query as unknown as API.BaseTreeDetailRequestParams;
  const result = createBaseTreeDetailRes(params);
  return res.json(result);
};

export default {
  'GET /api/v1/base/data/base/tree/list': fetchBaseTreeListHandler,
  'GET /api/v1/base/data/base/tree/detail': fetchBaseTreeDetailHandler,
  // 'GET /api/v1/base/data/biz/tree/detail': fetchBizTreeDetailHandler,
  // 'GET /api/v1/base/data/biz/tree/list': fetchBizTreeListHandler,
};
