import { Request, Response } from 'express';
const phaseSubjectData: API.PhaseSubjectDataResponse = {
  code: 0,
  data: {
    phaseSubjects: [
      {
        key: 1,
        value: '小学',
        subjects: [
          {
            key: 1,
            value: '语文',
          },
          {
            key: 2,
            value: '数学',
          },
          {
            key: 3,
            value: '英语',
          },
          {
            key: 4,
            value: '物理',
          },
          {
            key: 5,
            value: '化学',
          },
          {
            key: 6,
            value: '生物',
          },
          {
            key: 7,
            value: '政治',
          },
          {
            key: 8,
            value: '历史',
          },
          {
            key: 9,
            value: '地理',
          },
        ],
      },
      {
        key: 2,
        value: '初中',
        subjects: [
          {
            key: 1,
            value: '语文',
          },
          {
            key: 2,
            value: '数学',
          },
          {
            key: 3,
            value: '英语',
          },
          {
            key: 4,
            value: '物理',
          },
          {
            key: 5,
            value: '化学',
          },
          {
            key: 6,
            value: '生物',
          },
          {
            key: 7,
            value: '政治',
          },
          {
            key: 8,
            value: '历史',
          },
          {
            key: 9,
            value: '地理',
          },
        ],
      },
      {
        key: 3,
        value: '高中',
        subjects: [
          {
            key: 1,
            value: '语文',
          },
          {
            key: 2,
            value: '数学',
          },
          {
            key: 3,
            value: '英语',
          },
          {
            key: 4,
            value: '物理',
          },
          {
            key: 5,
            value: '化学',
          },
          {
            key: 6,
            value: '生物',
          },
          {
            key: 7,
            value: '政治',
          },
          {
            key: 8,
            value: '历史',
          },
          {
            key: 9,
            value: '地理',
          },
        ],
      },
    ],
  },
  message: 'ok',
};

function fetchPhaseSubjectDict(req: Request, res: Response) {
  return res.json(phaseSubjectData);
}

export default {
  'GET /api/v1/enums': fetchPhaseSubjectDict,
};
