import { Request, Response } from 'express';
import Mock from 'mockjs';

const difficultNames = ['简单', '较易', '中等', '较难', '困难'];

const createExerciseQuestionListRes = (params: API.ExerciseQuestionListRequestParams) => {
  const { bizTreeNodeId, sceneCategory } = params;

  // 创建难度统计数据
  const difficultStat = difficultNames.map((name, index) => ({
    difficultName: name,
    difficultNeedQuestionCount: Mock.Random.integer(5, 10),
    difficultQuestionCount: Mock.Random.integer(10, 20),
  }));

  // 创建题目列表
  const list = Array.from({ length: 25 }, (_, index) => {
    const questionId = Mock.Random.guid();
    return {
      questionId,
      question_id: questionId,
      questionYear: Mock.Random.integer(2020, 2024),
      questionContent: JSON.stringify({
        question_order: index + 1,
        question_score: Mock.Random.integer(2, 5),
        question_origin_name: Mock.Random.ctitle(10, 20),
        question_stem: Mock.Random.paragraph(2, 4),
        question_option_list: Array.from({ length: 4 }, (_, i) => ({
          option_key: String.fromCharCode(65 + i), // A, B, C, D
          option_val: Mock.Random.sentence(5, 10),
        })),
      }),
      questionType: Mock.Random.integer(1, 5),
      questionAnswer: Mock.Random.word(5, 10),
      questionDifficult: Mock.Random.integer(1, 5),
      questionExplanation: Mock.Random.paragraph(1, 2),
      aiScene: Mock.Random.integer(0, 1),
      areaCode: Mock.Random.integer(100000, 999999),
      baseTreeId: Mock.Random.integer(1, 100),
      baseTreeNodeIds: [Mock.Random.integer(1, 100), Mock.Random.integer(1, 100)],
      cityCode: Mock.Random.integer(100000, 999999),
      consolidateScene: 1,
      phase: Mock.Random.integer(1, 3),
      provinceCode: Mock.Random.integer(100000, 999999),
      questionExtra: JSON.stringify({
        extra: Mock.Random.paragraph(1),
      }),
      questionSource: Mock.Random.integer(1, 5),
      questionTopic: Mock.Random.word(5, 10),
      subject: Mock.Random.integer(1, 5),
      optionsLayout: Mock.Random.integer(1, 2),
      options: Array.from({ length: 4 }, (_, i) => ({
        key: String.fromCharCode(65 + i), // A, B, C, D
        content: Mock.Random.sentence(5, 10),
      })),
      // 练习题特有字段
      addDelStatus: Mock.Random.integer(0, 2),
      auditStatus: Mock.Random.pick([2, 3]),
      questionStatus: Mock.Random.pick(['normal', 'deleted']),
    };
  });

  return {
    code: 0,
    message: 'success',
    data: {
      curAuditFailReason: Mock.Random.cparagraph(2, 4),
      curAuditTaskId: 120,
      // curAuditTaskId: Mock.Random.integer(1, 1000),
      curAuditTaskStatus: 3,
      // curAuditTaskStatus: Mock.Random.integer(1, 4),
      curAuditTaskStatusText: Mock.Random.pick(['待审核', '审核通过', '审核不通过']),
      difficultStat,
      list,
      questionSetId: Mock.Random.integer(1, 1000),
      questionVersionId: Mock.Random.integer(1, 1000),
      shelfStatus: 1,
    },
  };
};

export default {
  'GET /api/v1/scene/get/question/set/info': (req: Request, res: Response) => {
    const params = req.query as unknown as API.ExerciseQuestionListRequestParams;
    const result = createExerciseQuestionListRes(params);
    res.json(result);
  },
  'POST /api/v1/secene/add/question/set': (req: Request, res: Response) => {
    const params = req.body as API.ExerciseQuestionOperateRequestParams;
    const result: API.ExerciseQuestionOperateResponse = {
      code: 0,
      message: 'success',
      data: {
        questionSetId: params.questionSetId || Mock.Random.integer(1, 1000),
      },
    };
    res.json(result);
  },
  'POST /api/v1/scene/off/shelf/question/biz/tree/node': (req: Request, res: Response) => {
    const params = req.body as API.ExerciseQuestionOperateRequestParams;
    const result: API.ExerciseQuestionOperateResponse = {
      code: 0,
      message: 'success',
      data: {
        questionSetId: params.questionSetId || Mock.Random.integer(1, 1000),
      },
    };
    res.json(result);
  },
  'POST /api/v1/scene/on/shelf/question/biz/tree/node': (req: Request, res: Response) => {
    const params = req.body as API.ExerciseOnShelfRequestParams;
    const result: API.ExerciseOnShelfResponse = {
      code: 0,
      message: 'success',
      data: {
        auditTaskId: Mock.Random.integer(1, 1000),
        questionSetId: params.questionSetId,
      },
    };
    res.json(result);
  },
  'POST /api/v1/secene/proc/audit/fail/info': (req: Request, res: Response) => {
    const params = req.body as API.ExerciseAuditResultHandleRequestParams;
    const result: API.ExerciseAuditResultHandleResponse = {
      code: 0,
      message: 'success',
      data: {
        auditTaskId: params.auditTaskId,
        questionSetId: Mock.Random.integer(1, 1000),
      },
    };
    res.json(result);
  },
  'POST /api/v1/audit/recover': (req: Request, res: Response) => {
    const params = req.body as { auditTaskId: number };
    const result: API.ExerciseRevocationAuditResponse = {
      code: 0,
      message: 'success',
      data: {
        auditTaskId: params.auditTaskId,
        questionSetId: Mock.Random.integer(1, 1000),
      },
    };
    res.json(result);
  },
};
