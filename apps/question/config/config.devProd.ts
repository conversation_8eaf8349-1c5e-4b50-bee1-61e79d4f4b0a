// https://umijs.org/config/
import { defineConfig } from '@umijs/max';

const { REACT_APP_ENV = 'dev' } = process.env;
console.log('==devProd==>REACT_APP_ENV', REACT_APP_ENV);
console.log('==devProd==>UMI_ENV', process.env.UMI_ENV);

// export default defineConfig({
//   define: {
//     REMOTE_LOGIN_URL: 'http://web-admin.local.xiaoluxue.cn/user/login',
//     OPERATION_MANAGEMENT_PLATFORM_BACKEND: 'http://admin-api.local.xiaoluxue.cn',
//     API_BASE_URL: 'http://question.local.xiaoluxue.cn',
//     OSS_HOST: 'https://gil-test.oss-cn-beijing.aliyuncs.com',
//   },
// }) as any;
export default defineConfig({
  define: {
    REMOTE_LOGIN_URL: 'https://admin.dev.xiaoluxue.cn/user/login',
    OPERATION_MANAGEMENT_PLATFORM_BACKEND: 'https://admin-api.dev.xiaoluxue.cn',
    API_BASE_URL: 'https://question-api.dev.xiaoluxue.cn',
    OSS_HOST: 'https://gil-test.oss-cn-beijing.aliyuncs.com',
  },
}) as any;
