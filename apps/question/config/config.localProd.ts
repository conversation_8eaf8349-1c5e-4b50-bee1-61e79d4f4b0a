// https://umijs.org/config/
import { defineConfig } from '@umijs/max';

const { REACT_APP_ENV = 'dev' } = process.env;
console.log('==localProd==>REACT_APP_ENV', REACT_APP_ENV);
console.log('==localProd==>UMI_ENV', process.env.UMI_ENV);

export default defineConfig({
  define: {
    REMOTE_LOGIN_URL: 'http://web-admin.local.xiaoluxue.cn/user/login',
    OPERATION_MANAGEMENT_PLATFORM_BACKEND: 'http://admin-api.local.xiaoluxue.cn',
    API_BASE_URL: 'http://question.local.xiaoluxue.cn',
    OSS_HOST: 'https://gil-test.oss-cn-beijing.aliyuncs.com',
  },
}) as any;
