/* eslint-disable */
import { ChangeEventPayload } from '@/types/common/common';
import { mergeQuestionFilterOptions } from '@/utils';
import { DownOutlined, UpOutlined } from '@ant-design/icons';
import { useModel } from '@umijs/max';
import { Button, Flex } from 'antd';
import { cloneDeep } from 'lodash';
import React, { useEffect, useState } from 'react';
import TagSelect from '../TagSelect';
import useStyles from './index.style';

export interface FilterPanelProps {
  filters?: API.QuestionListRequestParams;
  onChange?: (params: ChangeEventPayload) => void;
}

const FilterPanel: React.FC<FilterPanelProps> = ({ filters, onChange }) => {
  const { styles } = useStyles();
  const { initialState } = useModel('@@initialState');
  const enumConstants = initialState?.globalDictValues?.enumConstants ?? {};

  const [normalFilters, setNormalFilters] = useState<API.FilterGroup[]>([]);
  const [collapseFilters, setCollapseFilters] = useState<API.FilterGroup[]>([]);

  const [collapsed, setCollapsed] = useState(false);

  useEffect(() => {
    const subject = filters?.subjectList?.[0];
    const questionFilterEnumData = mergeQuestionFilterOptions(
      enumConstants as API.EnumConstantData,
      subject,
      false,
    );
    let tagFilters = cloneDeep(questionFilterEnumData);

    if (filters) {
      tagFilters = tagFilters.map((filter) => ({
        ...filter,
        value: filters[filter.field] ?? [],
      }));
    }

    if (tagFilters.length > 2) {
      setNormalFilters([...tagFilters.slice(0, 2)]);
      setCollapseFilters([...tagFilters.slice(2)]);
    } else {
      setNormalFilters(tagFilters);
      setCollapseFilters([]);
    }
  }, [filters]);

  const handleReset = () => {
    // 重置所有筛选项
    onChange?.({
      type: 'reset',
      field: '',
      value: '',
    });
  };

  const handleFilterChange = (filterItem: API.FilterGroup, value: (string | number)[]) => {
    onChange?.({
      type: 'filter',
      field: filterItem.field,
      value: value,
    });
  };

  const renderFilterRow = (filterItem: API.FilterGroup) => (
    <div className={styles.filterRow} key={filterItem.field}>
      <div className={styles.filterLabel}>{filterItem.name}：</div>
      <div className={styles.filterContent}>
        <TagSelect
          onChange={(value) => handleFilterChange(filterItem, value)}
          value={filterItem.value}
        >
          {filterItem.options.map((option) => (
            <TagSelect.Option value={option.value} key={option.value}>
              {option.nameZh}
            </TagSelect.Option>
          ))}
        </TagSelect>
      </div>
    </div>
  );

  const renderSortButtons = () => (
    <Flex align="center" justify="end">
      <Button onClick={handleReset}>重置</Button>
      <a className={styles.collapseLink} onClick={() => setCollapsed(!collapsed)}>
        {collapsed ? '展开' : '折叠'}
        {collapsed ? (
          <DownOutlined style={{ marginLeft: 4 }} />
        ) : (
          <UpOutlined style={{ marginLeft: 4 }} />
        )}
      </a>
    </Flex>
  );

  return (
    <div>
      {normalFilters.map((filter) => renderFilterRow(filter))}
      {!collapsed && <>{collapseFilters.map((filter) => renderFilterRow(filter))}</>}
      {renderSortButtons()}
    </div>
  );
};

export default FilterPanel;
