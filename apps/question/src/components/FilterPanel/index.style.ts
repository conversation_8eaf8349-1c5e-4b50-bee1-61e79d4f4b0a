import { createStyles } from 'antd-style';

const useStyles = createStyles(({ token }) => ({
  filterRow: {
    display: 'flex',
    alignItems: 'flex-start',
    // marginBottom: token.marginSM,
    '&:last-child': {
      marginBottom: 0,
    },
  },
  filterLabel: {
    width: '80px',
    color: token.colorTextSecondary,
    flexShrink: 0,
    lineHeight: '32px',
  },
  filterContent: {
    flex: 1,
  },
  sortButton: {
    display: 'inline-flex',
    alignItems: 'center',
    cursor: 'pointer',
    marginRight: token.margin,
    color: token.colorTextSecondary,
    '&:hover': {
      color: token.colorPrimary,
    },
  },
  sortButtonChecked: {
    color: token.colorPrimary,
  },
  collapseLink: {
    display: 'inline-flex',
    alignItems: 'center',
    cursor: 'pointer',
    color: token.colorPrimary,
    marginLeft: token.marginLG,
    '&:hover': {
      color: token.colorPrimaryHover,
    },
  },
}));

export default useStyles;
