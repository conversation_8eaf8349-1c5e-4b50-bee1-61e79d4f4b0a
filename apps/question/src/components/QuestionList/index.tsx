import QuestionCorrectionBtn from '@/components/QuestionCorrectionBtn';
import QuestionItem from '@/components/QuestionItem';
import { formatMainQuestions } from '@/utils';
import { LikeOutlined } from '@ant-design/icons';
import { Button, Flex, List, Tag, Typography } from 'antd';
import { debounce } from 'lodash';
import type React from 'react';
import { useCallback, useMemo } from 'react';
const { Text } = Typography;
// import { QuestionItemType } from "@/pages/source-manage/question-manage/data"

/**
 * 题目列表类型枚举
 * @description
 * EXERCISE_SELECT - 选择题目场景（可添加/移除）
 * EXERCISE_SELECTED - 已选题目场景（可移除）
 * EXERCISE_SELECT_MY - 仅看我的上传
 * EXERCISE_AUDIT_FAIL - 审核不通过场景（可忽略/采纳） 已废弃
 * EXERCISE_AUDIT - 审核场景（显示审核状态）
 */
export enum QuestionListType {
  EXERCISE_SELECT = 'exercise-select',
  EXERCISE_SELECTED = 'exercise-selected',
  EXERCISE_SELECT_MY = 'exercise-select-my',
  EXERCISE_AUDIT_FAIL = 'exercise-audit-fail',
  EXERCISE_AUDIT = 'exercise-audit',
}

export interface QuestionListProps {
  questions: API.QuestionItemType[];
  loading?: boolean;
  pagination?: {
    pageSize: number;
    total: number;
    page: number;
  };
  onPageChange?: (page: number, pageSize: number) => void;
  type?: QuestionListType | string;
  // 问题组件子元素事件处理函数
  onQuestionEventHandler?: (question: API.QuestionItemType, type: string) => void;
  // 选择题目的回调
  onQuestionSelect?: (questionId: string | number, checked: boolean) => void;
  // 选中的题目ID列表
  selectedQuestionIds?: (string | number)[];
  refresh?: () => void; // 刷新题目列表
}

const QuestionList: React.FC<QuestionListProps> = ({
  questions,
  pagination,
  onPageChange,
  loading = false,
  type = '',
  onQuestionEventHandler,
  refresh,
}) => {
  const ExerciseSelectQuestion = useCallback(
    (question: API.QuestionItemType) => {
      if (type !== QuestionListType.EXERCISE_SELECT) {
        return null;
      }

      if (question.disableSelect) {
        return (
          <Tag color="#f50" style={{ position: 'absolute', right: 0, top: 0, margin: 0 }}>
            已被选入“{question.questionGroupName}”题组
          </Tag>
        );
      }
      if (question.isShowAddButton) {
        return (
          <Flex align="center" justify="end" gap={8}>
            <Button type="primary" onClick={() => onQuestionEventHandler?.(question, 'add')}>
              加入
            </Button>
          </Flex>
        );
      }
      return (
        <Flex align="center" justify="end" gap={8}>
          <Button onClick={() => onQuestionEventHandler?.(question, 'remove')}>移出</Button>
        </Flex>
      );
    },
    [type, onQuestionEventHandler],
  );
  const ExerciseSelectMyQuestion = useCallback(
    (question: API.QuestionItemType) => {
      if (type !== QuestionListType.EXERCISE_SELECT_MY) {
        return null;
      }
      const { viewStatus } = question;
      // 处理自己上传的题有审核的情况
      let auditView = null;
      if (viewStatus === 2) {
        auditView = (
          <Flex align="center" justify="end" gap={2}>
            <Text strong>待审核</Text>
            <Button
              color="primary"
              variant="text"
              size="small"
              onClick={() => onQuestionEventHandler?.(question, 'revokeAudit')}
            >
              撤销
            </Button>
          </Flex>
        );
      }

      if (question.disableSelect) {
        return (
          <Tag color="#f50" style={{ position: 'absolute', right: 0, top: 0, margin: 0 }}>
            已被选入“{question.questionGroupName}”题组
          </Tag>
        );
      }
      if (question.isShowAddButton) {
        return (
          <Flex align="center" justify="end" gap={8}>
            {auditView}
            <Button
              type="primary"
              onClick={() => onQuestionEventHandler?.(question, 'add')}
              disabled={viewStatus !== 3}
            >
              加入
            </Button>
          </Flex>
        );
      }
      return (
        <Flex align="center" justify="end" gap={8}>
          {auditView}
          <Button onClick={() => onQuestionEventHandler?.(question, 'remove')}>移出</Button>
        </Flex>
      );
    },
    [type, onQuestionEventHandler],
  );
  const ExerciseSelectedQuestion = useCallback(
    (question: API.QuestionItemType) => {
      if (type !== QuestionListType.EXERCISE_SELECTED) {
        return null;
      }
      const { addDelStatus } = question;
      let btn = null;
      if (addDelStatus === 2) {
        btn = (
          <Flex align="center" justify="end" gap={16}>
            <Tag color="#f50">删除题目</Tag>
            <Button type="primary" onClick={() => onQuestionEventHandler?.(question, 'add')}>
              加入
            </Button>
          </Flex>
        );
      } else {
        btn = (
          <Flex align="center" justify="end" gap={16}>
            {addDelStatus === 1 && <Tag color="#87d068">新增题目</Tag>}
            <Button onClick={() => onQuestionEventHandler?.(question, 'remove')}>移出</Button>
          </Flex>
        );
      }

      const recommendBtn = (
        <>
          <Button
            size="small"
            color={question.isRecommend ? 'primary' : 'default'}
            variant="text"
            icon={<LikeOutlined />}
            iconPosition="start"
            style={{ gap: 2 }}
            disabled={question.addDelStatus === 2}
            onClick={debounce(() => onQuestionEventHandler?.(question, 'recommend'), 500)}
          >
            {question.isRecommend ? '取消推荐题' : '设为推荐题'}
          </Button>
          <QuestionCorrectionBtn question={question} refresh={refresh} />
        </>
      );

      return (
        <QuestionItem question={question} footerExtraBtns={recommendBtn}>
          {btn}
        </QuestionItem>
      );
    },
    [type, onQuestionEventHandler],
  );

  const QuestionItemRender = (question: API.QuestionItemType) => {
    if (type === QuestionListType.EXERCISE_SELECT) {
      return (
        <QuestionItem
          question={question}
          footerExtraBtns={<QuestionCorrectionBtn question={question} refresh={refresh} />}
        >
          {ExerciseSelectQuestion(question)}
        </QuestionItem>
      );
    }
    if (type === QuestionListType.EXERCISE_SELECT_MY) {
      return (
        <QuestionItem
          question={question}
          footerExtraBtns={<QuestionCorrectionBtn question={question} refresh={refresh} />}
        >
          {ExerciseSelectMyQuestion(question)}
        </QuestionItem>
      );
    }
    if (type === QuestionListType.EXERCISE_SELECTED) {
      return ExerciseSelectedQuestion(question);
    }
    if (type === QuestionListType.EXERCISE_AUDIT_FAIL) {
      const { addDelStatus } = question;
      return (
        <QuestionItem question={question}>
          <Flex align="center" justify="end" gap={16}>
            {addDelStatus === 2 && <Tag color="#f50">移出题目</Tag>}
            {addDelStatus === 1 && <Tag color="#87d068">新增题目</Tag>}
            <Button onClick={() => onQuestionEventHandler?.(question, 'reject')}>忽略</Button>
            <Button type="primary" onClick={() => onQuestionEventHandler?.(question, 'accept')}>
              采纳
            </Button>
          </Flex>
        </QuestionItem>
      );
    }
    if (type === QuestionListType.EXERCISE_AUDIT) {
      const { addDelStatus } = question;
      const recommendBtn = (
        <Tag color="processing" icon={<LikeOutlined />} style={{ marginRight: 2 }}>
          推荐题
        </Tag>
      );
      return (
        <QuestionItem
          question={question}
          footerExtraBtns={question.isRecommend ? recommendBtn : null}
        >
          <Flex align="center" justify="end" gap={16}>
            {addDelStatus === 2 && <Tag color="#f50">删除</Tag>}
            {addDelStatus === 1 && <Tag color="#87d068">新增</Tag>}
          </Flex>
        </QuestionItem>
      );
    }
    return (
      <QuestionItem
        question={question}
        footerExtraBtns={<QuestionCorrectionBtn question={question} refresh={refresh} />}
      />
    );
  };

  const formatQuestions = useMemo(() => {
    return formatMainQuestions(questions);
    // return formatMainQuestions([...questions, wanxingMock, duoxuanMock]);
  }, [questions]);

  return (
    <List
      itemLayout="vertical"
      loading={loading}
      dataSource={formatQuestions}
      pagination={
        pagination && questions.length
          ? {
              current: pagination?.page ?? 1,
              pageSize: pagination?.pageSize ?? 10,
              total: pagination?.total ?? questions.length,
              showTotal: (total) => `共${total}道题`,
              showQuickJumper: true,
              onChange: (page, pageSize) => {
                onPageChange?.(pageSize === pagination?.pageSize ? page : 1, pageSize);
              },
            }
          : undefined
      }
      renderItem={(question) => (
        <List.Item style={{ padding: 0, marginBottom: 16, borderBottom: 'none' }}>
          {QuestionItemRender(question)}
        </List.Item>
      )}
      rowKey={(question) => question.questionId}
      style={{ width: '100%' }}
    />
  );
};

export default QuestionList;
