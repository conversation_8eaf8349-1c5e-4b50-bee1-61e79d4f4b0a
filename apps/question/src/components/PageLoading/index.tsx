import { Flex, Spin } from 'antd';
import useStyles from './index.style';

export default function PageLoading() {
  const { styles } = useStyles();
  return (
    <Flex vertical align="center" justify="center" className={styles.pageLoadingWarp}>
      <div className={styles.loadingContainer}>
        <Spin size="large" />
      </div>
      <div className={styles.loadingTitle}>正在加载资源</div>
      <div className={styles.loadingSubTitle}>初次加载资源可能需要较多时间 请耐心等待</div>
    </Flex>
  );
}
