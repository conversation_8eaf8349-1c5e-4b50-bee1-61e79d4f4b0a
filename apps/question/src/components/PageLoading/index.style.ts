import { createStyles } from 'antd-style';

const useStyles = createStyles(({}) => {
  return {
    pageLoadingWarp: {
      height: '100vh',
      width: '100vw',
      minHeight: '362px',
    },
    loadingContainer: {
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      padding: '24px',
    },
    loadingTitle: {
      fontSize: '1.1rem',
    },
    loadingSubTitle: {
      marginTop: '20px',
      fontSize: '1rem',
      color: '#888',
    },
  };
});

export default useStyles;
