import { DownOutlined } from '@ant-design/icons';
import { useModel } from '@umijs/max';
import { Button, Card, Col, Form, Row, Select, Space, theme } from 'antd';
import React, { useEffect, useState } from 'react';

const QuestionFilterForm: React.FC = () => {
  const { data: questionFilterEnumData, run: fetchQuestionFilterEnumRun } =
    useModel('questionFilterEnumModel');
  const [form] = Form.useForm();
  const [expand, setExpand] = useState(false);
  const { token } = theme.useToken();
  const formStyle: React.CSSProperties = {
    maxWidth: 'none',
    background: token.colorFillAlter,
    borderRadius: token.borderRadiusLG,
    padding: 24,
  };

  useEffect(() => {
    if (!questionFilterEnumData) {
      fetchQuestionFilterEnumRun();
    }
  }, [fetchQuestionFilterEnumRun]);
  const onFinish = (values: any) => {
    console.log('Received values of form: ', values);
  };
  const getFields = () => {
    return questionFilterEnumData?.filters.map((item: any) => {
      return (
        <Col span={8} key={item.field}>
          <Form.Item name={item.field} label={item.name}>
            <Select key={item.value} options={item.options} placeholder="请选择" />
          </Form.Item>
        </Col>
      );
    });
  };
  return (
    <Card>
      <Form form={form} name="advanced_search" style={formStyle} onFinish={onFinish}>
        <Row gutter={24}>{questionFilterEnumData && getFields()}</Row>
        <div style={{ textAlign: 'right' }}>
          <Space size="small">
            <Button type="primary" htmlType="submit">
              确定
            </Button>
            <Button
              onClick={() => {
                form.resetFields();
              }}
            >
              重置
            </Button>
            <a
              style={{ fontSize: 12 }}
              onClick={() => {
                setExpand(!expand);
              }}
            >
              <DownOutlined rotate={expand ? 180 : 0} /> 屏开
            </a>
          </Space>
        </div>
      </Form>
    </Card>
  );
};

export default QuestionFilterForm;
