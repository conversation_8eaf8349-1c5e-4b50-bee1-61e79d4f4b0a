import { useTree } from '@/hooks/useTree';
import { useDebounce } from 'ahooks';
import { Input, Tree } from 'antd';
import type { TreeProps } from 'antd/es/tree';
import React, { useMemo, useState } from 'react';

export interface TreeNodeType {
  title: string;
  key: string;
  children?: TreeNodeType[];
}

export interface SearchTreeProps extends Omit<TreeProps, 'treeData'> {
  treeData: TreeNodeType[];
}

const SearchTree: React.FC<SearchTreeProps> = ({ treeData, ...treeProps }) => {
  const { treeDataList } = useTree(treeData);
  const [searchValue, setSearchValue] = useState('');
  const debouncedSearchValue = useDebounce(searchValue, { wait: 500 });

  const filteredTreeData = useMemo(() => {
    if (!debouncedSearchValue) {
      return treeData;
    }

    const filteredKeys = treeDataList
      .filter((node) => node.title.toLowerCase().includes(debouncedSearchValue.toLowerCase()))
      .map((node) => node.key);

    const keepNode = (node: TreeNodeType): TreeNodeType | null => {
      // 如果当前节点匹配搜索条件，保留整个节点
      if (filteredKeys.includes(node.key)) {
        return node;
      }

      // 如果有子节点，递归处理子节点
      if (node.children) {
        const filteredChildren = node.children
          .map(keepNode)
          .filter((n): n is TreeNodeType => n !== null);

        // 如果过滤后还有子节点，返回当前节点及其过滤后的子节点
        if (filteredChildren.length > 0) {
          return {
            ...node,
            children: filteredChildren,
          };
        }
      }

      return null;
    };

    return treeData.map(keepNode).filter((n): n is TreeNodeType => n !== null);
  }, [treeData, treeDataList, debouncedSearchValue]);

  return (
    <div>
      <Input.Search
        placeholder="输入关键字搜索"
        style={{ width: 240, marginBottom: 16 }}
        value={searchValue}
        onChange={(e) => setSearchValue(e.target.value)}
      />

      <Tree
        key={debouncedSearchValue}
        autoExpandParent
        height={500}
        defaultExpandedKeys={treeData.map((item) => item.key)}
        defaultExpandAll={Boolean(debouncedSearchValue)}
        {...treeProps}
        treeData={filteredTreeData}
      />
    </div>
  );
};

export default SearchTree;
