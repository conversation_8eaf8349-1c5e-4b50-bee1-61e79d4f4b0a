/**
 * 场景题库-巩固练习/拓展练习
 */
import BizTreeCascader from '@/components/BizTreeCascader';
import ExerciseAuditFailModal from '@/components/exercise/ExerciseAuditFailModal';
import ExerciseBar from '@/components/exercise/ExerciseBar';
import ExerciseProgress from '@/components/exercise/ExerciseProgress';
import ExerciseQuestionList from '@/components/exercise/ExerciseQuestionList';
import ExerciseSelectQuestionModal from '@/components/exercise/ExerciseSelectQuestionModal';
import SubjectPhaseCascader from '@/components/SubjectPhaseCascader';
import { ExerciseContext } from '@/contexts/ExerciseContext';
import { fetchExerciseQuestionList } from '@/services/api';
import { savePageState, scrollToTop, treeShelfStatusOptions } from '@/utils';
import { getManageQuestionPagePhaseSubjectInitValue } from '@/utils/phaseSubject';
import { cardBodyStyle } from '@/utils/styles';
import { defaultQuestionListRequestParams } from '@/utils/tree';
import { PageContainer } from '@ant-design/pro-components';
import { useModel, useRequest } from '@umijs/max';
import { Card, Col, Grid, Radio, Row, Space } from 'antd';
import React, { useEffect, useState } from 'react';
import Sticky from 'react-stickynode';
import ExerciseTreeView from '../ExerciseTreeView';

export interface ExerciseViewProps {
  type: number;
}

const ExerciseView: React.FC<ExerciseViewProps> = ({ type = 0 }) => {
  const { initialState } = useModel('@@initialState');
  const phaseSubjects = initialState?.globalDictValues?.enumConstants?.phaseSubjectRelation ?? [];
  const [selectedSubject, setSelectedSubject] = useState<number[]>(
    getManageQuestionPagePhaseSubjectInitValue('exerciseSelectedSubject' + type, phaseSubjects),
  );
  const [exerciseInfo, setExerciseInfo] = useState<API.ExerciseQuestionListData>();
  const [treeStatus, setTreeStatus] = useState<number>(-1);
  const [treeId, setTreeId] = useState<number>();
  // 业务树选中的节点id
  const [treeNodeId, setTreeNodeId] = useState<number>();
  const [prevTreeNodeId, setPrevTreeNodeId] = useState<number>();
  const [shelfTreeNodeId, setShelfTreeNodeId] = useState<number>();
  const [unShelfTreeNodeId, setUnShelfTreeNodeId] = useState<number>();
  const [allShelfTreeNodeId, setAllShelfTreeNodeId] = useState<number>();
  const [, setSelecedQuestionListRequestParams] = useState<API.QuestionListRequestParams>();
  // 选中的树节点
  const [selectedTreeNode, setSelectedTreeNode] = useState<API.Common.BaseTreeNodeAntdType>();
  const [showEditExercise, setShowEditExercise] = useState<boolean>(false);
  const [showAuditFailModal, setShowAuditFailModal] = useState<boolean>(false);
  const [treeUpdateTimestamp, setTreeUpdateTimestamp] = useState<number>();
  const screens = Grid.useBreakpoint();
  const [affixEnabled, setAffixEnabled] = useState<boolean>(false);
  const [activeGroupId, setActiveGroupId] = useState<string | undefined>();
  // 业务树选中的节点值，它是级联选择器选中的值
  const [bizTreeValue, setBizTreeValue] = useState<number[]>([]);

  const { run: fetchQuestionListRun, loading: fetchQuestionListLoading } = useRequest(
    () => {
      if (!treeNodeId) {
        return Promise.resolve({
          data: {
            list: [],
          },
        });
      }
      return fetchExerciseQuestionList({
        bizTreeNodeId: treeNodeId,
        sceneCategory: type,
      });
    },
    {
      manual: true,
      throttleInterval: 1000,
      onSuccess: (data) => {
        setExerciseInfo(data);
      },
      onError: (error) => {
        console.log('获取练习详细信息 失败: ', error);
      },
    },
  );

  const onPhaseSubjectValueChange = (value: number[]) => {
    setSelectedSubject(value);
    setTreeId(undefined);
    setTreeNodeId(undefined);
    setExerciseInfo(undefined);
    setSelectedTreeNode(undefined);
  };
  const onTreeListSelectChange = (value: number[]) => {
    setBizTreeValue(value);
    setTreeId(value.at(-1));
    setTreeNodeId(undefined);
    setExerciseInfo(undefined);
    setSelectedTreeNode(undefined);
  };

  const onTreeChange = (value: number, node: API.Common.BaseTreeNodeAntdType) => {
    scrollToTop('.ant-pro-page-container');
    setTreeNodeId(value);
    setSelectedTreeNode(node);

    switch (treeStatus) {
      case 1:
        setShelfTreeNodeId(value);
        break;
      case 0:
        setUnShelfTreeNodeId(value);
        break;
      default:
        setAllShelfTreeNodeId(value);
    }

    setPrevTreeNodeId(undefined);
  };

  const onTreeStatusChange = (value: number) => {
    const id =
      value === -1 ? allShelfTreeNodeId : value === 1 ? shelfTreeNodeId : unShelfTreeNodeId;
    setTreeStatus(value);
    setPrevTreeNodeId(id);
  };

  useEffect(() => {
    if (selectedSubject && selectedSubject.length > 0) {
      savePageState('exerciseSelectedSubject' + type, selectedSubject);
    }
  }, [selectedSubject]);

  useEffect(() => {
    const [subjectEnum, phaseEnum] = selectedSubject;
    if ((!subjectEnum && !phaseEnum) || !treeNodeId) {
      setSelecedQuestionListRequestParams(undefined);
      return;
    }
    const subjectPhase = {
      subjectEnum,
      phaseEnum,
    };
    const bizTreeNodeIds = treeNodeId ? [treeNodeId] : [];
    const payload = {
      subjectPhase,
      bizTreeNodeIds,
    };
    setSelecedQuestionListRequestParams({
      ...defaultQuestionListRequestParams(),
      ...payload,
      pageSize: 100,
    });
  }, [selectedSubject, treeNodeId]);

  useEffect(() => {
    fetchQuestionListRun();
  }, [treeNodeId]);

  useEffect(() => {
    setAffixEnabled(!!screens.xl);
  }, [screens.xl]);

  return (
    <PageContainer pageHeaderRender={false}>
      <ExerciseContext.Provider
        value={{
          selectedSubject,
          setSelectedSubject,
          type,
          exerciseInfo,
          setExerciseInfo,
          selectedTreeNode,
          setSelectedTreeNode,
          fetchExerciseInfoFunc: fetchQuestionListRun,
          fetchExerciseInfoLoading: fetchQuestionListLoading,
          treeUpdateTimestamp,
          setTreeUpdateTimestamp,
          activeGroupId,
          setActiveGroupId,
          bizTreeValue,
        }}
      >
        <Card styles={cardBodyStyle()}>
          <Row gutter={16}>
            <Col xl={6} lg={24} md={24} sm={24} xs={24}>
              <Sticky enabled={affixEnabled} top={106} enableTransforms={false}>
                <Space direction="vertical" size="middle" style={{ display: 'flex' }}>
                  <SubjectPhaseCascader
                    selectedSubject={selectedSubject}
                    onChange={onPhaseSubjectValueChange}
                  />
                  <Card size="small">
                    <Space direction="vertical" size="middle" style={{ display: 'flex' }}>
                      <BizTreeCascader
                        onChange={onTreeListSelectChange}
                        selectedSubject={selectedSubject}
                      />
                      <Radio.Group
                        value={treeStatus}
                        onChange={(e) => onTreeStatusChange(e.target.value)}
                        block
                        options={treeShelfStatusOptions}
                      />
                      <ExerciseTreeView
                        treeId={treeId}
                        key={treeId}
                        onChange={onTreeChange}
                        treeNodeFilter={
                          treeStatus !== -1
                            ? {
                                key: 'shelfStatus',
                                value: treeStatus,
                                isEqual: true,
                              }
                            : undefined
                        }
                        isOnlyFilterLeafNode={true}
                        sceneCategory={type}
                        showRootNode={false}
                        defaultSelectedFirstLeafNode={true}
                        prevTreeNodeId={prevTreeNodeId}
                      />
                    </Space>
                  </Card>
                </Space>
              </Sticky>
            </Col>
            <Col xl={18} lg={24} md={24} sm={24} xs={24}>
              <Space direction="vertical" size="middle" style={{ display: 'flex' }}>
                {selectedTreeNode && (
                  <>
                    <ExerciseBar />
                    {exerciseInfo && (
                      <ExerciseProgress
                        onEdit={() => setShowEditExercise(true)}
                        onHandleException={() => setShowAuditFailModal(true)}
                      />
                    )}
                  </>
                )}

                {exerciseInfo && <ExerciseQuestionList refresh={fetchQuestionListRun} />}
              </Space>
              {/* 选择新问题 */}
              {showEditExercise && (
                <ExerciseSelectQuestionModal
                  node={selectedTreeNode}
                  showModal={showEditExercise}
                  onCloseModal={() => setShowEditExercise(false)}
                />
              )}
              <ExerciseAuditFailModal
                node={selectedTreeNode}
                showModal={showAuditFailModal}
                onCloseModal={() => setShowAuditFailModal(false)}
              />
            </Col>
          </Row>
        </Card>
      </ExerciseContext.Provider>
    </PageContainer>
  );
};

export default ExerciseView;
