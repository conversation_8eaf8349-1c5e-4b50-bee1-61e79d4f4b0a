interface TreeNodeObjects {
  treeDataList: API.Common.BaseTreeNodeAntdType[];
  treeDataLeafList: API.Common.BaseTreeNodeAntdType[];
  parentNodeList: API.Common.BaseTreeNodeAntdType[];
  topLevelNodes: API.Common.BaseTreeNodeAntdType[];
  treeDataMap: Record<number, API.Common.BaseTreeNodeAntdType>;
}

export function getBizShelfTreeNodeObjects(
  treeData: API.Common.BaseTreeNodeAntdType[],
): TreeNodeObjects {
  const treeDataList: API.Common.BaseTreeNodeAntdType[] = [];
  const treeDataMap: Record<number, API.Common.BaseTreeNodeAntdType> = {};
  const treeDataLeafList: API.Common.BaseTreeNodeAntdType[] = [];
  const topLevelNodes: API.Common.BaseTreeNodeAntdType[] = [];
  const parentNodeList: API.Common.BaseTreeNodeAntdType[] = [];

  // 遍历树节点生成树数据列表和叶子节点列表
  function traverse(node: API.Common.BaseTreeNodeAntdType) {
    if (treeDataMap[node.key]) {
      return;
    }
    treeDataMap[node.key] = node;
    treeDataList.push(node);
    if (node.children === undefined) {
      node.shelfStatus = node.shelfStatus === 1 ? 1 : 0;
      node.shelfOnLeafNodeStat = undefined;
      node.selectable = true;
      node.isLeaf = true;
      treeDataLeafList.push(node);
    }
    if (node.children) {
      node.shelfStatus = undefined;
      node.shelfOnLeafNodeStat = node.shelfOnLeafNodeStat ?? '';
      node.selectable = false;
      node.isLeaf = false;
      parentNodeList.push(node);
      node.children.forEach((child) => {
        traverse(child);
      });
    }
  }
  treeData.forEach((node) => {
    topLevelNodes.push(node);
    traverse(node);
  });

  return {
    treeDataList,
    treeDataLeafList,
    parentNodeList,
    treeDataMap,
    topLevelNodes,
  };
}

export function getTreeDataByFilter(
  treeData: API.Common.BaseTreeNodeAntdType[],
  filter: API.Common.TreeNodeFilterType,
  rootNodeKey: number | undefined = undefined,
): API.Common.BaseTreeNodeAntdType[] {
  if (!treeData || treeData.length === 0) {
    return [];
  }

  const { treeDataList, treeDataLeafList, treeDataMap } = getBizShelfTreeNodeObjects(treeData);

  // 过滤树节点
  function filterFn(nodes: API.Common.BaseTreeNodeAntdType[]): API.Common.BaseTreeNodeAntdType[] {
    return nodes.filter((node) => {
      const { key, value, isEqual } = filter;
      const nodeValue = node[key as keyof API.Common.BaseTreeNodeAntdType];
      if (nodeValue !== undefined) {
        if (!isEqual) {
          return String(nodeValue).includes(String(value));
        }
        return nodeValue === value;
      }
      return false;
    });
  }

  // 返回需要的顶层节点
  function getTopLevelNodes(
    nodes: API.Common.BaseTreeNodeAntdType[],
  ): API.Common.BaseTreeNodeAntdType[] {
    return nodes.filter((node) => {
      const { parentsKey } = node;
      if (rootNodeKey && parentsKey && parentsKey.length === 1) {
        return parentsKey[0] === rootNodeKey;
      }
      return !parentsKey || parentsKey.length === 0;
    });
  }

  // 只过滤叶子节点
  const filteredTreeDataList = filterFn(treeDataLeafList);
  const keys = filteredTreeDataList.map((node) => node.key);
  const unNeddNodes = treeDataLeafList.filter((node) => !keys.includes(node.key));
  unNeddNodes.forEach((node) => {
    const { parentsKey } = node;
    if (parentsKey && parentsKey.length > 0) {
      const parentNode = treeDataMap[parentsKey.at(-1) as number];
      if (parentNode) {
        parentNode.children = parentNode.children?.filter((child) => child.key !== node.key);
      }
    }
    if (parentsKey && parentsKey.length === 0) {
      treeDataList.splice(treeDataList.indexOf(node), 1);
    }
  });

  return getTopLevelNodes(treeDataList);
}
