import { ExerciseContext } from '@/contexts/ExerciseContext';
import { useFetchTreeDetail } from '@/services/api';
import { getTreeKeysToLevel } from '@/utils/tree';
import { Empty, Flex, Grid, Tree, Typography } from 'antd';
import { DataNode } from 'antd/es/tree';
import { cloneDeep } from 'lodash';
import React, { Key, useContext, useEffect, useMemo, useRef, useState } from 'react';
import { getBizShelfTreeNodeObjects, getTreeDataByFilter } from './tree';
const { Text } = Typography;

export interface ExerciseTreeViewProps {
  onChange: (value: number, node: API.Common.BaseTreeNodeAntdType) => void;
  treeId?: number;
  // 过滤条件
  treeNodeFilter?: API.Common.TreeNodeFilterType;
  // 只过滤叶子节点
  isOnlyFilterLeafNode?: boolean;
  // 只选择叶子节点
  // 是否显示搜索框
  // 是否显示根节点
  showRootNode?: boolean;
  // 场景分类
  sceneCategory?: number;
  defaultSelectedFirstLeafNode?: boolean;
  defaultExpandedLayers?: number;
  prevTreeNodeId?: number;
}

const ExerciseTreeView: React.FC<ExerciseTreeViewProps> = ({
  treeId,
  onChange,
  treeNodeFilter = undefined,
  showRootNode = true,
  sceneCategory = 0,
  defaultSelectedFirstLeafNode = false,
  defaultExpandedLayers = 0,
  prevTreeNodeId = undefined,
}: ExerciseTreeViewProps) => {
  const screens = Grid.useBreakpoint();
  // 树选择
  const [selectedTreeKey, setSelectedTreeKey] = useState<Key>();
  // 展开的节点
  const [expandedKeys, setExpandedKeys] = useState<Key[]>([]);
  const treeRef = useRef(null);
  const [, setTreeHeight] = useState<number>(0);
  const [treeViewHeight, setTreeViewHeight] = useState<string>('auto');
  const { treeUpdateTimestamp } = useContext(ExerciseContext);
  const {
    data: treeDetailData,
    run: fetchTreeDetailRun,
    mutate: mutateTreeDetail,
  } = useFetchTreeDetail(false, treeId ?? 0, sceneCategory, true);

  useEffect(() => {
    if (treeId) {
      setSelectedTreeKey(undefined);
      setExpandedKeys([]); // 重置展开状态
      fetchTreeDetailRun();
      return;
    }
    if (treeDetailData?.treeData) {
      const detailData = { ...treeDetailData, treeData: [] };
      mutateTreeDetail(detailData);
    }
  }, [treeId]);

  useEffect(() => {
    if (treeUpdateTimestamp) {
      fetchTreeDetailRun();
    }
  }, [treeUpdateTimestamp]);

  useEffect(() => {
    if (treeRef.current) {
      const treeElement = treeRef.current as unknown as HTMLDivElement;
      const rect = treeElement.getBoundingClientRect();
      setTreeHeight(rect.top);
    }
  }, []);

  useEffect(() => {
    setTimeout(() => {
      if (treeRef.current) {
        const treeElement = treeRef.current as unknown as HTMLDivElement;
        const rect = treeElement.getBoundingClientRect();
        const minHeight = `calc(100vh - ${rect.top}px - 32px - 16px - 12px - 2px)`;
        setTreeViewHeight(screens.xl ? minHeight : '400px');
      }
    }, 100);
  }, [screens.xl, treeRef.current]);

  const treeDetailInfo = useMemo(() => {
    if (treeDetailData?.treeData && treeDetailData.treeData.length > 0) {
      // 格式化上架信息和状态
      getBizShelfTreeNodeObjects(treeDetailData.treeData);
      let treeData: API.Common.BaseTreeNodeAntdType[] = cloneDeep(treeDetailData.treeData);
      const rootNode = treeData[0];
      // 是否显示根节点
      if (!showRootNode) {
        treeData = treeData[0].children ?? [];
      }
      if (treeNodeFilter) {
        treeData = getTreeDataByFilter(
          treeData,
          treeNodeFilter,
          showRootNode ? undefined : rootNode.key,
        );
      }

      const { treeDataLeafList, treeDataMap } = getBizShelfTreeNodeObjects(treeData);

      let initialExpandedKeys = getTreeKeysToLevel(treeData, defaultExpandedLayers);

      // if (selectedTreeKey && treeDataMap[selectedTreeKey as number]) {
      //     const parentsKey = treeDataMap[selectedTreeKey as number].parentsKey ?? [];
      //     initialExpandedKeys = [...initialExpandedKeys, ...parentsKey];
      // }

      let currentSelectedTreeKey = undefined;
      let firstSelectedNode = undefined;

      if (prevTreeNodeId && treeDataMap[prevTreeNodeId as number]) {
        currentSelectedTreeKey = prevTreeNodeId;
        firstSelectedNode = treeDataMap[prevTreeNodeId as number];
        const parentsKey = treeDataMap[prevTreeNodeId as number].parentsKey ?? [];
        initialExpandedKeys = [...initialExpandedKeys, ...parentsKey];
      } else if (selectedTreeKey && treeDataMap[selectedTreeKey as number]) {
        currentSelectedTreeKey = selectedTreeKey;
        const parentsKey = treeDataMap[selectedTreeKey as number].parentsKey ?? [];
        initialExpandedKeys = [...initialExpandedKeys, ...parentsKey];
      }

      // if (selectedTreeKey) {
      //     const node = treeDataMap[selectedTreeKey as number];
      //     if (node && node.parentsKey) {
      //         // 选中节点在当前的树中
      //         currentSelectedTreeKey = selectedTreeKey;
      //         const parentsKey = treeDataMap[selectedTreeKey as number].parentsKey ?? [];
      //         initialExpandedKeys = [...initialExpandedKeys, ...parentsKey];
      //     } else if (prevTreeNodeId && treeDataMap[prevTreeNodeId as number]) {
      //         // 有以前选中的节点，且在当前树中
      //         currentSelectedTreeKey = prevTreeNodeId;
      //         firstSelectedNode = treeDataMap[prevTreeNodeId as number];
      //         const parentsKey = treeDataMap[prevTreeNodeId as number].parentsKey ?? [];
      //         initialExpandedKeys = [...initialExpandedKeys, ...parentsKey];

      //     }
      // }

      // 如果当前没有选中节点
      if (!currentSelectedTreeKey && !firstSelectedNode) {
        // 并且有叶子节点，则选中第一个叶子节点
        if (defaultSelectedFirstLeafNode && treeDataLeafList.length > 0) {
          firstSelectedNode = treeDataLeafList[0];
        } else {
          firstSelectedNode = undefined;
        }
      }

      return {
        treeData,
        initialExpandedKeys,
        firstSelectedNode,
      };
    }
    return {
      treeData: [],
      initialExpandedKeys: [],
      firstSelectedNode: undefined,
    };
  }, [treeDetailData, treeNodeFilter, selectedTreeKey]);

  // 初始化展开状态
  useEffect(() => {
    if (treeDetailInfo.initialExpandedKeys.length > 0) {
      setExpandedKeys(treeDetailInfo.initialExpandedKeys);
    }
  }, [treeDetailInfo.initialExpandedKeys]);

  // 处理第一个叶子节点的选择
  useEffect(() => {
    if (treeDetailInfo.firstSelectedNode) {
      setSelectedTreeKey(treeDetailInfo.firstSelectedNode.key);
      onChange?.(treeDetailInfo.firstSelectedNode.key as number, treeDetailInfo.firstSelectedNode);
    }
  }, [treeDetailInfo.firstSelectedNode]);

  // 处理树节点选择
  const onSelect = (selectedKeys: Key[], { node }: { node: API.Common.BaseTreeNodeAntdType }) => {
    if (selectedKeys[0] === undefined) {
      return;
    }
    setSelectedTreeKey(selectedKeys[0]);
    onChange?.(selectedKeys[0] as number, node);
  };

  // 处理展开/收起
  const onExpand = (keys: Key[]) => {
    setExpandedKeys(keys);
  };

  const titleRender = (node: DataNode & { shelfStatus?: number; shelfOnLeafNodeStat?: string }) => {
    const { title, children, shelfStatus, shelfOnLeafNodeStat } = node;
    return (
      <Flex align="center">
        <Text style={{ flex: 1 }}>{typeof title === 'function' ? title(node) : title}</Text>
        <div>
          {children !== undefined ? (
            <Text type="secondary" style={{ fontSize: 10 }}>
              {shelfOnLeafNodeStat}
            </Text>
          ) : (
            <Text type="secondary" style={{ fontSize: 10 }}>
              {shelfStatus === 1 ? '已上架' : '未上架'}
            </Text>
          )}
        </div>
      </Flex>
    );
  };

  return (
    <div className="tree-view-container" ref={treeRef} style={{ height: treeViewHeight }}>
      {treeDetailInfo.treeData.length > 0 ? (
        <Tree
          treeData={treeDetailInfo.treeData ?? []}
          expandedKeys={expandedKeys}
          onExpand={onExpand}
          showLine={{ showLeafIcon: false }}
          selectedKeys={selectedTreeKey ? [selectedTreeKey] : []}
          onSelect={onSelect}
          blockNode
          titleRender={titleRender}
          rootStyle={{ height: '100%', overflow: 'auto' }}
        />
      ) : (
        <Empty />
      )}
    </div>
  );
};

export default ExerciseTreeView;
