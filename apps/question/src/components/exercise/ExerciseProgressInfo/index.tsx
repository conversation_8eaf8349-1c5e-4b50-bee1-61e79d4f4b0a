import { Flex } from 'antd';
import { FC, useMemo } from 'react';
import useStyles from './index.style';

export interface ExerciseProgressInfoProps {
  showLimitCount?: boolean;
  difficultStat: API.ExerciseDifficultStat[];
}

const ExerciseProgressInfo: FC<ExerciseProgressInfoProps> = ({
  showLimitCount = false,
  difficultStat = [],
}) => {
  const { styles } = useStyles();
  const colors = ['green', 'cyan', 'blue', 'orange', 'red'];

  const items = useMemo(() => {
    return difficultStat.map((item: API.ExerciseDifficultStat, index: number) => {
      const { difficultName, difficultNeedQuestionCount, difficultQuestionCount } = item;
      return (
        <div className={styles.progressInfoItem + ` ${colors[index]}`} key={difficultName}>
          {difficultName}
          {showLimitCount
            ? `${difficultQuestionCount}/${difficultNeedQuestionCount}`
            : `${difficultQuestionCount}`}
        </div>
      );
    });
  }, [difficultStat]);

  return (
    <Flex gap="small" wrap style={{ flex: 1 }} align="center" justify="center">
      {items}
    </Flex>
  );
};
export default ExerciseProgressInfo;
