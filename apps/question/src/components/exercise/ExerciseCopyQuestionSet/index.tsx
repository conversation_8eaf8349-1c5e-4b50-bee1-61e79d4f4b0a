import { ExerciseContext } from '@/contexts/ExerciseContext';
import { exerciseCopyFromOtherExercise, exerciseSearchBySetId } from '@/services/api';
import { Button, Flex, Input, message, Typography } from 'antd';
import { FC, useContext, useState } from 'react';
const { Search } = Input;
const { Title, Text } = Typography;

const ExerciseCopyQuestionSet: FC = () => {
  const [searchId, setSearchId] = useState<number>();
  const [searching, setSearching] = useState(false);
  const [searchResult, setSearchResult] = useState<string | null>(null);
  const { exerciseInfo, fetchExerciseInfoFunc } = useContext(ExerciseContext);
  const onSearch = async (value: string) => {
    const id = +value;
    if (searching || !id) {
      return;
    }
    setSearching(true);
    const res = await exerciseSearchBySetId(id);
    setSearching(false);
    if (res.code === 0) {
      setSearchResult(res.data.questionSetBrief);
      setSearchId(id);
      return;
    }
    setSearchResult('');
  };
  const onChange = () => {
    setSearchResult(null);
  };
  const onCopy = async () => {
    if (!searchId) {
      return;
    }
    const res = await exerciseCopyFromOtherExercise({
      fromQuestionSetId: searchId,
      questionSetId: exerciseInfo?.questionSetId ?? 0,
    });
    if (res.code === 0) {
      message.success('复制成功');
      fetchExerciseInfoFunc();
    } else {
      message.error('复制失败');
    }
  };

  return (
    <Flex vertical gap={16} style={{ maxWidth: 650, margin: '0 auto', paddingBottom: 100 }}>
      <Title level={4} style={{ margin: 0, textAlign: 'center', fontWeight: 'normal' }}>
        当前练习还没有选题，快来选题吧!
      </Title>
      <div>
        <Search
          placeholder="输入其他练习ID快速复用题目和题组"
          enterButton="查找"
          size="large"
          loading={searching}
          onSearch={onSearch}
          onChange={onChange}
        />
      </div>
      {searchResult && (
        <Flex gap={8} align="center" justify="flex-start">
          <div style={{ wordBreak: 'keep-all' }}>识别到练习:</div>
          <Text type="success" style={{ color: '#1677ff', textAlign: 'left' }}>
            {searchResult}
          </Text>
          <Button type="primary" size="small" onClick={onCopy}>
            采用
          </Button>
        </Flex>
      )}
      {searchResult === '' && (
        <Text type="danger" style={{ textAlign: 'left' }}>
          没有识别到练习，核对ID是否正确
        </Text>
      )}
    </Flex>
  );
};
export default ExerciseCopyQuestionSet;
