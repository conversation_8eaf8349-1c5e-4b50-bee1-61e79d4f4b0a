/**
 * 已选题目列表: 场景题库-巩固练习/拓展练习-选题预览
 */
import QuestionList from '@/components/QuestionList';
import { ExerciseContext } from '@/contexts/ExerciseContext';
import { useExerciseGroup } from '@/hooks/useExerciseGroup';
import {
  useExerciseQuestionBatchRecommend,
  useExerciseQuestionOperate,
  useExerciseQuestionRecommend,
} from '@/services/api';
import { Checkbox, CheckboxChangeEvent, Divider, Flex, Spin } from 'antd';
import { debounce } from 'lodash';
import { FC, useContext, useEffect, useMemo, useState } from 'react';

export interface ExerciseSelectedQuestionListProps {
  questions: API.QuestionItemType[];
  onPageChange?: (page: number, pageSize: number) => void;
}
/**
 * 已选题目列表
 */
const ExerciseSelectedQuestionList: FC<ExerciseSelectedQuestionListProps> = ({
  questions = [],
}) => {
  const {
    exerciseInfo,
    type: sceneCategory,
    fetchExerciseInfoFunc,
    selectedTreeNode,
    activeGroupId,
  } = useContext(ExerciseContext);
  const { exerciseQuestionMaps } = useExerciseGroup();

  const [unshelfQuestions, setUnshelfQuestions] = useState<API.QuestionItemType[]>([]);
  const [shelfQuestions, setShelfQuestions] = useState<API.QuestionItemType[]>([]);

  useEffect(() => {
    const unshelfQuestions: API.QuestionItemType[] = [];
    const shelfQuestions: API.QuestionItemType[] = [];

    questions.forEach((question) => {
      if (question.addDelStatus === 1) {
        unshelfQuestions.push(question);
        return;
      }
      shelfQuestions.push(question);
    });

    setUnshelfQuestions(unshelfQuestions);
    setShelfQuestions(shelfQuestions);
  }, [questions]);

  const { run: exerciseQuestionRemoveRun, loading: removeLoading } = useExerciseQuestionOperate(
    'remove',
    fetchExerciseInfoFunc,
  );
  const { run: exerciseQuestionAddRun, loading: addLoading } = useExerciseQuestionOperate(
    'add',
    fetchExerciseInfoFunc,
  );
  const { run: exerciseQuestionRecommendRun, loading: recommendLoading } =
    useExerciseQuestionRecommend(fetchExerciseInfoFunc);
  const { run: exerciseQuestionBatchRecommendRun, loading: batchRecommendLoading } =
    useExerciseQuestionBatchRecommend(fetchExerciseInfoFunc);

  const onQuestionEventHandler = (question: API.QuestionItemType, type: string) => {
    if (!exerciseInfo) {
      return;
    }
    const { questionSetId } = exerciseInfo;
    const payload: API.ExerciseQuestionOperateRequestParams = {
      questionSetId,
      questionId: question.questionId,
      sceneCategory,
    };
    const questionGroupId = Number(activeGroupId ?? 0);
    if (type === 'add') {
      exerciseQuestionAddRun({
        ...payload,
        bizTreeNodeId: selectedTreeNode?.key,
        questionGroupId,
      });
    }
    if (type === 'remove') {
      const q = exerciseQuestionMaps.get(question.questionId);
      if (q) {
        exerciseQuestionRemoveRun({ ...payload, questionGroupId: q.questionGroupId });
      }
    }
    if (type === 'recommend') {
      const recommend = question.isRecommend === 1 ? 0 : 1;
      exerciseQuestionRecommendRun({ ...payload, recommend, questionGroupId });
    }
  };

  // 全部设为推荐题
  const onCheckAllRecommendChange = (e: CheckboxChangeEvent) => {
    if (!exerciseInfo) {
      return;
    }
    const questionGroupId = Number(activeGroupId ?? 0);
    const { questionSetId } = exerciseInfo;
    exerciseQuestionBatchRecommendRun({
      recommend: e.target.checked ? 1 : 0,
      questionGroupId,
      questionSetId,
    });
  };

  // 题目全部设为推荐题状态
  const questionStatus = useMemo(() => {
    const validQuestions = questions.filter((question) => question.addDelStatus !== 2);
    const checkAll = validQuestions.every((question) => question.isRecommend === 1);
    const indeterminate =
      validQuestions.filter((question) => question.isRecommend === 1).length > 0 && !checkAll;
    return { checkAll, indeterminate };
  }, [questions]);

  // 计算整体loading状态
  const isLoading = addLoading || removeLoading || recommendLoading || batchRecommendLoading;

  return (
    <div>
      <Spin spinning={isLoading}>
        <Flex justify="end" className="mb-2">
          <Checkbox
            onChange={debounce(onCheckAllRecommendChange, 500)}
            indeterminate={questionStatus.indeterminate}
            checked={questionStatus.checkAll}
            style={{ userSelect: 'none' }}
          >
            全部设为推荐题
          </Checkbox>
        </Flex>
        <QuestionList
          questions={unshelfQuestions}
          type="exercise-selected"
          onQuestionEventHandler={onQuestionEventHandler}
          refresh={fetchExerciseInfoFunc}
        />
        <Divider variant="dashed" dashed>
          已上架题目
        </Divider>
        <QuestionList
          questions={shelfQuestions}
          type="exercise-selected"
          onQuestionEventHandler={onQuestionEventHandler}
          refresh={fetchExerciseInfoFunc}
        />
      </Spin>
    </div>
  );
};
export default ExerciseSelectedQuestionList;
