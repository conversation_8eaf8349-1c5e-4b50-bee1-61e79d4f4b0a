/**
 * 我的题目列表: 场景题库-巩固练习/拓展练习-编辑-仅看我的上传-列表区域
 */
import QuestionList from '@/components/QuestionList';
import { ExerciseContext } from '@/contexts/ExerciseContext';
import { useExerciseGroup } from '@/hooks/useExerciseGroup';
import {
  exerciseRevocationAudit,
  fetchMyQuestionList,
  useExerciseQuestionOperate,
} from '@/services/api';
import { useRequest } from '@umijs/max';
import { message } from 'antd';
import { FC, useContext, useEffect, useMemo } from 'react';

export interface ExerciseMyQuestionListProps {
  requestParams?: API.QuestionListRequestParams;
  onPageChange?: (page: number, pageSize: number) => void;
}
const ExerciseMyQuestionList: FC<ExerciseMyQuestionListProps> = ({
  requestParams,
  onPageChange,
}) => {
  const {
    exerciseInfo,
    type: sceneCategory,
    fetchExerciseInfoFunc,
    selectedTreeNode,
    activeGroupId,
  } = useContext(ExerciseContext);
  const { exerciseQuestions, exerciseQuestionMaps } = useExerciseGroup();

  // 请求问题列表
  const {
    data: questionListData,
    run: fetchQuestionListRun,
    loading: fetchQuestionListLoading,
  } = useRequest(
    () => {
      if (!requestParams) {
        return Promise.resolve({
          data: {
            total: 0,
            page: 1,
            pageSize: 10,
            list: [],
          },
        });
      }
      return fetchMyQuestionList(requestParams);
    },
    {
      manual: true,
      throttleInterval: 1000,
    },
  );

  const questionIds = useMemo(() => {
    if (!exerciseInfo || !exerciseInfo.list) {
      return {
        addedQuestionIds: [],
        deleteQuestionIds: [],
      };
    }
    const addedQuestionIds: string[] = [];
    const deleteQuestionIds: string[] = [];
    exerciseQuestions.forEach((item: API.ExerciseQuestionListItem) => {
      if (item.addDelStatus === 2) {
        deleteQuestionIds.push(item.questionId);
        return;
      }
      addedQuestionIds.push(item.questionId);
    });

    return {
      addedQuestionIds,
      deleteQuestionIds,
    };
  }, [exerciseInfo]);

  useEffect(() => {
    fetchQuestionListRun();
  }, [requestParams]);

  const questions = useMemo(() => {
    const list = questionListData?.list ?? [];
    const formatList = list.map((item: API.QuestionItemType) => ({
      ...item,
      isShowAddButton: !questionIds.addedQuestionIds.includes(item.questionId),
      questionGroupName: (() => {
        const q = exerciseQuestionMaps.get(item.questionId);
        if (q && q.questionGroupId !== Number(activeGroupId)) {
          return q?.questionGroupName || '';
        }
        return '';
      })(),
      disableSelect: (() => {
        const q = exerciseQuestionMaps.get(item.questionId);
        return q ? q.questionGroupId !== Number(activeGroupId) : false;
      })(),
    }));
    return formatList;
  }, [questionListData, questionIds, activeGroupId, exerciseQuestionMaps]);

  const { run: exerciseQuestionAddRun, loading: exerciseQuestionAddLoading } =
    useExerciseQuestionOperate('add', fetchExerciseInfoFunc);
  const { run: exerciseQuestionRemoveRun, loading: exerciseQuestionRemoveLoading } =
    useExerciseQuestionOperate('remove', fetchExerciseInfoFunc);
  const onQuestionEventHandler = (question: API.QuestionItemType, type: string) => {
    if (!exerciseInfo) {
      return;
    }
    const { questionSetId } = exerciseInfo;
    const payload: API.ExerciseQuestionOperateRequestParams = {
      questionSetId,
      questionId: question.questionId,
      sceneCategory,
    };
    if (type === 'add') {
      exerciseQuestionAddRun({
        ...payload,
        bizTreeNodeId: selectedTreeNode?.key,
        questionGroupId: Number(activeGroupId ?? 0),
      });
    }
    if (type === 'remove') {
      const q = exerciseQuestionMaps.get(question.questionId);
      if (q) {
        exerciseQuestionRemoveRun({ ...payload, questionGroupId: q.questionGroupId });
      }
    }
    if (type === 'revokeAudit') {
      onRevocationAudit(question.auditTaskId);
    }
  };

  // 撤回审核
  async function onRevocationAudit(taskId: number) {
    const res = await exerciseRevocationAudit(taskId);
    const { code } = res;
    if (code === 0) {
      message.success('撤回审核成功');
      if (requestParams?.page && requestParams?.page > 1) {
        onPageChange?.(1, requestParams?.pageSize ?? 10);
        return;
      }
      fetchQuestionListRun();
    } else {
      message.error('撤回审核不通过');
    }
  }

  return (
    <div>
      <QuestionList
        questions={questions}
        loading={
          fetchQuestionListLoading || exerciseQuestionAddLoading || exerciseQuestionRemoveLoading
        }
        pagination={{
          pageSize: questionListData?.pageSize ?? 10,
          total: questionListData?.total ?? 0,
          page: questionListData?.page ?? 1,
        }}
        onPageChange={(page, pageSize) => onPageChange?.(page, pageSize)}
        type="exercise-select-my"
        onQuestionEventHandler={onQuestionEventHandler}
        refresh={fetchQuestionListRun}
      />
    </div>
  );
};
export default ExerciseMyQuestionList;
