import { ExerciseContext } from '@/contexts/ExerciseContext';
import { useExerciseGroup } from '@/hooks/useExerciseGroup';
import { exerciseQuestionGroupEdit, exerciseQuestionGroupRemove } from '@/services/api';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { Button, Flex, Input, message, Modal, Select } from 'antd';
import { FC, useContext, useEffect, useMemo, useState } from 'react';

export interface GroupEditModalProps {
  showModal: boolean;
  showEditPosition?: boolean;
  onCloseModal: () => void;
}
const GroupEditModal: FC<GroupEditModalProps> = (props) => {
  const { showModal, showEditPosition = true, onCloseModal } = props;

  const { exerciseInfo, fetchExerciseInfoFunc, activeGroupId, setActiveGroupId } =
    useContext(ExerciseContext);
  const {
    exerciseGroupInsertPositionOpts,
    groups,
    activeGroupIndex,
    activeGroup,
    exerciseGroupNames,
  } = useExerciseGroup();
  const [groupName, setGroupName] = useState('');
  const [insertPosition, setInsertPosition] = useState<string | undefined>(undefined);
  const [modal, contextHolder] = Modal.useModal();

  const groupOptions = useMemo(() => {
    return exerciseGroupInsertPositionOpts.filter((item) => item.value !== activeGroupId);
  }, [exerciseGroupInsertPositionOpts, activeGroupId]);

  // 当前编辑组的前一个组的位置
  // 如果当前组是第一个组，则返回-1
  const currentPosition = useMemo(() => {
    if (activeGroupIndex !== -1) {
      return activeGroupIndex === 0 ? '-1' : String(groups[activeGroupIndex - 1].questionGroupId);
    }
    return undefined;
  }, [groups, activeGroupIndex]);

  const otherGroupNames = useMemo(() => {
    return exerciseGroupNames.filter((item) => item !== activeGroup?.questionGroupName);
  }, [exerciseGroupNames, activeGroup]);

  useEffect(() => {
    if (activeGroup && currentPosition) {
      setGroupName(activeGroup.questionGroupName);
      setInsertPosition(currentPosition);
    }
  }, [activeGroup, currentPosition]);

  const questionGroupRemove = async () => {
    if (activeGroupId) {
      const params: API.ExerciseQuestionGroupRemoveRequestParams = {
        questionGroupId: +activeGroupId,
        questionSetId: exerciseInfo?.questionSetId ?? 0,
      };
      const res = await exerciseQuestionGroupRemove(params);
      if (res.code === 0) {
        message.success('题组删除成功');
        const nextGroupIndex = activeGroupIndex === 0 ? 1 : activeGroupIndex - 1;
        const nextGroup = groups[nextGroupIndex];
        if (nextGroup) {
          setActiveGroupId(String(nextGroup.questionGroupId));
        } else {
          setActiveGroupId(undefined);
        }

        fetchExerciseInfoFunc();
        onCloseModal();
        return;
      }
      message.error('题组删除失败');
    }
  };

  const onRemoveHandler = async () => {
    const len = activeGroup?.questionStatusInfoList?.length ?? 0;
    if (len > 0) {
      modal.confirm({
        title: '是否确认删除题组',
        icon: <QuestionCircleOutlined />,
        content: '删除题组后，题组内题目将全部被移除',
        okText: '确认',
        cancelText: '取消',
        onOk: questionGroupRemove,
      });
      return;
    }
    questionGroupRemove();
  };

  const onSaveHandler = async () => {
    if (!activeGroupId) {
      return;
    }
    const questionGroupName = groupName.trim();
    if (!questionGroupName) {
      message.warning('请输入题组名称');
      return;
    }
    if (otherGroupNames.includes(questionGroupName)) {
      message.error('题组名称已存在');
      return;
    }
    if (insertPosition === undefined) {
      message.warning('请选择插入位置');
      return;
    }

    if (
      questionGroupName === activeGroup?.questionGroupName &&
      insertPosition === currentPosition
    ) {
      message.warning('题组名称和位置没有变化');
      onCloseModal();
      return;
    }

    const params: API.ExerciseQuestionGroupEditRequestParams = {
      questionSetId: exerciseInfo?.questionSetId ?? 0,
      questionGroupName,
      questionGroupId: +activeGroupId,
      afterInsertQuestionGroupId: +insertPosition,
    };

    const res = await exerciseQuestionGroupEdit(params);
    if (res.code === 0) {
      message.success('题组编辑成功');
      // setActiveGroupId(String(res.data.questionGroupId));
      fetchExerciseInfoFunc();
      // TODO: waiting backend fix
      // setActiveGroupId(res.data.questionGroupId);
      onCloseModal();
    }
  };

  const footer = useMemo(() => {
    return (
      <Flex justify="center" gap={16}>
        <Button type="primary" danger onClick={onRemoveHandler}>
          删除题组
        </Button>
        <Button type="primary" onClick={onSaveHandler}>
          保存
        </Button>
      </Flex>
    );
  }, [onRemoveHandler, onSaveHandler]);
  return (
    <Modal
      title="编辑题组"
      centered
      open={showModal}
      mask={true}
      width="500px"
      footer={footer}
      onCancel={onCloseModal}
    >
      <Flex vertical gap={24} style={{ padding: '24px' }}>
        <Flex gap={16} align="center">
          <span>题组名称:</span>
          <Input
            style={{ flex: 1 }}
            type="text"
            placeholder="请输入题组名称"
            value={groupName}
            maxLength={30}
            onChange={(e) => setGroupName(e.target.value)}
          />
        </Flex>
        {showEditPosition && (
          <Flex gap={16} align="center">
            <span>更换位置:</span>
            <Select
              style={{ flex: 1 }}
              options={groupOptions}
              placeholder="请选择插入位置"
              value={insertPosition}
              onChange={(value) => setInsertPosition(value)}
            />
          </Flex>
        )}
      </Flex>
      {contextHolder}
    </Modal>
  );
};
export default GroupEditModal;
