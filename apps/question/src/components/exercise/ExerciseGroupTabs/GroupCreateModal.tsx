import { ExerciseContext } from '@/contexts/ExerciseContext';
import { useExerciseGroup } from '@/hooks/useExerciseGroup';
import { exerciseQuestionGroupCreate } from '@/services/api';
import { Button, Flex, Input, message, Modal, Select } from 'antd';
import { FC, useContext, useEffect, useMemo, useState } from 'react';

export interface GroupCreateModalProps {
  showModal: boolean;
  onCloseModal: () => void;
}
const GroupCreateModal: FC<GroupCreateModalProps> = (props) => {
  const { showModal, onCloseModal } = props;

  const { exerciseInfo, fetchExerciseInfoFunc, setActiveGroupId } = useContext(ExerciseContext);
  const { exerciseGroupInsertPositionOpts, exerciseGroupNames } = useExerciseGroup();
  const [groupName, setGroupName] = useState('');
  const [insertPosition, setInsertPosition] = useState<string | undefined>(undefined);

  useEffect(() => {
    const lastGroup = exerciseGroupInsertPositionOpts.at(-1);
    if (lastGroup) {
      setInsertPosition(lastGroup.value);
    }
  }, [exerciseGroupInsertPositionOpts]);

  const onSaveHandler = async () => {
    const questionGroupName = groupName.trim();
    if (!questionGroupName) {
      message.error('请输入题组名称');
      return;
    }
    if (questionGroupName === '未命名') {
      message.error('题组名称不能为“未命名”');
      return;
    }
    if (exerciseGroupNames.includes(questionGroupName)) {
      message.error('题组名称已存在');
      return;
    }
    if (insertPosition === undefined) {
      message.error('请选择插入位置');
      return;
    }

    const params: API.ExerciseQuestionGroupCreateRequestParams = {
      questionGroupName,
      questionSetId: exerciseInfo?.questionSetId ?? 0,
      afterInsertQuestionGroupId: +insertPosition,
    };

    const res = await exerciseQuestionGroupCreate(params);
    if (res.code === 0) {
      message.success('题组创建成功');
      setActiveGroupId(String(res.data.questionGroupId));
      setGroupName('');
      setInsertPosition(undefined);
      fetchExerciseInfoFunc();
      onCloseModal();
      return;
    }
    message.error(res.message);
  };

  const footer = useMemo(() => {
    return (
      <Flex justify="center" gap={16}>
        <Button type="primary" onClick={onSaveHandler}>
          保存
        </Button>
      </Flex>
    );
  }, [onSaveHandler]);
  return (
    <Modal
      title="新增题组"
      centered
      open={showModal}
      mask={true}
      width="500px"
      footer={footer}
      onCancel={onCloseModal}
    >
      <Flex vertical gap={24} style={{ padding: '24px' }}>
        <Flex gap={16} align="center">
          <span>题组名称:</span>
          <Input
            style={{ flex: 1 }}
            type="text"
            placeholder="请输入题组名称"
            value={groupName}
            maxLength={30}
            onChange={(e) => {
              setGroupName(e.target.value);
            }}
          />
        </Flex>
        <Flex gap={16} align="center">
          <span>插入位置:</span>
          <Select
            style={{ flex: 1 }}
            options={exerciseGroupInsertPositionOpts}
            placeholder="请选择插入位置"
            value={insertPosition}
            onChange={(value) => setInsertPosition(value)}
          />
        </Flex>
      </Flex>
    </Modal>
  );
};
export default GroupCreateModal;
