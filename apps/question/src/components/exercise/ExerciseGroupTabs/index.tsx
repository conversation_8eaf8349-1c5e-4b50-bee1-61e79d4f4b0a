import GroupTabs from '@/components/GroupTabs';
import { ExerciseContext } from '@/contexts/ExerciseContext';
import { useExerciseGroup } from '@/hooks/useExerciseGroup';
import { FC, useCallback, useContext, useState } from 'react';
import ExerciseProgressInfo from '../ExerciseProgressInfo';
import GroupCreateModal from './GroupCreateModal';
import GroupEditModal from './GroupEditModal';

const ExerciseGroupTabs: FC<{ editable: boolean; addable: boolean }> = ({ editable, addable }) => {
  const { activeGroupId, setActiveGroupId } = useContext(ExerciseContext);
  const { exerciseGroupTabs, getGroupDifficultStat, exerciseUnnamedGroup, groups } =
    useExerciseGroup();
  const [showEditModal, setShowEditModal] = useState(false);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditPosition, setShowEditPosition] = useState(true);

  const onChange = (newActiveKey: string) => {
    setActiveGroupId(newActiveKey);
  };

  const onEdit = (
    targetKey: React.MouseEvent | React.KeyboardEvent | string,
    action: 'add' | 'remove',
  ) => {
    if (action === 'add') {
      if (exerciseUnnamedGroup) {
        setActiveGroupId(String(exerciseUnnamedGroup.questionGroupId));
        setShowEditPosition(false);
        setShowEditModal(true);
        return;
      }
      setShowCreateModal(true);
    }
  };

  const onEditGroup = () => {
    setShowEditModal(true);
  };

  const tabBarExtraContent = useCallback(() => {
    const difficultStat = getGroupDifficultStat(Number(activeGroupId));
    return {
      right: <ExerciseProgressInfo difficultStat={difficultStat} />,
    };
  }, [groups, activeGroupId]);

  return (
    <>
      <GroupTabs
        editable={editable}
        addable={addable}
        activeKey={activeGroupId}
        groups={exerciseGroupTabs}
        onChange={onChange}
        onEdit={onEditGroup}
        onAdd={onEdit}
        tabBarExtraContent={tabBarExtraContent()}
      />
      <GroupEditModal
        showModal={showEditModal}
        showEditPosition={showEditPosition}
        onCloseModal={() => {
          setShowEditModal(false);
          setShowEditPosition(true);
        }}
      />
      <GroupCreateModal
        showModal={showCreateModal}
        onCloseModal={() => setShowCreateModal(false)}
      />
    </>
  );
};
export default ExerciseGroupTabs;
