import { ExerciseContext } from '@/contexts/ExerciseContext';
import { Space, Typography } from 'antd';
import { FC, useContext, useMemo } from 'react';

const { Paragraph, Text, Title } = Typography;

export interface ExerciseBaseInfoProps {
  node?: API.Common.BaseTreeNodeAntdType;
  copyable?: boolean;
}
const ExerciseBaseInfo: FC<ExerciseBaseInfoProps> = ({ node, copyable = true }) => {
  const { title } = node ?? {};
  const { exerciseInfo } = useContext(ExerciseContext);

  const questionSetId = useMemo(() => {
    return exerciseInfo?.questionSetId;
  }, [exerciseInfo]);

  return (
    <Space align="end" style={{ flex: 1 }}>
      <Title level={5} style={{ margin: 0, fontWeight: 'normal' }}>
        {title}
      </Title>
      {questionSetId && (
        <Space align="center">
          <Text type="secondary">ID: </Text>
          <Paragraph type="secondary" copyable={copyable} style={{ margin: 0 }}>
            {questionSetId}
          </Paragraph>
        </Space>
      )}
    </Space>
  );
};
export default ExerciseBaseInfo;
