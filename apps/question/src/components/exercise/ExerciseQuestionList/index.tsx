/**
 * 场景题库-巩固练习/拓展练习-题目列表区域
 */
import QuestionCorrectionBtn from '@/components/QuestionCorrectionBtn';
import QuestionItem from '@/components/QuestionItem';
import { ExerciseContext } from '@/contexts/ExerciseContext';
import { useExerciseGroup } from '@/hooks/useExerciseGroup';
import { formatMainQuestions } from '@/utils';
import { Button, Card, Grid, List } from 'antd';
import { useContext, useEffect, useMemo, useRef, useState } from 'react';
import ExerciseCopyQuestionSet from '../ExerciseCopyQuestionSet';
import useStyles from './index.style';

const ExerciseQuestionList = ({ refresh }: { refresh?: () => void }) => {
  const { styles } = useStyles();
  const screens = Grid.useBreakpoint();
  const { exerciseInfo, fetchExerciseInfoLoading } = useContext(ExerciseContext);
  const { exerciseQuestions } = useExerciseGroup();
  const questionListCardRef = useRef<HTMLDivElement>(null);
  const [questionListCardMinHeight, setQuestionListCardMinHeight] = useState<string>('auto');

  const questions = useMemo(() => {
    const list = exerciseQuestions.filter((item) => item.addDelStatus !== 2);
    return formatMainQuestions(list);
  }, [exerciseInfo]);

  useEffect(() => {
    requestAnimationFrame(() => {
      if (questionListCardRef.current) {
        const rect = questionListCardRef.current?.getBoundingClientRect();
        const minHeight = screens.xl ? `calc(100vh - ${rect.top}px - 32px - 16px - 1px)` : 'auto';
        setQuestionListCardMinHeight(minHeight);
      }
    });
  }, [screens.xl, exerciseInfo]);

  const cardClassNames = useMemo(() => {
    return questions.length
      ? styles.questionCard
      : [styles.questionCard, styles.questionListEmpty].join(' ');
  }, [questions]);

  const Empty = useMemo(() => {
    if (!exerciseInfo?.questionSetId || fetchExerciseInfoLoading) {
      return <span />;
    }
    return <ExerciseCopyQuestionSet />;
  }, [exerciseInfo, fetchExerciseInfoLoading]);

  return (
    <div className="exercise-question-list-container">
      <Button type="primary" className={styles.selectedButton}>
        已选题目
      </Button>
      <Card
        className={cardClassNames}
        ref={questionListCardRef}
        style={{ minHeight: questionListCardMinHeight }}
        styles={{
          body: {
            width: '100%',
          },
        }}
      >
        <List
          itemLayout="vertical"
          loading={fetchExerciseInfoLoading}
          dataSource={questions}
          renderItem={(question: API.QuestionItemType) => (
            <List.Item style={{ padding: 0, marginBottom: 16, borderBottom: 'none' }}>
              <QuestionItem
                question={question}
                footerExtraBtns={<QuestionCorrectionBtn question={question} refresh={refresh} />}
              />
            </List.Item>
          )}
          style={{ width: '100%' }}
          locale={{
            emptyText: Empty,
          }}
        />
      </Card>
    </div>
  );
};
export default ExerciseQuestionList;
