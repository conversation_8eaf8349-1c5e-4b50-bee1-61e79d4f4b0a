import { createStyles } from 'antd-style';

const useStyles = createStyles(({ css }) => ({
  selectedButton: css`
    border-bottom-left-radius: 0 !important;
    border-bottom-right-radius: 0 !important;
  `,
  questionCard: css`
    .ant-card-body {
      padding: 16px;
    }
    border-top-left-radius: 0 !important;
  `,
  questionListEmpty: css`
    display: flex;
    align-items: center;
    justify-content: center;
  `,
}));

export default useStyles;
