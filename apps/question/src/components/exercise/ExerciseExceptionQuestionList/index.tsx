import QuestionList from '@/components/QuestionList';
import { ExerciseContext } from '@/contexts/ExerciseContext';
import { exerciseAuditProcess } from '@/services/api';
import { message } from 'antd';
import { FC, useContext } from 'react';

export interface ExerciseExceptionQuestionListProps {
  questions: API.QuestionItemType[];
}
/**
 * 审核不通过题目列表
 */
const ExerciseExceptionQuestionList: FC<ExerciseExceptionQuestionListProps> = ({
  questions = [],
}) => {
  const { exerciseInfo, fetchExerciseInfoFunc } = useContext(ExerciseContext);
  const onQuestionEventHandler = async (question: API.QuestionItemType, accept: string) => {
    if (!exerciseInfo) {
      return;
    }
    const { curAuditTaskId, questionSetId } = exerciseInfo;
    const res = await exerciseAuditProcess({
      auditTaskId: curAuditTaskId,
      questionId: question.questionId,
      accept: accept === 'accept' ? 1 : 2,
      questionSetId,
    });
    if (res.code === 0) {
      fetchExerciseInfoFunc();
      message.success('操作成功');
      return;
    }
    message.error('操作失败');
  };

  return (
    <div>
      <QuestionList
        questions={questions}
        type="exercise-audit-fail"
        onQuestionEventHandler={onQuestionEventHandler}
      />
    </div>
  );
};
export default ExerciseExceptionQuestionList;
