import { ExerciseContext } from '@/contexts/ExerciseContext';
import { useExercise } from '@/hooks/useExercise';
import { Button, Flex, message, Modal, Tag } from 'antd';
import { useContext, useMemo } from 'react';
import ExerciseBaseInfo from '../ExerciseBaseInfo';

// 提取样式常量，避免每次渲染时重复创建
const CONTAINER_STYLE = { height: 38 } as const;

const ExerciseBar = () => {
  const { selectedTreeNode } = useContext(ExerciseContext);
  const {
    isShelf,
    hasContentChanged,
    isAuditing,
    isAuditFailed,
    curAuditTaskFailReason,
    isAuditPassed,
  } = useExercise();
  const [modal, contextHolder] = Modal.useModal();

  const onShowAuditFailReason = () => {
    if (!curAuditTaskFailReason) {
      message.error('审核不通过原因不存在');
      return;
    }
    modal.info({
      title: '审核不通过原因',
      content: curAuditTaskFailReason,
      okText: '确认',
    });
  };

  const statusTag = useMemo(() => {
    if (isAuditing) {
      return (
        <Tag color="#757171" key="isAuditing">
          审核中
        </Tag>
      );
    }

    if (hasContentChanged) {
      return (
        <Flex gap={8}>
          {isAuditFailed && (
            <Flex gap={4} align="center">
              <Button
                color="primary"
                variant="link"
                onClick={onShowAuditFailReason}
                size="small"
                style={{ padding: 0, fontSize: 14, height: 22, lineHeight: '22px' }}
              >
                查看原因
              </Button>
              <Tag color="#f50" style={{ margin: 0 }}>
                审核不通过
              </Tag>
            </Flex>
          )}
          <Tag color="#757171" key="hasContentChanged">
            有内容变更需审核
          </Tag>
        </Flex>
      );
    }

    if (isAuditPassed) {
      return (
        <Tag color="#87d068" key="isAuditPassed">
          审核通过
        </Tag>
      );
    }

    return null;
  }, [
    isShelf,
    hasContentChanged,
    isAuditing,
    isAuditFailed,
    curAuditTaskFailReason,
    isAuditPassed,
  ]);

  return (
    <Flex align="center" justify="space-between" style={CONTAINER_STYLE}>
      {isShelf ? <Tag color="#87d068">已上架</Tag> : <Tag color="#f50">未上架</Tag>}
      <ExerciseBaseInfo node={selectedTreeNode} />
      <Flex align="center" justify="end">
        {statusTag}
      </Flex>
      {contextHolder}
    </Flex>
  );
};
export default ExerciseBar;
