import { LeftOutlined } from '@ant-design/icons';
import { <PERSON>ton, Card, Flex, Modal, theme, Typography } from 'antd';
import { FC, useContext, useMemo } from 'react';
import ExerciseBaseInfo from '../ExerciseBaseInfo';

import { ExerciseContext } from '@/contexts/ExerciseContext';
import { getFailQuestions } from '@/utils/questionInput';
import ExerciseExceptionQuestionList from '../ExerciseExceptionQuestionList';

export interface ExerciseAuditFailModalProps {
  node?: API.Common.BaseTreeNodeAntdType;
  showModal: boolean;
  onCloseModal: () => void;
}
const ExerciseAuditFailModal: FC<ExerciseAuditFailModalProps> = (props) => {
  const { node, showModal, onCloseModal } = props;
  const { exerciseInfo } = useContext(ExerciseContext);
  const [modal, contextHolder] = Modal.useModal();
  const { useToken } = theme;
  const { token } = useToken();

  const onBack = () => {
    onCloseModal();
  };

  const onViewReason = () => {
    if (!exerciseInfo || !exerciseInfo.curAuditTaskFailReason) {
      return;
    }
    modal.info({
      title: '不通过原因',
      content: exerciseInfo.curAuditTaskFailReason,
      okText: '返回',
    });
  };

  // 总审核状态为 3 且 题目中有审核状态为 3 且 题目没有被处理的项
  const fialQuestions = useMemo(() => getFailQuestions(exerciseInfo), [exerciseInfo]);

  return (
    <Modal
      centered
      open={showModal}
      mask={false}
      width={'100vw'}
      height={'100vh'}
      closable={false}
      footer={null}
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        borderRadius: 0,
        maxWidth: '100vw',
        maxHeight: '100vh',
      }}
      styles={{
        content: {
          padding: 0,
        },
        body: {
          padding: 0,
          height: '100vh',
          borderRadius: 0,
          backgroundColor: token.colorBgLayout,
        },
      }}
    >
      <div style={{ height: '100vh', padding: '50px 0 0' }}>
        <Flex
          align="center"
          justify="space-between"
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            height: 50,
            padding: '0 20px 0 0',
          }}
          gap={16}
        >
          <Button
            type="text"
            size="large"
            icon={<LeftOutlined />}
            style={{ gap: 4 }}
            onClick={onBack}
          >
            返回
          </Button>
          <ExerciseBaseInfo node={node} copyable={false} />
          <Typography.Link onClick={onViewReason}>查看原因</Typography.Link>
        </Flex>
        <Card style={{ height: '100%', overflow: 'auto', borderRadius: 0 }}>
          <ExerciseExceptionQuestionList questions={fialQuestions} />
        </Card>
      </div>
      {contextHolder}
    </Modal>
  );
};
export default ExerciseAuditFailModal;
