/**
 * 问题难易程度枚举
 */
export enum QuestionDifficulty {
  /** 简单 */
  EASY = 1,
  /** 较易 */
  RELATIVELY_EASY = 2,
  /** 中等 */
  MEDIUM = 3,
  /** 较难 */
  RELATIVELY_HARD = 4,
  /** 困难 */
  HARD = 5,
}

// export const progressInfo = (info: ): ProgressInfoItemType[] => {
//     [
//         {
//             label: '简单',
//             least: 20,
//             current: 0,
//             color: 'green',
//         },
//         {
//             label: '较易',
//             least: 20,
//             current: 0,
//             color: 'cyan',
//         },
//         {
//             label: '中等',
//             least: 20,
//             current: 0,
//             color: 'blue',
//         },
//         {
//             label: '较难',
//             least: 20,
//             current: 0,
//             color: 'orange',
//         },
//         {
//             label: '困难',
//             least: 20,
//             current: 0,
//             color: 'red',
//         },
//     ]
// }
