import { ExerciseContext } from '@/contexts/ExerciseContext';
import { useExercise } from '@/hooks/useExercise';
import { useExerciseGroup } from '@/hooks/useExerciseGroup';
import { exerciseRevocationAudit } from '@/services/api';
import { createAuditTask } from '@/services/api/review';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { Button, Card, Flex, message, Modal, Typography } from 'antd';
import { FC, Fragment, useCallback, useContext, useMemo } from 'react';
import ExerciseProgressInfo from '../ExerciseProgressInfo';
const { Title } = Typography;

export interface ExerciseProgressProps {
  node?: API.Common.BaseTreeNodeAntdType;
  onEdit: () => void;
  onHandleException: () => void;
}
const ExerciseProgress: FC<ExerciseProgressProps> = (props) => {
  const { onEdit } = props;
  const [modal, contextHolder] = Modal.useModal();
  const { exerciseInfo, fetchExerciseInfoFunc, selectedTreeNode, selectedSubject, type } =
    useContext(ExerciseContext);
  const { exerciseNoQuestionGroups, exerciseUnnamedGroup } = useExerciseGroup();
  const { isAuditing, hasContentChanged } = useExercise();

  const unSelectQuestionGroupContent = useCallback(
    (text: string) => {
      const groupNames = exerciseNoQuestionGroups.map((g) => g.questionGroupName);
      return (
        <Flex vertical gap={4}>
          <div>以下题组未选题：</div>
          <div style={{ marginLeft: 10 }}>
            {groupNames.map((name, idx) => (
              <div key={name}>
                {idx + 1}. {name}
              </div>
            ))}
          </div>
          <div>请完成选题，再{text}</div>
        </Flex>
      );
    },
    [exerciseNoQuestionGroups],
  );

  const confirm = (text: string = '上架') => {
    if (exerciseUnnamedGroup && type === 2) {
      modal.error({
        title: '有未命名的题组',
        content: '当前练习包含未命名的题组，请先修改组名再' + text,
        okText: '确认',
      });
      return;
    }
    if (exerciseNoQuestionGroups.length > 0) {
      modal.error({
        title: '有未选题的题组',
        content: unSelectQuestionGroupContent(text),
        okText: '确认',
      });
      return;
    }
    modal.confirm({
      title: '是否确认' + text,
      icon: <QuestionCircleOutlined />,
      content: '点击确认将进入审核阶段，通过后自动完成上架',
      okText: '确认',
      cancelText: '返回',
      onOk: onSubmitAudit,
    });
  };
  const revocation = () => {
    modal.confirm({
      title: '是否确认撤回审核',
      content: '点击确认将撤回审核，可以编辑选题后重新提交',
      icon: <QuestionCircleOutlined />,
      okText: '确认',
      cancelText: '返回',
      onOk: onRevocationAudit,
    });
  };
  const submitWarning = (msg: string) => {
    modal.warning({
      content: msg || '不满足上架题目要求的数量，请添加题目后重试',
      okText: '返回',
    });
  };

  // 上架 + 提交审核
  async function onSubmitAudit() {
    if (!exerciseInfo || !selectedTreeNode) {
      return;
    }
    const { questionSetId } = exerciseInfo;
    if (!questionSetId) return;
    const [phase, subject] = selectedSubject;
    const payload = {
      chooseQuestion: { questionSetId },
      auditTaskType: 2,
      phase,
      subject,
      // bizTreeNodeId: selectedTreeNode.key,
      // sceneCategory: type,
    };
    const res = await createAuditTask(payload);
    const { code, message: msg } = res;
    if (code === 0) {
      if (!!questionSetId) {
        message.success('提交审核成功');
      } else {
        message.success('上架成功');
      }

      fetchExerciseInfoFunc();
      // setTreeUpdateTimestamp(Date.now());
    } else {
      submitWarning(msg);
    }
  }

  // 撤回审核
  async function onRevocationAudit() {
    if (!exerciseInfo || !selectedTreeNode || !exerciseInfo.curAuditTaskId) {
      return;
    }
    const { curAuditTaskId } = exerciseInfo;

    const res = await exerciseRevocationAudit(curAuditTaskId);
    const { code } = res;
    if (code === 0) {
      message.success('撤回审核成功');
      fetchExerciseInfoFunc();
    } else {
      message.error('撤回审核不通过');
    }
  }

  const btnList = useMemo(() => {
    if (!exerciseInfo) {
      return [];
    }

    // 不管是否上架，审核状态处于审核中时
    if (isAuditing) {
      return [
        <Button size="middle" onClick={revocation} key="revocation">
          撤回审核
        </Button>,
      ];
    }
    const list = [
      <Button type="primary" size="middle" onClick={onEdit} key="edit">
        编辑
      </Button>,
    ];
    // 审核不通过且有异常题目
    // if (isAuditFailed) {
    //   list.push(
    //     <Button
    //       size="middle"
    //       type="primary"
    //       danger
    //       onClick={onHandleException}
    //       key="handleException"
    //     >
    //       处理异常
    //     </Button>,
    //   );
    // }

    if (hasContentChanged) {
      list.push(
        <Button size="middle" onClick={() => confirm('提交审核')} key="submitAudit">
          提交审核
        </Button>,
      );
    }

    // 上架后
    // 审核通过且没有内容变化
    // if (!hasContentChange) {
    //   return [
    //     <Button type="primary" size="middle" onClick={onEdit} key="edit">
    //       编辑
    //     </Button>,
    //   ];
    // }

    // return [
    //   <Button type="primary" size="middle" onClick={onEdit} key="edit">
    //     编辑
    //   </Button>,
    //   <Button size="middle" onClick={() => confirm('提交审核')} key="submitAudit">
    //     提交审核
    //   </Button>,
    // ];
    return list;
  }, [exerciseInfo]);

  const difficultStat = useMemo(() => {
    return exerciseInfo?.difficultStat ?? [];
  }, [exerciseInfo]);

  return (
    <Card>
      <Flex justify="space-between" align="center" gap="small">
        <Title level={5} style={{ margin: 0, fontWeight: 'normal' }}>
          选题进度
        </Title>
        <ExerciseProgressInfo difficultStat={difficultStat} />
        <Flex gap="small" wrap>
          {btnList.map((btn, index) => (
            <Fragment key={index}>{btn}</Fragment>
          ))}
        </Flex>
      </Flex>
      {contextHolder}
      {/* {revocationContextHolder} */}
    </Card>
  );
};
export default ExerciseProgress;
