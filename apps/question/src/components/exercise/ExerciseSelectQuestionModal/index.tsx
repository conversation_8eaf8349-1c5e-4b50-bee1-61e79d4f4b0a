/**
 * 场景题库-巩固练习/拓展练习-编辑-弹窗
 */
import FilterPanel from '@/components/FilterPanel';
import QuestionSearch from '@/components/QuestionSearch';
import TreeNodeFilter from '@/components/TreeNodeFilter';
import { ExerciseContext } from '@/contexts/ExerciseContext';
import { useExerciseGroup } from '@/hooks/useExerciseGroup';
import { ChangeEventPayload } from '@/types/common/common';
import { scrollToTop } from '@/utils';
import {
  EXERCISE_SELECT_QUESTION_MODAL_TREE_HEIGHT,
  EXERCISE_SELECT_QUESTION_MODAL_TREE_HEIGHT_2,
  sortOptions,
} from '@/utils/constants';
import { TreeTypeEnum } from '@/utils/phaseSubject';
import { defaultQuestionListRequestParams } from '@/utils/tree';
import { ContainerOutlined, LeftOutlined } from '@ant-design/icons';
import {
  Affix,
  Badge,
  Button,
  Card,
  Checkbox,
  CheckboxProps,
  Col,
  Flex,
  Modal,
  Radio,
  RadioChangeEvent,
  Row,
  Space,
  theme,
  Typography,
} from 'antd';
import React, { FC, useContext, useMemo, useState } from 'react';
import ExerciseBaseInfo from '../ExerciseBaseInfo';
import ExerciseGroupTabs from '../ExerciseGroupTabs';
import ExerciseMyQuestionList from '../ExerciseMyQuestionList';
import ExerciseSelectedQuestionList from '../ExerciseSelectedQuestionList';
import ExerciseSelectQuestionList from '../ExerciseSelectQuestionList';

export interface ExerciseSelectQuestionModalProps {
  node?: API.Common.BaseTreeNodeAntdType;
  showModal: boolean;
  onCloseModal: () => void;
}
const ExerciseSelectQuestionModal: FC<ExerciseSelectQuestionModalProps> = (props) => {
  const { node, showModal, onCloseModal } = props;
  const { selectedSubject, selectedTreeNode, bizTreeValue, type } = useContext(ExerciseContext);
  const { exerciseQuestions, activeGroupQuestions } = useExerciseGroup();
  const [questionListRequestParams, setQuestionListRequestParams] =
    useState<API.QuestionListRequestParams>();
  const [sort, setSort] = useState<string>('createTime');
  const [container, setContainer] = useState<HTMLDivElement | null>(null);
  const [top] = React.useState<number>(26);
  const [showSelectedQuestionView, setShowSelectedQuestionView] = useState<boolean>(false);
  const [groupEditable, setGroupEditable] = useState<boolean>(true);
  const [groupAddable, setGroupAddable] = useState<boolean>(true);
  const [viewMyQuestion, setViewMyQuestion] = useState<boolean>(false);
  const { useToken } = theme;
  const { token } = useToken();

  const contentHeight = useMemo(() => {
    if (type !== 2) {
      return `calc(100vh - 50px)`;
    }
    return `calc(100vh - 91px)`;
  }, [type]);

  const affixHeight = useMemo(() => {
    if (type !== 2) {
      return `calc(100vh - 74px - 24px)`;
    }
    return `calc(100vh - 74px - 24px - 41px)`;
  }, [type]);

  const treeHeight = useMemo(() => {
    if (type !== 2) {
      return EXERCISE_SELECT_QUESTION_MODAL_TREE_HEIGHT;
    }
    return EXERCISE_SELECT_QUESTION_MODAL_TREE_HEIGHT_2;
  }, [type]);

  const onBack = () => {
    if (showSelectedQuestionView) {
      setShowSelectedQuestionView(false);
      setGroupEditable(true);
      setGroupAddable(true);
      return;
    }
    onCloseModal();
  };

  /**
   * 显示已选题目视图
   */
  const onSelectedQuestion = () => {
    const value = !showSelectedQuestionView;
    setShowSelectedQuestionView(value);
    setGroupEditable(!value);
    setGroupAddable(!value);
  };

  const onFilterChange = (params: ChangeEventPayload) => {
    const { type, field, value } = params;
    if (!questionListRequestParams) {
      return;
    }

    if (type === 'search') {
      setQuestionListRequestParams({
        ...questionListRequestParams,
        keyword: value,
        page: 1,
      });
      return;
    }
    questionListRequestParams.page = 1;
    if (type === 'filter') {
      const params: API.QuestionListRequestParams = { ...questionListRequestParams, page: 1 };
      params[field] = value;
      setQuestionListRequestParams(params);
      return;
    }

    if (type === 'sort') {
      setQuestionListRequestParams({
        ...questionListRequestParams,
        sort: value,
      });
      return;
    }

    if (type === 'reset') {
      const { phaseList, subjectList, baseTreeNodeIds, bizTreeNodeIds, sort, keyword } =
        questionListRequestParams;
      const requestParams = defaultQuestionListRequestParams();
      setQuestionListRequestParams({
        ...requestParams,
        phaseList,
        subjectList,
        baseTreeNodeIds,
        bizTreeNodeIds,
        sort,
        keyword,
      });
      return;
    }

    // 发送请求
  };
  const onSortTypeChange = (e: RadioChangeEvent) => {
    const { value } = e.target;
    setSort(value);
    onFilterChange({
      type: 'sort',
      field: 'sort',
      value,
    });
  };

  const onPageChange = (page: number, pageSize: number) => {
    if (!questionListRequestParams) {
      return;
    }
    setQuestionListRequestParams({
      ...questionListRequestParams,
      page,
      pageSize,
    });
    // 滚动到页面顶部
    scrollToTop('.exercise-select-question-modal-card');
  };

  const onTreeNodeChange = (
    value: number,
    node: API.Common.BaseTreeNodeAntdType,
    isBaseTree: boolean,
  ) => {
    if (!value) {
      return;
    }
    scrollToTop('.exercise-select-question-modal-card');
    const params = isBaseTree
      ? {
          baseTreeNodeIds: [value],
          bizTreeNodeIds: [],
        }
      : {
          bizTreeNodeIds: [value],
          baseTreeNodeIds: [],
        };
    const [phaseId, subjectId] = selectedSubject;
    const phaseList = phaseId ? [phaseId] : [];
    const subjectList = subjectId ? [subjectId] : [];
    setQuestionListRequestParams({
      ...questionListRequestParams,
      ...params,
      phaseList,
      subjectList,
      page: 1,
    });
  };

  const onKeywordSearch = (keyword: string) => {
    onFilterChange({
      type: 'search',
      field: '',
      value: keyword,
    });
  };

  const onCheckedMyQuestion: CheckboxProps['onChange'] = (e) => {
    setViewMyQuestion(e.target.checked);
  };

  const onUploadQuestion = () => {
    const [phaseId, subjectId] = selectedSubject;
    window.open(`/question-input/create?phaseId=${phaseId}&subjectId=${subjectId}`, '_blank');
  };

  return (
    <Modal
      centered
      open={showModal}
      mask={false}
      width={'100vw'}
      height={'100vh'}
      closable={false}
      footer={null}
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        // padding: 0,
        borderRadius: 0,
        maxWidth: '100vw',
        maxHeight: '100vh',
      }}
      styles={{
        content: {
          padding: 0,
        },
        body: {
          padding: 0,
          height: '100vh',
          borderRadius: 0,
          backgroundColor: token.colorBgLayout,
        },
      }}
    >
      <div style={{ height: '100vh', padding: '50px 0 0' }}>
        <Flex
          align="center"
          justify="space-between"
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            height: 50,
            padding: '0 20px 0 0',
          }}
          gap={16}
        >
          <Button
            type="text"
            size="large"
            icon={<LeftOutlined />}
            style={{ gap: 4 }}
            onClick={onBack}
          >
            返回
          </Button>
          <ExerciseBaseInfo node={node} copyable={false} />

          <Typography.Text>选题预览</Typography.Text>

          <Badge count={exerciseQuestions.length} size="small" showZero={true}>
            <Button
              type="text"
              icon={<ContainerOutlined style={{ fontSize: 20 }} />}
              size="small"
              onClick={onSelectedQuestion}
            />
          </Badge>
        </Flex>
        {type === 2 && <ExerciseGroupTabs editable={groupEditable} addable={groupAddable} />}
        <Card
          className="exercise-select-question-modal-card"
          style={{ height: contentHeight, overflow: 'auto', borderRadius: 0 }}
          ref={setContainer}
        >
          <Row gutter={16}>
            <Col span={6}>
              <div style={{ minHeight: affixHeight }}>
                <Affix offsetTop={top} target={() => container} style={{ maxHeight: affixHeight }}>
                  <TreeNodeFilter
                    selectedSubject={selectedSubject}
                    onChange={onTreeNodeChange}
                    isOnlyFilterLeafNode={false}
                    treeHeight={treeHeight}
                    queryTypeProp={TreeTypeEnum.BIZ_TREE}
                    treeNodeIdProp={selectedTreeNode?.key}
                    defaultTreeValue={bizTreeValue}
                  />
                </Affix>
              </div>
            </Col>
            <Col span={18}>
              <Space direction="vertical" size={16} style={{ width: '100%' }}>
                <Card>
                  <FilterPanel filters={questionListRequestParams} onChange={onFilterChange} />
                </Card>
                <Flex align="center" justify="space-between">
                  <Radio.Group
                    value={sort}
                    options={sortOptions}
                    defaultValue="Apple"
                    optionType="button"
                    buttonStyle="solid"
                    onChange={onSortTypeChange}
                  />
                  <Space>
                    <Checkbox checked={viewMyQuestion} onChange={onCheckedMyQuestion}>
                      仅看我的上传
                    </Checkbox>
                    <QuestionSearch
                      onSearch={onKeywordSearch}
                      value={questionListRequestParams?.search}
                    />
                    <Button type="primary" onClick={onUploadQuestion}>
                      上传题目
                    </Button>
                  </Space>
                </Flex>
                {viewMyQuestion ? (
                  <ExerciseMyQuestionList
                    requestParams={questionListRequestParams}
                    onPageChange={onPageChange}
                  />
                ) : (
                  <ExerciseSelectQuestionList
                    requestParams={questionListRequestParams}
                    onPageChange={onPageChange}
                  />
                )}
              </Space>
            </Col>
          </Row>
        </Card>
        {showSelectedQuestionView && (
          <Card
            style={{
              height: contentHeight,
              overflow: 'auto',
              borderRadius: 0,
              position: 'fixed',
              top: type === 2 ? '91px' : '50px',
              left: 0,
              right: 0,
              bottom: 0,
              zIndex: 1000,
            }}
          >
            <ExerciseSelectedQuestionList questions={activeGroupQuestions} />
          </Card>
        )}
      </div>
    </Modal>
  );
};
export default ExerciseSelectQuestionModal;
