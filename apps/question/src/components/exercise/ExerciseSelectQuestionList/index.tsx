/**
 * 题目选择列表: 场景题库-巩固练习/拓展练习-编辑-列表区域
 */
import QuestionList from '@/components/QuestionList';
import { ExerciseContext } from '@/contexts/ExerciseContext';
import useEnumManagerMap from '@/hooks/useEnumManager';
import { useExerciseGroup } from '@/hooks/useExerciseGroup';
import { fetchQuestionList, useExerciseQuestionOperate } from '@/services/api';
import { compareNumberArrayEqual } from '@/utils';
import { useRequest } from '@umijs/max';
import { cloneDeep } from 'lodash';
import { FC, useContext, useEffect, useMemo } from 'react';

export interface ExerciseSelectQuestionListProps {
  requestParams?: API.QuestionListRequestParams;
  onPageChange?: (page: number, pageSize: number) => void;
}
const ExerciseSelectQuestionList: FC<ExerciseSelectQuestionListProps> = ({
  requestParams,
  onPageChange,
}) => {
  const {
    exerciseInfo,
    type: sceneCategory,
    fetchExerciseInfoFunc,
    selectedTreeNode,
    activeGroupId,
  } = useContext(ExerciseContext);
  const { exerciseQuestions, exerciseQuestionMaps } = useExerciseGroup();
  const { provinceList } = useEnumManagerMap();

  // 请求问题列表
  const {
    data: questionListData,
    run: fetchQuestionListRun,
    loading: fetchQuestionListLoading,
  } = useRequest(
    () => {
      if (!requestParams) {
        return Promise.resolve({
          data: {
            total: 0,
            page: 1,
            pageSize: 10,
            list: [],
          },
        });
      }
      const payload = cloneDeep(requestParams);
      const { questionProvince } = payload;
      const provinces = provinceList?.enums.map((item) => item.value) ?? [];
      if (questionProvince && compareNumberArrayEqual(questionProvince, provinces)) {
        payload.questionProvince = [];
      }
      return fetchQuestionList(payload);
    },
    {
      manual: true,
      throttleInterval: 1000,
    },
  );

  const { run: exerciseQuestionAddRun, loading: exerciseQuestionAddLoading } =
    useExerciseQuestionOperate('add', fetchExerciseInfoFunc);
  const { run: exerciseQuestionRemoveRun, loading: exerciseQuestionRemoveLoading } =
    useExerciseQuestionOperate('remove', fetchExerciseInfoFunc);

  const questionIds = useMemo(() => {
    if (!exerciseInfo || !exerciseInfo.list) {
      return {
        addedQuestionIds: [],
        deleteQuestionIds: [],
      };
    }
    const addedQuestionIds: string[] = [];
    const deleteQuestionIds: string[] = [];
    exerciseQuestions.forEach((item: API.ExerciseQuestionListItem) => {
      if (item.addDelStatus === 2) {
        deleteQuestionIds.push(item.questionId);
        return;
      }
      addedQuestionIds.push(item.questionId);
    });

    return {
      addedQuestionIds,
      deleteQuestionIds,
    };
  }, [exerciseInfo]);

  useEffect(() => {
    fetchQuestionListRun();
  }, [requestParams]);

  const questions = useMemo(() => {
    const list = questionListData?.list ?? [];
    const formatList = list.map((item: API.QuestionItemType) => ({
      ...item,
      isShowAddButton: !questionIds.addedQuestionIds.includes(item.questionId),
      questionGroupName: (() => {
        const q = exerciseQuestionMaps.get(item.questionId);
        if (q && q.questionGroupId !== Number(activeGroupId)) {
          return q?.questionGroupName || '';
        }
        return '';
      })(),
      disableSelect: (() => {
        const q = exerciseQuestionMaps.get(item.questionId);
        return q ? q.questionGroupId !== Number(activeGroupId) : false;
      })(),
    }));
    return formatList;
  }, [questionListData, questionIds, activeGroupId, exerciseQuestionMaps]);

  const onQuestionEventHandler = (question: API.QuestionItemType, type: string) => {
    if (!exerciseInfo) {
      return;
    }
    const { questionSetId } = exerciseInfo;
    const payload: API.ExerciseQuestionOperateRequestParams = {
      questionSetId,
      questionId: question.questionId,
      sceneCategory,
    };
    if (type === 'add') {
      exerciseQuestionAddRun({
        ...payload,
        bizTreeNodeId: selectedTreeNode?.key,
        questionGroupId: Number(activeGroupId ?? 0),
      });
    }
    if (type === 'remove') {
      const q = exerciseQuestionMaps.get(question.questionId);
      if (q) {
        exerciseQuestionRemoveRun({ ...payload, questionGroupId: q.questionGroupId });
      }
    }
  };
  return (
    <div>
      <QuestionList
        questions={questions}
        loading={
          fetchQuestionListLoading || exerciseQuestionAddLoading || exerciseQuestionRemoveLoading
        }
        pagination={{
          pageSize: questionListData?.pageSize ?? 10,
          total: questionListData?.total ?? 0,
          page: questionListData?.page ?? 1,
        }}
        onPageChange={(page, pageSize) => onPageChange?.(page, pageSize)}
        type="exercise-select"
        onQuestionEventHandler={onQuestionEventHandler}
        refresh={fetchQuestionListRun}
      />
    </div>
  );
};
export default ExerciseSelectQuestionList;
