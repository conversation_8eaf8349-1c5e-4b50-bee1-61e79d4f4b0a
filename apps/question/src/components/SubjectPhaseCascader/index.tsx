import { useModel } from '@umijs/max';
import { Cascader } from 'antd';
import { createStyles } from 'antd-style';
import React from 'react';

const useStyles = createStyles(({ token }) => ({
  treeSelect: {
    '.ant-select-selector': {
      backgroundColor: `${token.colorPrimary} !important`,
      borderColor: `${token.colorPrimary} !important`,
      color: 'white !important',
    },
    '.ant-select-selection-placeholder': {
      color: 'rgba(255, 255, 255, 0.8) !important',
    },
    '.ant-select-arrow': {
      color: 'white !important',
    },
    // 添加焦点状态下的样式
    '.ant-select-selection-item': {
      color: 'white !important',
    },
  },
}));

export interface SubjectPhaseCascaderProps {
  onChange?: (value: number[]) => void;
  selectedSubject?: number[];
  isPrimary?: boolean;
  changeOnSelect?: boolean;
}

const SubjectPhaseCascader: React.FC<SubjectPhaseCascaderProps> = ({
  selectedSubject,
  onChange,
  isPrimary = true,
  changeOnSelect = false,
}) => {
  const { styles } = useStyles();
  const { initialState } = useModel('@@initialState');
  // 学段科目数据
  // const phaseSubjects = initialState?.globalDictValues?.phaseSubjects ?? [];
  const enumConstants = (initialState?.globalDictValues?.enumConstants ??
    {}) as API.EnumConstantData;
  const phaseSubjects = enumConstants?.phaseSubjectRelation ?? [];

  // console.log('initialState ********: ', phaseSubjects);

  const onPhaseSubjectValueChange = (value: number[]) => {
    onChange?.(value);
  };

  return (
    <Cascader
      style={{ width: '100%' }}
      fieldNames={{ label: 'nameZh', value: 'value', children: 'subjectList' }}
      options={phaseSubjects}
      defaultValue={selectedSubject}
      onChange={onPhaseSubjectValueChange}
      changeOnSelect={changeOnSelect}
      size="large"
      placeholder="请选择科目"
      className={isPrimary ? styles.treeSelect : ''}
      allowClear={false}
    />
  );
};

export default SubjectPhaseCascader;
