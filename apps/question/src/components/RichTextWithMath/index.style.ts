import { createStyles } from 'antd-style';

const useStyles = createStyles(({}) => {
  return {
    richTextStyle: {
      whiteSpace: 'pre-wrap',
      lineHeight: '2.5',
      p: {
        margin: 0,
      },
      '[data-tiptype="question-blank_filling"]': {
        position: 'relative',
        display: 'inline-block',
        width: '60px',
        lineHeight: 1.5,
        borderBottom: '1px solid #333',
        textAlign: 'center',
        margin: '0 4px',
        minHeight: '1.5em',
        verticalAlign: 'text-bottom',

        '&::after': {
          content: 'attr(data-index)',
          display: 'inline-block',
          position: 'absolute',
          right: 0,
          left: 0,
          bottom: '2px',
          lineHeight: 1,
        },
      },
    },
  };
});

export default useStyles;
