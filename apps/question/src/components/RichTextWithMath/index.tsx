import { fetchQuestionResource } from '@/services/api';
import { placeholderImage } from '@/utils';
import { addHostToImgSrc } from '@/utils/common';
import { MathJax, MathJaxContext } from 'better-react-mathjax';
import katex from 'katex';
import 'katex/dist/katex.min.css';
import { useEffect, useMemo, useRef } from 'react';
import useStyles from './index.style';
// import { convertLatexToMarkup } from "mathlive";
interface RichTextWithMathProps {
  htmlContent: string; // 后端返回的富文本（可能包含 \(...\) 或 $$...$$ 公式）
  questionId?: string;
}

export function RichTextWithMath2({ htmlContent, questionId }: RichTextWithMathProps) {
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!containerRef.current) return;

    // 1. 找到所有公式占位符（\(...\) 或 $$...$$）
    const inlineRegex = /\\\((.*?)\\\)/g; // 匹配 \(...\)
    const dollarInlineRegex = /\$(.*?)\$/g; // 匹配 $...$
    // const blockRegex = /\\\[(.*?[^\\])\\\]/g;   // 匹配 \[...\]
    const blockRegex = /\\\[([\s\S]*?[^\\])\\\]/g; // 匹配 \[...\]
    const dollarBlockRegex = /\$\$(.*?)\$\$/g; // 匹配 $$...$$

    // 2. 替换公式为 KaTeX 渲染的 HTML
    const renderFormulas = (html: string) => {
      let processedHtml = html;

      // 处理行内公式（\(...\)）
      processedHtml = processedHtml.replace(inlineRegex, (_, formula) => {
        try {
          return katex.renderToString(formula, { throwOnError: false, displayMode: false });
        } catch (e) {
          console.error('KaTeX 渲染失败:', formula, e);
          return formula; // 渲染失败时回退原始文本
        }
      });
      // 处理行内公式 $...$
      processedHtml = processedHtml.replace(dollarInlineRegex, (_, formula) => {
        try {
          return katex.renderToString(formula, { throwOnError: false, displayMode: false });
        } catch (e) {
          console.error('KaTeX 渲染失败:', formula, e);
          return formula; // 渲染失败时回退原始文本
        }
      });

      // 处理块级公式 \[...\]
      processedHtml = processedHtml.replace(blockRegex, (_, formula) => {
        try {
          return katex.renderToString(formula, { throwOnError: false, displayMode: true });
        } catch (e) {
          console.error('KaTeX 渲染失败:', formula, e);
          return formula;
        }
      });
      // 处理块级公式（$$...$$）
      processedHtml = processedHtml.replace(dollarBlockRegex, (_, formula) => {
        try {
          return katex.renderToString(formula, { throwOnError: false, displayMode: true });
        } catch (e) {
          console.error('KaTeX 渲染失败:', formula, e);
          return formula;
        }
      });

      return processedHtml;
    };

    // 如果上面的处理不行，可以试试联合下面的处理

    // const handleDollerStr = (content: string) => {
    //   const paragraphs = content.split("\n");
    //   // console.log("paragraphs", paragraphs);

    //   return paragraphs.map((paragraph: string) => {
    //     const paragraphElement = document.createElement("div");
    //     paragraphElement.style.marginBottom = "1em"; // 添加一些间距

    //     // 匹配块级公式（$$ ... $$）
    //     if (paragraph.startsWith("$$") && paragraph.endsWith("$$")) {
    //       const blockFormula = paragraph.slice(2, -2).trim();
    //       const blockMathElement = <BlockMath math={blockFormula} />;
    //       const blockWrapper = document.createElement("div");
    //       const root = createRoot(blockWrapper);
    //       root.render(blockMathElement);
    //       paragraphElement.appendChild(blockWrapper);
    //       return paragraphElement.innerHTML;
    //     } else {
    //       // 处理普通文本和行内公式
    //       const parts = paragraph.split(/(\$.*?\$)/); // 按照 $...$ 分割
    //       console.log("parts", parts);
    //       return parts.map((part: string) => {
    //         if (part.startsWith("$") && part.endsWith("$")) {
    //           let inlineFormula = part.slice(1, -1).trim();
    //           const html = katex.renderToString(inlineFormula, {
    //             displayMode: false, // 块级公式
    //             throwOnError: false
    //           });
    //           return html
    //         } else {
    //           return part;
    //         }
    //       }).join("");
    //     }

    //     // if (containerRef.current) {
    //     //   containerRef.current.appendChild(paragraphElement);
    //     // }
    //   }).join("\n");
    // };

    const formulasHtml = renderFormulas(htmlContent);
    // const dollerHTML = handleDollerStr(formulasHtml);

    const newFormulasHtml = addHostToImgSrc(formulasHtml, OSS_HOST);

    // 3. 更新 DOM
    containerRef.current.innerHTML = newFormulasHtml;
  }, [htmlContent]);

  useEffect(() => {
    const handleImageError = async (event: Event) => {
      const imgElement = event.target as HTMLImageElement;
      const src = imgElement.src;
      if (src.includes('data:image/')) {
        return;
      }
      const originSrc = imgElement.dataset.originSrc;
      const fileExt = originSrc?.split('.').at(-1) || 'png';
      console.log('originSrc', originSrc, fileExt);
      try {
        // 调用后端API获取新的图片地址
        const newSrc = await fetchQuestionResource({
          questionId: questionId || '',
          resourceFileName: originSrc?.split('/').at(-1) || '',
        }).then((res) => {
          console.log('res', res);
          return res.data.resourceData;
        });

        // const newSrc = templateImage;

        if (newSrc) {
          // 替换为新的图片地址
          imgElement.src = 'data:image/' + fileExt + ';base64,' + newSrc;
        } else {
          // 如果没有获取到新地址，可以设置一个默认的占位图
          imgElement.src = placeholderImage;
        }
      } catch (error) {
        console.error('获取备用图片失败:', error);
        imgElement.src = placeholderImage;
      }
    };
    const errorHandler = (event: Event) => handleImageError(event);
    if (containerRef.current) {
      const images = containerRef.current.querySelectorAll('img');
      Array.from(images).forEach((image) => {
        image.addEventListener('error', errorHandler);
      });
    }

    return () => {
      if (containerRef.current) {
        const images = containerRef.current.querySelectorAll('img');
        Array.from(images).forEach((image) => {
          image.removeEventListener('error', errorHandler);
        });
      }
    };
  }, [containerRef.current]);

  // 4. 初始渲染（此时公式尚未处理）
  return <div style={{ whiteSpace: 'pre-wrap', lineHeight: '2.5' }} ref={containerRef} />;
}

const RichTextWithMath = ({
  htmlContent,
  questionId,
}: {
  htmlContent: string;
  questionId?: string;
}) => {
  const { styles } = useStyles();
  const formatMathFieldHtml = useMemo(() => {
    // 将 <math-field> 内容转换为静态 HTML
    const html = htmlContent.replace(/<math-field[^>]*>([^<]*)<\/math-field>/g, (match, latex) => {
      // return convertLatexToMarkup(latex.trim());
      return `\\(${latex.trim()}\\)`;
    });
    return html;
  }, [htmlContent]);

  return (
    <MathJaxContext>
      <div className={styles.richTextStyle}>
        {/* 使用 MathJax 包裹整个内容 */}
        <MathJax>
          <RichTextWithMath2 htmlContent={formatMathFieldHtml} questionId={questionId} />
        </MathJax>
      </div>
    </MathJaxContext>
  );
};

export default RichTextWithMath;
