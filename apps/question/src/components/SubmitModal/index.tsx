import { Modal } from 'antd';

interface SubmitModalProps {
  title: string;
  content: string;
  onOk: () => void;
  onCancel?: () => void;
  okText?: string;
  cancelText?: string;
}

export const showSubmitModal = ({
  title,
  content,
  onOk,
  onCancel,
  okText = '确认',
  cancelText = '取消',
}: SubmitModalProps) => {
  return Modal.confirm({
    title,
    content,
    okText,
    cancelText,
    onOk,
    onCancel,
  });
};
