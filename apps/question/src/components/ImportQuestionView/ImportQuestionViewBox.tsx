import { cn } from '@repo/ui/lib/utils';
import { Flex, Typography } from 'antd';
import type React from 'react';
const { Text } = Typography;

interface ImportQuestionViewBoxProps {
  children: React.ReactNode;
  title: React.ReactNode;
  titleIconColor?: string;
  className?: string;
}

const ImportQuestionViewBox: React.FC<ImportQuestionViewBoxProps> = ({
  children,
  title,
  titleIconColor = '',
  className = '',
}) => {
  return (
    <Flex vertical gap={16} className={cn('bg-gray-100 p-4 rounded-md', className)}>
      <Flex align="center" justify="start" gap={4}>
        <div className={cn('w-1 h-4 rounded-sm bg-blue-500', titleIconColor)}></div>
        <Text strong className="text-lg">
          {title}
        </Text>
      </Flex>

      <div className="ml-6">{children}</div>
    </Flex>
  );
};

export default ImportQuestionViewBox;
