import { Space, Typography } from 'antd';
import type React from 'react';
import ExtractQuestionAnswer from '../QuestionItem/ExtractQuestionAnswer';
import QuestionAnswerAnalysis from '../QuestionItem/QuestionAnswerAnalysis';
import QuestionInfo from '../QuestionItem/QuestionInfo';
import ImportQuestionViewBox from './ImportQuestionViewBox';

const { Paragraph } = Typography;
interface ImportQuestionViewProps {
  question: API.QuestionItemType;
}

const ImportQuestionView: React.FC<ImportQuestionViewProps> = ({ question }) => {
  if (!question) {
    return null;
  }

  const titleRender = () => {
    return (
      <>
        <Space>
          题目:
          <Paragraph type="secondary" copyable className="!m-0 !text-xs">
            {question.questionId}
          </Paragraph>
        </Space>
      </>
    );
  };

  return (
    <Space direction="vertical" size="middle" style={{ width: '100%', overflow: 'hidden' }}>
      <ImportQuestionViewBox title={titleRender()} titleIconColor="bg-blue-500">
        <QuestionInfo
          question={question}
          showQuestionNo={false}
          showQuestionMark={false}
        ></QuestionInfo>
      </ImportQuestionViewBox>

      <ImportQuestionViewBox title="答案" titleIconColor="bg-green-500">
        {(question.extractQuestionAnswers || []).map((answer, idx) => (
          <ExtractQuestionAnswer key={idx} questionAnswer={answer} />
        ))}
      </ImportQuestionViewBox>
      <ImportQuestionViewBox title="解析" titleIconColor="bg-orange-500">
        <QuestionAnswerAnalysis
          questionExplanation={question.questionExplanation}
          questionId={question.questionId}
        />
      </ImportQuestionViewBox>
    </Space>
  );
};

export default ImportQuestionView;
