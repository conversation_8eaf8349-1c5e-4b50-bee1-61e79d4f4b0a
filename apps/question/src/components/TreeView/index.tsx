import { useFetchTreeDetail } from '@/services/api';
import { getTreeDataByFilter, getTreeKeysToLevel, getTreeNodeObjects } from '@/utils/tree';
import { SearchOutlined } from '@ant-design/icons';
import { Empty, Flex, Grid, Input, Tree, Typography } from 'antd';
import { DataNode } from 'antd/es/tree';
import { cloneDeep } from 'lodash';
import React, { Key, useEffect, useMemo, useRef, useState } from 'react';
const { Text } = Typography;

export interface TreeViewProps {
  onChange: (value: number, node: API.Common.BaseTreeNodeAntdType) => void;
  isBaseTree?: boolean;
  treeId?: number;
  // 过滤条件
  treeNodeFilter?: API.Common.TreeNodeFilterType;
  // 只过滤叶子节点
  isOnlyFilterLeafNode?: boolean;
  // 只选择叶子节点
  isOnlyLeafNodeSelectable?: boolean;
  // 是否显示上架状态
  showShelfStatus?: boolean;
  // 是否显示搜索框
  showSearch?: boolean;
  // 是否显示根节点
  showRootNode?: boolean;
  // 场景分类
  sceneCategory?: number;
  defaultSelectedFirstLeafNode?: boolean;
  defaultExpandedLayers?: number;
  treeHeight?: string;
  // 默认选中节点id
  defaultSelectedTreeNodeId?: number | undefined;
}

const TreeView: React.FC<TreeViewProps> = ({
  treeId,
  onChange,
  isBaseTree = true,
  treeNodeFilter = undefined,
  isOnlyFilterLeafNode = false,
  isOnlyLeafNodeSelectable = false,
  showShelfStatus = false,
  showSearch = true,
  showRootNode = true,
  sceneCategory = 0,
  defaultSelectedFirstLeafNode = false,
  defaultExpandedLayers = 2,
  treeHeight = '400px',
  defaultSelectedTreeNodeId = undefined,
}: TreeViewProps) => {
  const screens = Grid.useBreakpoint();
  // 树选择
  const [selectedTreeKey, setSelectedTreeKey] = useState<Key>();
  // 展开的节点
  const [expandedKeys, setExpandedKeys] = useState<Key[]>([]);
  const [searchKeyword, setSearchKeyword] = useState<string>('');
  const treeRef = useRef(null);
  const {
    data: treeDetailData,
    run: fetchTreeDetailRun,
    mutate: mutateTreeDetail,
  } = useFetchTreeDetail(isBaseTree, treeId ?? 0, sceneCategory, showShelfStatus);

  useEffect(() => {
    if (treeId) {
      setSelectedTreeKey(undefined);
      setExpandedKeys([]); // 重置展开状态
      if (treeDetailData) {
        treeDetailData.treeData = [];
      }
      fetchTreeDetailRun();
      return;
    }
    if (treeDetailData?.treeData) {
      const detailData = { ...treeDetailData, treeData: [] };
      mutateTreeDetail(detailData);
    }
  }, [treeId]);

  useEffect(() => {
    if (defaultSelectedTreeNodeId) {
      setSelectedTreeKey(defaultSelectedTreeNodeId.toString());
    }
  }, [defaultSelectedTreeNodeId]);

  const treeDetailInfo = useMemo(() => {
    if (treeDetailData?.treeData && treeDetailData.treeData.length > 0) {
      let treeData: API.Common.BaseTreeNodeAntdType[] = cloneDeep(treeDetailData.treeData);
      const rootNode = treeData[0];
      // 是否显示根节点
      if (!showRootNode) {
        treeData = treeData[0].children ?? [];
      }
      if (treeNodeFilter) {
        treeData = getTreeDataByFilter(
          treeData,
          treeNodeFilter,
          isOnlyFilterLeafNode,
          rootNode.key,
        );
      }

      if (searchKeyword) {
        treeData = getTreeDataByFilter(
          treeData,
          { key: 'title', value: searchKeyword, isEqual: false },
          false,
          rootNode.key,
        );
      }
      const { parentNodeList, treeDataLeafList, treeDataList, treeDataMap } =
        getTreeNodeObjects(treeData);
      parentNodeList.forEach((node) => {
        node.selectable = !isOnlyLeafNodeSelectable;
      });
      let initialExpandedKeys = getTreeKeysToLevel(treeData, defaultExpandedLayers);
      // 处理第一次选中节点
      let firstSelectedNode = undefined;
      if (searchKeyword) {
        initialExpandedKeys = treeDataList.map((node) => node.key);
      }

      const currentSelectedTreeNodeKey = selectedTreeKey
        ? selectedTreeKey
        : defaultSelectedTreeNodeId
          ? defaultSelectedTreeNodeId.toString()
          : undefined;
      // 如果选中节点id存在，则选中该节点
      if (currentSelectedTreeNodeKey && treeDataMap[currentSelectedTreeNodeKey as number]) {
        const parentsKey = treeDataMap[currentSelectedTreeNodeKey as number].parentsKey ?? [];
        initialExpandedKeys = [...initialExpandedKeys, ...parentsKey];
        firstSelectedNode = treeDataMap[currentSelectedTreeNodeKey as number];
      } else {
        // 并且有叶子节点，则选中第一个叶子节点
        if (defaultSelectedFirstLeafNode && treeDataLeafList.length > 0) {
          firstSelectedNode = treeDataLeafList[0];
        } else {
          firstSelectedNode = rootNode;
        }
      }

      // 如果当前没有选中节点
      // if (!currentSelectedTreeNodeKey) {
      //     // 并且有叶子节点，则选中第一个叶子节点
      //     if (defaultSelectedFirstLeafNode && treeDataLeafList.length > 0) {
      //         firstSelectedNode = treeDataLeafList[0];
      //     } else {
      //         firstSelectedNode = rootNode;
      //     }
      // }

      return {
        treeData,
        initialExpandedKeys,
        firstSelectedNode,
      };
    }
    return {
      treeData: [],
      initialExpandedKeys: [],
      firstSelectedNode: undefined,
    };
  }, [treeDetailData, treeNodeFilter, selectedTreeKey, searchKeyword]);

  // 初始化展开状态
  useEffect(() => {
    if (treeDetailInfo.initialExpandedKeys.length > 0) {
      setExpandedKeys(treeDetailInfo.initialExpandedKeys);
    }
  }, [treeDetailInfo.initialExpandedKeys]);

  // 处理第一个叶子节点的选择
  useEffect(() => {
    if (treeDetailInfo.firstSelectedNode) {
      const key = treeDetailInfo.firstSelectedNode.key;
      if (key !== selectedTreeKey) {
        setSelectedTreeKey(key);
        onChange?.(key as number, treeDetailInfo.firstSelectedNode);
      }
    }
  }, [treeDetailInfo.firstSelectedNode]);

  // 处理树节点选择
  const onSelect = (selectedKeys: Key[], info: any) => {
    const { node } = info;
    setSelectedTreeKey(selectedKeys[0]);
    onChange?.(selectedKeys[0] as number, node);
  };

  // 处理展开/收起
  const onExpand = (keys: Key[]) => {
    setExpandedKeys(keys);
  };

  const onSearchChange = (value: string) => {
    setSearchKeyword(value);
  };

  const titleRender = (node: DataNode & { shelfStatus?: number; shelfOnLeafNodeStat?: string }) => {
    const { title, children, shelfStatus, shelfOnLeafNodeStat } = node;
    return (
      <Flex align="center">
        <Text style={{ flex: 1 }}>{typeof title === 'function' ? title(node) : title}</Text>
        {showShelfStatus && (
          <div>
            {children && children.length > 0 ? (
              <Text type="secondary" style={{ fontSize: 10 }}>
                {shelfOnLeafNodeStat}
              </Text>
            ) : (
              <Text type="secondary" style={{ fontSize: 10 }}>
                {shelfStatus === 1 ? '已上架' : '未上架'}
              </Text>
            )}
          </div>
        )}
      </Flex>
    );
  };

  return (
    <Flex vertical gap={16}>
      {showSearch && (
        <Input
          placeholder="请输入关键字"
          allowClear
          value={searchKeyword}
          style={{ width: '100%' }}
          suffix={<SearchOutlined style={{ color: 'rgba(0,0,0,.45)' }} />}
          onChange={(e) => {
            onSearchChange(e.target.value);
          }}
        />
      )}
      <div
        className="tree-view-container"
        ref={treeRef}
        style={{ height: screens.xl ? treeHeight : '400px' }}
      >
        {treeDetailInfo.treeData.length > 0 ? (
          <Tree
            treeData={treeDetailInfo.treeData ?? []}
            expandedKeys={expandedKeys}
            onExpand={onExpand}
            showLine={{ showLeafIcon: false }}
            selectedKeys={selectedTreeKey ? [selectedTreeKey] : []}
            onSelect={onSelect}
            blockNode={!isBaseTree}
            titleRender={titleRender}
            rootStyle={{ height: '100%', overflow: 'auto' }}
          />
        ) : (
          <Empty />
        )}
      </div>
    </Flex>
  );
};

export default TreeView;
