import { PageContainer, useToken, type PageContainerProps } from '@ant-design/pro-components';
import { css, cx } from '@emotion/css';
import { Modal, ModalProps } from 'antd';
import { FC, memo } from 'react';

type PageContainerModalProps = PageContainerProps & { modalProps: ModalProps };

/**
 * PageContainer Modal
 */
const PageContainerModal: FC<PageContainerModalProps> = ({ modalProps, ...props }) => {
  const { token } = useToken();

  return (
    <Modal
      {...modalProps}
      title={false}
      getContainer={false}
      footer={null}
      closeIcon={false}
      rootClassName={style}
      width={'100%'}
      style={{
        backgroundColor: token.colorBgLayout,
      }}
    >
      <PageContainer
        {...props}
        style={{
          ...props.style,
          backgroundColor: token.colorBgLayout,
        }}
        className={cx(props.className, style)}
      />
    </Modal>
  );
};

export default memo(PageContainerModal);

const style = css`
  .ant-modal-mask {
    display: none;
  }

  .ant-modal-wrap {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 10;
    width: 100% !important;
    height: 100% !important;

    .ant-modal {
      top: unset;
      width: 100% !important;
      height: 100% !important;
      padding-bottom: unset;

      > div,
      .ant-modal-content {
        height: 100%;
        padding: unset;
        background-color: unset;
        border-radius: unset;
      }

      .ant-modal-body {
        height: 100%;
      }
    }
  }
  /* position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%; */
`;
