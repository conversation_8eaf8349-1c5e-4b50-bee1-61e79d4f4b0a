import { EditOutlined } from '@ant-design/icons';
import { Button, Tabs, TabsProps } from 'antd';
import { FC, ReactNode, useCallback, useMemo } from 'react';
import useStyles from './index.style';

interface GroupTabsProps {
  activeKey: string | undefined;
  editable: boolean;
  addable: boolean;
  tabBarExtraContent?: ReactNode | { left?: ReactNode; right?: ReactNode };
  groups: API.ExerciseGroupTabItem[];
  itemRenderFn?: (item: API.ExerciseGroupTabItem) => ReactNode;
  onChange?: (key: string) => void;
  onEdit?: (key: string) => void;
  onAdd?: (
    targetKey: React.MouseEvent | React.KeyboardEvent | string,
    action: 'add' | 'remove',
  ) => void;
}

const GroupTabs: FC<GroupTabsProps> = ({
  editable,
  addable,
  tabBarExtraContent = null,
  onChange,
  onEdit,
  onAdd,
  activeKey,
  groups,
  itemRenderFn,
}) => {
  const { styles } = useStyles();

  // 自定义标签渲染
  const renderTabLabel = useCallback(
    (label: string, key: string) => {
      const isActive = activeKey === key;
      return (
        <div className={styles.tab}>
          <span>{label}</span>
          {isActive && editable && (
            <Button
              type="text"
              icon={<EditOutlined className={styles.activeEdit} />}
              size="small"
              style={{ marginLeft: 8 }}
              onClick={(e) => {
                e.stopPropagation();
                onEdit?.(key);
              }}
            />
          )}
        </div>
      );
    },
    [activeKey, editable],
  );

  // 转换为 Antd Tabs 需要的 items 格式
  const tabItems: TabsProps['items'] = useMemo(() => {
    return groups.map((item) => ({
      ...item,
      label: itemRenderFn ? itemRenderFn(item) : renderTabLabel(item.label, item.key),
    }));
  }, [groups, activeKey, editable]);

  return (
    <Tabs
      className={styles.customTabs}
      type="editable-card"
      onChange={onChange}
      activeKey={activeKey}
      onEdit={onAdd}
      items={tabItems}
      tabBarGutter={4}
      hideAdd={!addable}
      style={{
        padding: '0 24px',
      }}
      tabBarExtraContent={tabBarExtraContent}
    />
  );
};
export default GroupTabs;
