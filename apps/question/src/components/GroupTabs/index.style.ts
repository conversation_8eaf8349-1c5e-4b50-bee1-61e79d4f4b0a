import { createStyles } from 'antd-style';

const useStyles = createStyles(({ token, css }) => {
  const customTabs = css`
    .ant-tabs-nav {
      margin-bottom: 0;
      border-bottom: none;
    }
    .ant-tabs-tab {
      background-color: #fff !important;

      &:hover {
        color: ${token.colorPrimary};
      }

      &.ant-tabs-tab-active {
        background-color: ${token.colorPrimary} !important;
        border-bottom: none !important;

        .ant-tabs-tab-btn {
          color: #fff;
        }
      }
    }
  `;
  return {
    tab: {
      display: 'flex',
      alignItems: 'center',
    },
    activeEdit: {
      color: '#fff',
    },
    customTabs,
  };
});

export default useStyles;
