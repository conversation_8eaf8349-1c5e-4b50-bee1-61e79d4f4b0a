import { PageContainer, useToken, type PageContainerProps } from '@ant-design/pro-components';
import { css, cx } from '@emotion/css';
import { FC, memo } from 'react';

/**
 * PageContainer 覆盖层
 */
const PageContainerOverlay: FC<PageContainerProps> = (props) => {
  const { token } = useToken();

  return (
    <PageContainer
      {...props}
      style={{
        ...props.style,
        backgroundColor: token.colorBgLayout,
      }}
      className={cx(props.className, 'gil-page-container-overlay', style)}
    />
  );
};

export default memo(PageContainerOverlay);

const style = css`
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
`;
