import { TreeTypeEnum, TreeTypeOptions } from '@/utils/phaseSubject';
import { Radio } from 'antd';
import React from 'react';

interface TreeTypeProps {
  type?: TreeTypeEnum;
  onChange?: (value: TreeTypeEnum) => void;
}

const TreeType: React.FC<TreeTypeProps> = ({ type = TreeTypeEnum.BASE_TREE, onChange }) => {
  return (
    <Radio.Group
      value={type}
      onChange={(e) => onChange?.(e.target.value)}
      buttonStyle="solid"
      style={{ width: '100%' }}
    >
      {TreeTypeOptions.map((item) => (
        <Radio.Button
          key={item.value}
          value={item.value}
          style={{ width: '50%', textAlign: 'center' }}
        >
          {item.label}
        </Radio.Button>
      ))}
    </Radio.Group>
  );
};

export default TreeType;
