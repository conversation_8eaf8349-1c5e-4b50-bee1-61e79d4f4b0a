import { useFetchTreeList } from '@/services/api';
import { Cascader } from 'antd';
import React, { useEffect, useMemo, useState } from 'react';

export interface BizTreeCascaderProps {
  onChange?: (value: number[]) => void;
  selectedSubject?: number[];
  defaultTreeValue?: number[];
}

const BizTreeCascader: React.FC<BizTreeCascaderProps> = ({
  onChange,
  selectedSubject = [],
  defaultTreeValue,
}) => {
  // 基础树和章节树
  // 树列表下拉框状态
  const [treeListSelectValue, setTreeListSelectValue] = useState<number[]>();
  const { data: queryTreeListData, run: fetchTreeListRun } = useFetchTreeList(
    false,
    selectedSubject,
  );

  const treeData = useMemo(() => {
    const data = queryTreeListData?.formatList ?? [];
    return data;
  }, [queryTreeListData]);

  useEffect(() => {
    // 如果默认选中值存在，则设置选中值
    if (defaultTreeValue) {
      setTreeListSelectValue(defaultTreeValue);
    }
  }, [defaultTreeValue]);

  useEffect(() => {
    // 树列表下拉框清空
    setTreeListSelectValue(undefined);
    // 重新获取树列表
    fetchTreeListRun();
  }, [selectedSubject]);

  useEffect(() => {
    if (!treeData || treeData.length === 0) {
      onChange?.([]);
      return;
    }
    const firstItem = treeData[0];
    let value: number[] = [];

    const oldTreeListValue = treeListSelectValue
      ? [...treeListSelectValue]
      : defaultTreeValue
        ? [...defaultTreeValue]
        : [];

    // 检查选择的树是否是有效的
    if (oldTreeListValue && oldTreeListValue.length > 0) {
      const selectedItem = treeData.find((item) => item.value === oldTreeListValue[0]);
      if (selectedItem) {
        value = [selectedItem.value];
        if (
          selectedItem.children &&
          selectedItem.children.length > 0 &&
          oldTreeListValue.length > 1
        ) {
          const selectedItemChild = selectedItem.children.find(
            (item) => item.value === oldTreeListValue[1],
          );
          if (selectedItemChild) {
            value.push(selectedItemChild.value);
          } else {
            value = [];
          }
        }
      }
    }

    // 业务树
    if (value.length === 0 && firstItem) {
      if (firstItem.children && firstItem.children.length > 0) {
        value = [firstItem.value, firstItem.children[0].value];
      } else {
        value = [firstItem.value];
      }
    }

    setTreeListSelectValue(value);
    onChange?.(value);
  }, [treeData]);

  const onCascaderChange = (value: number[]) => {
    setTreeListSelectValue(value);
    onChange?.(value);
  };

  return (
    <>
      <Cascader
        options={treeData}
        onChange={onCascaderChange}
        placeholder="请选择章节树"
        style={{ width: '100%' }}
        allowClear={false}
        // popupClassName={'custom-biztree-cascader-dropdown'}
        // className={'cascader-treelist'}
        value={treeListSelectValue}
        // getPopupContainer={() => document.querySelector('.cascader-treelist') || document.body}
      />
    </>
  );
};

export default BizTreeCascader;
