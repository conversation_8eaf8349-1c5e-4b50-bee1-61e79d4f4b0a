import TreeListSelect from '@/components/TreeListSelect';
import TreeType from '@/components/TreeType';
import TreeView from '@/components/TreeView';
import { isBaseTree, TreeTypeEnum } from '@/utils/phaseSubject';
import { Card, Flex, Space } from 'antd';
import React, { useEffect, useMemo, useState } from 'react';
import BizTreeCascader from '../BizTreeCascader';

interface TreeNodeFilterProps {
  selectedSubject: number[];
  isOnlyFilterLeafNode?: boolean;
  onChange: (value: number, node: API.Common.BaseTreeNodeAntdType, isBaseTree: boolean) => void;
  onTreeListChange?: (value: number) => void;
  treeHeight?: string;
  queryTypeProp?: TreeTypeEnum;
  treeNodeIdProp?: number;
  defaultTreeValue?: number[];
}

const TreeNodeFilter: React.FC<TreeNodeFilterProps> = ({
  selectedSubject,
  onChange,
  isOnlyFilterLeafNode = true,
  treeHeight = '400px',
  queryTypeProp,
  treeNodeIdProp,
  defaultTreeValue,
}) => {
  // 查询类型
  const [queryType, setQueryType] = useState<TreeTypeEnum>(queryTypeProp ?? TreeTypeEnum.BASE_TREE);
  const [treeId, setTreeId] = useState<number>();
  const [treeNodeId, setTreeNodeId] = useState<number>(treeNodeIdProp ?? 0);

  const [isInit, setIsInit] = useState(true);
  const [cache, setCache] = useState({
    baseTree: {
      treeId: 0,
      nodeId: 0,
    },
    bizTree: {
      treeId: [] as number[],
      nodeId: 0,
    },
  });

  const currentBizTreeIds = useMemo(() => {
    if (cache.bizTree.treeId.length) {
      return cache.bizTree.treeId;
    }
    return defaultTreeValue;
  }, [cache.bizTree.treeId, defaultTreeValue]);

  useEffect(() => {
    if (isInit) {
      setIsInit(false);
      return;
    }
    if (isBaseTree(queryType)) {
      setTreeId(cache.baseTree.treeId);
      setTreeNodeId(cache.baseTree.nodeId);
    } else {
      setTreeId(cache.bizTree.treeId.at(-1));
      setTreeNodeId(cache.bizTree.nodeId);
    }
  }, [queryType]);

  const onTreeListSelectChange = (value: number | undefined) => {
    setTreeId(value);
    const { baseTree, bizTree } = cache;
    baseTree.treeId = value ?? 0;
    setCache({ baseTree, bizTree });
  };

  const onBizTreeCascaderChange = (value: number[]) => {
    setTreeId(value.at(-1));
    if (value.length) {
      const { baseTree, bizTree } = cache;
      bizTree.treeId = value;
      setCache({ baseTree, bizTree });
    }
  };

  const onTreeChange = (value: number, node: API.Common.BaseTreeNodeAntdType) => {
    onChange(value, node, isBaseTree(queryType));

    const { baseTree, bizTree } = cache;
    if (isBaseTree(queryType)) {
      baseTree.nodeId = node.key as number;
    } else {
      bizTree.nodeId = node.key as number;
    }
    setCache({ baseTree, bizTree });
  };
  const onQueryTypeChange = (value: TreeTypeEnum) => {
    setQueryType(value);
    setTreeId(undefined);
  };

  useEffect(() => {
    if (queryTypeProp) {
      setQueryType(queryTypeProp);
    }
    if (defaultTreeValue?.length) {
      setTreeId(defaultTreeValue.at(-1));
    }
  }, [queryTypeProp, defaultTreeValue]);

  return (
    <Flex vertical gap={16} style={{ flex: 1 }}>
      <TreeType type={queryType} onChange={onQueryTypeChange} />
      <Card size="small">
        <Space direction="vertical" size="middle" style={{ display: 'flex' }}>
          {isBaseTree(queryType) ? (
            <TreeListSelect
              onChange={onTreeListSelectChange}
              selectedSubject={selectedSubject}
              isBaseTree={true}
            />
          ) : (
            <BizTreeCascader
              selectedSubject={selectedSubject}
              onChange={onBizTreeCascaderChange}
              defaultTreeValue={currentBizTreeIds}
            />
          )}

          <TreeView
            treeId={treeId}
            onChange={onTreeChange}
            isBaseTree={isBaseTree(queryType)}
            isOnlyFilterLeafNode={true}
            isOnlyLeafNodeSelectable={isOnlyFilterLeafNode}
            showRootNode={false}
            defaultExpandedLayers={0}
            treeHeight={treeHeight}
            defaultSelectedTreeNodeId={treeNodeId}
          />
        </Space>
      </Card>
    </Flex>
  );
};

export default TreeNodeFilter;
