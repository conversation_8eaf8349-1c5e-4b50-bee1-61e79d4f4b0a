import { ArrowLeftOutlined } from '@ant-design/icons';
import { css } from '@emotion/css';
import { Card } from 'antd';
import React, { ReactNode } from 'react';

/**
 * 覆盖详情组件属性
 */
export interface OverlayDetailProps {
  /** 标题 */
  title: string;
  /** 副标题 */
  subTitle?: string;
  /** 返回按钮文本 */
  backText?: string;
  /** 返回按钮点击事件 */
  onBack: () => void;
  /** 子元素 */
  children: ReactNode;
}

// 样式定义
const style = css`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10;
  background-color: #fff;
  overflow: auto;

  /* 卡片样式 */
  .card {
    height: 100%;
    border-radius: 0;
    box-shadow: none;
  }

  /* 标题容器 */
  .title-container {
    display: flex;
    align-items: center;
    margin-bottom: 24px;
  }

  /* 返回按钮 */
  .back-button {
    display: flex;
    align-items: center;
    margin-right: 16px;
    color: #1890ff;
    cursor: pointer;

    .icon {
      margin-right: 8px;
    }
  }

  /* 标题 */
  .title {
    margin: 0;
    font-weight: 500;
    font-size: 20px;
  }

  /* 副标题 */
  .subtitle {
    margin-left: 8px;
    color: #999;
    font-weight: normal;
    font-size: 14px;
  }
`;

/**
 * 覆盖详情组件
 *
 * @param props - 组件属性
 * @returns 覆盖详情组件
 */
const OverlayDetail: React.FC<OverlayDetailProps> = ({
  title,
  subTitle,
  backText = '返回',
  onBack,
  children,
}) => {
  return (
    <div className={style}>
      <Card className="card">
        <div className="title-container">
          <div className="back-button" onClick={onBack}>
            <ArrowLeftOutlined className="icon" />
            <span>{backText}</span>
          </div>
          <h1 className="title">
            {title}
            {subTitle && <span className="subtitle">{subTitle}</span>}
          </h1>
        </div>
        {children}
      </Card>
    </div>
  );
};

export default OverlayDetail;
