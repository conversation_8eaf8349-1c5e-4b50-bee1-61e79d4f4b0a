import { SearchOutlined } from '@ant-design/icons';
import { Input } from 'antd';
import type React from 'react';
import { useState } from 'react';

interface QuestionSearchProps {
  onSearch: (keyword: string) => void;
  value?: string;
}

const QuestionSearch: React.FC<QuestionSearchProps> = ({ onSearch, value = '' }) => {
  const [searchKeyword, setSearchKeyword] = useState<string>('');

  // useEffect(() => {
  //     setSearchKeyword(value);
  // }, [value]);

  const onSearchChange = (value: string) => {
    setSearchKeyword(value);
    onSearch(value);
  };

  return (
    <Input
      placeholder="请输入题目关键词"
      style={{ maxWidth: 400 }}
      allowClear
      defaultValue={value}
      value={searchKeyword}
      suffix={<SearchOutlined style={{ color: 'rgba(0,0,0,.45)' }} />}
      onChange={(e) => {
        onSearchChange(e.target.value);
      }}
      onPressEnter={() => {
        onSearchChange(searchKeyword);
      }}
    />
  );
};

export default QuestionSearch;
