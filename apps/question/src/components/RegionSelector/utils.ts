import type { DataNode } from 'antd/es/tree';

// 类型定义
interface RegionNode {
  nameEn: string;
  nameZh: string;
  value: number;
  cityList?: RegionNode[];
  areaList?: RegionNode[];
}

interface RegionList {
  provinceList: RegionNode[];
}

// 缓存已计算的路径，避免重复计算
const pathCache = new Map<number, number[]>();

// 清理路径缓存
export const clearPathCache = (): void => {
  pathCache.clear();
};

// 获取缓存统计信息
export const getPathCacheStats = () => {
  return {
    size: pathCache.size,
    keys: Array.from(pathCache.keys()),
  };
};

// 将地区列表转换为树形结构
export const convertToTreeData = (regionList: RegionList | null | undefined): DataNode[] => {
  if (!regionList?.provinceList) return [];

  const processNode = (node: RegionNode, parentPath: number[] = []): DataNode => {
    const currentPath = [...parentPath, node.value];

    return {
      title: node.nameZh,
      key: node.value,
      value: node.value,
      pathValues: currentPath,
      children:
        node.cityList?.map((city) => processNode(city, currentPath)) ||
        node.areaList?.map((area) => processNode(area, currentPath)) ||
        undefined,
      isLeaf: !node.cityList?.length && !node.areaList?.length,
    } as any;
  };

  return regionList.provinceList.map((province) => processNode(province));
};

// 根据叶子节点值获取完整路径
export const getFullPathFromLeaf = (
  regionList: RegionList | null | undefined,
  leafValue: number,
): number[] => {
  // 参数验证
  if (!leafValue || !regionList?.provinceList) {
    return [];
  }

  // 检查缓存
  if (pathCache.has(leafValue)) {
    return pathCache.get(leafValue)!;
  }

  const findPath = (
    nodes: RegionNode[],
    targetValue: number,
    currentPath: number[] = [],
  ): number[] | null => {
    for (const node of nodes) {
      const newPath = [...currentPath, node.value];

      if (node.value === targetValue) {
        // 返回完整路径，不限制长度
        return newPath;
      }

      // 递归搜索子节点
      const childNodes = node.cityList || node.areaList || [];
      if (childNodes.length > 0) {
        const result = findPath(childNodes, targetValue, newPath);
        if (result) return result;
      }
    }
    return null;
  };

  const result = findPath(regionList.provinceList, leafValue);

  if (!result) {
    // 缓存空结果
    pathCache.set(leafValue, []);
    return [];
  }

  // 缓存结果
  pathCache.set(leafValue, result);
  return result;
};

// 根据完整路径获取叶子节点值
export const getLeafValueFromPath = (path: number[]): number | null => {
  if (!path?.length) return null;

  // 返回路径中的最后一个非0值
  for (let i = path.length - 1; i >= 0; i--) {
    if (path[i] !== 0) {
      return path[i];
    }
  }

  return null;
};

// 根据路径获取显示文本
export const getDisplayTextFromPath = (
  regionList: RegionList | null | undefined,
  path: number[],
): string => {
  if (!path?.length || !regionList?.provinceList) {
    return '';
  }

  const findNodeByPath = (
    nodes: RegionNode[],
    targetPath: number[],
    currentIndex: number = 0,
  ): RegionNode | null => {
    if (currentIndex >= targetPath.length) return null;

    const targetValue = targetPath[currentIndex];
    const node = nodes.find((n) => n.value === targetValue);

    if (!node) return null;

    if (currentIndex === targetPath.length - 1) {
      return node;
    }

    const childNodes = node.cityList || node.areaList || [];
    return findNodeByPath(childNodes, targetPath, currentIndex + 1);
  };

  const node = findNodeByPath(regionList.provinceList, path);
  return node ? node.nameZh : '';
};
