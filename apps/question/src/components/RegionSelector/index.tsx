import useRegionDict from '@/hooks/useRegionDict';
import { TreeSelect, TreeSelectProps } from 'antd';
import { useCallback, useMemo } from 'react';
import { convertToTreeData, getFullPathFromLeaf, getLeafValueFromPath } from './utils';

interface RegionSelectorProps extends Omit<TreeSelectProps, 'onChange' | 'value' | 'treeData'> {
  value?: number[];
  onChange?: (value: number[]) => void;
}

const RegionSelector: React.FC<RegionSelectorProps> = ({
  value = [],
  onChange,
  ...treeSelectProps
}) => {
  const regionList = useRegionDict();

  // 处理地区选择变化
  const handleRegionChange = useCallback(
    (selectedValue: any) => {
      // 处理清除或空值的情况
      if (!selectedValue || selectedValue === undefined || selectedValue === null) {
        onChange?.([]);
        return;
      }

      // 如果传入的是单个值，转换为完整路径
      if (typeof selectedValue === 'number') {
        const fullPath = getFullPathFromLeaf(regionList, selectedValue);
        onChange?.(fullPath);
        return;
      }

      // 如果传入的是数组，确保是完整路径
      if (Array.isArray(selectedValue)) {
        if (selectedValue.length === 0) {
          onChange?.([]);
          return;
        }

        // 对于数组，我们取最后一个值作为叶子节点，然后获取完整路径
        const lastValue = selectedValue[selectedValue.length - 1];
        const fullPath = getFullPathFromLeaf(regionList, lastValue);
        onChange?.(fullPath);
        return;
      }

      // 其他情况，清空选择
      onChange?.([]);
    },
    [onChange, regionList],
  );

  // 获取当前值对应的叶子节点值（用于TreeSelect显示）
  const currentLeafValue = useMemo(() => {
    if (!value?.length) {
      return undefined;
    }

    const leafValue = getLeafValueFromPath(value);
    return leafValue || undefined;
  }, [value]);

  // 缓存树形数据
  const treeData = useMemo(() => {
    return convertToTreeData(regionList);
  }, [regionList]);

  // 计算需要展开的节点key
  const expandedKeys = useMemo(() => {
    if (!value?.length || !regionList?.provinceList) {
      return [];
    }

    // 展开所有父级节点
    const keys: number[] = [];
    for (let i = 0; i < value.length - 1; i++) {
      if (value[i] !== 0) {
        keys.push(value[i]);
      }
    }

    return keys;
  }, [value, regionList]);

  return (
    <TreeSelect
      treeData={treeData}
      value={currentLeafValue}
      onChange={handleRegionChange}
      treeDefaultExpandedKeys={expandedKeys}
      {...treeSelectProps}
    />
  );
};

export default RegionSelector;
