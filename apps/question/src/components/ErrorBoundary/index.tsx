import { ReloadOutlined } from '@ant-design/icons';
import { Button, Result } from 'antd';
import { ErrorBoundary } from 'react-error-boundary';

interface ErrorFallbackProps {
  error: Error;
  resetErrorBoundary: () => void;
}

const ErrorFallbackComponent: React.FC<ErrorFallbackProps> = ({ error, resetErrorBoundary }) => {
  // 检测是否为 chunk 加载错误
  if (error?.message.includes('Loading chunk')) {
    return (
      <Result
        status="warning"
        title="应用需要更新"
        subTitle="新版本已经准备就绪，请刷新页面"
        extra={[
          <Button
            key="refresh"
            type="primary"
            icon={<ReloadOutlined />}
            onClick={() => window.location.reload()}
          >
            刷新页面
          </Button>,
        ]}
      />
    );
  }

  // 默认错误展示
  return (
    <Result
      status="error"
      title="发生错误"
      subTitle={error?.message || '抱歉，出现了一些问题'}
      extra={[
        <Button key="retry" type="primary" onClick={resetErrorBoundary}>
          重试
        </Button>,
      ]}
    />
  );
};

interface ErrorBoundaryWrapperProps {
  children: React.ReactNode;
}

const ErrorBoundaryWrapper: React.FC<ErrorBoundaryWrapperProps> = ({ children }) => {
  return <ErrorBoundary FallbackComponent={ErrorFallbackComponent}>{children}</ErrorBoundary>;
};

export default ErrorBoundaryWrapper;
