import { useEffect, useRef, useState } from 'react';

interface UsePollingOptions<T, P> {
  requestFn: (params: P) => Promise<T>;
  interval?: number;
  onData?: (data: T) => void;
}

export function usePolling<T = any, P = any>({
  requestFn,
  interval = 1000 * 30,
  onData,
}: UsePollingOptions<T, P>) {
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const paramsRef = useRef<P | null>(null); // 存储最近一次的参数
  const [data, setData] = useState<T | null>(null);
  const [isPolling, setIsPolling] = useState(false);

  const fetchAndUpdate = async () => {
    if (!paramsRef.current) return;
    try {
      const result = await requestFn(paramsRef.current);
      setData(result);
      onData?.(result);
    } catch (err) {
      stop();
      console.error('轮询失败:', err);
    }
  };

  const start = (params: P) => {
    if (intervalRef.current) return;
    paramsRef.current = params;
    setIsPolling(true);
    fetchAndUpdate();
    intervalRef.current = setInterval(fetchAndUpdate, interval);
  };

  const stop = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
      setIsPolling(false);
    }
  };

  useEffect(() => {
    return () => stop();
  }, []);

  return { data, isPolling, start, stop };
}
