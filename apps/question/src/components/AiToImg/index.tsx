import { fetchUploadPolicy2, noticUploadResult, uploadFileToOss } from '@/services/api';
import { ExpandOutlined } from '@ant-design/icons';
import { css } from '@emotion/css';
import { Button, message } from 'antd';
import { useEffect, useRef, useState } from 'react';

const style = css`
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  .labelWrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .imgArea {
    display: flex;
    flex: 1;
    flex-shrink: 0;
    height: 60%;
    margin-top: 6px;
    padding: 6px;
    background-color: white;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 6px;
    img {
      height: 100%;
    }
    :empty:before {
      color: #bfbfbf;
      content: attr(data-placeholder);
      pointer-events: none;
    }
  }
`;

const AiToImg = (props: any) => {
  // const { data, start } = usePolling({
  //   requestFn: fetchParseResult,
  //   interval: 5000,
  //   onData: (result) => {
  //     console.log('收到轮询结果:', result);
  //     // 你可以在这里触发其他副作用，比如发通知、更新状态等
  //   },
  // });

  const handleUpload = async (file: any) => {
    try {
      // step1: 获取oss上传信息
      // @ts-ignore
      const policyRes = await fetchUploadPolicy2({
        fileName: file.name,
        phase: props.phase, // 学科学段
        subject: props.subject,
        generateChannel: 2,
      });
      if (policyRes.code !== 0 || !policyRes.data) {
        message.error('获取上传签名失败');
        return;
      }
      const { paperId, formData, fileName } = policyRes.data;
      // step2: 上传文件到oss
      const result = await uploadFileToOss(fileName, formData.policyToken, file);

      if (result.code !== 0) {
        message.error('上传失败');
      }
      const uploadStatus = result.code === 0 ? 1 : 0;
      // step3: 通知后端上传的结果
      const noticeRes = await noticUploadResult({
        paperId,
        result: {
          uploadStatus,
        },
      });
      if (noticeRes.code !== 0) {
        message.error('通知服务端失败');
        return;
      }
      // step4: 如果上传成功，则提示成功
      if (uploadStatus === 1) {
        message.success('上传成功');
        if (props.onLoadend) {
          props.onLoadend(paperId);
        }

        // start(paperId);
      }
    } catch (error) {
      message.error('上传流程异常');
      console.log('上传流程异常： ', error);
    } finally {
    }
  };

  const getFileFromClipBoard = (item: any): any => {
    return new Promise((resolve, reject) => {
      const isImg = item.type.indexOf('image') > -1;

      if (!isImg) {
        message.error('请仅粘贴图片');
        reject('不是图片');
      }
      const file: any = item.getAsFile();
      const reader = new FileReader();
      reader.onloadend = () => {
        resolve({
          base64String: reader.result,
          file,
        });
      };
      reader.readAsDataURL(file); // 读取文件为 Base64
    });
  };

  const divRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const el = divRef.current;
    if (!el) return;

    // const handleKey = (e: KeyboardEvent) => e.preventDefault();
    // const handlePaste = (e: ClipboardEvent) => e.preventDefault();
    // const handleDrop = (e: DragEvent) => e.preventDefault();
    // const handleCut = (e: ClipboardEvent) => e.preventDefault();
    // const handleComposition = (e: CompositionEvent) => e.preventDefault();

    // el.addEventListener('keydown', handleKey);
    // el.addEventListener('compositionstart', handleComposition);
    // el.addEventListener('compositionupdate', handleComposition);
    // el.addEventListener('compositionend', handleComposition);
    // el.addEventListener('paste', handlePaste);
    // el.addEventListener('drop', handleDrop);
    // el.addEventListener('cut', handleCut);

    return () => {
      // el.removeEventListener('keydown', handleKey);
      // el.removeEventListener('compositionstart', handleComposition);
      // el.removeEventListener('compositionupdate', handleComposition);
      // el.removeEventListener('compositionend', handleComposition);
      // el.removeEventListener('paste', handlePaste);
      // el.removeEventListener('drop', handleDrop);
      // el.removeEventListener('cut', handleCut);
    };
  }, []);

  const [fileInfo, setFileInfo] = useState<any>({});
  const [content, setContent] = useState('');
  const onPaste = async (e: React.ClipboardEvent<HTMLDivElement>) => {
    const [item] = e.clipboardData.items;
    const { base64String, file } = await getFileFromClipBoard(item);
    setFileInfo({ base64String, file });
    setContent(`<img src="${base64String}" alt="image" />`);
  };

  const onStartAi = () => {
    if (!fileInfo.file) {
      message.error('请先粘贴图片');
      return;
    }

    props.onSubmit?.(() => {
      handleUpload(fileInfo.file);
    });
  };

  return (
    <div className={style}>
      <div className="labelWrapper">
        <span>智能识别</span>
        <Button color="primary" variant="outlined" icon={<ExpandOutlined />} onClick={onStartAi}>
          开始识别
        </Button>
      </div>

      <div
        ref={divRef}
        className="imgArea"
        contentEditable={true}
        onPaste={onPaste}
        dangerouslySetInnerHTML={{ __html: content }}
        data-placeholder="粘贴完整题目（图片），自动拆分题干、答案和解析"
      ></div>
    </div>
  );
};

export default AiToImg;
