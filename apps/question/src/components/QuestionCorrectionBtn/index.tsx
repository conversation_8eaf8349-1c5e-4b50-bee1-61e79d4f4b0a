/**
 * 题目纠错按钮
 */
import usePermission from '@/hooks/usePermission';
import CreateQuestion from '@/pages/source-manage/question-create/CreateQuestionModal';
import { EditOutlined } from '@ant-design/icons';
import { Button } from 'antd';
import { memo, useState } from 'react';

const QuestionCorrectionBtn = (props: any) => {
  const { question, refresh } = props;
  const { checkActionPermission } = usePermission();
  const [open, setOpen] = useState<boolean>(false);
  return (
    <>
      {checkActionPermission('question-correction') && (
        <Button
          // size="small"
          color="default"
          variant="text"
          icon={<EditOutlined />}
          iconPosition="start"
          style={{
            fontSize: 14,
            fontWeight: 600,
          }}
          onClick={() => setOpen(true)}
        >
          题目纠错
        </Button>
      )}
      <CreateQuestion
        onSubmit={() => {
          refresh();
          setOpen(false);
        }}
        data={question}
        open={open}
        onBack={() => setOpen(false)}
        way="correction"
      />
    </>
  );
};
export default memo(QuestionCorrectionBtn);
