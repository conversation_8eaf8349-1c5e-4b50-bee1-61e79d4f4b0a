import cn from 'classnames';
import type React from 'react';
import { memo, useMemo } from 'react';

interface QuestionNoProps {
  questionNo: string;
  level?: number;
  style?: React.CSSProperties;
  className?: string;
}

const QuestionNo: React.FC<QuestionNoProps> = ({
  questionNo,
  level = 1,
  style = {},
  className,
}) => {
  const questionNoRender = useMemo(() => {
    if (level === 1) {
      return questionNo;
    }
    if (level === 2) {
      return `（${questionNo}）`;
    }
    if (level === 3) {
      return (
        <span className="border font-bold rounded-full border-[#000] w-6 h-6 flex items-center justify-center text-sm scale-[0.66] ml-5">
          {questionNo}
        </span>
      );
    }
    return questionNo;
  }, [level, questionNo]);

  return (
    <span className={cn('min-w-5 mr-1 m-0 flex items-center', className)} style={style}>
      {questionNoRender}
    </span>
  );
};

export default memo(QuestionNo);
