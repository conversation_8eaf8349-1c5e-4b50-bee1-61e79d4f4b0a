import type React from 'react';
// import QuestionOption from './QuestionOption';
import { Flex, Typography } from 'antd';
import QuestionOptionResponsiveLayout from './QuestionOptionResponsiveLayout';
const { Text } = Typography;

interface QuestionOptionMatrixProps {
  questionId: string;
  options: API.QuestionContentOptionItem[][];
  style?: React.CSSProperties;
}

const QuestionOptionMatrix: React.FC<QuestionOptionMatrixProps> = ({
  questionId,
  options = [],
  style = {},
}) => {
  return (
    <div style={{ width: '100%', ...style }}>
      {options.map((option, idx) => (
        <Flex key={idx} align="baseline" gap={4}>
          <Text strong className="w-8 text-center">
            {idx + 1}.
          </Text>
          <QuestionOptionResponsiveLayout
            key={idx}
            questionId={questionId}
            options={option}
            ossHost={OSS_HOST}
          />
        </Flex>
      ))}
    </div>
  );
};

export default QuestionOptionMatrix;
