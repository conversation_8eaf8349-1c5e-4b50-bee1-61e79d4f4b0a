import RichTextView from '@repo/core/components/rich-text-view';
import { Flex } from 'antd';
import type React from 'react';
import { memo, useMemo } from 'react';

interface QuestionAnswerProps {
  questionAnswer: API.QuestionAnswerType;
  isMainQuestion?: boolean;
}

const QuestionAnswer: React.FC<QuestionAnswerProps> = memo(
  ({ questionAnswer, isMainQuestion = true }) => {
    const subQuestionAnswers = useMemo(() => {
      return Array.isArray(questionAnswer.subQuestionAnswerList)
        ? questionAnswer.subQuestionAnswerList
        : [];
    }, [questionAnswer]);

    const questionNo = useMemo(() => {
      // 主问题不显示题号
      if (isMainQuestion) {
        return '';
      }
      // 非主问题有子问题时，不显示题号
      if (subQuestionAnswers.length > 0) {
        return '';
      }
      // 非主问题没有子问题时，显示题号
      return questionAnswer.questionNo;
    }, [isMainQuestion, questionAnswer.questionNo, subQuestionAnswers]);

    const answerText = useMemo(() => {
      const { answerOptionList, questionAnswerMode } = questionAnswer;
      const keyMode = [1, 2, 4];
      const valMode = [3, 5];
      const isKeyMode = questionAnswerMode && keyMode.includes(questionAnswerMode);
      const isValMode = questionAnswerMode && valMode.includes(questionAnswerMode);

      if (answerOptionList && answerOptionList.length > 0) {
        return answerOptionList
          .map((item) => {
            if (isKeyMode) {
              return `${String(item.optionKey)}`.trim();
            }
            if (isValMode) {
              return `${String(item.optionVal)}`.trim();
            }
            return `${String(item.optionKey)} ${String(item.optionVal)}`.trim();
          })
          .join('   ');
      }
      return '';
    }, [questionAnswer]);

    return (
      <div style={{ flex: 1 }}>
        <Flex>
          {questionNo && (
            <div style={{ marginRight: 4, minWidth: 30, lineHeight: '35px' }}>{questionNo}:</div>
          )}
          <div className="question-answer-text" style={{ flex: 1 }}>
            <RichTextView
              htmlContent={answerText}
              questionId={questionAnswer.questionId}
              ossHost={OSS_HOST}
            />
          </div>
        </Flex>
        {subQuestionAnswers.length > 0 && (
          <div>
            {subQuestionAnswers.map((item: API.QuestionAnswerType) => {
              return (
                <QuestionAnswer
                  key={item.questionId}
                  questionAnswer={item}
                  isMainQuestion={false}
                />
              );
            })}
          </div>
        )}
      </div>
    );
  },
);

QuestionAnswer.displayName = 'QuestionAnswer';

export default QuestionAnswer;
