import RichTextView from '@repo/core/components/rich-text-view';
import { Typography } from 'antd';
import type React from 'react';
const { Paragraph } = Typography;

export interface QuestionStemProps {
  content: string;
  id: string;
}

const QuestionStem: React.FC<QuestionStemProps> = ({ content, id }) => {
  return (
    <Paragraph style={{ marginBottom: 0 }}>
      <div>
        <RichTextView htmlContent={content} questionId={id} ossHost={OSS_HOST} />
      </div>
    </Paragraph>
  );
};

export default QuestionStem;
