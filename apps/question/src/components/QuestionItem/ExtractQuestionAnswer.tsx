import RichTextView from '@repo/core/components/rich-text-view';
import { Flex, Typography } from 'antd';
import type React from 'react';
import { memo, useMemo } from 'react';
import QuestionNo from './QuestionNo';
const { Text } = Typography;

interface ExtractQuestionAnswerProps {
  questionAnswer: API.QuestionAnswerType;
}

const ExtractQuestionAnswer: React.FC<ExtractQuestionAnswerProps> = memo(({ questionAnswer }) => {
  const mutiAnswers = useMemo(() => {
    const { answerOptionMatrix } = questionAnswer;
    if (answerOptionMatrix && answerOptionMatrix.length > 1) {
      return answerOptionMatrix.map((item) => item.map((item) => item.optionKey || item.optionVal));
    }
    return [];
  }, [questionAnswer]);

  const singleAnswer = useMemo(() => {
    const { answerOptionMatrix } = questionAnswer;
    if (answerOptionMatrix && answerOptionMatrix.length === 1) {
      return answerOptionMatrix[0].map((item) => item.optionKey || item.optionVal);
    }
    return null;
  }, [questionAnswer]);

  return (
    <Flex style={{ flex: 1 }} align="baseline">
      {questionAnswer.questionNo && (
        <Flex align="center">
          {/* <Text>{questionAnswer.questionNo}. </Text> */}
          <QuestionNo questionNo={questionAnswer.questionNo} level={questionAnswer.level} />
          {!questionAnswer.isChild && <Text style={{ marginLeft: 4 }}>答案：</Text>}
        </Flex>
      )}
      {!questionAnswer.isChild && (
        <Flex style={{ flex: 1 }} wrap="wrap">
          {singleAnswer && (
            <Flex>
              {singleAnswer.map((item) => (
                <div key={item} className="mr-4">
                  <RichTextView
                    htmlContent={item}
                    questionId={questionAnswer.questionId}
                    ossHost={OSS_HOST}
                  />
                </div>
              ))}
            </Flex>
          )}
          {mutiAnswers.map((answer, idx) => (
            <Flex key={idx} align="baseline" className="mr-6">
              <Text className="mr-2 whitespace-nowrap">{idx + 1}.</Text>
              <Flex>
                {answer.map((item) => (
                  <div key={item} className="mr-4">
                    <RichTextView
                      htmlContent={item}
                      questionId={questionAnswer.questionId}
                      ossHost={OSS_HOST}
                    />
                  </div>
                ))}
              </Flex>
            </Flex>
          ))}
        </Flex>
      )}
    </Flex>
  );
});

ExtractQuestionAnswer.displayName = 'ExtractQuestionAnswer';

export default ExtractQuestionAnswer;
