import useEnumManagerMap from '@/hooks/useEnumManager';
import { questionDifficultColors } from '@/utils/constants';
import { Button, Collapse, Divider, Flex, Space, Tag, Typography } from 'antd';
import { createStyles } from 'antd-style';
import type React from 'react';
import { useState } from 'react';
// import QuestionAnswer from './QuestionAnswer';
import ExtractQuestionAnswer from './ExtractQuestionAnswer';
import QuestionAnswerAnalysis from './QuestionAnswerAnalysis';

const { Text, Paragraph } = Typography;

const useStyles = createStyles(({ token }) => ({
  analysisCollapse: {
    '&.ant-collapse': {
      border: 'none',
      backgroundColor: 'transparent',
    },
    '& .ant-collapse-item': {
      border: 'none',
    },
    '& .ant-collapse-content': {
      backgroundColor: 'transparent',
    },
    '& .ant-collapse-content-box': {
      padding: '4px 0 0',
    },
    '& .ant-collapse-item > .ant-collapse-content > .ant-collapse-content-box': {
      padding: '4px 0 0',
    },
    '& .ant-collapse-header': {
      display: 'none !important',
    },
  },
  analysisWrapper: {
    marginTop: token.marginXS,
  },
}));

export interface Option {
  key: string;
  content: React.ReactNode;
}

interface QuestionAnswerAnalysisDetailProps {
  questionTypeName: string;
  questionExplanation: string;
  questionAnswer: API.QuestionAnswerType;
  questionDifficult: number;
  questionId: string;
  footerExtraBtns: React.ReactNode;
  extractQuestionAnswers: API.QuestionAnswerType[];
}

const QuestionAnswerAnalysisDetail: React.FC<QuestionAnswerAnalysisDetailProps> = ({
  questionTypeName,
  questionExplanation,
  // questionAnswer,
  questionDifficult,
  questionId,
  footerExtraBtns,
  extractQuestionAnswers,
}) => {
  const [showAnalysis, setShowAnalysis] = useState(false);
  const { styles } = useStyles();
  const {
    questionDifficultList,
    // questionTypeList
  } = useEnumManagerMap();

  const toggleAnalysis = () => {
    setShowAnalysis((prev) => !prev);
  };

  const AnalysisButton = (showAnalysis: boolean) => {
    return (
      <Button
        color="default"
        variant={showAnalysis ? 'filled' : 'text'}
        style={{
          fontSize: 14,
          fontWeight: 600,
        }}
        onClick={toggleAnalysis}
      >
        答案解析
      </Button>
    );
  };

  const DifficultTag = (questionDifficult: number) => {
    const difficult =
      questionDifficult as API.EnumConstantData['questionDifficultList'][number]['value'];
    const label = questionDifficultList?.getLabelByValue(difficult);
    if (!label) {
      return null;
    }
    return <Tag color={questionDifficultColors[difficult]}>{label}</Tag>;
  };
  // const getQuestionTypeLabel = (type: number) => {
  //   const questionType = type as API.EnumConstantData['questionTypeList'][number]['value'];
  //   const label = questionTypeList?.getLabelByValue(questionType);
  //   if (!label) {
  //     return null;
  //   }
  //   return label;
  // };

  const AnalysisContent = () => {
    return (
      <div style={{ color: '#6b6666', paddingTop: 10 }}>
        <Space direction="vertical" size="small" style={{ width: '100%' }}>
          <Flex justify="start" align="center" gap={20}>
            <Space>
              <Text type="secondary" style={{ color: '#6b6666' }}>
                题目ID:{' '}
              </Text>
              <Paragraph type="secondary" copyable style={{ margin: 0 }}>
                {questionId}
              </Paragraph>
            </Space>
            <Text type="secondary" style={{ color: '#6b6666' }}>
              题型: {questionTypeName || ''}
            </Text>
          </Flex>
          <Flex justify="start" align="start" gap={20}>
            <span style={{ whiteSpace: 'nowrap', lineHeight: '2.5' }}>答案:</span>
            <div style={{ flex: 1 }}>
              {(extractQuestionAnswers || []).map((answer, idx) => (
                <ExtractQuestionAnswer key={idx} questionAnswer={answer} />
              ))}
              {/* <QuestionAnswer questionAnswer={questionAnswer} /> */}
            </div>
          </Flex>

          <Flex justify="start" align="start" gap={20}>
            <span style={{ whiteSpace: 'nowrap', lineHeight: '2.5' }}>解析:</span>
            <div style={{ flex: 1 }}>
              <QuestionAnswerAnalysis
                questionExplanation={questionExplanation}
                questionId={questionId}
              />
            </div>
          </Flex>
        </Space>
      </div>
    );
  };

  return (
    <div className={styles.analysisWrapper}>
      <Divider style={{ margin: '8px 0' }} />
      <div style={{ padding: '0px 10px 0px 18px', color: '#ccc' }}>
        <Flex align="center">
          <div style={{ flex: 1 }}>{DifficultTag(questionDifficult)}</div>
          {footerExtraBtns}
          {AnalysisButton(showAnalysis)}
        </Flex>
        <Collapse
          activeKey={showAnalysis ? ['1'] : []}
          ghost
          className={styles.analysisCollapse}
          items={[
            {
              key: '1',
              showArrow: false,
              label: null,
              children: <AnalysisContent />,
            },
          ]}
        />
      </div>
    </div>
  );
};

export default QuestionAnswerAnalysisDetail;
