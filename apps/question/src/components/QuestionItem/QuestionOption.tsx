import RichTextView from '@repo/core/components/rich-text-view';
import { Col, Row, Space, Typography } from 'antd';
import type React from 'react';

const { Text } = Typography;

interface QuestionOptionProps {
  questionId: string;
  options: API.QuestionContentOptionItem[];
  style?: React.CSSProperties;
}

const QuestionOption: React.FC<QuestionOptionProps> = ({
  questionId,
  options = [],
  style = {},
}) => {
  return (
    <div style={{ width: '100%', ...style }}>
      <Row gutter={[16, 16]}>
        {options.map((option) => (
          <Col key={option.optionKey} span={24}>
            <Space align="start" style={{ width: '100%', display: 'flex' }}>
              <Text strong style={{ flexShrink: 0, whiteSpace: 'nowrap', lineHeight: '2.5' }}>
                {option.optionKey}.
              </Text>
              <div style={{ flex: 1, whiteSpace: 'normal', wordBreak: 'break-word' }}>
                <RichTextView
                  htmlContent={option.optionVal}
                  questionId={questionId}
                  ossHost={OSS_HOST}
                />
              </div>
            </Space>
          </Col>
        ))}
      </Row>
    </div>
  );
};

export default QuestionOption;
