import RichTextView from '@repo/core/components/rich-text-view';
import type React from 'react';

interface QuestionAnswerAnalysisProps {
  questionExplanation: string;
  questionId: string;
}

const QuestionAnswerAnalysis: React.FC<QuestionAnswerAnalysisProps> = ({
  questionExplanation,
  questionId,
}) => {
  return (
    <div>
      <RichTextView htmlContent={questionExplanation} questionId={questionId} ossHost={OSS_HOST} />
    </div>
  );
};

export default QuestionAnswerAnalysis;
