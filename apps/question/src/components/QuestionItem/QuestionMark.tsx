import useEnumManagerMap from '@/hooks/useEnumManager';
import { Tag } from 'antd';
import type React from 'react';
import { useMemo } from 'react';

export interface QuestionMarkProps {
  questionYear: string | number;
  provinceCode: number;
  questionOriginName: string;
}

const QuestionMark: React.FC<QuestionMarkProps> = ({
  questionYear,
  provinceCode,
  questionOriginName,
}) => {
  const { provinceList } = useEnumManagerMap();

  const markText = useMemo(() => {
    const result = [];
    if (questionYear) {
      const year = String(questionYear).includes('年') ? questionYear : `${questionYear}年`;
      result.push(year);
    }
    if (provinceCode) {
      const province = provinceList?.getLabelByValue(provinceCode);
      if (province) {
        result.push(province);
      }
    }
    if (questionOriginName) {
      result.push(questionOriginName);
    }
    if (result.length === 0) {
      return null;
    }

    return result.join('•');
  }, [questionYear, provinceCode, questionOriginName]);

  return markText ? <Tag color="default">{markText}</Tag> : null;
};

export default QuestionMark;
