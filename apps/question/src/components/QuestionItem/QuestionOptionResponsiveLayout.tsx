'use client';

import katex from 'katex'; // 确保已安装并导入
import 'katex/dist/katex.min.css'; // 确保 KaTeX CSS 已导入
import { useCallback, useEffect, useLayoutEffect, useMemo, useRef, useState } from 'react';
// 导入你的真实组件、类型和常量
import { useDebounceFn } from 'ahooks';
// import RichTextWithMath from '../RichTextWithMath'; // 请确认路径
import RichTextView from '@repo/core/components/rich-text-view';

// --- 常量和类型定义 ---
const GAP_X_PIXELS = 16; // 选项网格水平间隙 (对应 Tailwind gap-x-4)
const OPTION_CONTENT_WIDTH_DELTA = 10; // 选项内容与选项序号之间的间隙
const INNER_FLEX_GAP_PIXELS = 8; // 选项内部标识和内容间隙 (对应 Tailwind gap-2)
const RESIZE_DEBOUNCE_MS = 0; // Resize 事件防抖延迟 (毫秒)

type OptionRefs = {
  outer: HTMLDivElement | null;
  keyEl: HTMLDivElement | null;
  contentEl: HTMLDivElement | null;
};

type QuestionOptionResponsiveLayoutProps = {
  options: API.QuestionContentOptionItem[];
  questionId: string;
  ossHost?: string;
  resourceApiHost?: string;
};

// --- 固有宽度计算缓存 ---
const intrinsicWidthCache = new Map<string, number>();

/**
 * 带缓存的宽度计算函数 (使用临时 DOM 元素)
 */
const getHtmlContentIntrinsicWidth = (htmlContent: string): number => {
  if (typeof document === 'undefined' || !htmlContent) return 0;
  if (intrinsicWidthCache.has(htmlContent)) {
    return intrinsicWidthCache.get(htmlContent)!;
  }
  let processedHtml = htmlContent;
  try {
    const inlineRegex = /\\\((.*?)\\\)/g;
    const dollarInlineRegex = /\$(.*?)\$/g;
    const blockRegex = /\\\[([\s\S]*?[^\\])\\\]/g;
    const dollarBlockRegex = /\$\$(.*?)\$\$/g;
    processedHtml = processedHtml.replace(inlineRegex, (_, formula) =>
      katex.renderToString(formula, { throwOnError: false, displayMode: false }),
    );
    processedHtml = processedHtml.replace(dollarInlineRegex, (_, formula) =>
      katex.renderToString(formula, { throwOnError: false, displayMode: false }),
    );
    processedHtml = processedHtml.replace(blockRegex, (_, formula) =>
      katex.renderToString(formula, { throwOnError: false, displayMode: true }),
    );
    processedHtml = processedHtml.replace(dollarBlockRegex, (_, formula) =>
      katex.renderToString(formula, { throwOnError: false, displayMode: true }),
    );
    // processedHtml = addHostToImgSrc(processedHtml, DEVELOPMENT_OSS_HOST); // 如需处理图片路径
  } catch (e) {
    console.error('测量宽度时 KaTeX 渲染失败:', e);
    return 0;
  }

  const tempElement = document.createElement('div');
  // 关键样式，确保与实际渲染环境一致
  tempElement.style.position = 'absolute';
  tempElement.style.left = '-9999px';
  tempElement.style.top = '-9999px';
  tempElement.style.visibility = 'hidden';
  tempElement.style.pointerEvents = 'none';
  tempElement.style.display = 'inline-block';
  tempElement.style.width = 'auto';
  tempElement.style.whiteSpace = 'pre-wrap';
  tempElement.style.lineHeight = '2.5';
  tempElement.style.fontFamily = '"Alibaba PuHuiTi 3.0", sans-serif'; // 请根据项目调整字体
  tempElement.style.fontSize = '16px'; // 请根据项目调整字号 (text-base)

  tempElement.innerHTML = processedHtml;
  document.body.appendChild(tempElement);
  const measuredWidth = tempElement.offsetWidth;
  document.body.removeChild(tempElement);
  intrinsicWidthCache.set(htmlContent, measuredWidth);
  return measuredWidth;
};

// ========================================================================
// 核心 QuestionDisplay 组件 (高性能 + 防闪烁 + Tailwind)
// ========================================================================
export default function QuestionOptionResponsiveLayout({
  options,
  ossHost = '',
  resourceApiHost = '',
  questionId,
}: QuestionOptionResponsiveLayoutProps) {
  // 提取选项数据
  const formatOptions = useMemo(() => (Array.isArray(options) ? options : []), [options]);

  // State 定义
  const [optionsLayout, setOptionsLayout] = useState('grid-cols-1'); // 布局状态
  const [isLayoutReady, setIsLayoutReady] = useState(false); // 可见性状态

  // Refs 定义
  const containerRef = useRef<HTMLDivElement>(null);
  const optionRefs = useRef<Map<string, OptionRefs>>(new Map());
  const calculationRafId = useRef<number | null>(null);

  // 计算单个选项宽度 (使用缓存)
  const getOptionIntrinsicWidth = useCallback(
    (key: string): number => {
      const refs = optionRefs.current.get(key);
      const keyElement = refs?.keyEl;
      const optionData = formatOptions.find((opt) => opt.optionKey === key);
      const originalHtmlContent = optionData?.optionVal;
      if (!keyElement || typeof originalHtmlContent === 'undefined') {
        console.warn(`选项 ${key} Ref 或内容缺失`);
        return 0;
      }
      const keyWidth = keyElement.offsetWidth;
      const contentWidth = getHtmlContentIntrinsicWidth(originalHtmlContent);
      const totalWidth = keyWidth + contentWidth + INNER_FLEX_GAP_PIXELS;
      return totalWidth;
    },
    [formatOptions],
  );

  // 核心布局计算逻辑 (最后设置布局和可见性)
  const calculateLayout = useCallback(() => {
    if (calculationRafId.current) {
      cancelAnimationFrame(calculationRafId.current);
      calculationRafId.current = null;
    }

    const containerElement = containerRef.current;
    const allRefsReady =
      formatOptions.length === 4 &&
      optionRefs.current.size === 4 &&
      Array.from(optionRefs.current.values()).every((refs) => refs.keyEl);
    let finalLayout = 'grid-cols-1'; // 默认最终布局

    if (!containerElement) {
      /* 容器不存在 */
    } else if (formatOptions.length !== 4 || !allRefsReady) {
      /* 非4选项或Ref未就绪 */
      if (formatOptions.length <= 3) finalLayout = 'grid-cols-1';
      else finalLayout = 'grid-cols-1';
    } else {
      // 4选项且Ref就绪，执行计算
      const containerWidth = containerElement.offsetWidth;
      const optionWidths = formatOptions.map((opt) => getOptionIntrinsicWidth(opt.optionKey));
      if (optionWidths.some((w) => w <= 0)) {
        /* 测量失败 */ finalLayout = 'grid-cols-1';
      } else {
        // 测量成功，判断布局
        const maxOptionWidth = Math.max(...optionWidths);
        const optionTotalWidth =
          (maxOptionWidth + OPTION_CONTENT_WIDTH_DELTA) * formatOptions.length;
        const totalWidth4Cols = optionTotalWidth + (formatOptions.length - 1) * GAP_X_PIXELS;
        if (totalWidth4Cols <= containerWidth) {
          finalLayout = 'grid-cols-4';
        } else {
          const widthRow1 = optionWidths[0] + optionWidths[1] + GAP_X_PIXELS;
          const widthRow2 = optionWidths[2] + optionWidths[3] + GAP_X_PIXELS;
          if (widthRow1 <= containerWidth && widthRow2 <= containerWidth) {
            finalLayout = 'grid-cols-2';
          } else {
            finalLayout = 'grid-cols-1';
          }
        }
      }
    }
    // **在最后同时设置布局和可见性**
    setOptionsLayout(finalLayout);
    setIsLayoutReady(true);
  }, [formatOptions, getOptionIntrinsicWidth]);

  // Effect: 使用 useLayoutEffect 检查 Refs 并调度初始计算
  useLayoutEffect(() => {
    optionRefs.current.clear();
    setIsLayoutReady(false); // 初始设为不可见

    const checkRefsAndSchedule = () => {
      // 检查 Ref 是否就绪（这里可以简化，因为 calculateLayout 内部也会检查）
      // const allRefsReady = options.length === 4 && optionRefs.current.size === 4 && Array.from(optionRefs.current.values()).every(refs => refs.keyEl);
      // 总是调度计算，让 calculateLayout 自己判断
      if (calculationRafId.current) cancelAnimationFrame(calculationRafId.current);
      calculationRafId.current = requestAnimationFrame(calculateLayout);
    };
    checkRefsAndSchedule();

    return () => {
      // 清理函数
      if (calculationRafId.current) {
        cancelAnimationFrame(calculationRafId.current);
        calculationRafId.current = null;
      }
    };
  }, [formatOptions, calculateLayout]); // 依赖项

  // Effect: 使用 Debounce 处理 Resize
  const { run: debouncedCalculateLayout } = useDebounceFn(calculateLayout, {
    wait: RESIZE_DEBOUNCE_MS,
  });
  useEffect(() => {
    const containerElement = containerRef.current;
    if (!containerElement) return;
    const observer = new ResizeObserver(() => {
      debouncedCalculateLayout();
    });
    observer.observe(containerElement);
    return () => {
      observer.disconnect();
    };
  }, [debouncedCalculateLayout]); // 依赖项

  // Ref 设置函数
  const setOptionRefs = (key: string, part: keyof OptionRefs, element: HTMLDivElement | null) => {
    if (!optionRefs.current.has(key)) {
      optionRefs.current.set(key, { outer: null, keyEl: null, contentEl: null });
    }
    const refs = optionRefs.current.get(key);
    if (refs) {
      refs[part] = element;
    }
  };

  // 渲染 UI
  return useMemo(
    () => (
      <div className="question-content w-full max-w-full">
        {/* 渲染选项区域 */}
        {formatOptions && formatOptions.length > 0 && (
          <div
            ref={containerRef}
            // **修改点: 使用 Tailwind 类控制可见性与透明度**
            className={`invisible opacity-0
                    grid ${optionsLayout} gap-x-4 gap-y-2
                    transition-opacity duration-300 ease-in-out
                    ${isLayoutReady ? '!visible opacity-100' : 'invisible opacity-0'}
                `}
          >
            {formatOptions.map((option) => (
              // 每个选项容器 (使用基线对齐)
              <div
                key={option.optionKey}
                ref={(el) => setOptionRefs(option.optionKey, 'outer', el)}
                className="flex items-center gap-2 mb-1 " // 使用 items-baseline 对齐
              >
                {/* 选项序号 */}
                <div
                  ref={(el) => setOptionRefs(option.optionKey, 'keyEl', el)}
                  className="font-medium min-w-[20px] text-center" // 保持序号样式
                >
                  {option.optionKey}.
                </div>
                {/* 选项内容 */}
                <div
                  ref={(el) => setOptionRefs(option.optionKey, 'contentEl', el)}
                  className="flex-1 overflow-hidden" // 内容区域占据剩余空间
                >
                  <RichTextView
                    htmlContent={option.optionVal}
                    ossHost={ossHost}
                    resourceApiHost={resourceApiHost}
                    questionId={questionId}
                  />
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    ),
    [formatOptions, optionsLayout, isLayoutReady],
  ); // 依赖项包括 isLayoutReady
}
