import { Flex, Space } from 'antd';
import type React from 'react';
import { useMemo } from 'react';
import QuestionMark from './QuestionMark';
import QuestionNo from './QuestionNo';
// import QuestionOption from './QuestionOption';
import QuestionOptionMatrix from './QuestionOptionMatrix';
import QuestionOptionResponsiveLayout from './QuestionOptionResponsiveLayout';
import QuestionStem from './QuestionStem';

interface QuestionInfoProps {
  question: API.QuestionItemType;
  children?: React.ReactNode;
  isMainQuestion?: boolean;
  showQuestionNo?: boolean;
  showQuestionMark?: boolean;
}

const QuestionInfo: React.FC<QuestionInfoProps> = ({
  question,
  children,
  isMainQuestion = true,
  showQuestionNo = true,
  showQuestionMark = true,
}) => {
  const {
    questionNo,
    questionYear,
    questionContent,
    questionId,
    provinceCode,
    subQuestionList,
    questionAnswerMode,
  } = useMemo(() => question, [question]);
  const questionContentInfo: API.QuestionContentInfo = useMemo(() => {
    if (questionContent) {
      return questionContent;
    }
    return {
      questionOrder: 0,
      questionScore: 0,
      questionOriginName: '',
      questionStem: '',
      questionOptionList: [],
      questionOptionMatrix: [],
    };
  }, [questionContent]);

  const noQuestionMark = useMemo(() => {
    if (isMainQuestion) {
      return !questionYear && !provinceCode && !questionContentInfo.questionOriginName;
    }
    return true;
  }, [questionYear, provinceCode, questionContentInfo.questionOriginName, isMainQuestion]);

  const subQuestions = useMemo(() => {
    if (Array.isArray(subQuestionList)) {
      return subQuestionList;
    }
    return [];
  }, [subQuestionList]);

  const isMatrixOption = useMemo(() => {
    return (
      questionContentInfo.questionOptionMatrix &&
      questionContentInfo.questionOptionMatrix.length > 1
    );
  }, [questionContentInfo.questionOptionMatrix]);

  return (
    <div>
      <Flex justify="space-between" align="start">
        {showQuestionNo && (
          <QuestionNo
            questionNo={questionNo}
            level={question.level}
            style={{
              lineHeight: noQuestionMark ? '34px' : 1.5,
              height: noQuestionMark ? '34px' : '22px',
            }}
          />
        )}
        <Space direction="vertical" size="middle" className="flex-1 overflow-hidden">
          {isMainQuestion && showQuestionMark && (
            <QuestionMark
              questionYear={questionYear}
              provinceCode={provinceCode}
              questionOriginName={questionContentInfo.questionOriginName}
            />
          )}
          <QuestionStem content={questionContentInfo.questionStem} id={questionId} />
          {questionAnswerMode !== 4 && isMatrixOption && subQuestions.length === 0 && (
            <QuestionOptionMatrix
              questionId={questionId}
              options={questionContentInfo.questionOptionMatrix ?? []}
            />
          )}
          {questionAnswerMode !== 4 && !isMatrixOption && subQuestions.length === 0 && (
            <QuestionOptionResponsiveLayout
              questionId={questionId}
              options={questionContentInfo.questionOptionList}
              ossHost={OSS_HOST}
            />
          )}
        </Space>
        {isMainQuestion && children}
      </Flex>
      {subQuestions.map((item) => {
        return (
          <div key={item.questionId} className="ml-5">
            <QuestionInfo question={item} isMainQuestion={false} />
          </div>
        );
      })}
    </div>
  );
};

export default QuestionInfo;
