{"compilerOptions": {"jsx": "react-jsx", "target": "es5", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true}, "paths": {"@repo/core": ["../../../../../packages/core/src/*"], "@repo/lib": ["../../../../../packages/lib/src/*"], "@repo/ui": ["../../../../../packages/ui/src/*"]}, "include": ["src/**/*", "global.d.ts", "./**/*.d.ts", "./**/*.ts", "./**/*.tsx"]}