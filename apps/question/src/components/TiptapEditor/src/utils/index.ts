import { Feature } from './enum';

export const stripOuterNode = (html: string) => {
  const parser = new DOMParser();
  const doc = parser.parseFromString(html, 'text/html');
  const body = doc.body;
  if (body.children.length === 1) {
    return body.children[0].innerHTML;
  }
  return html;
};

export const calculateFeatures = (includeFeatures?: Feature[], excludeFeatures?: Feature[]) => {
  const defaultFeatures = [
    Feature.bold,
    Feature.italic,
    Feature.underline,
    Feature.alignLeft,
    Feature.alignCenter,
    Feature.alignRight,
    Feature.textColor,
    Feature.backgroundColor,
    Feature.formula,
    Feature.insertQs,
    Feature.lineSolid,
    Feature.lineDashed,
    Feature.imgupload,
    Feature.table,
    Feature.dot,
    Feature.wavy,
  ];

  if (includeFeatures) {
    return includeFeatures;
  }

  if (excludeFeatures) {
    return defaultFeatures.filter((feature) => !excludeFeatures.includes(feature));
  }

  return defaultFeatures;
};
// 将文本中的换行符转换为 HTML 段落
export const convertLineBreaksToHtml = (text: string): string => {
  if (!text) return '';

  // 将换行符分割成段落
  const paragraphs = text.split('\n').filter((p) => p.trim() !== '');

  // 如果没有段落，返回空字符串
  if (paragraphs.length === 0) return '';

  // 如果只有一个段落，直接返回
  if (paragraphs.length === 1) return paragraphs[0];

  // 将每个段落包装成 <p> 标签
  return paragraphs.map((p) => `<p>${p}</p>`).join('');
};

// 特殊处理旧的富文本内容中的一些数据
export const handleOldData = (content: any) => {
  if (!content) return '';

  // 检查是否已经包含 HTML 标签
  const hasHTMLTags = /<[^>]+>/.test(content);

  if (hasHTMLTags) {
    // 如果已经是 HTML，直接返回，不做任何处理
    return content;
  } else {
    // 如果是纯文本，处理换行符转换为段落
    return convertLineBreaksToHtml(content);
  }
};

// 生成 UUID
export function generateUUID() {
  return performance.now().toString(36).replace('.', '') + Math.random().toString(36).slice(2);
}

/**
 * 从 URL 中提取文件名（可选择是否包含扩展名）
 * @param {string} url - 文件的 URL 地址
 * @param {boolean} withExtension - 是否包含扩展名，默认 false
 * @returns {string|null} - 文件名，匹配不到则返回 null
 */
export const getFilename = (url: string, withExtension = false) => {
  if (typeof url !== 'string') return null;

  // 提取完整文件名（含扩展名）
  // eslint-disable-next-line no-useless-escape
  const match = url.match(/\/([^\/?#]+)(?=$|[?#])/);
  if (!match) return null;

  const filenameWithExt = match[1];

  if (withExtension) {
    return filenameWithExt;
  } else {
    const dotIndex = filenameWithExt.lastIndexOf('.');
    return dotIndex !== -1 ? filenameWithExt.substring(0, dotIndex) : filenameWithExt;
  }
};

/**
 * 获取文件链接的后缀名（正则表达式版）
 * @param {string} fileUrl 文件链接或文件名
 * @returns {string} 文件后缀名（小写），如果没有后缀则返回空字符串
 */
export const getFileExtension = (fileUrl: string) => {
  if (!fileUrl || typeof fileUrl !== 'string') return '';

  // 正则表达式匹配后缀名
  // 1. [^?#]* 匹配除了?和#以外的所有字符（处理查询参数和哈希）
  // 2. \. 匹配点号
  // 3. ([^?#/.]*) 匹配后缀名（不包含点号、斜杠、?和#）
  const match = fileUrl.match(/[^?#]*\.([^?#/.]*)(?:[?#].*)?$/i);

  return match ? match[1].toLowerCase() : '';
};
