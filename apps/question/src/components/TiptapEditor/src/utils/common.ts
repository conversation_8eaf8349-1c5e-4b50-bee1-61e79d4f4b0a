export const parseCssValueToNumber = (value: string | number) => {
  // 如果已经是数字类型且有效，直接返回
  if (typeof value === 'number' && !isNaN(value)) {
    return value;
  }

  // 如果不是字符串，返回 undefined
  if (typeof value !== 'string') {
    return undefined;
  }

  // 去除前后空格
  const trimmedValue = value.trim();

  // 空字符串返回 undefined
  if (trimmedValue === '') {
    return undefined;
  }

  // 检查是否是纯数字字符串
  if (/^-?\d*\.?\d+$/.test(trimmedValue)) {
    return parseFloat(trimmedValue);
  }

  // 检查是否是带单位的数字
  const numericPart = parseFloat(trimmedValue);
  if (!isNaN(numericPart)) {
    // 验证剩余部分是否是合法单位
    const unitPart = trimmedValue.slice(String(numericPart).length).trim();
    if (
      unitPart === '' ||
      [
        'px',
        '%',
        'em',
        'rem',
        'vh',
        'vw',
        'vmin',
        'vmax',
        'pt',
        'cm',
        'mm',
        'in',
        'pc',
        'ex',
        'ch',
        'deg',
        'rad',
        'grad',
        'turn',
        's',
        'ms',
      ].includes(unitPart)
    ) {
      return numericPart;
    }
  }

  // 其他情况返回 undefined
  return undefined;
};
