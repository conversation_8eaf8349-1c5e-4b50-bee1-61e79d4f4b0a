import { Editor, Extension } from '@tiptap/core';
import renderMathInElement from 'katex/contrib/auto-render';
import 'katex/contrib/mhchem/mhchem';
import 'katex/dist/katex.min.css';
import { Plugin, PluginKey, TextSelection } from 'prosemirror-state';
import { Decoration, DecorationSet } from 'prosemirror-view';
import emitter from '../../../utils/emitter';

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    formula: {
      updateFormula: (pos: number, end: number, latex: string) => ReturnType;
      deleteFormula: (pos: number, end: number) => ReturnType;
    };
  }
}

// 公式节点接口 - 简化为最核心的信息
interface FormulaNode {
  content: string; // 公式内容（不含分隔符）
  fullMatch: string; // 完整匹配（含分隔符）
  start: number; // 开始位置
  end: number; // 结束位置
  type: 'inline' | 'block';
}

// 公式解析规则 - 按优先级排序，捕获组只包含公式内容
const FORMULA_PATTERNS = [
  { regex: /\\\[([\s\S]*?)\\\]/g, type: 'block' as const, priority: 1 },
  { regex: /\$\$([\s\S]*?)\$\$/g, type: 'block' as const, priority: 2 },
  { regex: /\\\((.*?)\\\)/g, type: 'inline' as const, priority: 3 },
  // 修改单$公式匹配规则，避免将纯中文识别为公式
  // 更严格的匹配：不能包含中文字符和中文标点，且必须包含数学相关字符
  {
    regex:
      /\$(?!\$)([^$\u4e00-\u9fff\u3000-\u303f\uff00-\uffef]*?(?:[a-zA-Z0-9+\-*/=<>(){}[\]\\^_|&!~.,;:'"` ]|\\[a-zA-Z]+)[^$\u4e00-\u9fff\u3000-\u303f\uff00-\uffef]*?(?<!\\))\$/g,
    type: 'inline' as const,
    priority: 4,
  },
  { regex: /\\rm\{([\s\S]*?)\}/g, type: 'inline' as const, priority: 5 },
] as const;

// 解析所有公式 - 核心函数，确保准确性
function parseFormulas(text: string): FormulaNode[] {
  const formulas: FormulaNode[] = [];

  // 按优先级处理，避免冲突
  for (const pattern of FORMULA_PATTERNS) {
    let match;
    pattern.regex.lastIndex = 0;

    while ((match = pattern.regex.exec(text)) !== null) {
      const start = match.index;
      const end = start + match[0].length;

      // 检查是否与已存在的公式重叠
      const hasOverlap = formulas.some(
        (formula) =>
          (start >= formula.start && start < formula.end) ||
          (end > formula.start && end <= formula.end) ||
          (start <= formula.start && end >= formula.end),
      );

      if (!hasOverlap) {
        formulas.push({
          content: match[1], // 公式内容（不含分隔符）
          fullMatch: match[0], // 完整匹配（含分隔符）
          start,
          end,
          type: pattern.type,
        });
      }
    }
  }

  // 按位置排序
  return formulas.sort((a, b) => a.start - b.start);
}

// 查找光标位置的公式上下文（使用修正后的位置）
// 获取当前光标位置的公式上下文（基于ProseMirror文档结构）
function getFormulaContextFromState(state: any, cursorPos: number, direction?: 'left' | 'right') {
  const allFormulas: Array<{
    formula: FormulaNode;
    absoluteStart: number;
    absoluteEnd: number;
  }> = [];

  // 收集所有公式及其绝对位置
  state.doc.descendants((node: any, pos: number) => {
    if (node.isText && node.text) {
      const text = node.text;
      const formulas = parseFormulas(text);

      for (const formula of formulas) {
        const absoluteStart = pos + formula.start;
        const absoluteEnd = pos + formula.end;

        allFormulas.push({
          formula,
          absoluteStart,
          absoluteEnd,
        });
      }
    }
  });

  // 检查是否在公式内部
  for (const { formula, absoluteStart, absoluteEnd } of allFormulas) {
    if (cursorPos > absoluteStart && cursorPos < absoluteEnd) {
      return {
        type: 'inside' as const,
        formula: {
          ...formula,
          start: absoluteStart,
          end: absoluteEnd,
        },
        canEdit: false,
      };
    }
  }

  // 检查边界位置，考虑连续公式的情况
  const boundaryFormulas = allFormulas.filter(({ absoluteStart, absoluteEnd }) => {
    return cursorPos === absoluteStart || cursorPos === absoluteEnd;
  });

  if (boundaryFormulas.length > 0) {
    let targetItem = boundaryFormulas[0];

    // 如果有多个公式在同一边界位置，根据方向选择
    if (boundaryFormulas.length > 1 && direction) {
      if (direction === 'right') {
        // 向右移动时，优先选择开始位置匹配的公式（下一个公式）
        const nextItem = boundaryFormulas.find(({ absoluteStart }) => {
          return cursorPos === absoluteStart;
        });
        if (nextItem) targetItem = nextItem;
      } else if (direction === 'left') {
        // 向左移动时，优先选择结束位置匹配的公式（前一个公式）
        const prevItem = boundaryFormulas.find(({ absoluteEnd }) => {
          return cursorPos === absoluteEnd;
        });
        if (prevItem) targetItem = prevItem;
      }
    }

    const { formula, absoluteStart, absoluteEnd } = targetItem;

    return {
      type: 'boundary' as const,
      formula: {
        ...formula,
        start: absoluteStart,
        end: absoluteEnd,
      },
      canEdit: false,
      isAtStart: cursorPos === absoluteStart,
      isAtEnd: cursorPos === absoluteEnd,
    };
  }

  // 在普通文本中
  return {
    type: 'text' as const,
    canEdit: true,
  };
}

// 查找指定位置的导航目标（公式或段落）
function findNavigationTarget(
  state: any,
  cursorPos: number,
  direction: 'up' | 'down',
): { type: 'formula' | 'paragraph'; position: number } | null {
  // 构建段落信息
  const paragraphs: Array<{
    start: number;
    end: number;
    index: number;
  }> = [];

  // 遍历所有段落，收集段落的开始和结束位置
  state.doc.descendants((node: any, pos: number) => {
    if (node.type.name === 'paragraph') {
      paragraphs.push({
        start: pos + 1, // 段落开始位置
        end: pos + node.nodeSize - 1, // 段落结束位置
        index: paragraphs.length,
      });
    }
  });

  // 找到当前光标所在的段落
  const currentParagraph = paragraphs.find((p) => cursorPos >= p.start && cursorPos <= p.end);

  if (!currentParagraph) {
    return null;
  }

  const currentIndex = currentParagraph.index;

  if (direction === 'down') {
    // 向下移动：移动到下一段落的开始
    if (currentIndex + 1 < paragraphs.length) {
      const nextParagraph = paragraphs[currentIndex + 1];
      return {
        type: 'paragraph',
        position: nextParagraph.start,
      };
    }
  } else {
    // 向上移动：移动到上一段落的开始
    if (currentIndex > 0) {
      const prevParagraph = paragraphs[currentIndex - 1];
      return {
        type: 'paragraph',
        position: prevParagraph.start,
      };
    }
  }

  return null;
}

// 安全的光标定位
function safeSetCursor(view: any, targetPos: number): boolean {
  try {
    const { state } = view;
    const safePos = Math.max(0, Math.min(state.doc.content.size, targetPos));
    const resolvedPos = state.doc.resolve(safePos);

    // 尝试创建选区
    const selection = TextSelection.near(resolvedPos);
    if (selection) {
      const tr = state.tr.setSelection(selection).scrollIntoView();
      view.dispatch(tr);
      return true;
    }
  } catch (error) {
    // 光标定位失败，静默处理
  }
  return false;
}

export const Formula = Extension.create({
  name: 'formula',

  addCommands() {
    return {
      // insertFormula 命令已移除，现在直接在菜单栏组件中处理插入逻辑

      updateFormula:
        (pos: number, end: number, latex: string) =>
        ({ editor }: { editor: Editor }) => {
          if (!latex?.trim()) {
            return editor.commands.deleteFormula(pos, end);
          }

          const { state } = editor;

          // 更新公式时直接替换内容，不添加额外空格
          // 因为原有的空格（如果需要的话）已经存在于原始文本中
          const tr = state.tr.replaceWith(pos, end, state.schema.text(latex));
          editor.view.dispatch(tr);
          return true;
        },

      deleteFormula:
        (pos: number, end: number) =>
        ({ editor }: { editor: Editor }) => {
          const { state } = editor;
          const tr = state.tr.delete(pos, end);
          editor.view.dispatch(tr);
          // 触发公式删除事件，用于关闭弹窗
          emitter.emit('formula-deleted');
          return true;
        },
    };
  },

  addProseMirrorPlugins() {
    return [
      // 渲染插件
      new Plugin({
        props: {
          decorations: (state) => {
            const decorations: Decoration[] = [];

            // 逐段解析公式，避免换行导致的公式分割问题
            state.doc.descendants((node, pos) => {
              if (node.isText && node.text) {
                const text = node.text;
                const formulas = parseFormulas(text);

                // 为当前文本节点中的每个公式创建装饰器
                for (const formula of formulas) {
                  // 计算在整个文档中的绝对位置
                  const absoluteStart = pos + formula.start;
                  const absoluteEnd = pos + formula.end;

                  // 验证位置是否在文档范围内
                  if (
                    absoluteStart >= 0 &&
                    absoluteEnd <= state.doc.content.size &&
                    absoluteStart < absoluteEnd
                  ) {
                    // 隐藏原始文本
                    decorations.push(
                      Decoration.inline(absoluteStart, absoluteEnd, {
                        style: 'display: none;',
                      }),
                    );

                    // 创建公式渲染widget
                    decorations.push(
                      Decoration.widget(
                        absoluteStart,
                        (view) => {
                          const container = document.createElement('span');
                          container.className = 'formula-widget';
                          container.style.cssText = `
                            display: ${formula.type === 'block' ? 'block' : 'inline-block'};
                            position: relative;
                            cursor: pointer;
                            margin: ${formula.type === 'block' ? '8px 0' : '0 2px'};
                            padding: 4px 6px;
                            border: 1px solid transparent;
                            border-radius: 4px;
                            transition: all 0.15s ease;
                            background: rgba(0, 0, 0, 0.02);
                            vertical-align: ${formula.type === 'inline' ? 'baseline' : 'unset'};
                          `;

                          // 交互状态样式
                          container.addEventListener('mouseenter', () => {
                            container.style.background = 'rgba(24, 144, 255, 0.08)';
                            container.style.borderColor = '#1890ff';
                          });

                          container.addEventListener('mouseleave', () => {
                            container.style.background = 'rgba(0, 0, 0, 0.02)';
                            container.style.borderColor = 'transparent';
                          });

                          // 渲染数学内容
                          const mathSpan = document.createElement('span');
                          mathSpan.style.userSelect = 'none';
                          mathSpan.style.pointerEvents = 'none';

                          try {
                            const tempDiv = document.createElement('div');
                            // 使用完整的公式文本（包含分隔符）
                            tempDiv.textContent = formula.fullMatch;

                            renderMathInElement(tempDiv, {
                              delimiters: [
                                { left: '$$', right: '$$', display: true },
                                { left: '$', right: '$', display: false },
                                { left: '\\(', right: '\\)', display: false },
                                { left: '\\[', right: '\\]', display: true },
                              ],
                              throwOnError: false,
                            });

                            mathSpan.innerHTML = tempDiv.innerHTML;
                          } catch (error) {
                            // 公式渲染失败，显示原始内容
                            mathSpan.textContent = formula.content;
                          }

                          // 点击事件 - 选中公式并打开弹窗
                          container.addEventListener('click', (e) => {
                            e.stopPropagation();

                            try {
                              // 选中整个公式
                              const { state } = view;
                              const selection = TextSelection.create(
                                state.doc,
                                absoluteStart,
                                absoluteEnd,
                              );
                              const tr = state.tr.setSelection(selection);
                              view.dispatch(tr);

                              // 触发编辑弹窗
                              try {
                                emitter.emit('handle-formula-click', {
                                  content: formula.content,
                                  pos: absoluteStart,
                                  end: absoluteEnd,
                                });
                              } catch (error) {
                                console.warn('打开弹窗失败:', error);
                              }
                            } catch (error) {
                              console.warn('选中公式失败:', error);
                            }
                          });

                          // 存储位置信息
                          container.setAttribute('data-formula-start', absoluteStart.toString());
                          container.setAttribute('data-formula-end', absoluteEnd.toString());

                          container.appendChild(mathSpan);
                          return container;
                        },
                        {
                          side: 1,
                          marks: [],
                        },
                      ),
                    );
                  }
                }
              }
            });

            return DecorationSet.create(state.doc, decorations);
          },
        },
      }),

      // 键盘交互插件
      new Plugin({
        key: new PluginKey('formula-interaction'),
        props: {
          handleKeyDown: (view, event) => {
            const { state } = view;
            const { selection } = state;
            const cursorPos = selection.from;

            // 处理选中状态下的删除
            if ((event.key === 'Backspace' || event.key === 'Delete') && !selection.empty) {
              // 检查选中范围内是否包含公式
              const selectedText = state.doc.textBetween(selection.from, selection.to);
              const formulas = parseFormulas(selectedText);

              if (formulas.length > 0) {
                // 选中的内容包含公式，触发删除事件
                event.preventDefault();
                const tr = state.tr.delete(selection.from, selection.to);
                view.dispatch(tr);
                // 触发公式删除事件，用于关闭弹窗
                emitter.emit('formula-deleted');
                return true;
              }
            }

            // 处理上下方向键的导航
            if (event.key === 'ArrowUp' || event.key === 'ArrowDown') {
              const direction = event.key === 'ArrowUp' ? 'up' : 'down';
              const target = findNavigationTarget(state, cursorPos, direction);

              if (target) {
                event.preventDefault();
                // 导航到目标位置
                return safeSetCursor(view, target.position);
              }

              // 如果没有找到目标，允许默认行为
              return false;
            }

            // 根据按键确定左右移动方向
            let direction: 'left' | 'right' | undefined;
            if (event.key === 'ArrowLeft') direction = 'left';
            if (event.key === 'ArrowRight') direction = 'right';

            const context = getFormulaContextFromState(state, cursorPos, direction);

            // 在公式内部 - 阻止编辑，处理导航
            if (context.type === 'inside') {
              const { formula } = context;
              // formula 已经包含修正后的位置

              if (event.key === 'ArrowLeft') {
                event.preventDefault();
                return safeSetCursor(view, formula.start);
              }

              if (event.key === 'ArrowRight') {
                event.preventDefault();
                return safeSetCursor(view, formula.end);
              }

              if (event.key === 'Backspace' || event.key === 'Delete') {
                event.preventDefault();
                const tr = state.tr.delete(formula.start, formula.end);
                view.dispatch(tr);
                // 触发公式删除事件，用于关闭弹窗
                emitter.emit('formula-deleted');
                return true;
              }

              // 阻止其他输入
              if (event.key.length === 1 || event.key === 'Space') {
                event.preventDefault();
                return true;
              }
            }

            // 在公式边界 - 精确控制导航
            if (context.type === 'boundary') {
              const { formula, isAtStart, isAtEnd } = context;
              // formula 已经包含修正后的位置

              if (event.key === 'ArrowLeft' && isAtStart) {
                // 在公式开始处按左箭头，正常向前移动
                return false;
              }

              if (event.key === 'ArrowRight' && isAtEnd) {
                // 在公式结束处按右箭头，正常向后移动
                return false;
              }

              if (event.key === 'ArrowLeft' && isAtEnd) {
                // 在公式结束处按左箭头，跳到公式开始
                event.preventDefault();
                return safeSetCursor(view, formula.start);
              }

              if (event.key === 'ArrowRight' && isAtStart) {
                // 在公式开始处按右箭头，跳到公式结束
                event.preventDefault();
                return safeSetCursor(view, formula.end);
              }

              if (event.key === 'Backspace') {
                if (isAtEnd) {
                  // 在公式后按退格，删除公式
                  event.preventDefault();
                  const tr = state.tr.delete(formula.start, formula.end);
                  view.dispatch(tr);
                  // 触发公式删除事件，用于关闭弹窗
                  emitter.emit('formula-deleted');
                  return true;
                }
                // 在公式开始位置按退格，手动处理删除前面的文字
                if (isAtStart) {
                  event.preventDefault();

                  // 手动删除光标前的一个字符
                  if (cursorPos > 0) {
                    const tr = state.tr.delete(cursorPos - 1, cursorPos);
                    view.dispatch(tr);
                    return true;
                  }

                  return false;
                }
              }

              if (event.key === 'Delete') {
                if (isAtStart) {
                  // 在公式前按删除，删除公式
                  event.preventDefault();
                  const tr = state.tr.delete(formula.start, formula.end);
                  view.dispatch(tr);
                  // 触发公式删除事件，用于关闭弹窗
                  emitter.emit('formula-deleted');
                  return true;
                }
              }
            }

            // 普通文本区域 - 保持原生行为
            return false;
          },

          // 简化点击处理 - 选中公式并打开弹窗
          handleClick: (view, pos, event) => {
            const { state } = view;
            const context = getFormulaContextFromState(state, pos);

            // 点击公式内部时处理
            if (context.type === 'inside') {
              event.preventDefault();

              try {
                // 选中整个公式
                const selection = TextSelection.create(
                  state.doc,
                  context.formula.start,
                  context.formula.end,
                );
                const tr = state.tr.setSelection(selection);
                view.dispatch(tr);

                // 直接触发菜单栏的公式点击处理（打开弹窗）
                try {
                  const { start, end } = context.formula;
                  const content = state.doc.textBetween(start, end);
                  emitter.emit('handle-formula-click', {
                    content,
                    pos: start,
                    end,
                  });
                } catch (error) {
                  console.warn('打开弹窗失败:', error);
                }
              } catch (error) {
                console.warn('选中公式失败:', error);
              }

              return true;
            }

            return false;
          },
        },
      }),
    ];
  },
});
