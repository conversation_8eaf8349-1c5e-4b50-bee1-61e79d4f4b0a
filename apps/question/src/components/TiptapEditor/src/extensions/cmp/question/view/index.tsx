import { TextSelection } from 'prosemirror-state';
import './style.scss';

interface QuestionViewProps {
  index: number;
  uuid: string;
  editor?: any;
  getPos?: () => number;
}

const QuestionView = ({ index, uuid, editor, getPos }: QuestionViewProps) => {
  const handleClick = (event: React.MouseEvent) => {
    event.preventDefault();
    event.stopPropagation();

    // 先处理光标移动（同步执行，保证响应速度）
    if (editor && getPos) {
      try {
        const pos = getPos();
        const { view } = editor;
        const { state } = view;

        // 获取当前节点
        const node = state.doc.nodeAt(pos);
        if (node) {
          // 计算光标应该移动到的位置（作答区域后面）
          const cursorPos = pos + node.nodeSize;

          // 确保位置在文档范围内
          const docSize = state.doc.content.size;
          const safeCursorPos = Math.min(cursorPos, docSize);

          // 直接移动光标
          const resolvedPos = state.doc.resolve(safeCursorPos);
          const selection = TextSelection.near(resolvedPos);
          const tr = state.tr.setSelection(selection);

          view.dispatch(tr);
          view.focus();
        }
      } catch (error) {
        console.warn('设置光标位置失败:', error);
      }
    }

    // 发送事件
    if (editor) {
      try {
        editor.emit('question:click', {
          uuid,
          No: index,
          index: index - 1,
          action: 'click',
          type: 'question-blank_filling',
        });
      } catch (error) {
        console.warn('发送事件失败:', error);
      }
    }
  };

  return (
    <div
      className="question"
      onClick={handleClick}
      style={{
        cursor: 'pointer',
        display: 'inline-block',
        userSelect: 'none',
      }}
    >
      {index && (
        <div className="question-index" style={{ userSelect: 'none' }}>
          {index}
        </div>
      )}
    </div>
  );
};

export default QuestionView;
