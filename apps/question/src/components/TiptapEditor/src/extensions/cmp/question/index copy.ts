/* eslint-disable */
import { mergeAttributes, Node, ReactNodeViewRenderer } from '@tiptap/react';
import { Node as ProseMirrorNode } from 'prosemirror-model';
import { Plugin } from 'prosemirror-state';
import { generateUUID } from '../../../utils';
import NodeView from './node-view';
import { createCopyHandlerPlugin } from './plugins/copy-handler';

interface QuestionNodeSnapshot {
  uuid: string;
  index: number;
  pos?: number;
}

type QuestionEventType = 'insert' | 'delete' | 'click';

export interface QuestionExtensionEventData {
  uuid: string;
  action: string;
  type: string;
  No?: number;
  index?: number;
  position?: number;
}

type EmitEventFunction = (eventType: QuestionEventType, data: QuestionExtensionEventData) => void;

/**
 * 从第一个数组中找出其不在第二个数组中的插入点
 * @param first
 * @param seconde
 * @returns 第一个数组中不在第二个数组中的元素
 */
function getDifferenceQuestionNode(first: QuestionNodeSnapshot[], seconde: QuestionNodeSnapshot[]) {
  const secondUuids = new Set(seconde.map((node) => node.uuid));
  const secondUuidsSet = new Set(secondUuids);
  return first.filter((item) => !secondUuidsSet.has(item.uuid));
}

/**
 * 插件：在每次文档变动后，统一更新指定类型节点的 data-index
 */
function IndexUpdatePlugin(nodeTypeName = 'question', emitEvent?: EmitEventFunction) {
  let lastNodesSnapshot: QuestionNodeSnapshot[] = [];

  return new Plugin({
    appendTransaction(transactions, oldState, newState) {
      const docChanged = transactions.some((tr) => tr.docChanged);
      if (!docChanged) return null;

      const tr = newState.tr;
      let index = 1;
      const currentNodesSnapshot: QuestionNodeSnapshot[] = [];

      newState.doc.descendants((node, pos) => {
        if (node.type.name === nodeTypeName) {
          const currentIndex = node.attrs['data-index'];
          let fillUuid = node.attrs['data-fill-uuid'];

          if (!fillUuid) {
            fillUuid = generateUUID();
            tr.setNodeMarkup(pos, undefined, {
              ...node.attrs,
              'data-fill-uuid': fillUuid,
            });
          }
          if (currentIndex !== index) {
            tr.setNodeMarkup(pos, undefined, {
              ...node.attrs,
              'data-index': index,
            });
          }

          // 记录当前节点快照
          currentNodesSnapshot.push({
            uuid: fillUuid,
            index: index,
            pos: pos,
          });

          index++;
        }
        return true;
      });

      if (emitEvent) {
        const deletedNodes = getDifferenceQuestionNode(lastNodesSnapshot, currentNodesSnapshot);
        const addNodes = getDifferenceQuestionNode(currentNodesSnapshot, lastNodesSnapshot);

        if (deletedNodes.length > 0) {
          // 发送删除事件，包含被删除节点的详细信息
          deletedNodes.forEach((deletedNode) => {
            emitEvent('delete', {
              action: 'delete',
              uuid: deletedNode.uuid,
              type: 'question-blank_filling',
              No: deletedNode.index,
              index: deletedNode.index - 1,
            });
          });
        }
        if (addNodes.length > 0) {
          // 发送添加事件，包含被添加节点的详细信息
          addNodes.forEach((addNode) => {
            emitEvent('insert', {
              action: 'insert',
              uuid: addNode.uuid,
              type: 'question-blank_filling',
              No: addNode.index,
              index: addNode.index - 1,
            });
          });
        }
      }

      // 更新快照
      lastNodesSnapshot = currentNodesSnapshot;

      return tr.docChanged ? tr : null;
    },
  });
}

// 初始化的时候调用
export const descendants = (editor: any) => {
  // 手动派发 transaction，让插件工作一次
  const { state, view } = editor;
  const tr = state.tr;

  let index = 1;
  state.doc.descendants((node: any, pos: any) => {
    if (node.type.name === 'question') {
      const fillUuid = node.attrs['data-fill-uuid'];
      tr.setNodeMarkup(pos, undefined, {
        ...node.attrs,
        'data-index': index,
        'data-fill-uuid': fillUuid || generateUUID(),
      });
      index++;
    }
  });

  if (tr.docChanged) {
    view.dispatch(tr);
  }
};

// 辅助函数：发送事件
function emitQuestionEvent(
  editor: any,
  eventType: QuestionEventType,
  data: QuestionExtensionEventData,
) {
  // 方法1: 使用 Tiptap 编辑器的事件系统
  editor.emit(`question:${eventType}`, data);

  // 方法2: 使用浏览器的自定义事件
  const event = new CustomEvent(`tiptap-question-${eventType}`, {
    detail: data,
    bubbles: true,
  });
  if (editor.view?.dom) {
    editor.view.dom.dispatchEvent(event);
  }
}

export const Question: any = Node.create({
  name: 'question',
  group: 'inline',
  inline: true,
  content: 'text*', //节点内是否允许有内容

  // 关键设置：标记为 void 节点
  atom: true,
  selectable: false,
  draggable: false,

  addAttributes() {
    return {
      'data-tiptype': {
        default: 'question-blank_filling', // question-cloze_test 完型
      },
      'data-index': {
        default: '',
      },
      'data-fill-uuid': {
        default: '',
      },
    };
  },

  parseHTML() {
    return [{ tag: 'span[data-tiptype]' }];
  },

  renderHTML({ HTMLAttributes }) {
    return ['span', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0];
  },

  toDOM(node: ProseMirrorNode) {
    return [
      'span',
      {
        'data-tiptype': node.attrs['data-tiptype'],
        'data-index': node.attrs['data-index'],
        'data-fill-uuid': node.attrs['data-fill-uuid'],
      },
      0,
    ];
  },

  // 使用ReactNodeViewRenderer包装你的组件
  addNodeView() {
    return ReactNodeViewRenderer(NodeView, {
      // 传递编辑器实例给 NodeView
      as: 'span',
      contentDOMElementTag: 'span',
    });
  },
  // @ts-ignore
  addCommands() {
    return {
      insertQs:
        (arg = 'solid') =>
        ({ editor, commands }: any) => {
          const fillUuid = generateUUID();

          // 获取当前光标位置
          const { from } = editor.state.selection;

          // 统计光标位置之前的填空点数量
          let questionsBeforeCursor = 0;
          editor.state.doc.descendants((node: any, pos: number) => {
            if (node.type.name === 'question' && pos < from) {
              questionsBeforeCursor++;
            }
          });

          // 新插入的填空点索引 = 光标之前的填空点数量 + 1
          const newIndex = questionsBeforeCursor + 1;

          const myNode = {
            type: 'question', // 这个名称最好和节点的 name 保持一致，否则如果自定义nodeview ,会导致无法解析
            attrs: {
              'data-tiptype': 'question-blank_filling',
              'data-fill-uuid': fillUuid,
              'data-index': newIndex, // 基于光标位置计算的索引
            },
          };

          const result = commands.insertContent(myNode);
          return result;
        },
    };
  },

  addProseMirrorPlugins() {
    return [
      createCopyHandlerPlugin({
        onCopy: this.options?.onCopy,
        questionNodeName: 'question',
      }),
      IndexUpdatePlugin(
        'question',
        (eventType: QuestionEventType, data: QuestionExtensionEventData) => {
          emitQuestionEvent(this.editor, eventType, data);
        },
      ),
    ];
  },
});

Question.descendants = descendants;
