# TiptapEditor 公式扩展技术文档

## 概述

本文档记录了 TiptapEditor 公式扩展的技术实现细节、关键改动过程以及重要注意事项。公式扩展支持在编辑器中插入和渲染 LaTeX 数学公式，包括行内公式和块级公式。

## 技术架构

### 核心组件

1. **Formula Extension** (`index.ts`) - 主要的 ProseMirror 扩展
2. **Formula Menu** (`menu-bar/cmp/formula/index.tsx`) - 公式插入菜单组件
3. **KaTeX 渲染器** - 负责将 LaTeX 转换为可视化数学公式

### 支持的公式格式

- 行内公式：`\(formula\)`、`$formula$`、`\rm{formula}`
- 块级公式：`\[formula\]`、`$$formula$$`

## 关键技术点

### 1. 正则表达式设计

**核心问题**：捕获组的正确设置直接影响公式内容的提取。

```javascript
const FORMULA_PATTERNS = [
  { regex: /\\\[([\s\S]*?)\\\]/g, type: 'block' }, // \[...\]
  { regex: /\$\$([\s\S]*?)\$\$/g, type: 'block' }, // $$...$$
  { regex: /\\\((.*?)\\\)/g, type: 'inline' }, // \(...\)
  { regex: /\$(?!\$)([^$]*?(?<!\\))\$/g, type: 'inline' }, // $...$
  { regex: /\\rm\{([\s\S]*?)\}/g, type: 'inline' }, // \rm{...}
];
```

**关键点**：

- 捕获组 `(...)` 只包含公式内容，不包含分隔符
- 错误示例：`/(\\\(.*?\\\))/g` 会导致 `match[1]` 包含分隔符
- 正确示例：`/\\\((.*?)\\\)/g` 使得 `match[1]` 只包含纯公式内容

### 2. 位置修正机制

**核心问题**：插入位置与解析位置存在 1 字符偏移。

```javascript
// 装饰器创建时的位置修正
const actualStart = formula.start + 1;
const actualEnd = formula.end + 1;
```

**原因分析**：

- 插入操作在特定位置插入公式文本
- 解析操作基于完整文档文本，可能包含前一个字符
- 需要在装饰器创建和上下文判断中统一应用 +1 修正

### 3. 装饰器管理

**核心功能**：

- 隐藏原始 LaTeX 文本
- 在相同位置显示渲染后的数学公式

```javascript
// 隐藏原始文本
decorations.push(
  Decoration.inline(actualStart, actualEnd, {
    style: 'display: none;',
  }),
);

// 显示渲染后的公式
decorations.push(
  Decoration.widget(actualStart, () => {
    const element = document.createElement('span');
    element.className = 'formula-widget';
    katex.render(formula.content, element, {
      displayMode: formula.type === 'block',
      throwOnError: false,
    });
    return element;
  }),
);
```

### 4. 光标导航逻辑

**三种光标状态**：

- `inside`：光标在公式内部，阻止编辑，支持跳跃导航
- `boundary`：光标在公式边界，支持精确导航控制
- `text`：光标在普通文本中，正常编辑

**水平导航（左右方向键）**：

```javascript
function getFormulaContextFromState(state: any, cursorPos: number, direction?: 'left' | 'right') {
  // 收集所有公式及其绝对位置
  const allFormulas = [];
  state.doc.descendants((node, pos) => {
    if (node.isText && node.text) {
      const formulas = parseFormulas(node.text);
      for (const formula of formulas) {
        allFormulas.push({
          formula,
          absoluteStart: pos + formula.start,
          absoluteEnd: pos + formula.end,
        });
      }
    }
  });

  // 检查是否在公式内部、边界或普通文本中
  // 支持连续公式的方向感知导航
}
```

**垂直导航（上下方向键）**：

```javascript
function findNavigationTarget(state: any, cursorPos: number, direction: 'up' | 'down') {
  // 收集所有公式和段落信息
  const allFormulas = [];
  const allParagraphs = [];

  state.doc.descendants((node, pos) => {
    if (node.type.name === 'paragraph') {
      allParagraphs.push({ pos: pos + 1, hasFormula: false });
    }
    if (node.isText && node.text) {
      // 收集公式并标记包含公式的段落
    }
  });

  // 优先导航到公式，没有公式则导航到下一段落开头
  if (direction === 'down') {
    const nextFormula = allFormulas.find(f => f.absoluteStart > cursorPos);
    if (nextFormula) return { type: 'formula', position: nextFormula.absoluteStart };

    // 找下一个包含文本内容的段落
    return { type: 'paragraph', position: nextParagraphPos };
  }
}
```

**导航规则**：

1. **水平导航**：在公式内部跳转到边界，在边界间精确移动
2. **垂直导航**：优先跳转到公式位置，无公式时跳转到段落开头
3. **连续公式**：根据移动方向智能选择目标公式
4. **跨段落**：确保跳转到合理的编辑位置，避免卡在公式中间

### 5. 事务处理

**核心问题**：Tiptap 命令系统与 ProseMirror 底层事务存在冲突。

**解决方案**：绕过 Tiptap 命令系统，直接使用 ProseMirror API。

```javascript
// 在菜单组件中直接处理插入
const handleInsert = (latex: string) => {
  const { state, dispatch } = editor.view;
  const { selection } = state;
  const insertPosition = selection.from;

  // 直接使用 ProseMirror 事务
  const tr = state.tr.insertText(latex, insertPosition);
  dispatch(tr);
};
```

### 6. 连续公式处理

**关键挑战**：当多个公式紧挨着时，它们会共享边界位置，导致光标导航混乱。

**解决策略**：

- 在 `getFormulaContext` 中添加方向参数
- 根据移动方向选择正确的目标公式
- 向右移动时优先选择下一个公式
- 向左移动时优先选择前一个公式

```javascript
// 处理连续公式的边界位置选择
if (boundaryFormulas.length > 1 && direction) {
  if (direction === 'right') {
    // 优先选择开始位置匹配的公式（下一个公式）
    const nextFormula = boundaryFormulas.find((formula) => cursorPos === formula.start + 1);
    if (nextFormula) targetFormula = nextFormula;
  } else if (direction === 'left') {
    // 优先选择结束位置匹配的公式（前一个公式）
    const prevFormula = boundaryFormulas.find((formula) => cursorPos === formula.end + 1);
    if (prevFormula) targetFormula = prevFormula;
  }
}
```

### 7. 逐段解析机制

**核心问题**：换行操作会改变 ProseMirror 文档结构，将原本连续的文本分割到不同段落中。

**解决方案**：使用 `state.doc.descendants()` 逐个文本节点进行解析，而不是使用 `textBetween()` 获取连续文本。

```javascript
// 逐段解析公式，避免换行导致的公式分割问题
state.doc.descendants((node, pos) => {
  if (node.isText && node.text) {
    const text = node.text;
    const formulas = parseFormulas(text);

    // 为当前文本节点中的每个公式创建装饰器
    for (const formula of formulas) {
      // 计算在整个文档中的绝对位置
      const absoluteStart = pos + formula.start;
      const absoluteEnd = pos + formula.end;

      // 创建装饰器...
    }
  }
});
```

**关键优势**：

- 避免段落分隔符干扰公式解析
- 确保公式在任何文档结构变化下都保持完整
- 支持跨段落的复杂文档编辑操作
- 准确计算公式在整个文档中的绝对位置

## 重大改动历史

### 阶段一：边界处理重构

- **问题**：光标无法在字符间正确插入
- **解决**：重构边界检测逻辑，定义三种光标状态
- **影响**：改善了基本的光标导航体验

### 阶段二：正则表达式修复

- **问题**：插入公式后出现多余字符
- **根因**：正则表达式捕获组包含了分隔符
- **解决**：修正所有正则表达式，确保捕获组只包含公式内容
- **影响**：解决了多余字符问题

### 阶段三：事务错误处理

- **问题**：出现 "Applying a mismatched transaction" 错误
- **根因**：Tiptap 命令系统与 ProseMirror 底层事务冲突
- **解决**：绕过 Tiptap 命令系统，直接使用 ProseMirror API
- **影响**：彻底解决了事务错误问题

### 阶段四：位置修正

- **问题**：插入公式后前方字符被删除，出现多余字符
- **根因**：插入位置与解析位置存在 1 字符偏移
- **解决**：在装饰器创建时应用 +1 位置修正
- **影响**：解决了字符删除和多余字符问题

### 阶段五：光标导航统一

- **问题**：光标导航出现跳跃和卡顿
- **根因**：`getFormulaContext` 使用原始位置，但其他逻辑使用修正位置
- **解决**：统一所有位置判断逻辑，都使用修正后的位置
- **影响**：实现了流畅的光标导航体验

### 阶段六：连续公式光标导航修复

- **问题**：连续公式时光标从前往后移动会跳跃到最后
- **根因**：两个公式共享边界位置时，`getFormulaContext` 总是返回第一个匹配的公式
- **解决**：
  - 在 `getFormulaContext` 函数中添加方向参数
  - 当多个公式共享边界位置时，根据移动方向选择正确的公式
  - 向右移动时优先选择开始位置匹配的公式（下一个公式）
  - 向左移动时优先选择结束位置匹配的公式（前一个公式）
- **影响**：解决了连续公式的光标导航问题

### 阶段七：更新公式空格处理优化

- **问题**：更新公式时会产生多余的空格
- **根因**：`updateFormula` 命令重复应用了空格管理逻辑
- **解决**：更新公式时直接替换内容，不添加额外空格，保持原有的文本格式
- **影响**：确保插入和更新公式的一致性体验

### 阶段八：换行时公式分割问题修复

- **问题**：在公式前换行时，公式被分割，出现多余的 `\)` 字符
- **根因**：使用 `state.doc.textBetween(0, state.doc.content.size)` 获取连续文本进行解析，但换行会插入段落分隔符，导致公式被分割到不同段落
- **现象**：
  - 换行后文档结构发生变化，公式跨越两个段落
  - 装饰器基于连续文本解析，但实际文档是分段的
  - 隐藏的原始文本被分割（如 `\(\dfrac{x}{y}\` 和 `\)` 分离）
  - 第一个段落包含公式widget，第二个段落包含被分割的公式文本
- **解决**：
  - 改用逐段解析方式：`state.doc.descendants()` 遍历每个文本节点
  - 为每个文本节点单独进行公式解析，避免段落分隔符干扰
  - 计算公式在整个文档中的绝对位置：`pos + formula.start`
  - 更新所有相关逻辑使用新的 `getFormulaContextFromState` 函数
- **核心变更**：

  ```javascript
  // 之前：全文档连续文本解析
  const text = state.doc.textBetween(0, state.doc.content.size);
  const formulas = parseFormulas(text);

  // 现在：逐段解析
  state.doc.descendants((node, pos) => {
    if (node.isText && node.text) {
      const text = node.text;
      const formulas = parseFormulas(text);
      // 计算绝对位置：pos + formula.start
    }
  });
  ```

- **影响**：彻底解决了换行操作导致的公式分割问题，确保公式在任何文档结构变化下都保持完整性

### 阶段九：上下方向键导航优化

- **问题**：使用上下方向键时，光标会跳过公式widget，直接到达下一段的中间位置
- **现象**：
  - 从第一段向下移动时，光标跳过所有公式，直接到"电"字后的文本中间
  - 从最后一段向上移动时，无法有效导航到公式位置
  - 公式在垂直导航中被完全忽略，影响编辑体验
- **根因**：只处理了水平方向键（左右），没有处理垂直方向键（上下）的公式导航逻辑
- **初期方案**：
  - 新增 `findNavigationTarget` 函数，支持上下方向的智能导航
  - 收集文档中所有公式和段落的位置信息
  - 实现优先级导航：公式 > 段落开头
  - 区分纯公式段落和包含文本的段落
- **问题修正**：初期方案过于复杂，导致在跨段落导航时出现错误跳转
  - 光标在第二段开头（第二个公式前）向上移动，应该到第一段开头（"新"字前），但实际到了第一个公式前
  - 光标在第一段开头（"新"字前）向下移动，应该到第二段开头（第二个公式前），但实际到了第一个公式前
  - 导航逻辑试图在元素间导航而不是段落间导航，造成用户体验混乱
- **最终解决方案**：
  - 简化导航逻辑，改为纯段落间导航
  - 移除复杂的公式优先级逻辑和文本段检测
  - 实现简单直观的上下段落跳转
- **核心实现**：

  ```javascript
  function findNavigationTarget(state, cursorPos, direction) {
    // 构建段落信息
    const paragraphs = [];

    // 遍历所有段落，收集段落的开始和结束位置
    state.doc.descendants((node, pos) => {
      if (node.type.name === 'paragraph') {
        paragraphs.push({
          start: pos + 1, // 段落开始位置
          end: pos + node.nodeSize - 1, // 段落结束位置
          index: paragraphs.length,
        });
      }
    });

    // 找到当前光标所在的段落
    const currentParagraph = paragraphs.find((p) => cursorPos >= p.start && cursorPos <= p.end);

    if (!currentParagraph) return null;
    const currentIndex = currentParagraph.index;

    if (direction === 'down') {
      // 向下移动：移动到下一段落的开始
      if (currentIndex + 1 < paragraphs.length) {
        const nextParagraph = paragraphs[currentIndex + 1];
        return { type: 'paragraph', position: nextParagraph.start };
      }
    } else {
      // 向上移动：移动到上一段落的开始
      if (currentIndex > 0) {
        const prevParagraph = paragraphs[currentIndex - 1];
        return { type: 'paragraph', position: prevParagraph.start };
      }
    }

    return null;
  }
  ```

- **设计原则**：
  - **简单明确**：上下方向键只在段落间移动，不考虑元素内容
  - **用户直觉**：向上移动到上一段落开头，向下移动到下一段落开头
  - **可预测性**：导航行为完全可预测，无复杂的条件判断
  - **一致体验**：无论段落内容如何（纯文本、纯公式、混合内容），导航行为保持一致

- **最终导航行为**：
  - **光标在第二段开头（第二个公式前）→ 按向上键**：移动到第一段开头（"新"字前面）
  - **光标在第一段开头（"新"字前面）→ 按向下键**：移动到第二段开头（第二个公式前）
  - **任何段落内位置 → 按上下键**：始终移动到目标段落的开头位置

- **影响**：实现了简单直观的段落间跳转，彻底解决了跨段落导航问题，提供了用户期望的一致导航体验

## 重要注意事项

### 1. 文档解析方式

- **关键原则**：必须使用逐段解析，不能使用全文档连续文本解析
- **正确做法**：使用 `state.doc.descendants()` 遍历文本节点
- **错误做法**：使用 `state.doc.textBetween(0, state.doc.content.size)` 获取连续文本
- **原因**：换行操作会改变文档结构，连续文本解析会导致公式被分割
- **后果**：错误的解析方式会导致换行时公式分割、多余字符、装饰器范围错误

### 2. 位置一致性

- **关键原则**：所有位置相关的逻辑必须使用相同的位置计算方式
- **包括**：装饰器创建、上下文判断、键盘处理、点击处理
- **重要提醒**：在逐段解析中，使用 `pos + formula.start` 计算绝对位置
- **验证方法**：检查装饰器的 `data-formula-start` 和 `data-formula-end` 属性是否与实际公式位置匹配
- **后果**：位置不一致会导致光标导航混乱、公式分割、装饰器范围错误

### 3. 正则表达式维护

- **关键原则**：捕获组只包含公式内容，不包含分隔符
- **验证方法**：确保 `match[1]` 返回纯公式内容
- **后果**：捕获组错误会导致多余字符或渲染失败

### 4. 事务处理

- **关键原则**：避免在 Tiptap 命令中处理复杂的插入逻辑
- **推荐做法**：在菜单组件中直接使用 ProseMirror API
- **后果**：事务冲突会导致编辑器状态异常

### 5. 装饰器管理

- **关键原则**：确保每个公式只有一个装饰器
- **实现方法**：在逐段解析中为每个公式创建唯一装饰器
- **后果**：重复装饰器会导致渲染异常

### 6. 调试策略

- **位置调试**：记录原始位置、修正位置、文本内容
- **上下文调试**：记录光标位置和对应的上下文类型
- **事务调试**：记录事务前后的文档状态

## 性能优化建议

1. **装饰器缓存**：考虑缓存已渲染的公式，避免重复渲染
2. **位置计算优化**：对于大文档，考虑优化公式解析算法
3. **事件处理优化**：减少不必要的事件处理，特别是鼠标移动事件

## 测试要点

1. **基本功能测试**：
   - 插入各种格式的公式
   - 公式正确渲染
   - 光标导航流畅

2. **边界情况测试**：
   - 文档开头/结尾插入公式
   - 连续插入多个公式
   - 公式与中文字符混合
   - 连续公式的光标导航
   - 公式更新操作的空格处理
   - 在公式前后进行换行操作
   - 公式在段落分割时的完整性
   - 跨段落的上下方向键导航
   - 段落间导航的准确性和一致性

3. **交互测试**：
   - 键盘导航
   - 鼠标点击
   - 复制粘贴
   - 点击公式时的选中状态
   - 选中公式后的快捷键操作（复制、删除）
   - 删除公式时弹窗自动关闭

4. **文档结构测试**：
   - 在公式前后换行
   - 在公式中间位置换行（应该被阻止）
   - 复杂段落结构中的公式处理
   - 删除段落时的公式完整性

## 故障排除

### 问题：光标导航异常

- **检查**：位置修正是否一致
- **验证**：`getFormulaContext` 返回的位置是否正确
- **解决**：统一所有位置相关逻辑

### 问题：出现多余字符

- **检查**：正则表达式捕获组是否正确
- **验证**：`match[1]` 是否只包含公式内容
- **解决**：修正正则表达式

### 问题：事务错误

- **检查**：是否在 Tiptap 命令中处理复杂逻辑
- **验证**：事务是否与编辑器状态匹配
- **解决**：使用 ProseMirror 底层 API

### 问题：公式渲染异常

- **检查**：装饰器是否重复创建
- **验证**：KaTeX 输入是否包含正确的分隔符
- **解决**：优化装饰器创建逻辑

### 问题：连续公式导航跳跃

- **检查**：是否有多个公式共享边界位置
- **验证**：`getFormulaContext` 是否正确传递方向参数
- **解决**：在键盘处理中根据按键方向传递正确的方向参数

### 问题：更新公式时产生多余空格

- **检查**：更新操作是否重复应用了空格管理逻辑
- **验证**：更新前后的文本内容对比
- **解决**：更新公式时直接替换内容，不添加额外空格

### 问题：换行时公式被分割

- **现象**：在公式前换行时，出现多余的 `\)` 字符，公式装饰器位置错误
- **检查**：装饰器的隐藏范围是否完整覆盖公式文本
- **验证**：检查 `data-formula-start` 和 `data-formula-end` 属性与实际公式位置是否匹配
- **解决**：确保使用正确的位置，不进行错误的位置修正

## 版本兼容性

- **ProseMirror**: 1.x
- **Tiptap**: 2.x
- **KaTeX**: 0.16.x
- **React**: 18.x

## 阶段十：公式点击选中功能

### 问题描述

用户反馈希望在点击公式时，除了打开编辑弹窗外，还能同时选中当前公式，这样便于后续的复制、删除等操作。

### 需求分析

- **保持现有逻辑**：点击公式时仍然打开编辑弹窗
- **新增选中功能**：点击公式时同时选中整个公式文本
- **用户体验**：为用户提供更便捷的公式操作方式

### 实现方案

#### 1. 公式容器点击事件优化

在公式装饰器的点击事件中，添加选中逻辑：

```javascript
// 点击事件
container.addEventListener('click', (e) => {
  e.stopPropagation();

  // 选中当前公式
  const { state, dispatch } = view;
  const selection = TextSelection.create(state.doc, absoluteStart, absoluteEnd);
  const tr = state.tr.setSelection(selection);
  dispatch(tr);

  // 触发编辑弹窗
  emitter.emit('formula-click', {
    content: formula.content,
    pos: absoluteStart,
    end: absoluteEnd,
  });
});
```

#### 2. ProseMirror handleClick 优化

修改 ProseMirror 的 handleClick 处理，当点击公式内部时选中整个公式：

```javascript
// 改进点击处理
handleClick: (view, pos, event) => {
  const { state } = view;
  const context = getFormulaContextFromState(state, pos);

  // 点击公式内部时，选中整个公式
  if (context.type === 'inside') {
    event.preventDefault();
    // 选中整个公式
    const selection = TextSelection.create(state.doc, context.formula.start, context.formula.end);
    const tr = state.tr.setSelection(selection);
    view.dispatch(tr);
    return true;
  }

  return false;
},
```

### 核心实现

#### 选中逻辑的关键点

1. **TextSelection.create**：创建文本选择范围
2. **absoluteStart 和 absoluteEnd**：使用准确的公式位置
3. **事务处理**：通过 `tr.setSelection()` 设置选择状态
4. **视图更新**：通过 `dispatch(tr)` 更新编辑器状态

#### 双重点击处理

- **公式容器点击**：处理公式装饰器的直接点击
- **ProseMirror 点击**：处理公式内部区域的点击
- **统一行为**：两种点击方式都会选中公式

### 交互流程

1. **用户点击公式**
2. **选中公式文本**：整个公式被选中（高亮显示）
3. **打开编辑弹窗**：保持原有的编辑功能
4. **后续操作**：用户可以进行复制（Ctrl+C）、删除（Delete/Backspace）等操作

### 设计原则

- **功能叠加**：新功能不影响现有的编辑弹窗逻辑
- **用户直觉**：点击选中是用户期望的自然行为
- **操作便捷**：选中后可以直接使用快捷键操作
- **视觉反馈**：选中状态提供明确的视觉提示

### 影响与优势

1. **操作效率**：用户可以快速选中公式进行复制、删除
2. **交互一致性**：与其他可选中元素的行为保持一致
3. **功能完整性**：编辑和选择功能并存，满足不同使用场景
4. **用户体验**：提供更直观的公式操作方式

## 阶段十一：公式删除自动关闭弹窗

### 问题描述

当用户选中公式并通过键盘删除（Backspace、Delete 键）时，公式编辑弹窗仍然保持打开状态，这会造成用户困惑，因为弹窗中显示的是已经被删除的公式内容。

### 解决方案

在公式删除的各个触发点添加事件通知机制，当公式被删除时自动关闭编辑弹窗。

**关键修复**：添加了对选中状态下删除的处理，这是解决问题的核心。

#### 1. 选中状态删除处理（核心修复）

在键盘处理的最前面添加对选中状态的检测：

```javascript
// 处理选中状态下的删除
if ((event.key === 'Backspace' || event.key === 'Delete') && !selection.empty) {
  // 检查选中范围内是否包含公式
  const selectedText = state.doc.textBetween(selection.from, selection.to);
  const formulas = parseFormulas(selectedText);

  if (formulas.length > 0) {
    // 选中的内容包含公式，触发删除事件
    event.preventDefault();
    const tr = state.tr.delete(selection.from, selection.to);
    view.dispatch(tr);
    // 触发公式删除事件，用于关闭弹窗
    emitter.emit('formula-deleted');
    return true;
  }
}
```

#### 2. 其他删除场景的事件触发

在所有删除公式的地方添加事件触发：

```javascript
// 在公式内部删除
if (event.key === 'Backspace' || event.key === 'Delete') {
  event.preventDefault();
  const tr = state.tr.delete(formula.start, formula.end);
  view.dispatch(tr);
  // 触发公式删除事件，用于关闭弹窗
  emitter.emit('formula-deleted');
  return true;
}

// 在公式边界删除
if (event.key === 'Backspace' && isAtEnd) {
  event.preventDefault();
  const tr = state.tr.delete(formula.start, formula.end);
  view.dispatch(tr);
  // 触发公式删除事件，用于关闭弹窗
  emitter.emit('formula-deleted');
  return true;
}

// 通过命令删除
deleteFormula: (pos: number, end: number) => ({ editor }: { editor: Editor }) => {
  const { state } = editor;
  const tr = state.tr.delete(pos, end);
  editor.view.dispatch(tr);
  // 触发公式删除事件，用于关闭弹窗
  emitter.emit('formula-deleted');
  return true;
}
```

#### 3. 弹窗组件监听删除事件

在公式菜单组件中添加删除事件监听：

```javascript
// 处理公式删除事件
const handleFormulaDeleted = useCallback(() => {
  // 公式被删除时关闭弹窗
  setOpen(false);
}, []);

useEffect(() => {
  emitter.on('formula-click', handleFormulaClick);
  emitter.on('formula-deleted', handleFormulaDeleted);
  return () => {
    emitter.off('formula-click', handleFormulaClick);
    emitter.off('formula-deleted', handleFormulaDeleted);
  };
}, [handleFormulaClick, handleFormulaDeleted]);
```

### 用户体验改进

- ✅ **智能关闭**：删除公式时弹窗自动关闭，避免显示无效内容
- ✅ **一致性**：无论通过键盘还是命令删除，都能正确关闭弹窗
- ✅ **即时反馈**：删除操作立即反映在界面状态上

### 覆盖场景

1. **键盘删除**：
   - 在公式内部按 Backspace/Delete 键
   - 在公式后面按 Backspace 键
   - 在公式前面按 Delete 键

2. **命令删除**：
   - 通过 `deleteFormula` 命令删除
   - 通过 `updateFormula` 命令清空内容时的删除

## 最终成果

经过十二个阶段的系统性重构，公式扩展现在具备：

### 核心功能

1. **完善的公式解析**：支持多种公式格式（`$...$`、`\(...\)`、`$$...$$`、`\[...\]`、`\rm{...}`），正确处理分隔符和内容
2. **精确的光标控制**：在公式内部、边界和普通文本间流畅切换
3. **全方向导航**：
   - **水平导航**（左右键）：连续公式的方向感知导航
   - **垂直导航**（上下键）：简单直观的段落间跳转
4. **文档结构兼容**：正确处理换行、段落等结构变化
5. **无多余字符**：插入和更新公式时保持内容纯净
6. **选中式点击交互**：
   - **单击公式**：选中公式并打开编辑弹窗
   - **状态清晰**：选中高亮提供明确的视觉反馈
   - **功能完整**：支持复制、删除、编辑等所有操作
7. **智能弹窗管理**：删除公式时自动关闭编辑弹窗

### 技术特色

1. **逐段解析机制**：避免段落分隔符干扰，确保公式完整性
2. **绝对位置计算**：正确处理跨段落的位置映射
3. **智能边界检测**：支持连续公式的复杂导航场景
4. **高性能装饰器**：使用ProseMirror装饰器实现高效渲染
5. **四方向键盘支持**：水平导航支持公式感知，垂直导航实现段落跳转
6. **选中状态管理**：正确处理选中和光标状态的互斥关系
7. **双重事件覆盖**：ProseMirror 和装饰器层面都支持选中式交互

### 用户体验

- ✅ 公式插入无多余字符
- ✅ 光标可在任意位置精确定位
- ✅ 方向键导航流畅自然
- ✅ 换行操作不影响公式完整性
- ✅ 连续公式间智能跳转
- ✅ 上下导航实现段落间跳转
- ✅ 公式更新和删除功能完善
- ✅ 单击公式实现选中式交互：选中公式并打开编辑弹窗
- ✅ 选中高亮提供清晰的视觉反馈，支持复制删除操作
- ✅ 删除公式时弹窗自动关闭，避免显示无效内容
- ✅ 交互逻辑简单清晰，符合编辑器使用习惯

这套解决方案彻底解决了原有的边界处理、字符多余、导航卡顿、换行分割等所有问题，同时提供了丰富的交互方式，为用户提供了流畅完整的数学公式编辑体验。

## 阶段十二：选中公式并打开弹窗交互

### 问题描述

在前期实现中发现了一个重要的技术冲突：**选中公式和光标移动不能同时存在**。

在 ProseMirror 中，文本选中状态（TextSelection）和光标位置是互斥的：

- 当选中一段文本时，光标状态会被选中状态覆盖
- 当移动光标时，选中状态会被清除

因此，原本设想的"同时选中公式、移动光标到公式后面、打开弹窗"的一体化交互在技术上不可行。

### 解决方案

采用更实用的交互方式：**点击公式 → 选中公式 + 打开弹窗**

这种方式的优势：

1. **技术可行**：避免了状态冲突问题
2. **功能完整**：用户可以直接复制、删除公式，也可以在弹窗中编辑
3. **操作直观**：选中状态提供清晰的视觉反馈
4. **灵活性强**：如果需要光标在公式后面，用户可以按右箭头键或点击公式后面

#### 1. 简化 ProseMirror 点击处理

```javascript
// 简化点击处理 - 选中公式并打开弹窗
handleClick: (view, pos, event) => {
  const { state } = view;
  const context = getFormulaContextFromState(state, pos);

  // 点击公式内部时处理
  if (context.type === 'inside') {
    event.preventDefault();

    try {
      // 选中整个公式
      const selection = TextSelection.create(
        state.doc,
        context.formula.start,
        context.formula.end,
      );
      const tr = state.tr.setSelection(selection);
      view.dispatch(tr);

      // 延迟打开弹窗，让选中效果先显示
      setTimeout(() => {
        try {
          // 触发菜单栏的公式点击处理（打开弹窗）
          const { start, end } = context.formula;
          const content = state.doc.textBetween(start, end);
          emitter.emit('handle-formula-click', {
            content,
            pos: start,
            end,
          });
        } catch (error) {
          console.warn('打开弹窗失败:', error);
        }
      }, 100); // 100ms 延迟，让选中效果显示

    } catch (error) {
      console.warn('选中公式失败:', error);
    }

    return true;
  }

  return false;
},
```

#### 2. 简化装饰器点击处理

```javascript
// 点击事件 - 选中公式并打开弹窗
container.addEventListener('click', (e) => {
  e.stopPropagation();

  try {
    // 选中整个公式
    const { state } = view;
    const selection = TextSelection.create(state.doc, absoluteStart, absoluteEnd);
    const tr = state.tr.setSelection(selection);
    view.dispatch(tr);

    // 延迟打开弹窗，让选中效果先显示
    setTimeout(() => {
      try {
        // 触发编辑弹窗
        emitter.emit('handle-formula-click', {
          content: formula.content,
          pos: absoluteStart,
          end: absoluteEnd,
        });
      } catch (error) {
        console.warn('打开弹窗失败:', error);
      }
    }, 100); // 100ms 延迟，让选中效果显示
  } catch (error) {
    console.warn('选中公式失败:', error);
  }
});
```

### 核心技术实现

#### 1. 移除双击检测

- **删除复杂的时间检测逻辑**：移除 `clickTimer`、`lastClickTime` 等状态管理
- **简化事件处理**：所有公式点击都执行相同的逻辑
- **提高可靠性**：减少状态管理的复杂性和潜在错误

#### 2. 选中状态管理

- **立即选中**：点击后立即选中公式，提供即时视觉反馈
- **状态一致性**：确保选中状态在 ProseMirror 和装饰器层面都正确设置
- **错误处理**：为选中操作添加完善的错误处理

#### 3. 弹窗时序控制

- **延迟打开**：100ms 延迟确保选中效果先显示
- **异步处理**：避免选中和弹窗操作相互干扰
- **事件解耦**：通过事件系统实现模块间解耦

### 交互流程

1. **用户点击公式**
2. **立即选中**：公式被选中，显示蓝色高亮效果
3. **100ms 后**：编辑弹窗打开
4. **用户可以**：
   - **直接复制**：使用 Ctrl+C 复制选中的公式
   - **直接删除**：使用 Delete/Backspace 删除选中的公式
   - **编辑公式**：在弹窗中修改公式内容
   - **移动光标**：按右箭头键将光标移到公式后面

### 用户体验改进

- ✅ **视觉反馈明确**：点击后立即看到选中高亮，用户知道操作成功
- ✅ **功能完整覆盖**：一次点击满足复制、删除、编辑等主要需求
- ✅ **操作逻辑简单**：所有公式点击行为一致，无需记忆不同操作
- ✅ **技术实现稳定**：避免状态冲突，提高交互可靠性
- ✅ **性能优化显著**：移除复杂的双击检测，减少计算开销

### 设计优势

1. **技术合理性**：符合 ProseMirror 的状态管理模型
2. **用户直觉性**：选中 → 操作的流程符合用户习惯
3. **功能完整性**：覆盖用户的主要操作需求
4. **维护简便性**：代码逻辑清晰，易于理解和维护
5. **扩展灵活性**：为未来功能扩展提供良好基础

### 技术洞察

这次修改揭示了一个重要的技术原理：**在富文本编辑器中，选中状态和光标位置是互斥的**。这是 ProseMirror 等编辑器的基本设计原则，任何试图同时维护两种状态的尝试都会导致冲突。

正确的做法是：

- **根据用户需求选择合适的状态**
- **通过键盘操作实现状态切换**
- **设计清晰的交互流程指导用户**

这种设计不仅技术上更加稳定，也更符合用户对文本编辑器的认知模型。

## 扩展建议

1. **公式编辑器**：集成可视化公式编辑器
2. **公式模板**：提供常用公式模板
3. **公式搜索**：支持公式内容搜索
4. **导出功能**：支持公式导出为图片或其他格式
5. **三击交互**：可以考虑添加三击进入专门的公式编辑模式
6. **手势支持**：在触屏设备上支持长按、双指缩放等手势
7. **快捷键增强**：添加更多公式相关的快捷键操作

---
