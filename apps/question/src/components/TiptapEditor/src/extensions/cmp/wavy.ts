import { Mark, mergeAttributes } from '@tiptap/core';

export const Wavy = Mark.create({
  name: 'wavy',
  addAttributes() {
    return {
      class: {
        default: 'wavy',
      },
    };
  },
  // @ts-ignore
  parseHTML() {
    return [
      {
        tag: 'span',
        getAttrs: (element) => {
          return element.getAttribute('class')?.indexOf('wavy') > -1;
        },
      },
    ];
  },
  renderHTML({ HTMLAttributes }) {
    return ['span', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0];
  },
  // @ts-ignore
  addCommands() {
    return {
      setWavy:
        () =>
        ({ commands }) => {
          return commands.setMark(this.name);
        },
      toggleWavy:
        () =>
        ({ commands }) => {
          return commands.toggleMark(this.name);
        },
      unsetWavy:
        () =>
        ({ commands }) => {
          return commands.unsetMark(this.name);
        },
    };
  },
});
