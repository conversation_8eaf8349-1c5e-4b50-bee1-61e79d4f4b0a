import { DOMSerializer, Fragment } from 'prosemirror-model';
import { Plugin } from 'prosemirror-state';
import { generateUUID } from '../../../../utils';

export interface CopyHandlerOptions {
  onCopy?: (data: { html: string; json: any; text: string; event: ClipboardEvent }) => void;
  questionNodeName?: string;
}

// 递归替换 fragment 中所有 question 节点的 uuid
function replaceQuestionUuid(fragment: any, questionNodeName: string): any {
  const children: any[] = [];
  fragment.forEach((child: any) => {
    if (child.type.name === questionNodeName) {
      children.push(
        child.type.create(
          { ...child.attrs, 'data-fill-uuid': generateUUID() },
          child.content,
          child.marks,
        ),
      );
    } else if (child.content && child.childCount) {
      children.push(child.copy(replaceQuestionUuid(child.content, questionNodeName)));
    } else {
      children.push(child);
    }
  });
  return Fragment.fromArray(children);
}

export function createCopyHandlerPlugin(options: CopyHandlerOptions = {}) {
  const questionNodeName = options.questionNodeName || 'question';
  return new Plugin({
    props: {
      handleDOMEvents: {
        copy: (view, event) => {
          const { state } = view;
          const { from, to } = state.selection;
          if (from === to) return false;
          const slice = state.doc.slice(from, to);
          const fragment = slice.content;
          const schema = state.schema;

          // 替换 uuid
          const replacedFragment = replaceQuestionUuid(fragment, questionNodeName);
          // 健壮处理 fragment：inline 合并为 paragraph，block 直接加进 doc
          const blocks: any[] = [];
          let inlineBuffer: any[] = [];
          replacedFragment.forEach((child: any) => {
            if (child.isInline) {
              inlineBuffer.push(child);
            } else {
              if (inlineBuffer.length) {
                blocks.push(schema.nodes.paragraph.create(null, inlineBuffer));
                inlineBuffer = [];
              }
              blocks.push(child);
            }
          });
          if (inlineBuffer.length) {
            blocks.push(schema.nodes.paragraph.create(null, inlineBuffer));
          }
          const tempDoc = schema.node('doc', null, blocks);
          if (!tempDoc) return false;

          const serializer = DOMSerializer.fromSchema(schema);
          console.log('serializer:', serializer);

          // HTML
          let html = '';
          try {
            const serializer = DOMSerializer.fromSchema(schema);
            const doc = document.implementation.createHTMLDocument('clipboard');
            const fragment = serializer.serializeFragment(tempDoc.content, { document: doc });
            doc.body.appendChild(fragment);
            html = doc.body.innerHTML;
            console.log('html==>: ', html);
          } catch (e) {
            console.log('生成html出错: ', e);
          }
          // JSON
          const json = tempDoc.toJSON();
          // Text
          const text = tempDoc.textContent;
          console.log('html: ', html, 'json: ', json, 'text: ', text);

          // 阻止默认 copy 行为，并写入我们生成的新内容
          event.preventDefault();
          if (event.clipboardData) {
            event.clipboardData.setData('text/html', html);
            event.clipboardData.setData('text/plain', text);
          }
          // 调用插件 options.onCopy 回调
          if (typeof options.onCopy === 'function') {
            options.onCopy({ html, json, text, event });
          }
          return true; // 告诉 ProseMirror 已处理 copy
        },
      },
    },
  });
}
