import { Editor, Extension } from '@tiptap/core';
import renderMathInElement from 'katex/contrib/auto-render';
import 'katex/contrib/mhchem/mhchem';
import { Plugin } from 'prosemirror-state';
import { Decoration, DecorationSet } from 'prosemirror-view';
// import { TextSelection } from 'prosemirror-state';
import emitter from '../../../utils/emitter';

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    formula: {
      insertFormula: (latex: string) => ReturnType;
      updateFormula: (pos: number, end: number, latex: string) => ReturnType;
    };
  }
}

/**
 * 替换字符串中指定位置的子串
 * @param original 原始字符串
 * @param start 开始位置（从0开始）
 * @param length 要替换的长度
 * @param replacement 替换的字符串
 * @returns 替换后的新字符串
 */
function replaceSubstring(
  original: string,
  start: number,
  length: number,
  replacement: string,
): string {
  // 参数校验
  if (start < 0 || start >= original.length) {
    return original;
  }
  if (length < 0 || start + length > original.length) {
    return original;
  }

  // 拼接字符串：前部分 + 替换内容 + 后部分
  return original.substring(0, start) + replacement + original.substring(start + length);
}

export const Formula = Extension.create({
  name: 'formula',

  addCommands() {
    return {
      insertFormula:
        (latex: string) =>
        ({ editor }: { editor: Editor }) => {
          console.log('insertFormula', latex);
          return editor.commands.insertContent(latex);
        },
      updateFormula:
        (pos: number, end: number, latex: string) =>
        ({ editor }: { editor: Editor }) => {
          console.log(111, pos, end, latex);

          // 先删除旧内容
          editor.commands.deleteRange({ from: pos, to: end });
          // 再插入新内容
          return editor.commands.insertContentAt(pos, latex);
        },
    };
  },

  addProseMirrorPlugins() {
    return [
      new Plugin({
        props: {
          decorations: (state) => {
            const decorations: Decoration[] = [];

            state.doc.descendants((node, pos) => {
              if (node.isText) {
                let text = node.text || '';

                // 定义各类 LaTeX 匹配规则
                const rules: { regex: RegExp; type: string }[] = [
                  { regex: /(\\\(.*?\\\))/g, type: 'inline' }, // 匹配 \( ... \)
                  { regex: /(\\\[[\s\S]*?\\\])/g, type: 'block' }, // 匹配 \[ ... \]
                  { regex: /(\$\$[\s\S]*?\$\$)/g, type: 'block' }, // 匹配 $$ ... $$
                  { regex: /(\$(?!\$).*?(?<!\\)\$)/g, type: 'inline' }, // 匹配 $...$，避免 $$ 和转义 $
                  { regex: /(\\rm\{[\s\S]*?\})/g, type: 'rm' }, // 匹配 \rm{ ... }
                ];

                // 遍历所有规则
                for (const rule of rules) {
                  let match;
                  while ((match = rule.regex.exec(text)) !== null) {
                    const formulaContent = match[1];
                    const start = pos + match.index;
                    const end = start + match[0].length;

                    // 使用 inline 装饰器来隐藏原始文本
                    decorations.push(
                      Decoration.inline(start, end, {
                        style: 'display: none;',
                      }),
                    );

                    // 使用 widget 装饰器来显示渲染后的公式
                    const decoration = Decoration.widget(
                      start,
                      () => {
                        const container = document.createElement('span');
                        container.className = 'formula-container';
                        container.style.cssText = `display: ${rule.type === 'block' ? 'block' : 'inline-block'}; position: relative; cursor: pointer;`;
                        container.setAttribute('data-formula-start', start.toString());
                        container.setAttribute('data-formula-end', end.toString());

                        const formula = document.createElement('span');
                        formula.className = 'formula-decoration';
                        formula.style.cssText =
                          rule.type === 'block' ? 'display: block;' : 'display: inline-block;';

                        // 创建临时容器
                        const temp = document.createElement('div');
                        temp.innerHTML = formulaContent;

                        // 使用 KaTeX 渲染这个容器
                        renderMathInElement(temp, {
                          delimiters: [
                            { left: '$$', right: '$$', display: true },
                            { left: '$', right: '$', display: false },
                            { left: '\\(', right: '\\)', display: false },
                            { left: '\\[', right: '\\]', display: true },
                          ],
                          throwOnError: false,
                        });

                        // 获取渲染后的 HTML 字符串
                        formula.innerHTML = temp.innerHTML;

                        // 添加点击事件
                        container.addEventListener('click', (event) => {
                          event.stopPropagation();
                          emitter.emit('formula-click', {
                            content: formulaContent,
                            pos: start,
                            end,
                          });
                        });

                        container.appendChild(formula);
                        return container;
                      },
                      {
                        selectable: true,
                        side: 1, // 将 widget 放在文本后面，这样光标可以显示在公式后面
                        marks: [],
                        relaxedSide: true,
                      },
                    );

                    decorations.push(decoration);
                    text = replaceSubstring(
                      text,
                      match.index,
                      match[0].length,
                      '_'.repeat(match[0].length),
                    );
                  }
                }
              }
              return true;
            });

            return DecorationSet.create(state.doc, decorations);
          },
        },
      }),

      // 添加事件处理插件
      new Plugin({
        props: {
          handleDOMEvents: {
            keydown: (view, event) => {
              const { state } = view;
              const { selection } = state;
              const { $from } = selection;

              console.log('handleDOMEvents: ', view, $from);
              console.log('event: ', event);
              console.log('cursor position: ', $from.pos);

              // 检查当前光标位置是否在公式装饰器内
              const formulaElements = view.dom.querySelectorAll('.formula-container');
              let currentFormula = null;

              // 遍历所有公式，找到当前光标所在的公式
              for (let i = 0; i < formulaElements.length; i++) {
                const formulaElement = formulaElements[i];
                const start = parseInt(formulaElement.getAttribute('data-formula-start') || '0');
                const end = parseInt(formulaElement.getAttribute('data-formula-end') || '0');

                console.log(
                  `formula ${i}: start=${start}, end=${end}, cursor=${$from.pos}, in range: ${$from.pos >= start && $from.pos <= end}`,
                );

                // 检查光标是否在公式范围内（包括 widget 装饰器）
                // 添加小的容差来处理边界情况
                const tolerance = 1;
                const inRange = $from.pos >= start - tolerance && $from.pos <= end + tolerance;
                console.log(
                  `formula ${i}: tolerance check - start-${tolerance}=${start - tolerance}, end+${tolerance}=${end + tolerance}, inRange=${inRange}`,
                );

                if (inRange) {
                  currentFormula = { element: formulaElement, start, end, index: i };
                  console.log(
                    `Found current formula: index=${i}, start=${start}, end=${end}, cursor=${$from.pos}`,
                  );
                  break;
                }
              }

              if (currentFormula) {
                const { start, end, index } = currentFormula;
                console.log('currentFormula: ', currentFormula, index);
                console.log(
                  `Processing key: ${event.key}, cursor at ${$from.pos}, formula range: ${start}-${end}`,
                );

                // 处理删除键
                if (event.key === 'Backspace') {
                  // 如果光标在公式内部，删除整个公式
                  const tr = state.tr.delete(start, end);
                  view.dispatch(tr);
                  return true;
                }

                //处理方向键
                // if (event.key === 'ArrowLeft') {
                //   console.log(`ArrowLeft: cursor=${$from.pos}, start=${start}, end=${end}, index=${index}`);
                //   console.log(`Condition check: $from.pos > start = ${$from.pos > start}`);
                //   console.log('node =====<<<<<', state.doc.nodeAt(start), state.doc.resolve(start));

                //   // 如果光标在公式内部，移动到公式开始位置
                //   // if ($from.pos > start) {
                //   //   console.log('node =====<<<<<', state.doc.nodeAt(start), state.doc.resolve(start));
                //   //   const tr = state.tr.setSelection(TextSelection.near(state.doc.resolve(start)));
                //   //   console.log('左移到公式开始: ', tr, start, state.doc);
                //   //   view.dispatch(tr);
                //   //   return true;
                //   // }
                // }

                // if (event.key === 'ArrowRight') {
                //   console.log(`ArrowRight: cursor=${$from.pos}, end=${end}, index=${index}`);
                //   // 如果光标在公式内部，移动到公式结束位置
                //   if ($from.pos <= end) {

                //     const movePos = state.doc.resolve(start);
                //     const nearPos = TextSelection.near(movePos, 1);
                //     console.log('=====>', movePos, nearPos, movePos.nodeAfter);
                //     const tr = state.tr.setSelection(nearPos);
                //     // console.log('右移到公式结束: ', tr, end);
                //     view.dispatch(tr);
                //     return true;
                //   }
                // }
              }

              return false;
            },
          },
        },
      }),
    ];
  },
});
