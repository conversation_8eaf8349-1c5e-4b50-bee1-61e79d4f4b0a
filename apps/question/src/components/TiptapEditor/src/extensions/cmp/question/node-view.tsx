/* eslint-disable */
import { NodeViewWrapper } from '@tiptap/react';
import QuestionView from './view';

const NodeView = ({ view, node, updateAttributes, getPos, editor }: any) => {
  return (
    <NodeViewWrapper as="span" contentEditable={false}>
      <QuestionView
        index={node.attrs['data-index']}
        uuid={node.attrs['data-fill-uuid']}
        editor={editor}
        getPos={getPos}
      />
    </NodeViewWrapper>
  );
};

export default NodeView;
