import { mergeAttributes, Node } from '@tiptap/core';

export const Horizontal = Node.create({
  name: 'horizontal',
  group: 'block',

  addAttributes() {
    return {
      class: {
        default: 'solid',
      },
    };
  },

  parseHTML() {
    return [{ tag: 'hr' }];
  },

  renderHTML({ HTMLAttributes }) {
    return ['hr', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes)];
  },
  // @ts-ignore
  addCommands() {
    return {
      insertHr:
        (arg = 'solid') =>
        (editor: any) => {
          const myNode = {
            type: this.name,
            attrs: {
              class: arg,
            },
          };

          return editor
            .chain()
            .insertContent([
              myNode,
              { type: 'paragraph' }, // 可选：添加段落用于继续编辑
            ])
            .run();
        },
    };
  },

  // 确保被视为 void 节点
  atom: true,
  selectable: false,
  draggable: false,
});
