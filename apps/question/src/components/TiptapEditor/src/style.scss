.editable {
  min-width: 800px;
  background-color: white;
  border: rgba($color: #000000, $alpha: 0.1) solid 1px;
  border-radius: 0 0 6px 6px !important;
  .menuBar {
    background-color: white;
    border-bottom: none;
    border-radius: 6px 6px 0 0 !important;
  }
}

.tiptap-editor {
  position: relative;
  box-sizing: border-box;
  padding: 4px;
  .menuBar {
    position: absolute;
    right: -1px;
    left: -1px;
    z-index: 100;
    display: flex;
    flex-shrink: 0;
    flex-wrap: wrap;
    align-items: center;
    padding: 4px 0;
    // background-color: #fff;
    border: 1px rgba($color: #000000, $alpha: 0.1) solid;

    .group {
      display: flex;
      align-items: center;
      &::after {
        width: 1px;
        height: 16px;
        margin: 0 6px;
        background-color: rgba($color: #000000, $alpha: 0.1);
        content: '';
      }

      &:last-child {
        &::after {
          display: none;
        }
      }
      & > * {
        height: 26px;
        margin: 0 1px;
        padding: 0px 6px;
        border-radius: 2px;
        svg,
        img {
          width: 16px;
          height: 26px;
        }
      }
    }
  }

  .editorContent {
    display: flex;
    flex: 1;
    max-height: 400px;

    overflow: auto;

    > *:first-child {
      padding-top: 4px;
    }

    // h1,
    // h2,
    // h3,
    // h4,
    // h5,
    // h6 {
    //   margin-top: 0;
    //   margin-bottom: 0.5em;
    //   font-weight: 500;
    //   font-size: initial;
    // }

    // 标题样式
    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
      margin: 5px 0 !important;
      font-weight: 500;
      line-height: 1.1;
      text-wrap: pretty;
    }

    h1 {
      font-size: 1.4rem;
    }

    h2 {
      font-size: 1.2rem;
    }

    h3 {
      font-size: 1.1rem;
    }

    h4,
    h5,
    h6 {
      font-size: 1rem;
    }

    .ProseMirror {
      width: 100%;
      // 一下三个属性 就是自适应剩余父元素的高度 将剩余的空间铺满
      height: 100%;

      // 确保空白字符正确显示
      white-space: pre-wrap;
      outline: none;

      // padding: 10px;
      > * + * {
        margin-top: 0.75em;
      }

      ul,
      ol {
        padding: 0 1rem;
      }

      h1,
      h2,
      h3,
      h4,
      h5,
      h6 {
        line-height: 1.1;
      }

      code {
        color: #616161;
        background-color: rgba(#616161, 0.1);
      }

      pre {
        padding: 0.75rem 1rem;
        color: #fff;
        font-family: 'JetBrainsMono', monospace;
        background: #0d0d0d;
        border-radius: 0.5rem;

        code {
          padding: 0;
          color: inherit;
          font-size: 0.8rem;
          background: none;
        }
      }

      img {
        max-width: 100%;
        height: auto;
      }

      blockquote {
        padding-left: 1rem;
        border-left: 2px solid rgba(#0d0d0d, 0.1);
      }

      hr {
        margin: 2rem 0;
        border: none;
        border-top: 2px solid rgba(#0d0d0d, 0.1);
      }

      p.is-editor-empty:first-child::before {
        float: left;
        height: 0;
        color: #adb5bd;
        content: attr(data-placeholder);
        pointer-events: none;
      }

      //  自定义样式
      .solid {
        border-top: 1px solid black !important;
      }
      .dashed {
        border-top: 1px dashed black !important;
      }

      .dot {
        text-emphasis: dot;
        text-emphasis-position: under left;
      }

      .wavy {
        text-decoration: underline wavy;
      }

      /* 可以放在你的全局 CSS 或组件样式文件中 */
      table {
        width: 100%;
        border-collapse: collapse;
      }

      th,
      td {
        padding: 2px 8px;
        border: 1px solid #ddd;
      }

      th {
        text-align: left;
        background-color: #f2f2f2;
      }

      math-field[readonly] {
        border: none; /* 去掉边框 */
        outline: none; /* 去掉聚焦时的轮廓 */
      }

      math-field[editable] {
        border: 1px solid rgba($color: #000000, $alpha: 0.1);
        border-radius: 2px;
      }

      @media not (pointer: coarse) {
        math-field::part(virtual-keyboard-toggle) {
          display: none;
        }
        math-field::part(menu-toggle) {
          display: none;
        }
      }
    }
  }
}

// .is-input {
//   width: 300px;
//   min-height: 32px;
//   padding: 4px 11px;
//   color: rgba(0, 0, 0, 0.88);
//   font-size: 14px;
//   line-height: 1.5715;
//   background: #fff;
//   border: 1px solid #d9d9d9;
//   border-radius: 6px;
//   cursor: pointer;
//   transition: border-color 0.2s;
//   &:hover {
//     border-color: #1890ff;
//   }
//   p {
//     margin: 0;
//   }
// }

// .is-textarea {
//   min-height: 80px;
//   padding: 6px 11px;
//   color: rgba(0, 0, 0, 0.88);
//   font-size: 14px;
//   line-height: 1.5715;
//   white-space: pre-wrap;
//   background: #fff;
//   border: 1px solid #d9d9d9;
//   border-radius: 6px;
//   cursor: pointer;
//   transition: border-color 0.2s;
//   &:hover {
//     border-color: #1890ff;
//   }
// }

// math-filed 的软键盘隐藏
math-field::part(virtual-keyboard-toggle),
math-field::part(menu-toggle) {
  display: none !important;
}

// math-filed 的边框隐藏
math-field {
  // border: none !important;
  border: 1px solid rgba($color: #000000, $alpha: 0.08);
  outline: none !important;
}
