/* eslint-disable */
import { Button, Tooltip } from 'antd';
import { useState } from 'react';
// @ts-ignore
import ohmImg from '../../../assets/ohm.svg';
import { useEditorConfig } from '../../../config-ctx';
import { Feature } from '../../../utils/enum';

const InsertQs = () => {
  const config = useEditorConfig();
  const editor = config.editor!;
  if (!config.features.includes(Feature.insertQs)) return null;
  const [open, setOpen] = useState(false);

  const ok = () => {
    // @ts-ignore
    editor.chain().focus().insertQs().run();
    config.onInsertQs && config.onInsertQs();
  };

  return (
    <Tooltip title="插入作答区">
      <Button
        onMouseDown={(e) => e.preventDefault()}
        onClick={ok}
        color="default"
        variant="filled"
        autoInsertSpace
      >
        <img src={ohmImg} style={{ width: 18 }} />
      </Button>
    </Tooltip>
  );
};
InsertQs.id = 'insertQs';
export default InsertQs;
