.formula-editor-container {
  width: 600px;

  math-field {
    width: 100%;
    border-radius: 6px;
    min-height: 50px;
  }
}

.custom-tabpanel {
  display: flex;
  flex-wrap: wrap;
  // width: 360px;
  height: 160px;
  box-sizing: border-box;
  background-color: rgba($color: #000000, $alpha: 0.04);
  align-items: flex-start; // 默认值为 stretch，意味着子元素会在交叉轴上拉伸
  align-content: flex-start;
  gap: 10px;
  overflow-y: auto;
  padding: 10px;
  .formula-item {
    position: relative;
    .formula-item-mask {
      z-index: 1;
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      cursor: pointer;
    }
  }
}

.mf-preview {
  margin-top: 10px;
  width: 100%;
  display: flex;
  align-items: center;
  border-bottom: rgba($color: #000000, $alpha: 0.1) solid 1px;
  .mf-preview-title {
    color: rgba($color: #000000, $alpha: 0.6);
    width: 60px;
  }
  .mf {
    overflow-x: auto;
    padding: 2px;
  }
}

.mf-preview-input {
  .mf-preview-input-title {
    color: rgba($color: #000000, $alpha: 0.6);
    width: 60px;
    margin-right: 10px;
  }
  width: 100%;
  display: flex;
  // align-items: center;
  min-height: 50px;
  overflow-y: auto;
  padding: 2px;
  border-bottom: rgba($color: #000000, $alpha: 0.1) solid 1px;
  textarea {
    // outline: none;
    // border: none !important;
    width: 100%;
    border: 1px solid rgba($color: #000000, $alpha: 0.1);
    border-radius: 2px;
  }
}
.mf-preview-input-btn {
  text-align: right;
  margin-top: 10px;
}