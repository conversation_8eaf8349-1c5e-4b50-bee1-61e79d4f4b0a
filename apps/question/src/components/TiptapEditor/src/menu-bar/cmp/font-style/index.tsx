import { <PERSON><PERSON><PERSON><PERSON>, RiEmphasisCn, <PERSON><PERSON><PERSON><PERSON><PERSON>, R<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, RiUnderline } from '@remixicon/react';
import type { Editor } from '@tiptap/react';
import { <PERSON><PERSON>, Tooltip } from 'antd';
import { useEditorConfig } from '../../../config-ctx';
// 文本波浪线图标
const Wavy = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M18.5 3.04883C18.5 6.38214 18.5 7.66699 18.5 11.0003C18.5 14.5902 15.5898 17.5003 12 17.5003C8.41015 17.5003 5.5 14.5902 5.5 11.0003C5.5 7.66699 5.5 6.38214 5.5 3.04883"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
    />
    <path
      d="M3 21L6.00002 23L8.99998 21L12 23L15 21L18 23L21 21"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);
const buttonGroupTemp: Array<any> = [
  {
    id: 'bold',
    icon: RiBold,
    action: (editor: Editor) => {
      editor.chain().focus().toggleBold().run();
      // editor.commands.focus();
    },
    isActive: (editor: Editor) => editor.isActive('bold'),
    canExecute: (editor: Editor) =>
      editor.can().chain().focus().toggleBold().run() && !editor.isActive('codeBlock'),
    tooltip: '粗体',
  },
  {
    id: 'italic',
    icon: RiItalic,
    action: (editor: Editor) => editor.chain().focus().toggleItalic().run(),
    isActive: (editor: Editor) => editor.isActive('italic'),
    canExecute: (editor: Editor) =>
      editor.can().chain().focus().toggleItalic().run() && !editor.isActive('codeBlock'),
    tooltip: '斜体',
  },
  {
    id: 'underline',
    icon: RiUnderline,
    action: (editor: Editor) => editor.chain().focus().toggleUnderline().run(),
    isActive: (editor: Editor) => editor.isActive('underline'),
    canExecute: (editor: Editor) =>
      editor.can().chain().focus().toggleUnderline().run() && !editor.isActive('codeBlock'),
    tooltip: '下划线',
  },
  {
    id: 'wavy',
    icon: Wavy,
    action: (editor: Editor) => (editor as any).chain().focus().toggleWavy().run(),
    isActive: (editor: Editor) => editor.isActive('wavy'),
    canExecute: (editor: Editor) =>
      (editor as any).can().chain().focus().toggleWavy().run() && !editor.isActive('codeBlock'),
    tooltip: '波浪线',
  },
  {
    id: 'strike',
    icon: RiStrikethrough,
    action: (editor: Editor) => editor.chain().focus().toggleStrike().run(),
    isActive: (editor: Editor) => editor.isActive('strike'),
    canExecute: (editor: Editor) =>
      editor.can().chain().focus().toggleStrike().run() && !editor.isActive('codeBlock'),
    tooltip: '删除线',
  },
  {
    id: 'dot',
    icon: RiEmphasisCn,
    action: (editor: Editor) => (editor as any).chain().focus().toggleDot().run(),
    isActive: (editor: Editor) => editor.isActive('dot'),
    canExecute: (editor: Editor) =>
      (editor as any).can().chain().focus().toggleDot().run() && !editor.isActive('codeBlock'),
    tooltip: '强调',
  },
];

const FontStyle = () => {
  const config = useEditorConfig();
  const editor = config.editor!;

  const buttonGroup = buttonGroupTemp.filter((item) => config.features.includes(item.id));
  if (!buttonGroup.length) return null;

  return (
    <div className="group">
      {buttonGroup.map(({ icon: Icon, tooltip, isActive, action, id }) => (
        <Tooltip title={tooltip} key={id}>
          <Button
            onMouseDown={(e) => e.preventDefault()}
            onClick={() => action(editor)}
            color="default"
            variant={isActive(editor) ? 'solid' : 'filled'}
            autoInsertSpace
          >
            <Icon />
          </Button>
        </Tooltip>
      ))}
    </div>
  );
};

export default FontStyle;
