.color-picker{
    .ant-popover-inner{
        padding: 0;
    }
}
.color-menu {
    // background: #fff;
    // border: 1px solid #ddd;
    // padding: 8px;
    width: 260px;
    border-radius: 6px;
    // font-size: 12px;
    // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.12);
  
    .color-grid {
      display: grid;
      grid-template-columns: repeat(8, 1fr);
      gap: 4px;
      margin-bottom: 10px;
  
      .color-swatch {
        width: 16px;
        height: 16px;
        border: 1px solid #bbb;
        border-radius: 2px;
        cursor: pointer;
      }
    }
  
    .custom-picker {
      margin-top: 8px;
  
      .input-row {
        display: flex;
        align-items: center;
        gap: 6px;
        margin-top: 6px;
  
        input {
          width: 100px;
          padding: 4px;
          font-size: 12px;
          border: 1px solid #ccc;
          border-radius: 4px;
        }
  
        button {
          padding: 4px 8px;
          font-size: 12px;
          border: none;
          border-radius: 4px;
          background-color: #eee;
          cursor: pointer;
  
          &:hover {
            background-color: #ddd;
          }
        }
      }
    }
  }