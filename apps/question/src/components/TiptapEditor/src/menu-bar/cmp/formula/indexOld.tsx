/* eslint-disable */
import { RiFormula } from '@remixicon/react';
import { Button, Checkbox, Flex, Input, Popover, Tabs, TabsProps, Typography } from 'antd';
import { useEffect, useMemo, useState } from 'react';
import { useEditorConfig } from '../../../config-ctx';
import emitter from '../../../utils/emitter';
import { Feature } from '../../../utils/enum';
import presets from './latex-presets';
import './style.scss';
import { FormulaRegex, formulaRegexs } from './util';
const { TextArea } = Input;
const { Text } = Typography;

// @ts-ignore
const FormulaContent = ({ editor, onClose, pos, end, mfPreviewVal, setMfPreviewVal }) => {
  const [isBlock, setIsBlock] = useState(false);
  const [formulaRegex, setFormulaRegex] = useState<FormulaRegex | null>(null);
  const [formulaContent, setFormulaContent] = useState('');
  const onChange = (key: string) => {
    // console.log(key);
  };

  const onClickItem = (value: any) => {
    console.log('onClickItem', value);
    setFormulaContent(formulaContent + value.latex);
  };

  const newPreviewVal = useMemo(() => {
    let start = '';
    let end = '';
    if (
      (isBlock && formulaRegex?.tag === 'block') ||
      (!isBlock && formulaRegex?.tag === 'inline')
    ) {
      start = formulaRegex.start;
      end = formulaRegex.end;
    } else {
      start = isBlock ? '$$' : '\\(';
      end = isBlock ? '$$' : '\\)';
    }
    return formulaContent ? start + formulaContent + end : '';
  }, [formulaRegex, formulaContent, isBlock]);

  const insertFormula = () => {
    try {
      if (pos) {
        editor.commands.updateFormula(pos, end, newPreviewVal);
      } else {
        editor.commands.insertFormula(newPreviewVal);
      }
    } catch (error) {
      // console.log(error);
    }
    onClose();
  };

  const items: TabsProps['items'] = presets.map((item) => ({
    key: item.key,
    label: item.label,
    children: (
      <div className="custom-tabpanel" key={item.key}>
        {item.content.map((it) => (
          <div className="formula-item" key={it.latex}>
            <div
              className="formula-item-mask"
              onClick={() => onClickItem(it)}
              onMouseDown={(e) => e.preventDefault()}
            ></div>
            <math-field contentEditable={false}>{it.latex}</math-field>
          </div>
        ))}
      </div>
    ),
  }));

  useEffect(() => {
    if (mfPreviewVal) {
      const regex = formulaRegexs.find((item: FormulaRegex) => mfPreviewVal.match(item.regex));
      console.log('effect regex: ', regex);
      if (regex) {
        setFormulaRegex(regex);
        const matchs = mfPreviewVal.match(regex.regex);
        console.log('matchs: ', matchs, matchs[1]);
        setFormulaContent(matchs[1] ?? '');
        setIsBlock(regex.tag === 'block');
        return;
      }
    }
    setFormulaRegex(null);
    setFormulaContent('');
    setIsBlock(false);
  }, [mfPreviewVal]);

  useEffect(() => {
    console.log('newPreviewVal ====>: ', newPreviewVal);
  }, [newPreviewVal]);

  const onMathFieldInput = (evt: any) => {
    console.log('编辑预览的输入: ', (evt.target as HTMLInputElement)?.value);
    setFormulaContent((evt.target as HTMLInputElement)?.value);
  };

  return (
    <Flex vertical gap={10} className="formula-editor-container no-blur">
      <Tabs
        defaultActiveKey="1"
        items={items}
        onChange={onChange}
        onMouseDown={(e) => e.preventDefault()}
      />
      <Flex>
        <Text>预览：</Text>
        <div className="flex-1 mf">
          <math-field onInput={onMathFieldInput}>{formulaContent}</math-field>
        </div>
      </Flex>
      <Flex className=" no-blur">
        <Text>源码：</Text>
        <div className="flex-1">
          <TextArea
            value={formulaContent}
            onChange={(evt) => setFormulaContent(evt.target.value)}
          />
        </div>
      </Flex>
      <Flex justify="space-between" align="center">
        <Checkbox checked={isBlock} onChange={(e) => setIsBlock(e.target.checked)}>
          块级显示
        </Checkbox>
        <Button type="primary" onClick={insertFormula} onMouseDown={(e) => e.preventDefault()}>
          插入
        </Button>
      </Flex>
    </Flex>
  );
};

const Formula = () => {
  const config = useEditorConfig();
  const editor = config.editor!;
  if (!config.features.includes(Feature.formula)) return null;

  const [open, setOpen] = useState(false);
  const [pos, setPos] = useState(0);
  const [end, setEnd] = useState(0);
  const [mfPreviewVal, setMfPreviewVal] = useState('');

  const handleOpenChange = (newOpen: boolean) => {
    setOpen(newOpen);
    if (!newOpen) {
      editor.commands.focus();
    }
  };

  const handleFormulaClick = ({ content, pos, end }: any) => {
    if (Boolean(editor.isEditable)) {
      console.log('handleFormulaClick', content, pos, end);
      setOpen(true);
      setPos(pos);
      setEnd(end);
      setMfPreviewVal(content);
    }
  };

  useEffect(() => {
    emitter.on('formula-click', handleFormulaClick);
    return () => {
      emitter.off('formula-click', handleFormulaClick);
    };
  }, [editor]);

  useEffect(() => {
    if (!open) {
      setMfPreviewVal('');
      setPos(0);
      setEnd(0);
    }
  }, [open]);

  return (
    <Popover
      content={
        <FormulaContent
          pos={pos}
          end={end}
          mfPreviewVal={mfPreviewVal}
          setMfPreviewVal={setMfPreviewVal}
          editor={editor}
          onClose={() => handleOpenChange(false)}
        />
      }
      open={open}
      trigger="click"
      destroyOnHidden={true}
      onOpenChange={handleOpenChange}
    >
      <Button
        onClick={() => setOpen(true)}
        color="default"
        variant="filled"
        autoInsertSpace
        // onMouseDown={(e) => e.preventDefault()}
      >
        <RiFormula />
      </Button>
    </Popover>
  );
};
Formula.id = 'formula';

export default Formula;
