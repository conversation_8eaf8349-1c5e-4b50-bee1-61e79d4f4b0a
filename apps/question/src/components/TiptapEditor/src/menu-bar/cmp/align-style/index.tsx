import { RiMenu2Fill, RiMenu3Line, RiMenu5Line } from '@remixicon/react';
import { Editor } from '@tiptap/core';
import { <PERSON><PERSON>, Tooltip } from 'antd';
import { useEditorConfig } from '../../../config-ctx';

const buttonGroupTemp: Array<any> = [
  {
    id: 'alignLeft',
    icon: RiMenu2Fill,
    action: (editor: Editor) => {
      editor.chain().focus().setTextAlign('left').run();
    },
    isActive: (editor: Editor) => editor.isActive({ textAlign: 'left' }),
    canExecute: (editor: Editor) =>
      editor.can().chain().focus().setTextAlign('left').run() && !editor.isActive('codeBlock'),
    tooltip: '居左',
  },

  {
    id: 'alignCenter',
    icon: RiMenu5Line,
    action: (editor: Editor) => {
      editor.chain().focus().setTextAlign('center').run();
    },
    isActive: (editor: Editor) => editor.isActive({ textAlign: 'center' }),
    canExecute: (editor: Editor) =>
      editor.can().chain().focus().setTextAlign('center').run() && !editor.isActive('codeBlock'),
    tooltip: '居中',
  },
  {
    id: 'alignRight',
    icon: RiMenu3Line,
    action: (editor: Editor) => {
      editor.chain().focus().setTextAlign('right').run();
    },
    isActive: (editor: Editor) => editor.isActive({ textAlign: 'right' }),
    canExecute: (editor: Editor) =>
      editor.can().chain().focus().setTextAlign('right').run() && !editor.isActive('codeBlock'),
    tooltip: '居右',
  },
];

const AlignStyle = () => {
  const config = useEditorConfig();
  const editor = config.editor!;
  const buttonGroup = buttonGroupTemp.filter((item) => config.features.includes(item.id));
  if (!buttonGroup.length) return null;
  return (
    <div className="group">
      {buttonGroup.map(({ icon: Icon, tooltip, isActive, action, id }) => (
        <Tooltip title={tooltip} key={id}>
          <Button
            onClick={() => action(editor)}
            color="default"
            variant={isActive(editor) ? 'solid' : 'filled'}
            autoInsertSpace
          >
            <Icon />
          </Button>
        </Tooltip>
      ))}
    </div>
  );
};

export default AlignStyle;
