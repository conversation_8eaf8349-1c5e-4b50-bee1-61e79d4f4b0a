export const inlineRegex = /\\\((.*?)\\\)/; // 匹配 \(...\)
export const dollarInlineRegex = /\$(.*?)\$/; // 匹配 $...$
export const blockRegex = /\\\[([\s\S]*?[^\\])\\\]/; // 匹配 \[...\]
export const dollarBlockRegex = /\$\$(.*?)\$\$/; // 匹配 $$...$$

export interface FormulaRegex {
    regex: RegExp;
    tag: 'inline' | 'block';
    label: string;
    start: string;
    end: string;
}

export const formulaRegexs: FormulaRegex[] = [
    {
        regex: inlineRegex,
        tag: 'inline',
        label: '行内公式',
        start: '\\(',
        end: '\\)',
    },
    {
        regex: blockRegex,
        tag: 'block',
        label: '块级公式',
        start: '\\[',
        end: '\\]',
    },
    {
        regex: dollarBlockRegex,
        tag: 'block',
        label: '块级公式',
        start: '$$',
        end: '$$',
    },
    {
        regex: dollarInlineRegex,
        tag: 'inline',
        label: '行内公式',
        start: '$',
        end: '$',
    },
];
