.table-selector {
  display: inline-block;
  user-select: none;
  width: max-content;

  .grid {
    display: grid;
    gap: 2px;
  }

  .cell {
    width: 14px;
    height: 14px;
    background-color: #f5f5f5;
    border: 1px solid #dcdcdc;
    cursor: pointer;
    transition: background-color 0.2s;

    &.selected {
      background-color: #cce4ff;
    }
  }

  .dimensions {
    margin-top: 10px;
    font-size: 14px;
    color: #555;
    text-align: center;
  }
}
