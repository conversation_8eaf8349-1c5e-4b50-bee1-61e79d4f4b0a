export default {
  key: 'greekLetter',
  label: '希腊字母',
  content: [
    {
      remark: 'alpha',
      latex: '\\alpha',
    },

    {
      remark: 'beta',
      latex: '\\beta',
    },
    {
      remark: 'gamma',
      latex: '\\gamma',
    },
    {
      remark: 'delta',
      latex: '\\delta',
    },
    {
      remark: 'epsilon',
      latex: '\\epsilon',
    },
    {
      remark: 'varepsilon',
      latex: '\\varepsilon',
    },
    {
      remark: 'zeta',
      latex: '\\zeta',
    },
    {
      remark: 'eta',
      latex: '\\eta',
    },
    {
      remark: 'theta',
      latex: '\\theta',
    },
    {
      remark: 'vartheta',
      latex: '\\vartheta',
    },
    {
      remark: 'iota',
      latex: '\\iota',
    },
    {
      remark: 'kappa',
      latex: '\\kappa',
    },
    {
      remark: 'lambda',
      latex: '\\lambda',
    },
    //\mu \nu \xi  o \pi \varpi \rho \varrho \sigma \varsigma \tau \upsilon \phi \varphi \chi \psi \omega
    {
      remark: 'mu',
      latex: '\\mu',
    },
    {
      remark: 'nu',
      latex: '\\nu',
    },
    {
      remark: 'xi',
      latex: '\\xi',
    },
    {
      remark: 'omicron',
      latex: 'o',
    },
    {
      remark: 'pi',
      latex: '\\pi',
    },
    {
      remark: 'varpi',
      latex: '\\varpi',
    },
    {
      remark: 'rho',
      latex: '\\rho',
    },
    {
      remark: 'varrho',
      latex: '\\varrho',
    },
    {
      remark: 'sigma',
      latex: '\\sigma',
    },
    {
      remark: 'varsigma',
      latex: '\\varsigma',
    },
    {
      remark: 'tau',
      latex: '\\tau',
    },
    {
      remark: 'upsilon',
      latex: '\\upsilon',
    },
    {
      remark: 'phi',
      latex: '\\phi',
    },
    {
      remark: 'varphi',
      latex: '\\varphi',
    },
    {
      remark: 'chi',
      latex: '\\chi',
    },
    {
      remark: 'psi',
      latex: '\\psi',
    },
    {
      remark: 'omega',
      latex: '\\omega',
    },
    {
      remark: 'Gamma',
      latex: '\\Gamma',
    },
    {
      remark: 'Delta',
      latex: '\\Delta',
    },
    {
      remark: 'Theta',
      latex: '\\Theta',
    },
    {
      remark: 'Lambda',
      latex: '\\Lambda',
    },
    {
      remark: 'Xi',
      latex: '\\Xi',
    },
    {
      remark: 'Pi',
      latex: '\\Pi',
    },
    {
      remark: 'Sigma',
      latex: '\\Sigma',
    },
    {
      remark: 'Upsilon',
      latex: '\\Upsilon',
    },
    {
      remark: 'Phi',
      latex: '\\Phi',
    },
    {
      remark: 'Psi',
      latex: '\\Psi',
    },
    {
      remark: 'Omega',
      latex: '\\Omega',
    },
  ],
};
