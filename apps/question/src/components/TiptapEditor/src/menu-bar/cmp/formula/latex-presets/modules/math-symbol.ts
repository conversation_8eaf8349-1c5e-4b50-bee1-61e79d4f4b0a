export default {
  key: 'MathSymbol',
  label: '符号',
  content: [
    // 基本操作符
    {
      latex: '+',
      remark: '加',
    },

    {
      latex: '-',
      remark: '减',
    },
    {
      latex: '\\times',
      remark: '乘',
    },
    {
      latex: '\\div',
      remark: '除',
    },
    {
      latex: '/',
      remark: '除',
    },
    {
      latex: '\\pm',
      remark: '加减',
    },
    {
      latex: '\\mp',
      remark: '减加',
    },
    {
      latex: '\\ne',
      remark: '不等于',
    },
    {
      latex: '\\circ',
      remark: '圆',
    },
    {
      latex: '\\bigcirc',
      remark: '大圆',
    },

    // 关系运算符
    {
      latex: '\\in',
      remark: '属于',
    },
    {
      latex: '\\notin',
      remark: '不属于',
    },
    {
      latex: '\\emptyset',
      remark: '空集',
    },
    {
      latex: '\\subset',
      remark: '包含于',
    },
    {
      latex: '\\subseteq',
      remark: '真包含于',
    },
    // {
    //   latex: "\\notsubset",
    //   remark: "不包含于",
    // },
    {
      latex: '\\supset',
      remark: '包含',
    },
    {
      latex: '\\supseteq',
      remark: '真包含',
    },
    // {
    //   latex: "\\notsupset",
    //   remark: "不包含",
    // },
    {
      latex: '\\sim',
      remark: '相似',
    },
    {
      latex: '\\equiv',
      remark: '恒等于',
    },
    {
      latex: '\\cong',
      remark: '全等于',
    },
    {
      latex: '\\lt',
      remark: '小于',
    },
    {
      latex: '≮',
      remark: '不小于',
    },
    {
      latex: '\\gt',
      remark: '大于',
    },
    {
      latex: '≯',
      remark: '不大于',
    },
    {
      latex: '\\Leftrightarrow',
      remark: '等价',
    },
    {
      latex: '\\forall',
      remark: '任意',
    },
  ],
};
