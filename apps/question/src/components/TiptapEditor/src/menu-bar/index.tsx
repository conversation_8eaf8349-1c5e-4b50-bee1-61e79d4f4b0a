import Color from './cmp/color';
import Table from './cmp/table';
// import Shape from './cmp/shape';
import { useEffect, useRef, useState } from 'react';
import { useEditorConfig } from '../config-ctx';
import Action from './cmp/action';
import AlignStyle from './cmp/align-style';
import FontStyle from './cmp/font-style';
import Formula from './cmp/formula';
import Heading from './cmp/heading';
import ImgUpload from './cmp/img-upload';
import InsertLine from './cmp/insert-line';
import InsertQs from './cmp/insert-qs';
import GroupCmp from './group-cmp';

const MenuBar = () => {
  const config = useEditorConfig();
  const toolbarRef = useRef<HTMLDivElement>(null);

  const [toolbarHeight, setToolbarHeight] = useState(0);

  useEffect(() => {
    if (toolbarRef.current) {
      setToolbarHeight(toolbarRef.current.offsetHeight);
    }
  }, []);
  if (!config.editor) return null;
  return (
    <div className="menuBar" ref={toolbarRef} style={{ top: `-${toolbarHeight}px` }}>
      <Heading />
      <FontStyle />
      <InsertLine />
      <Color />
      <AlignStyle />
      {/* <Shape/> */}
      <GroupCmp>
        <Table />
        <Formula />
        <ImgUpload />
        <InsertQs />
      </GroupCmp>
      <Action />
    </div>
  );
};

export default MenuBar;
