import { Editor } from '@tiptap/react';
import { MathfieldElement } from 'mathlive';

declare module '*.svg' {
  const ReactComponent: React.FC<React.SVGProps<SVGSVGElement>>;
  export default ReactComponent;
}

declare module 'react' {
  namespace JSX {
    interface IntrinsicElements {
      'math-field': React.DetailedHTMLProps<
        React.HTMLAttributes<MathfieldElement>,
        MathfieldElement
      >;
    }
  }
}

// 编辑器默认配置
declare interface EditorDefaultConfig {
  imgBaseUrl?: string;
  imageUploadHandler?: (file: File) => Promise<string>;
  onImageUpload?: ({
    file,
    base64,
    url,
    id,
    md5FileName,
  }: {
    file: File;
    base64: string;
    url: string;
    id: string;
    md5FileName: string;
  }) => void;
  placeholder?: string;
  clickToEdit?: boolean;
}

// 编辑器配置
type EditorConfig = {
  content?: string;
  onSave?: (content: string) => Promise<string>;
  placeholder?: string;
  editor?: Editor;
  onBlur?: (arg: any) => void;
  onFocus?: (arg: any) => void;
  onQuestionFillAreaInsert?: (event: any) => void;
  onQuestionFillAreaDelete?: (event: any) => void;
  onQuestionFillAreaClick?: (event: any) => void;
  stripOuterNode?: boolean;
  [str: string]: any;
} & EditorDefaultConfig;
