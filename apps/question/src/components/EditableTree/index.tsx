import { DeleteOutlined, EditOutlined, MenuOutlined, PlusOutlined } from '@ant-design/icons';
import { Button, Input, message, Space, Tree } from 'antd';
import type { DataNode, TreeProps } from 'antd/es/tree';
import React, { useCallback, useState } from 'react';
import { DraggableProvidedDragHandleProps } from 'react-beautiful-dnd';

interface EditableTreeProps {
  treeData: DataNode[];
  onTreeDataChange?: (newTreeData: DataNode[]) => void;
  addChildPosition?: 'first' | 'last'; // 新增属性，控制添加子节点位置
}

const EditableTree: React.FC<EditableTreeProps> = ({
  treeData: initialTreeData,
  onTreeDataChange,
  addChildPosition = 'last', // 默认添加到末尾
}) => {
  const [treeData, setTreeData] = useState<DataNode[]>(initialTreeData);
  const [editingKey, setEditingKey] = useState<string>('');
  const [editValue, setEditValue] = useState<string>('');
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);

  // 递归查找节点
  const findNode = useCallback((key: string, data: DataNode[]): DataNode | null => {
    for (let node of data) {
      if (node.key === key) return node;
      if (node.children) {
        const found = findNode(key, node.children);
        if (found) return found;
      }
    }
    return null;
  }, []);

  // 递归更新树数据
  const updateTreeData = useCallback(
    (list: DataNode[], key: React.Key, callback: (node: DataNode) => DataNode): DataNode[] => {
      return list.map((node) => {
        if (node.key === key) {
          return callback(node);
        }
        if (node.children) {
          return {
            ...node,
            children: updateTreeData(node.children, key, callback),
          };
        }
        return node;
      });
    },
    [],
  );

  // 开始编辑
  const startEdit = (key: string, title: string) => {
    setEditingKey(key);
    setEditValue(title);
  };

  // 保存编辑
  const saveEdit = useCallback(() => {
    if (!editValue.trim()) {
      message.warning('请输入节点内容');
      return;
    }
    const newTreeData = updateTreeData(treeData, editingKey, (node) => ({
      ...node,
      title: editValue,
    }));
    setTreeData(newTreeData);
    onTreeDataChange?.(newTreeData);
    setEditingKey('');
  }, [editValue, editingKey, treeData, updateTreeData, onTreeDataChange]);

  // 添加子节点
  const addChild = useCallback(
    (parentKey: string) => {
      // 确保父节点是展开状态
      if (!expandedKeys.includes(parentKey)) {
        setExpandedKeys([...expandedKeys, parentKey]);
      }

      const newKey = Date.now().toString();
      const newNode: DataNode = {
        title: '新节点',
        key: newKey,
      };

      const newTreeData = updateTreeData(treeData, parentKey, (node) => ({
        ...node,
        children:
          addChildPosition === 'first'
            ? [newNode, ...(node.children || [])]
            : [...(node.children || []), newNode],
      }));

      setTreeData(newTreeData);
      onTreeDataChange?.(newTreeData);

      // 自动开始编辑新节点
      setTimeout(() => {
        startEdit(newKey, '新节点');
      }, 0);
    },
    [addChildPosition, expandedKeys, treeData, updateTreeData, onTreeDataChange],
  );

  // 删除节点
  const removeNode = useCallback(
    (key: string) => {
      const removeNodeRecursive = (data: DataNode[]): DataNode[] => {
        return data
          .filter((node) => node.key !== key)
          .map((node) => ({
            ...node,
            children: node.children ? removeNodeRecursive(node.children) : [],
          }));
      };

      const newTreeData = removeNodeRecursive(treeData);
      setTreeData(newTreeData);
      onTreeDataChange?.(newTreeData);
    },
    [treeData, onTreeDataChange],
  );

  // 处理拖拽
  const onDrop: TreeProps['onDrop'] = useCallback(
    (info: any) => {
      const dropKey = info.node.key;
      const dragKey = info.dragNode.key;
      const dropPos = info.node.pos.split('-');
      const dropPosition = info.dropPosition - Number(dropPos[dropPos.length - 1]);

      const loop = (
        data: DataNode[],
        key: React.Key,
        callback: (node: DataNode, i: number, data: DataNode[]) => void,
      ) => {
        for (let i = 0; i < data.length; i++) {
          if (data[i].key === key) {
            callback(data[i], i, data);
            return;
          }
          if (data[i].children) {
            loop(data[i].children!, key, callback);
          }
        }
      };

      const data = [...treeData];

      // 找到拖拽的节点
      let dragObj: DataNode = {} as DataNode;
      loop(data, dragKey, (item, index, arr) => {
        arr.splice(index, 1);
        dragObj = item;
      });

      if (!info.dropToGap) {
        // 放到节点内
        loop(data, dropKey, (item) => {
          item.children = item.children || [];
          // 根据 addChildPosition 决定插入位置
          if (addChildPosition === 'first') {
            item.children.unshift(dragObj);
          } else {
            item.children.push(dragObj);
          }
        });
      } else {
        let ar: DataNode[] = [];
        let i: number;
        loop(data, dropKey, (item, index, arr) => {
          ar = arr;
          i = index;
        });
        if (dropPosition === -1) {
          // 放到节点前面
          ar.splice(i!, 0, dragObj);
        } else {
          // 放到节点后面
          ar.splice(i! + 1, 0, dragObj);
        }
      }

      setTreeData(data);
      onTreeDataChange?.(data);
    },
    [addChildPosition, treeData, onTreeDataChange],
  );

  // 渲染节点标题
  const renderTitle = (node: DataNode, dragHandleProps?: DraggableProvidedDragHandleProps) => {
    if (editingKey === node.key.toString()) {
      return (
        <Input
          value={editValue}
          onChange={(e) => setEditValue(e.target.value)}
          onPressEnter={saveEdit}
          onBlur={saveEdit}
          autoFocus
          size="small"
          style={{ width: 'calc(100% - 80px)' }}
        />
      );
    }

    return (
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          width: '100%',
        }}
      >
        <span>{node.title as React.ReactNode}</span>
        <Space className="node-actions" style={{ marginLeft: 8 }}>
          <Button
            icon={<EditOutlined />}
            size="small"
            type="text"
            onClick={(e) => {
              e.stopPropagation();
              startEdit(node.key.toString(), node.title as string);
            }}
          />
          <Button
            icon={<PlusOutlined />}
            size="small"
            type="text"
            onClick={(e) => {
              e.stopPropagation();
              addChild(node.key.toString());
            }}
          />
          <Button
            icon={<DeleteOutlined />}
            size="small"
            type="text"
            onClick={(e) => {
              e.stopPropagation();
              removeNode(node.key.toString());
            }}
          />
          <span {...dragHandleProps}>
            <MenuOutlined style={{ cursor: 'move' }} />
          </span>
        </Space>
      </div>
    );
  };

  return (
    <Tree
      blockNode
      showLine
      draggable
      height={500}
      expandedKeys={expandedKeys}
      onExpand={(keys) => setExpandedKeys(keys)}
      onDrop={onDrop}
      treeData={treeData}
      titleRender={(node) => renderTitle(node)}
      style={{ padding: '16px' }}
    />
  );
};

export default EditableTree;
