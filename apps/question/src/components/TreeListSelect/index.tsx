import { useFetchTreeList } from '@/services/api';
import { Select } from 'antd';
import React, { useEffect, useState } from 'react';

export interface TreeListSelectrProps {
  onChange?: (value: number | undefined) => void;
  isBaseTree?: boolean;
  selectedSubject?: number[];
}

const TreeListSelect: React.FC<TreeListSelectrProps> = ({
  onChange,
  isBaseTree = true,
  selectedSubject = [],
}) => {
  // 基础树和章节树
  const queryTreePlaceHolder = isBaseTree ? '请选择基础树' : '请选择章节树';
  // 树列表下拉框状态
  const [treeListSelectValue, setTreeListSelectValue] = useState<number>();
  const { data: queryTreeListData, run: fetchTreeListRun } = useFetchTreeList(
    isBaseTree,
    selectedSubject,
  );

  useEffect(() => {
    // 树列表下拉框清空
    setTreeListSelectValue(undefined);
    // 重新获取树列表
    fetchTreeListRun();
    // console.log('重新获取树列表: ', selectedSubject);
  }, [selectedSubject, isBaseTree]);

  useEffect(() => {
    if (queryTreeListData?.formatList && queryTreeListData.formatList.length > 0) {
      const value = queryTreeListData.formatList[0].value;
      setTreeListSelectValue(value);
      onChange?.(value);
    } else {
      onChange?.(undefined);
    }
  }, [queryTreeListData]);

  const onTreeListSelectChange = (value: number) => {
    setTreeListSelectValue(value);
    onChange?.(value);
  };

  return (
    <Select
      style={{ width: '100%' }}
      options={queryTreeListData?.formatList ?? []}
      placeholder={queryTreePlaceHolder}
      onChange={onTreeListSelectChange}
      value={treeListSelectValue}
    />
  );
};

export default TreeListSelect;
