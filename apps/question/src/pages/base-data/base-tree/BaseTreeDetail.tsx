import SearchTree, { TreeNodeType } from '@/components/tree';
import useEnumManagerMap from '@/hooks/useEnumManager';
import { fetchBaseTreeDetail } from '@/services/api';
import { ArrowLeftOutlined } from '@ant-design/icons';
import { ProCard } from '@ant-design/pro-components';
import { message, Space, Typography } from 'antd';
import React, { useEffect, useState } from 'react';

/**
 * 基础树详情组件
 */
const BaseTreeDetail: React.FC<BaseTreeDetailProps> = ({ record, onBack }) => {
  const { phaseList: phaseListEnumManager, subjectList: subjectListEnumManager } =
    useEnumManagerMap();

  const [detailData, setDetailData] = useState<API.BaseTreeDetailData>();
  const [treeData, setTreeData] = useState<TreeNodeType[]>([]);
  const [loading, setLoading] = useState(false);

  // 获取详情数据
  const fetchDetail = async () => {
    try {
      setLoading(true);
      const res = await fetchBaseTreeDetail(record.baseTreeId);
      if (res.code === 0) {
        setDetailData(res.data);

        // 将后端返回的树形数据转换为组件需要的格式
        const convertToTreeData = (node: API.BaseTreeNode): TreeNodeType => ({
          title: `${node.baseTreeNodeName} (${node.baseTreeNodeId})`,
          key: String(node.baseTreeNodeId),
          children: node.baseTreeNodeChildren?.map(convertToTreeData) || [],
        });

        const treeData = convertToTreeData(res.data.baseTreeDetail);
        setTreeData([treeData]);
      } else {
        message.error('获取详情失败：' + res.message);
      }
    } catch (error) {
      message.error('获取详情失败');
      console.error('获取详情失败:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDetail();
  }, []);

  return (
    <ProCard direction="column" ghost gutter={[0, 16]}>
      <ProCard>
        <div>
          <span onClick={onBack} style={{ cursor: 'pointer' }}>
            <ArrowLeftOutlined style={{ marginRight: 8 }} />
            返回基础树管理
          </span>
        </div>
      </ProCard>

      <ProCard
        title={
          <>
            <div style={{ fontSize: 16, fontWeight: 500 }}>
              {detailData?.baseTreeName || record.baseTreeName}
              <span style={{ marginLeft: 8, color: '#1890ff', fontWeight: 'normal', fontSize: 14 }}>
                (线上版本 {detailData?.baseTreeVersion || record.baseTreeVersion})
              </span>
            </div>
            <div style={{ marginTop: 8, color: 'rgba(0, 0, 0, 0.45)', fontSize: 14 }}>
              <Space size={30}>
                <Space align="center">
                  <Typography.Text type="secondary">ID: </Typography.Text>
                  <Typography.Paragraph type="secondary" copyable style={{ margin: 0 }}>
                    {record.baseTreeId}
                  </Typography.Paragraph>
                </Space>

                <span>学段: {phaseListEnumManager?.getLabelByValue(record.phase)}</span>

                <span>学科: {subjectListEnumManager?.getLabelByValue(record.subject)}</span>
              </Space>
            </div>
          </>
        }
        headerBordered
        loading={loading}
      >
        {treeData.length > 0 && <SearchTree treeData={treeData} showLine />}
      </ProCard>
    </ProCard>
  );
};

export default BaseTreeDetail;

/**
 * 基础树详情组件属性
 */
export interface BaseTreeDetailProps {
  /** 基础树数据 */
  record: API.BaseTreeItem;
  /** 返回按钮点击事件 */
  onBack: () => void;
}
