import PageContainerModal from '@/components/PageContainerModal';
import usePhaseSubject from '@/hooks/usePhaseSubject';
import { fetchBaseTreeList } from '@/services/api';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { PageContainer, ProTable } from '@ant-design/pro-components';
import { Space } from 'antd';
import React, { useRef, useState } from 'react';
import BaseTreeDetail from './BaseTreeDetail';

/**
 * 基础树管理组件
 *
 * @returns 基础树管理页面
 */
const BaseTree: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const [currentRecord, setCurrentRecord] = useState<API.BaseTreeItem | undefined>(undefined);

  const {
    selectedPhases,
    selectedSubjects,
    phaseOptions,
    subjectOptions,
    setSelectedPhases,
    setSelectedSubjects,
  } = usePhaseSubject();

  /**
   * 处理查看操作
   *
   * @param record - 表格行数据
   */
  const handleView = (record: API.BaseTreeItem): void => {
    setCurrentRecord(record);
  };

  /**
   * 表格列配置
   */
  const columns: ProColumns<API.BaseTreeItem>[] = [
    {
      title: 'ID',
      dataIndex: 'baseTreeId',
      width: 80,
      search: false,
    },
    {
      title: '基础树名称',
      dataIndex: 'baseTreeName',
      ellipsis: true,
      order: 8,
      fieldProps: {
        placeholder: '请输入基础树名称关键词',
      },
      formItemProps: {
        name: 'baseTreeNameKey',
      },
    },
    {
      title: '学段',
      dataIndex: 'phase',
      width: 100,
      valueType: 'select',
      fieldProps: {
        mode: 'multiple',
        options: phaseOptions,
        onChange: setSelectedPhases,
        value: selectedPhases,
      },
      formItemProps: {
        name: 'phaseList',
      },
      order: 10,
    },
    {
      title: '学科',
      dataIndex: 'subject',
      width: 100,
      valueType: 'select',
      fieldProps: {
        mode: 'multiple',
        options: subjectOptions,
        onChange: setSelectedSubjects,
        value: selectedSubjects,
      },
      formItemProps: {
        name: 'subjectList',
      },
      order: 9,
    },
    {
      title: '线上版本',
      dataIndex: 'baseTreeVersion',
      width: 100,
      search: false,
    },
    {
      title: '操作',
      dataIndex: 'operation',
      width: 80,
      search: false,
      render: (_, record) => (
        <Space>
          <a key="view" onClick={() => handleView(record)}>
            查看
          </a>
        </Space>
      ),
    },
  ];

  return (
    <>
      <PageContainer>
        <ProTable<API.BaseTreeItem>
          columns={columns}
          actionRef={actionRef}
          cardBordered
          onReset={() => {
            setSelectedPhases([]);
            setSelectedSubjects([]);
          }}
          request={async (params = {}) => {
            const { current, pageSize, baseTreeNameKey, phaseList, subjectList } = params;

            const res = await fetchBaseTreeList({
              baseTreeNameKey,
              page: current,
              pageSize,
              phaseList,
              subjectList,
            });

            return {
              data: res.data.list || [],
              success: true,
              total: res.data.total || 0,
            };
          }}
          rowKey="baseTreeId"
          search={{
            labelWidth: 'auto',
            defaultCollapsed: false,
            span: {
              xs: 24,
              sm: 12,
              md: 8,
              lg: 8,
              xl: 8,
              xxl: 6,
            },
            layout: 'horizontal',
            defaultColsNumber: 3,
          }}
          options={{
            setting: {
              listsHeight: 400,
            },
          }}
          form={{
            syncToUrl: false,
            colon: false,
          }}
          pagination={{
            pageSize: 10,
          }}
          dateFormatter="string"
          headerTitle="基础树管理"
        />
      </PageContainer>

      <PageContainerModal
        modalProps={{
          open: Boolean(currentRecord),
          destroyOnClose: true,
        }}
        pageHeaderRender={false}
      >
        <BaseTreeDetail
          record={currentRecord!}
          onBack={() => {
            setCurrentRecord(undefined);
          }}
        />
      </PageContainerModal>
    </>
  );
};

export default BaseTree;
