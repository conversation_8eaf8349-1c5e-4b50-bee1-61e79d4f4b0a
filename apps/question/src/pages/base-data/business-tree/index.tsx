import PageContainerModal from '@/components/PageContainerModal';
import useEnumManagerMap from '@/hooks/useEnumManager';
import usePhaseSubject from '@/hooks/usePhaseSubject';
import { fetchBizTreeList } from '@/services/api';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { PageContainer, ProTable } from '@ant-design/pro-components';
import { Space } from 'antd';
import React, { useRef, useState } from 'react';
import BusinessTreeDetail from './BusinessTreeDetail';

/**
 * 业务树管理组件
 *
 * @returns 业务树管理页面
 */
const BusinessTree: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const [currentRecord, setCurrentRecord] = useState<API.BizTreeItem | undefined>(undefined);

  const {
    selectedPhases,
    selectedSubjects,
    phaseOptions,
    subjectOptions,
    setSelectedPhases,
    setSelectedSubjects,
  } = usePhaseSubject();

  const { materialList: materialListEnumManager } = useEnumManagerMap();

  /**
   * 处理查看操作
   *
   * @param record - 表格行数据
   */
  const handleView = (record: API.BizTreeItem): void => {
    setCurrentRecord(record);
  };

  /**
   * 关闭详情页面
   */
  const handleCloseDetail = () => {
    setCurrentRecord(undefined);
  };

  /**
   * 表格列配置
   */
  const columns: ProColumns<API.BizTreeItem>[] = [
    {
      title: 'ID',
      dataIndex: 'bizTreeId',
      width: 80,
      search: false,
    },
    {
      title: '业务树名称',
      dataIndex: 'bizTreeName',
      ellipsis: true,
      order: 8,
      fieldProps: {
        placeholder: '请输入业务树名称关键词',
      },
      formItemProps: {
        name: 'bizTreeNameKey',
      },
    },
    // 教材版本
    {
      title: '教材版本',
      dataIndex: 'material',
      width: 100,
      search: false,
      valueEnum: materialListEnumManager?.getProTableValueEnum(),
    },
    {
      title: '学段',
      dataIndex: 'phase',
      width: 100,
      valueType: 'select',
      fieldProps: {
        mode: 'multiple',
        options: phaseOptions,
        onChange: setSelectedPhases,
        value: selectedPhases,
      },
      formItemProps: {
        name: 'phaseList',
      },
      order: 10,
    },
    {
      title: '学科',
      dataIndex: 'subject',
      width: 100,
      valueType: 'select',
      fieldProps: {
        mode: 'multiple',
        options: subjectOptions,
        onChange: setSelectedSubjects,
        value: selectedSubjects,
      },
      formItemProps: {
        name: 'subjectList',
      },
      order: 9,
    },
    {
      title: '线上版本',
      dataIndex: 'bizTreeVersion',
      width: 100,
      search: false,
    },
    {
      title: '操作',
      dataIndex: 'operation',
      width: 80,
      search: false,
      render: (_, record) => (
        <Space>
          <a key="view" onClick={() => handleView(record)}>
            查看
          </a>
        </Space>
      ),
    },
  ];

  return (
    <>
      <PageContainer>
        <ProTable<API.BizTreeItem>
          columns={columns}
          actionRef={actionRef}
          cardBordered
          onReset={() => {
            setSelectedPhases([]);
            setSelectedSubjects([]);
          }}
          request={async (params = {}) => {
            const { current, pageSize, bizTreeNameKey, phaseList, subjectList } = params;

            const res = await fetchBizTreeList({
              bizTreeNameKey,
              page: current,
              pageSize: pageSize,
              phaseList,
              subjectList,
            });

            return {
              data: res.data.list || [],
              success: true,
              total: res.data.total || 0,
            };
          }}
          rowKey="bizTreeId"
          search={{
            labelWidth: 'auto',
            defaultCollapsed: false,
            span: {
              xs: 24,
              sm: 12,
              md: 8,
              lg: 8,
              xl: 8,
              xxl: 6,
            },
            layout: 'horizontal',
            defaultColsNumber: 3,
          }}
          options={{
            setting: {
              listsHeight: 400,
            },
          }}
          form={{
            syncToUrl: false,
            colon: false,
          }}
          pagination={{
            pageSize: 10,
          }}
          dateFormatter="string"
          headerTitle="业务树管理"
        />
      </PageContainer>

      <PageContainerModal
        modalProps={{
          open: Boolean(currentRecord),
          destroyOnClose: true,
        }}
        pageHeaderRender={false}
      >
        <BusinessTreeDetail record={currentRecord!} onBack={handleCloseDetail} />
      </PageContainerModal>
    </>
  );
};

export default BusinessTree;
