import SearchTree, { TreeNodeType } from '@/components/tree';
import useEnumManagerMap from '@/hooks/useEnumManager';
import { fetchBizTreeDetail } from '@/services/api';
import { ArrowLeftOutlined } from '@ant-design/icons';
import { ProCard } from '@ant-design/pro-components';
import { message, Space, Typography } from 'antd';
import React, { useEffect, useState } from 'react';

const BusinessTreeDetail: React.FC<BusinessTreeDetailProps> = ({ record, onBack }) => {
  const [, setDetailData] = useState<API.BizTreeDetailData>();
  const [, setLoading] = useState(false);
  const [treeData, setTreeData] = useState<TreeNodeType[]>([]);

  const { phaseList: phaseListEnumManager, subjectList: subjectListEnumManager } =
    useEnumManagerMap();

  // 获取详情数据
  const fetchDetail = async () => {
    try {
      setLoading(true);
      const res = await fetchBizTreeDetail(record.bizTreeId);
      if (res.code === 0) {
        setDetailData(res.data);

        // 将后端返回的树形数据转换为组件需要的格式
        const convertToTreeData = (node: API.BizTreeNode): TreeNodeType => ({
          title: `${node.bizTreeNodeName} (${node.bizTreeNodeId})`,
          key: String(node.bizTreeNodeId),
          children: node.bizTreeNodeChildren?.map(convertToTreeData) || [],
        });

        const treeData = convertToTreeData(res.data.bizTreeDetail);
        setTreeData([treeData]);
      } else {
        message.error('获取详情失败：' + res.message);
      }
    } catch (error) {
      message.error('获取详情失败');
      console.error('获取详情失败:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDetail();
  }, []);

  return (
    <ProCard direction="column" ghost gutter={[0, 16]}>
      <ProCard>
        <div>
          <span onClick={onBack} style={{ cursor: 'pointer' }}>
            <ArrowLeftOutlined style={{ marginRight: 8 }} />
            返回业务树管理
          </span>
        </div>
      </ProCard>

      <ProCard
        title={
          <>
            <div style={{ fontSize: 16, fontWeight: 500 }}>
              {record.bizTreeName}
              <span style={{ marginLeft: 8, color: '#1890ff', fontWeight: 'normal', fontSize: 14 }}>
                (线上版本 {record.bizTreeVersion})
              </span>
            </div>
            <div style={{ marginTop: 8, color: 'rgba(0, 0, 0, 0.45)', fontSize: 14 }}>
              <Space align="center">
                <Typography.Text type="secondary">ID: </Typography.Text>
                <Typography.Paragraph type="secondary" copyable style={{ margin: 0 }}>
                  {record.bizTreeId}
                </Typography.Paragraph>
              </Space>
              <span style={{ marginLeft: 24 }}>
                学段: {phaseListEnumManager?.getLabelByValue(record.phase)}
              </span>
              <span style={{ marginLeft: 24 }}>
                学科: {subjectListEnumManager?.getLabelByValue(record.subject)}
              </span>
            </div>
          </>
        }
        headerBordered
      >
        {treeData.length > 0 && <SearchTree treeData={treeData} showLine />}
      </ProCard>
    </ProCard>
  );
};

export default BusinessTreeDetail;

export interface BusinessTreeDetailProps {
  record: API.BizTreeItem;
  onBack: () => void;
}
