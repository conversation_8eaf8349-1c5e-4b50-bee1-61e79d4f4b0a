import QuestionList, { QuestionListType } from '@/components/QuestionList';
import useEnumManagerMap from '@/hooks/useEnumManager';
import {
  getAuditTaskDetail,
  submitAuditTask,
  submitChooseQuestionAudit,
} from '@/services/api/review';
import { ArrowLeftOutlined } from '@ant-design/icons';
import { ModalForm, ProFormTextArea } from '@ant-design/pro-components';
import { Button, Card, Flex, message, Modal, Space, Tag } from 'antd';
import { produce } from 'immer';
import React, { useEffect, useState } from 'react';

export type ChooseQuestionReviewDetailProps = {
  onSubmit: () => void;
  onBack: () => void;
  canEdit: boolean;
  data: API.Review.AuditTaskItem;
};

const ChooseQuestionReviewDetail: React.FC<ChooseQuestionReviewDetailProps> = ({
  onSubmit,
  onBack,
  canEdit,
  data,
}) => {
  const {
    questionDifficultList: questionDifficultEnumManager,
    auditStatusList: auditsStatusEnumManager,
  } = useEnumManagerMap();

  const [loading, setLoading] = useState(false);
  const [detail, setDetail] = useState<API.Review.AuditTaskDetail | null>(null);
  const [difficultStat, setDifficultStat] = useState<
    API.Review.ChooseQuestionAudit['difficultStat']
  >([]);

  const [selectedDifficultLevel, setSelectedDifficultLevel] = useState<number | null>(null);
  const [questionList, setQuestionList] = useState<API.Review.ChooseQuestionAudit['list']>([]);
  const showQuestionList = React.useMemo(() => {
    if (selectedDifficultLevel === null) {
      return questionList;
    }

    return questionList.filter((question) => question.difficultLevel === selectedDifficultLevel);
  }, [questionList, selectedDifficultLevel]);

  const [modalVisible, setModalVisible] = useState(false);

  useEffect(() => {
    setLoading(true);

    getAuditTaskDetail({ auditTaskId: data.auditTaskId })
      .then((res) => {
        if (res.code) {
          message.error(res.message || '获取审核详情失败');

          return;
        }

        setDetail(res.data);
        setDifficultStat(res.data.chooseQuestion.difficultStat);
        setQuestionList(res.data.chooseQuestion.list);
        setSelectedDifficultLevel(res.data.chooseQuestion.list[0]?.difficultLevel ?? null);
      })
      .catch((error) => {
        console.error('获取审核详情失败:', error);
      })
      .finally(() => {
        setLoading(false);
      });
  }, []);

  // 获取标记为不通过的题目数量
  const getFailedQuestionCount = () => {
    return questionList.filter((q) => q.auditStatus === 3).length;
  };

  // 处理提交按钮点击
  const handleSubmit = () => {
    if (!detail) return;

    const failedCount = getFailedQuestionCount();

    if (failedCount > 0) {
      setModalVisible(true);
    } else {
      Modal.confirm({
        title: '是否确认审核通过',
        okText: '确认',
        cancelText: '返回',
        onOk: async () => {
          try {
            setLoading(true);
            const res = await submitAuditTask({
              auditTaskId: detail.auditTaskId,
            });

            if (res.code === 0) {
              message.success('提交成功');
              onSubmit();
            } else {
              message.error(res.message || '提交失败');
            }
          } catch (error) {
            console.error('提交审核结果失败:', error);
            message.error('提交失败');
          } finally {
            setLoading(false);
          }
        },
      });
    }
  };

  return (
    <Space direction="vertical" style={{ width: '100%' }}>
      <Card>
        <Space>
          <span style={{ cursor: 'pointer', fontWeight: 'normal', fontSize: 15 }} onClick={onBack}>
            <ArrowLeftOutlined />
            返回
          </span>

          <span style={{ fontSize: 18 }}>
            <span style={{ fontWeight: 'bold' }}>{data?.auditTaskName} </span>
            【ID: {data?.auditTaskId}】
          </span>
        </Space>
      </Card>

      <Card>
        <Flex align="center" justify="space-between">
          <Space size={16}>
            {questionDifficultEnumManager?.enums.map((item, index) => {
              return (
                <Tag
                  onClick={() => {
                    if (selectedDifficultLevel === item.value) {
                      setSelectedDifficultLevel(null);
                    } else {
                      setSelectedDifficultLevel(item.value);
                    }
                  }}
                  key={item.label}
                  color={selectedDifficultLevel === item.value ? 'processing' : 'default'}
                  style={{
                    cursor: 'pointer',
                    padding: '4px 8px',
                    border: selectedDifficultLevel === item.value ? '1px solid #1677ff' : undefined,
                  }}
                >
                  <Space size={4} align="center">
                    <span
                      style={{
                        display: 'inline-block',
                        width: '8px',
                        height: '8px',
                        borderRadius: '50%',
                        backgroundColor: item.color,
                        transform: 'translateY(-1px)',
                      }}
                    />
                    {item.label}
                    {/* 当前题目数量/需要题目数量 */}
                    {difficultStat[index]?.difficultQuestionCount ?? 0}/
                    {difficultStat[index]?.difficultNeedQuestionCount ?? 0}
                  </Space>
                </Tag>
              );
            })}
          </Space>

          {canEdit ? (
            <Button type="primary" onClick={handleSubmit} loading={loading}>
              提交
            </Button>
          ) : (
            <Tag
              color={auditsStatusEnumManager
                ?.getEnumByValue(data.auditStatus)
                ?.status.toLowerCase()}
            >
              {auditsStatusEnumManager?.getLabelByValue(data.auditStatus)}
            </Tag>
          )}
        </Flex>
      </Card>

      <Card>
        <QuestionList
          // @ts-ignore
          questions={showQuestionList}
          setQuestion={async (question) => {
            const index = questionList.findIndex((q) => q.questionId === question.questionId);

            if (index === -1) {
              return;
            }

            setQuestionList(
              produce(questionList, (draft) => {
                // @ts-ignore
                draft[index] = question;
              }),
            );

            const ret = await submitChooseQuestionAudit({
              auditTaskId: data.auditTaskId,
              questionId: question.questionId,
              auditStatus: question.auditStatus,
            });

            if (ret.code) {
              message.error(ret.message || '提交失败');

              setQuestionList(
                produce(questionList, (draft) => {
                  // @ts-ignore
                  draft[index] = {
                    ...question,
                    auditStatus: question.auditStatus === 2 ? 3 : 2,
                  };
                }),
              );
            }
          }}
          canEdit={canEdit}
          type={QuestionListType.EXERCISE_AUDIT}
          loading={loading}
        />
      </Card>

      <ModalForm<{ reason: string }>
        title="不通过原因"
        width={500}
        open={modalVisible}
        onOpenChange={setModalVisible}
        submitter={{
          searchConfig: {
            submitText: '确认',
            resetText: '返回',
          },
        }}
        modalProps={{
          destroyOnClose: true,
        }}
        submitTimeout={2000}
        layout="vertical"
        onFinish={async (values) => {
          if (!detail) {
            message.error('审核任务详情不存在');
            return false;
          }

          try {
            const res = await submitAuditTask({
              auditTaskId: detail.auditTaskId,
              reason: values.reason,
            });

            if (res.code === 0) {
              message.success('提交成功');
              onSubmit();
              setModalVisible(false);
              return true;
            } else {
              message.error(res.message || '提交失败');
              return false;
            }
          } catch (error) {
            console.error('提交审核结果失败:', error);
            message.error('提交失败');
            return false;
          }
        }}
      >
        <ProFormTextArea
          initialValue={`${getFailedQuestionCount()}道题目被标记为不通过，`}
          name="reason"
          label=""
          placeholder="请输入不通过原因"
          rules={[{ required: true, message: '请输入不通过原因' }]}
        />
      </ModalForm>
    </Space>
  );
};

export default ChooseQuestionReviewDetail;
