import { CloseOutlined, SearchOutlined } from '@ant-design/icons';
import { css } from '@emotion/css';
import { useDebounce } from 'ahooks';
import { Input, Modal, Tree, TreeProps } from 'antd';
import React, { useEffect, useState } from 'react';

export interface KnowledgePoint {
  key: string;
  title: string;
  children?: KnowledgePoint[];
}

interface KnowledgePointModalProps {
  open: boolean;
  treeData: API.BaseTreeNode[];
  onOk: (checkedNodes: API.BaseTreeNode[]) => void;
  onCancel: () => void;
  defaultCheckedKeys?: number[];
  defaultCheckedNames?: string[];
  onlyLeaf?: boolean;
}

const KnowledgePointModal: React.FC<KnowledgePointModalProps> = ({
  treeData,
  open,
  onOk,
  onCancel,
  defaultCheckedKeys = [],
  defaultCheckedNames = [],
  onlyLeaf = false,
}) => {
  // 分别存储选中的节点和半选中的节点
  const [checkedKeys, setCheckedKeys] = useState<number[]>(defaultCheckedKeys);
  const [checkedNodes, setCheckedNodes] = useState<API.BaseTreeNode[]>(
    defaultCheckedKeys.map((key, index) => {
      // @ts-ignore
      return {
        baseTreeNodeId: key,
        baseTreeNodeName: defaultCheckedNames[index] || '',
        baseTreeNodeChildren: [],
      } as API.BaseTreeNode;
    }),
  );
  useEffect(() => {
    setCheckedKeys(defaultCheckedKeys);
    setCheckedNodes(
      defaultCheckedKeys.map((key, index) => {
        // @ts-ignore
        return {
          baseTreeNodeId: key,
          baseTreeNodeName: defaultCheckedNames[index] || '',
          baseTreeNodeChildren: [],
        } as API.BaseTreeNode;
      }),
    );
  }, [defaultCheckedKeys, defaultCheckedNames]);

  const [searchValue, setSearchValue] = useState('');
  const debouncedSearchValue = useDebounce(searchValue, {
    wait: 500,
  });

  // 处理搜索逻辑
  const getFilteredTreeData = (data: API.BaseTreeNode[]): API.BaseTreeNode[] => {
    const searchLower = debouncedSearchValue.toLowerCase();

    const traverse = (node: API.BaseTreeNode): API.BaseTreeNode | null => {
      if (node.baseTreeNodeName.toLowerCase().includes(searchLower)) {
        return node;
      }

      if (node.baseTreeNodeChildren) {
        const filteredChildren = node.baseTreeNodeChildren
          .map(traverse)
          .filter((child): child is API.BaseTreeNode => child !== null);

        if (filteredChildren.length > 0) {
          return {
            ...node,
            baseTreeNodeChildren: filteredChildren,
          };
        }
      }

      return null;
    };

    return data.map(traverse).filter((node): node is API.BaseTreeNode => node !== null);
  };

  // 处理树形数据，为每个节点添加 checkable 属性
  const processedTreeData = React.useMemo(() => {
    const processNode = (node: API.BaseTreeNode): API.BaseTreeNode => {
      return {
        ...node,
        checkable: !onlyLeaf || !node.baseTreeNodeChildren?.length,
        baseTreeNodeChildren: node.baseTreeNodeChildren?.map(processNode),
      };
    };
    return (debouncedSearchValue ? getFilteredTreeData(treeData) : treeData).map(processNode);
  }, [treeData, onlyLeaf, debouncedSearchValue]);

  const handleOk = () => {
    onOk(checkedNodes);
  };

  const handleCheck = (checked: { checked: number[] }, info: any) => {
    if (onlyLeaf) {
      const node = info.node;
      // 如果不是末级节点，则不允许选择
      if (node.baseTreeNodeChildren?.length > 0) {
        return;
      }
    }
    setCheckedKeys(checked.checked || []);
    setCheckedNodes((info.checkedNodes as unknown as API.BaseTreeNode[]) || []);
  };

  return (
    <Modal
      title="编辑知识点"
      open={open}
      afterOpenChange={(open) => {
        if (!open) {
          setCheckedKeys(defaultCheckedKeys);
          setCheckedNodes(
            defaultCheckedKeys.map((key, index) => {
              // @ts-ignore
              return {
                baseTreeNodeId: key,
                baseTreeNodeName: defaultCheckedNames[index] || '',
              } as API.BaseTreeNode;
            }),
          );
          setSearchValue('');
        }
      }}
      okButtonProps={{
        disabled: checkedKeys.length === 0,
      }}
      onOk={handleOk}
      onCancel={onCancel}
      width={'1000px'}
      okText="确定"
      cancelText="取消"
      className={modalCls}
    >
      <div className={modalContentCls}>
        <div className="selected-section">
          <div className="section-header">已选择 {checkedKeys.length} 个知识点</div>
          <div className="selected-list">
            {checkedNodes.map((node) => {
              return (
                <div key={node.baseTreeNodeId} className="selected-item">
                  <span className="item-title">{node.baseTreeNodeName}</span>
                  <CloseOutlined
                    className="delete-icon"
                    onClick={() => {
                      setCheckedKeys(checkedKeys.filter((key) => key !== node.baseTreeNodeId));
                      setCheckedNodes(
                        checkedNodes.filter((n) => n.baseTreeNodeId !== node.baseTreeNodeId),
                      );
                    }}
                  />
                </div>
              );
            })}
          </div>
        </div>

        <div className="tree-section">
          <Input
            placeholder="搜索"
            prefix={<SearchOutlined />}
            value={searchValue}
            onChange={(e) => setSearchValue(e.target.value.trim())}
            className="search-input"
            allowClear
          />

          <Tree
            key={debouncedSearchValue + 'base-tree'}
            defaultExpandAll={Boolean(debouncedSearchValue)}
            defaultExpandedKeys={checkedKeys}
            checkable
            checkStrictly
            autoExpandParent
            height={400}
            checkedKeys={checkedKeys}
            onCheck={handleCheck as TreeProps['onCheck']}
            // @ts-ignore
            treeData={processedTreeData}
            fieldNames={{
              title: 'baseTreeNodeName',
              key: 'baseTreeNodeId',
              children: 'baseTreeNodeChildren',
            }}
            className="knowledge-tree"
          />
        </div>
      </div>
    </Modal>
  );
};

export default KnowledgePointModal;

const modalCls = css`
  .ant-modal-header {
    padding-right: 16px;
  }
  .ant-modal-footer {
    padding-right: 16px;
  }
`;

const modalContentCls = css`
  display: flex;
  height: 480px;

  .selected-section {
    display: flex;
    flex-direction: column;
    width: 300px;
    padding: 16px 0 16px 16px;
    border-right: 1px solid #f0f0f0;

    .section-header {
      flex-shrink: 0;
      margin-bottom: 12px;
      padding-right: 16px;
      color: rgba(0, 0, 0, 0.45);
      font-size: 14px;
    }

    .selected-list {
      flex: 1;
      padding-right: 16px;
      overflow: auto;

      // 优化滚动条样式
      &::-webkit-scrollbar {
        width: 6px;
        height: 6px;
      }

      &::-webkit-scrollbar-thumb {
        background: #ccc;
        border-radius: 3px;
      }

      &::-webkit-scrollbar-track {
        background: transparent;
      }

      .selected-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 8px;
        padding: 4px 8px;
        font-size: 14px;
        background: #f5f5f5;
        border-radius: 2px;

        &:last-child {
          margin-bottom: 0;
        }

        .item-title {
          flex: 1;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }

        .delete-icon {
          flex-shrink: 0;
          margin-left: 8px;
          color: rgba(0, 0, 0, 0.45);
          cursor: pointer;
          transition: color 0.3s;

          &:hover {
            color: #ff4d4f;
          }
        }
      }
    }
  }

  .tree-section {
    display: flex;
    flex: 1;
    flex-direction: column;
    padding: 16px 0 16px 16px;

    .search-input {
      width: 300px;
      margin: 0 16px 16px;
    }

    .knowledge-tree {
      flex: 1;
      overflow: auto;
    }
  }
`;
