import { submitImportQuestionAudit } from '@/services/api/review';
import { css } from '@emotion/css';
import { Button, Form, Input, message, Modal, Space } from 'antd';
import to from 'await-to-js';
import React, { useEffect, useState } from 'react';

const { TextArea } = Input;

const errorTypes = ['questionWrong', 'answerWrong', 'explanationWrong'] as const;
const errorTypeLabels: Record<keyof RejectReason, string> = {
  questionWrong: '题目错误',
  answerWrong: '答案错误',
  explanationWrong: '解析错误',
};

export type RejectReason = {
  questionWrong: string;
  answerWrong: string;
  explanationWrong: string;
};

type RejectModalProps = {
  open: boolean;
  auditTaskId: number;
  activeQuestion: API.Review.ImportQuestionAudit['list'][number];
  onOk: (reason: RejectReason) => void;
  onCancel: () => void;
};

const RejectModal: React.FC<RejectModalProps> = ({
  open,
  auditTaskId,
  onOk,
  onCancel,
  activeQuestion,
}) => {
  const [selectedTypes, setSelectedTypes] = useState<(typeof errorTypes)[number][]>(
    errorTypes.slice(0, 1),
  );
  const [form] = Form.useForm();

  useEffect(() => {
    if (open && activeQuestion) {
      // 初始化选中的错误类型
      const types = errorTypes.filter((type) => activeQuestion[type]);
      setSelectedTypes(types.length > 0 ? types : errorTypes.slice(0, 1));

      // 设置表单初始值
      const initialValues = {
        questionWrong: activeQuestion.questionWrong || undefined,
        answerWrong: activeQuestion.answerWrong || undefined,
        explanationWrong: activeQuestion.explanationWrong || undefined,
      };
      form.setFieldsValue(initialValues);
    }
  }, [open, activeQuestion, form]);

  const handleOk = async () => {
    const [err, reason] = await to(form.validateFields());

    if (err) {
      return;
    }

    const param = {
      auditTaskId,
      questionId: activeQuestion.questionId,
      answerWrong: reason.answerWrong,
      explanationWrong: reason.explanationWrong,
      questionWrong: reason.questionWrong,
      auditStatus: 3 as API.EnumConstantData['auditStatusList'][number]['value'],
      baseTreeNodeIds: activeQuestion.baseTreeNodeIds || [],
    };

    const [error, res] = await to(submitImportQuestionAudit(param));

    if (error) {
      return;
    }

    if (res?.code) {
      message.error('操作失败: ' + res?.message);
      return;
    }

    form.resetFields();
    setSelectedTypes(errorTypes.slice(0, 1));
    onOk(reason);
  };

  const handleCancel = () => {
    form.resetFields();
    setSelectedTypes(errorTypes.slice(0, 1));
    onCancel();
  };

  const handleTypeClick = (type: (typeof errorTypes)[number]) => {
    if (selectedTypes.includes(type)) {
      // 如果已选中，则移除（但至少保留一个）
      if (selectedTypes.length > 1) {
        setSelectedTypes(selectedTypes.filter((t) => t !== type));
        form.setFieldValue(type, undefined);
      }
    } else {
      // 如果未选中，则添加
      setSelectedTypes([...selectedTypes, type]);
    }
  };

  return (
    <Modal
      title="不通过原因"
      open={open}
      onOk={handleOk}
      onCancel={handleCancel}
      footer={[
        <Button key="cancel" onClick={handleCancel}>
          返回
        </Button>,
        <Button key="submit" type="primary" onClick={handleOk}>
          确认
        </Button>,
      ]}
    >
      <div className={rejectModalCls}>
        <div className="error-types">
          <Space>
            {errorTypes.map((type) => (
              <Button
                key={type}
                type={selectedTypes.includes(type) ? 'primary' : 'default'}
                onClick={() => handleTypeClick(type)}
              >
                {errorTypeLabels[type]}
              </Button>
            ))}
          </Space>
        </div>
        <Form form={form} layout="vertical">
          {selectedTypes.map((type) => (
            <Form.Item
              key={type}
              name={type}
              label={errorTypeLabels[type]}
              rules={[{ required: true, message: `请输入${errorTypeLabels[type]}的原因` }]}
            >
              <TextArea rows={4} placeholder={`请输入${errorTypeLabels[type]}的原因`} />
            </Form.Item>
          ))}
        </Form>
      </div>
    </Modal>
  );
};

export default RejectModal;

const rejectModalCls = css`
  .error-types {
    margin-bottom: 16px;
  }
  .error-input {
    margin-top: 12px;
    margin-bottom: 16px;
  }
`;
