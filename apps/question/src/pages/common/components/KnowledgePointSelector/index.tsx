import { fetchBaseTreeDetail } from '@/services/api';
import { getBaseTreeNodeMap } from '@/utils/tree';
import { css } from '@emotion/css';
import { Button, message, Space, Spin } from 'antd';
import to from 'await-to-js';
import React, { useEffect, useMemo, useState } from 'react';
import KnowledgePointModal from '../KnowledgePointModal';

export interface KnowledgePointSelectorProps {
  value?: number[];
  onChange?: (value: { baseTreeNodeIds: number[]; BaseTreeNames: string[] }) => void;
  canEdit?: boolean;
  baseTreeId?: number;
  onlyLeaf?: boolean;
}

const KnowledgePointSelector: React.FC<KnowledgePointSelectorProps> = ({
  value,
  onChange,
  canEdit = true,
  baseTreeId,
  onlyLeaf = false,
}) => {
  const [knowledgePointModalOpen, setKnowledgePointModalOpen] = useState(false);
  const [treeData, setTreeData] = useState<API.BaseTreeNode[]>([]);
  const [treeDataMap, setTreeDataMap] = useState<Map<number, API.BaseTreeNode> | null>(null);
  const [loading, setLoading] = useState(false);

  const defaultCheckedBaseTreeNames = useMemo(() => {
    if (!treeDataMap || !value || value.length === 0) return [];

    return value.map((id: number) => treeDataMap?.get(id)?.baseTreeNodeName || '').filter(Boolean);
  }, [value, treeDataMap]);

  // 获取详情数据
  const fetchDetail = async () => {
    if (!baseTreeId) return;

    setLoading(true);
    try {
      const [err, res] = await to(fetchBaseTreeDetail(baseTreeId));
      if (err || res.code) {
        message.error(err?.message || res?.message);
        return;
      }

      setTreeData([res.data.baseTreeDetail]);
      const treeDataMap = getBaseTreeNodeMap(res.data.baseTreeDetail);
      setTreeDataMap(treeDataMap);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDetail();
  }, [baseTreeId]);

  const handleModalOk = (checkedNodes: API.BaseTreeNode[]) => {
    setKnowledgePointModalOpen(false);
    onChange?.({
      baseTreeNodeIds: checkedNodes.map((node) => node.baseTreeNodeId),
      BaseTreeNames: checkedNodes.map((node) => node.baseTreeNodeName),
    });
  };
  const renderContent = () => (
    <div className={styles.container}>
      <Spin spinning={loading}>
        <Space style={{ flexWrap: 'wrap' }}>
          {defaultCheckedBaseTreeNames?.map((name) => (
            <Button key={name} size="small">
              {name}
            </Button>
          ))}

          {canEdit && (
            <Button
              type="link"
              size="small"
              onClick={() => {
                setKnowledgePointModalOpen(true);
              }}
            >
              更改
            </Button>
          )}
        </Space>
      </Spin>

      {canEdit && Boolean(treeData.length) && (
        <KnowledgePointModal
          treeData={treeData}
          open={knowledgePointModalOpen}
          onOk={handleModalOk}
          onCancel={() => setKnowledgePointModalOpen(false)}
          defaultCheckedKeys={value || []}
          defaultCheckedNames={defaultCheckedBaseTreeNames || []}
          onlyLeaf={onlyLeaf}
        />
      )}
    </div>
  );

  return renderContent();
};

export default KnowledgePointSelector;

const styles = {
  container: css`
    .ant-space {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
    }
  `,
};
