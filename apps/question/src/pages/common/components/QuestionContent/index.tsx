import RichTextWithMath from '@/components/RichTextWithMath';
import classNames from 'classnames';
import React from 'react';
import styles from './index.module.less';

interface QuestionOption {
  optionKey: string;
  optionVal: string;
}

interface IQuestionContent {
  questionStem?: string;
  questionOptionList?: QuestionOption[];
  subQuestionList?: Question[];
}

interface QuestionAnswer {
  answerOptionList: QuestionOption[];
}

interface Question {
  questionId: string;
  questionContent: IQuestionContent;
  questionAnswer: QuestionAnswer;
  subQuestionList?: Question[];
  questionAnswerMode: number;
}

interface QuestionContentProps {
  question: Question;
  level?: number;
}

const QuestionContent: React.FC<QuestionContentProps> = ({ question, level = 0 }) => {
  const { questionContent, questionAnswer, subQuestionList, questionAnswerMode } = question;

  return (
    <div className={styles.questionContent}>
      {questionContent.questionOptionList && questionContent.questionOptionList.length > 0 && (
        <div className={styles.content}>
          <div className={styles.title}>选项：</div>
          <div
            className={classNames(styles.options, {
              [styles.row]: questionAnswerMode === 4,
              [styles.column]: questionAnswerMode !== 4,
            })}
          >
            {questionContent.questionOptionList.map((option, idx) => (
              // optionListContent(option, idx, question.questionAnswerMode, question.questionId)
              <div key={idx} className={styles.optionItem}>
                {option.optionKey}
                {option.optionKey && option.optionVal ? '：' : ''}
                <RichTextWithMath htmlContent={option.optionVal} questionId={question.questionId} />
              </div>
            ))}
          </div>
        </div>
      )}
      {subQuestionList?.map((subQuestion: Question, idx: number) => (
        <div key={idx} className={classNames(styles.subQuestion, styles.paddingLeft24)}>
          <div className={styles.subQuestionTitle}>子题 {idx + 1}：</div>
          <RichTextWithMath
            htmlContent={subQuestion.questionContent.questionStem ?? ''}
            questionId={subQuestion.questionId}
          />
          <QuestionContent question={subQuestion} level={level + 1} />
        </div>
      ))}
      {questionAnswer?.answerOptionList && (
        <div className={styles.content}>
          <div className={styles.title}>答案：</div>
          {questionAnswer?.answerOptionList?.map((option, idx) => (
            // optionListContent(option, idx, question.questionAnswerMode, question.questionId)
            <div key={idx} className={styles.optionItem}>
              {option.optionKey}
              {option.optionKey && option.optionVal ? '：' : ''}
              <RichTextWithMath htmlContent={option.optionVal} questionId={question.questionId} />
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default QuestionContent;
