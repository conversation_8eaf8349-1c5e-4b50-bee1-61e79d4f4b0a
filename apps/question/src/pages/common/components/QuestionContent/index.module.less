.questionContent {
  .content {
    margin-bottom: 16px;

    .title {
      margin-bottom: 8px;
      font-weight: bold;
      font-size: 16px;
    }
    .options {
      display: flex;
      flex-direction: column;
      &.row {
        flex-direction: row;
        gap: 16px;
      }
      &.column {
        flex-direction: column;
      }
    }
    .optionItem {
      display: flex;
      align-items: start;
      line-height: 2.5;
    }
  }

  .subQuestion {
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px dashed #d9d9d9;
    &.paddingLeft24 {
      padding-left: 24px;
    }
    .subQuestionTitle {
      margin-bottom: 12px;
      color: rgba(0, 0, 0, 0.85);
      font-weight: 500;
    }
  }
}
