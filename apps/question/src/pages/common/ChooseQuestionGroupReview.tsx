import PageContainerModal from '@/components/PageContainerModal';
import React from 'react';
import ChooseQuestionGroupReviewDetail, {
  type ChooseQuestionGroupReviewDetailProps,
} from './ChooseQuestionGroupReviewDetail';

const ChooseQuestionGroupReview: React.FC<
  ChooseQuestionGroupReviewDetailProps & { open: boolean }
> = (props) => {
  return (
    <PageContainerModal
      modalProps={{
        open: props.open,
        destroyOnClose: true,
      }}
      pageHeaderRender={false}
    >
      <ChooseQuestionGroupReviewDetail {...props} />
    </PageContainerModal>
  );
};

export default ChooseQuestionGroupReview;
