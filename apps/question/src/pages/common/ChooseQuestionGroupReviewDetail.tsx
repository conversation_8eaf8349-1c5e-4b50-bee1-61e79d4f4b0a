import GroupTabs from '@/components/GroupTabs';
import QuestionList, { QuestionListType } from '@/components/QuestionList';
import { getAuditTaskDetail, submitAuditTask } from '@/services/api/review';
import { ArrowLeftOutlined } from '@ant-design/icons';
import { ModalForm, ProFormTextArea } from '@ant-design/pro-components';
import { Button, Card, Flex, message, Modal, Space, Tag } from 'antd';
import React, { useCallback, useEffect, useMemo, useState } from 'react';

const TAG_STYLE = {
  margin: 0,
  padding: '4px 8px',
};

export type ChooseQuestionGroupReviewDetailProps = {
  onSubmit: () => void;
  onBack: () => void;
  canEdit: boolean;
  data: API.Review.AuditTaskItem;
};

const ChooseQuestionGroupReviewDetail: React.FC<ChooseQuestionGroupReviewDetailProps> = ({
  onSubmit,
  onBack,
  canEdit,
  data,
}) => {
  const [modal, contextHolder] = Modal.useModal();
  const [groupActiveKey, setGroupActiveKey] = useState<string>('');
  const [recordGroupKey, setRecordGroupKey] = useState<string[]>([]);

  const groupItemRenderFn = useCallback(({ label, status }: API.ExerciseGroupTabItem) => {
    return (
      <Flex gap={16} justify="space-between">
        <span>{label}</span>
        {status === 1 && (
          <Tag color="#87d068" style={{ margin: 0 }}>
            新增
          </Tag>
        )}
        {status === 2 && (
          <Tag color="#f50" style={{ margin: 0 }}>
            删除
          </Tag>
        )}
      </Flex>
    );
  }, []);

  const [loading, setLoading] = useState(false);
  const [detail, setDetail] = useState<API.Review.AuditTaskDetail | null>(null);
  const [modalVisible, setModalVisible] = useState(false);

  const groupList = useMemo(() => {
    if (!detail) {
      return [];
    }

    return detail?.chooseQuestion?.list ?? [];
  }, [detail]);

  const sceneCategory = useMemo(() => {
    if (!detail) {
      return 0;
    }
    return detail?.chooseQuestion?.sceneCategory ?? 0;
  }, [detail]);

  const auditStatus = useMemo(() => {
    if (!detail) {
      return 0;
    }
    return detail.auditStatus ?? 0;
  }, [detail]);

  const auditResultInfo = useMemo(() => {
    if (!detail) {
      return '';
    }
    return detail.chooseQuestion?.curAuditTaskFailReason ?? '';
  }, [detail]);

  const groupTabs = useMemo(() => {
    return groupList.map((item) => ({
      label: item.questionGroupName,
      children: null,
      key: String(item.questionGroupId),
      editable: true,
      closable: false,
      status: item.addDelStatus ?? 0,
    }));
  }, [groupList]);

  const groupQuestionMap = useMemo(() => {
    const map = new Map<string, API.QuestionItemType[]>();

    groupList.forEach((item) => {
      const add: API.QuestionItemType[] = [];
      const normal: API.QuestionItemType[] = [];
      const del: API.QuestionItemType[] = [];
      const { questionGroupId, questionStatusInfoList } = item;
      questionStatusInfoList.forEach((info) => {
        const { addDelStatus, questionInfo } = info;
        const question = { ...info, ...questionInfo };
        if (addDelStatus === 1) {
          add.push(question);
        } else if (addDelStatus === 2) {
          del.push(question);
        } else {
          normal.push(question);
        }
      });
      map.set(String(questionGroupId), [...add, ...normal, ...del]);
    });
    return map;
  }, [groupList]);

  const showQuestionList = useMemo(() => {
    if (!groupActiveKey) {
      return [];
    }
    const groupQuestionList = groupQuestionMap.get(groupActiveKey);
    return groupQuestionList ?? [];
  }, [groupActiveKey, groupQuestionMap]);

  useEffect(() => {
    setLoading(true);

    getAuditTaskDetail({ auditTaskId: data.auditTaskId })
      .then((res) => {
        if (res.code) {
          message.error(res.message || '获取审核详情失败');
          return;
        }

        setDetail(res.data);
      })
      .catch((error) => {
        console.error('获取审核详情失败:', error);
      })
      .finally(() => {
        setLoading(false);
      });
  }, []);

  useEffect(() => {
    if (groupTabs.length === 0) {
      setRecordGroupKey([]);
      setGroupActiveKey('');

      return;
    }
    setRecordGroupKey([groupTabs[0].key]);
    setGroupActiveKey(groupTabs[0].key);
  }, [groupTabs]);

  useEffect(() => {
    if (groupActiveKey && !recordGroupKey.includes(groupActiveKey)) {
      setRecordGroupKey([...recordGroupKey, groupActiveKey]);
    }
  }, [groupActiveKey, recordGroupKey]);

  // 处理提交按钮点击
  const handleSubmit = () => {
    if (!detail) return;
    const unViewGroup = groupTabs.filter((item) => !recordGroupKey.includes(item.key));
    if (unViewGroup.length > 0) {
      const groupNames = unViewGroup.map((item) => item.label).join(',');
      message.warning(`${groupNames}题组尚未查看，请查看后再进行审核`);
      return;
    }

    modal.confirm({
      title: '是否确认审核通过',
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        try {
          setLoading(true);
          const res = await submitAuditTask({
            auditTaskId: detail.auditTaskId,
            auditTaskStatus: 2,
          });

          if (res.code === 0) {
            message.success('提交成功');
            onSubmit();
          } else {
            message.error(res.message || '提交失败');
          }
        } catch (error) {
          console.error('提交审核结果失败:', error);
          message.error('提交失败');
        } finally {
          setLoading(false);
        }
      },
    });
  };

  const onUnpass = () => {
    setModalVisible(true);
  };

  const onPass = () => {
    handleSubmit();
  };

  const onShowAuditFailReason = () => {
    if (!auditResultInfo) {
      message.error('审核不通过原因不存在');
      return;
    }
    modal.info({
      title: '审核不通过原因',
      content: auditResultInfo,
      okText: '确认',
    });
  };

  return (
    <Space direction="vertical" style={{ width: '100%' }}>
      <Card>
        <Flex gap={16}>
          <span style={{ cursor: 'pointer', fontWeight: 'normal', fontSize: 15 }} onClick={onBack}>
            <ArrowLeftOutlined />
            返回
          </span>

          <span style={{ fontSize: 18, flex: 1 }}>
            <span style={{ fontWeight: 'bold' }}>{data?.auditTaskName} </span>
            【ID: {data?.auditTaskId}】
          </span>
          {canEdit && auditStatus === 1 && (
            <Space size={16}>
              <Button type="primary" onClick={onUnpass} loading={loading} danger>
                不通过
              </Button>
              <Button type="primary" onClick={onPass} loading={loading}>
                通过
              </Button>
            </Space>
          )}
          {!canEdit && auditStatus === 2 && (
            <Space size={16}>
              <Tag color="#87d068" style={TAG_STYLE}>
                审核通过
              </Tag>
            </Space>
          )}
          {!canEdit && auditStatus === 3 && (
            <Space size={8}>
              <Button color="primary" variant="link" onClick={onShowAuditFailReason}>
                查看原因
              </Button>
              <Tag color="#f50" style={TAG_STYLE}>
                审核不通过
              </Tag>
            </Space>
          )}
          {!canEdit && auditStatus === 4 && (
            <Space size={8}>
              <Tag color="#2db7f5" style={TAG_STYLE}>
                审核已撤回
              </Tag>
            </Space>
          )}
        </Flex>
      </Card>

      <div>
        {sceneCategory !== 3 && (
          <GroupTabs
            editable={false}
            addable={false}
            groups={groupTabs}
            activeKey={groupActiveKey}
            onChange={setGroupActiveKey}
            itemRenderFn={groupItemRenderFn}
          />
        )}
        <Card>
          <QuestionList
            // @ts-ignore
            questions={showQuestionList}
            type={QuestionListType.EXERCISE_AUDIT}
            loading={loading}
          />
        </Card>
      </div>

      <ModalForm<{ reason: string }>
        title="不通过原因"
        width={500}
        open={modalVisible}
        onOpenChange={setModalVisible}
        submitter={{
          searchConfig: {
            submitText: '确认',
            resetText: '取消',
          },
        }}
        modalProps={{
          destroyOnClose: true,
        }}
        submitTimeout={2000}
        layout="vertical"
        onFinish={async (values) => {
          if (!detail) {
            message.error('审核任务详情不存在');
            return false;
          }

          try {
            const res = await submitAuditTask({
              auditTaskId: detail.auditTaskId,
              reason: values.reason,
              auditTaskStatus: 3,
            });

            if (res.code === 0) {
              message.success('提交成功');
              onSubmit();
              setModalVisible(false);
              return true;
            } else {
              message.error(res.message || '提交失败');
              return false;
            }
          } catch (error) {
            console.error('提交审核结果失败:', error);
            message.error('提交失败');
            return false;
          }
        }}
      >
        <ProFormTextArea
          initialValue={``}
          name="reason"
          label=""
          placeholder="请输入不通过原因"
          rules={[{ required: true, message: '请输入不通过原因' }]}
        />
      </ModalForm>
      {contextHolder}
    </Space>
  );
};

export default ChooseQuestionGroupReviewDetail;
