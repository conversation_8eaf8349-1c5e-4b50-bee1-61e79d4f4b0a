import PageContainerModal from '@/components/PageContainerModal';
import React from 'react';
import ImportQuestionReviewDetail from './ImportQuestionReviewDetail';

type ImportQuestionReviewProps = {
  id: number;
  data?: API.Review.AuditTaskItem;
  onBack: () => void;
  onSubmit?: () => void;
  open: boolean;
  canEdit?: boolean;
};

const ImportQuestionReview: React.FC<ImportQuestionReviewProps> = ({
  id,
  data,
  onBack,
  onSubmit,
  open,
  canEdit = false,
}) => {
  return (
    <PageContainerModal
      modalProps={{
        open,
        destroyOnClose: true,
      }}
      fixedHeader
      pageHeaderRender={false}
    >
      <ImportQuestionReviewDetail
        onSubmit={() => {
          onSubmit?.();
        }}
        onBack={onBack}
        data={
          {
            ...data,
            auditTaskId: id,
          } as API.Review.AuditTaskItem
        }
        canEdit={canEdit}
      />
    </PageContainerModal>
  );
};

export default ImportQuestionReview;
