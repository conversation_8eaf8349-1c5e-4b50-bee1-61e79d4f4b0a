import PageContainerModal from '@/components/PageContainerModal';
import React from 'react';
import ChooseQuestionReviewDetail, {
  type ChooseQuestionReviewDetailProps,
} from './ChooseQuestionReviewDetail';

const ChooseQuestionReview: React.FC<ChooseQuestionReviewDetailProps & { open: boolean }> = (
  props,
) => {
  return (
    <PageContainerModal
      modalProps={{
        open: props.open,
        destroyOnClose: true,
      }}
      pageHeaderRender={false}
    >
      <ChooseQuestionReviewDetail {...props} />
    </PageContainerModal>
  );
};

export default ChooseQuestionReview;
