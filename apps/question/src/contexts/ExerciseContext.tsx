import { createContext } from 'react';

interface ExerciseContextType {
  // 选中的学科学段
  selectedSubject: number[];
  setSelectedSubject: (value: number[]) => void;
  // 练习类型
  type: number;
  // 练习信息
  exerciseInfo: API.ExerciseQuestionListData | undefined;
  setExerciseInfo: (value: API.ExerciseQuestionListData) => void;
  // 选中的树节点
  selectedTreeNode: API.Common.BaseTreeNodeAntdType | undefined;
  setSelectedTreeNode: (value: API.Common.BaseTreeNodeAntdType) => void;
  // 获取练习信息
  fetchExerciseInfoFunc: () => void;
  // 获取练习信息loading
  fetchExerciseInfoLoading: boolean;
  // 树更新时间戳
  treeUpdateTimestamp: number | undefined;
  setTreeUpdateTimestamp: (value: number) => void;
  // 当前活动组id
  activeGroupId: string | undefined;
  setActiveGroupId: (value: string | undefined) => void;
  // 业务树值
  bizTreeValue: number[];
}

const ExerciseContext = createContext<ExerciseContextType>({
  selectedSubject: [],
  setSelectedSubject: () => {},
  type: 0,
  exerciseInfo: {} as API.ExerciseQuestionListData,
  setExerciseInfo: () => {},
  selectedTreeNode: undefined,
  setSelectedTreeNode: () => {},
  fetchExerciseInfoFunc: () => {},
  fetchExerciseInfoLoading: false,
  treeUpdateTimestamp: undefined,
  setTreeUpdateTimestamp: () => {},
  activeGroupId: undefined,
  setActiveGroupId: () => {},
  bizTreeValue: [],
});

export { ExerciseContext, ExerciseContextType };
