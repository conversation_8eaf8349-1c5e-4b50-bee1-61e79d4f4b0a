import {
  clearStorage,
  getStorageItem,
  removeStorageItem,
  setStorageItem,
} from '@repo/lib/utils/local-storage';

// 带过期时间的存储数据结构
interface StorageWithExpiry<T> {
  data: T;
  expiry: number; // 过期时间戳
}

export class LocalDraftService {
  private key: string;

  constructor() {
    this.key = 'question';
  }

  /**
   * 加载数据（不带过期时间）
   */
  load<T>(name: string, defaultValue?: T) {
    return getStorageItem<T>(`${this.key}-${name}`, defaultValue as T);
  }

  /**
   * 保存数据（不带过期时间）
   */
  save<T>(name: string, content: T) {
    setStorageItem<T>(`${this.key}-${name}`, content);
  }

  /**
   * 清除指定名称的数据
   */
  clear(name: string) {
    removeStorageItem(`${this.key}-${name}`);
  }

  /**
   * 加载带过期时间的数据，如果过期自动清除
   * @param name 存储名称
   * @param defaultValue 默认值
   * @returns 数据或默认值（如果过期或不存在）
   */
  loadWithExpiry<T>(name: string, defaultValue?: T): T | undefined {
    const key = `${this.key}-${name}-expiry`;
    const stored = getStorageItem<StorageWithExpiry<T> | null>(key, null);

    if (!stored) {
      return defaultValue;
    }

    // 检查是否过期
    if (Date.now() > stored.expiry) {
      // 过期了，自动删除数据
      this.clearWithExpiry(name);
      return defaultValue;
    }

    return stored.data;
  }

  /**
   * 保存带过期时间的数据
   * @param name 存储名称
   * @param content 数据内容
   * @param ttl 过期时间（毫秒），默认1小时
   */
  saveWithExpiry<T>(name: string, content: T, ttl: number = 60 * 60 * 1000) {
    const expiry = Date.now() + ttl;
    const dataWithExpiry: StorageWithExpiry<T> = {
      data: content,
      expiry,
    };
    const key = `${this.key}-${name}-expiry`;
    setStorageItem<StorageWithExpiry<T>>(key, dataWithExpiry);
  }

  /**
   * 清除带过期时间的数据
   * @param name 存储名称
   */
  clearWithExpiry(name: string) {
    const key = `${this.key}-${name}-expiry`;
    removeStorageItem(key);
  }

  /**
   * 清除所有数据
   */
  clearAll() {
    clearStorage();
  }
}
