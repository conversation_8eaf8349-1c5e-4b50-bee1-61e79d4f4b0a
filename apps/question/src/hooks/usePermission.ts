import { useModel } from '@umijs/max';
import { useCallback, useMemo } from 'react';

const usePermission = () => {
  const { initialState } = useModel('@@initialState');

  const menus = useMemo(() => {
    return initialState?.currentUser?.menus ?? [];
  }, [initialState]);

  const actionPermissions = useMemo(() => {
    return menus.filter((menu) => menu.menuType === 2);
  }, [menus]);

  const checkActionPermission = useCallback(
    (action: string) => {
      return actionPermissions.some((permission) => permission.menuPath === action);
    },
    [actionPermissions],
  );

  return {
    checkActionPermission,
  };
};

export default usePermission;
