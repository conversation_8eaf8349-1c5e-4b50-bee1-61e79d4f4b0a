import { ExerciseContext } from '@/contexts/ExerciseContext';
import { useCallback, useContext, useEffect, useMemo } from 'react';

export interface ExerciseGroupInsertPositionOpt {
  name: string;
  label: string;
  value: string;
}

export interface ExerciseGroup {
  groups: API.ExerciseQuestionGroupListItem[]; // 练习的分组
  groupTotalDifficultStat: API.ExerciseDifficultStat[]; // 练习的总体难度
  activeGroup: API.ExerciseQuestionGroupListItem | undefined; // 当前选择的组
  activeGroupIndex: number; // 当前选择组件的索引
  exerciseQuestions: API.ExerciseQuestionListItem[]; // 练习的所有问题
  exerciseQuestionMaps: Map<string, API.ExerciseQuestionListItem>; // 练习中所有问题的 map
  exerciseGroupTabs: API.ExerciseGroupTabItem[]; // 由组构成的 tabs
  exerciseGroupInsertPositionOpts: ExerciseGroupInsertPositionOpt[]; // 组可以插入的位置
  getGroupDifficultStat: (questionGroupId: number) => API.ExerciseDifficultStat[]; // 获取单个组的难度
  activeGroupDifficultStat: API.ExerciseDifficultStat[]; // 当前选中组的难度
  getGroupQuestions: (questionGroupId: number) => API.ExerciseQuestionListItem[]; // 通过组id获取组下的问题
  activeGroupQuestions: API.ExerciseQuestionListItem[]; // 当前选择组下的所有问题
  exerciseNoQuestionGroups: API.ExerciseQuestionGroupListItem[]; // 练习中没有问题的组
  exerciseUnnamedGroup: API.ExerciseQuestionGroupListItem | undefined; // 练习中未命名的组
  exerciseGroupNames: string[]; // 练习中组的名字
}

export const useExerciseGroup = (): ExerciseGroup => {
  const { exerciseInfo, setActiveGroupId, activeGroupId } = useContext(ExerciseContext);

  const groups = useMemo(() => {
    const list = exerciseInfo?.list ?? [];
    return list.filter((item) => item.addDelStatus !== 2);
  }, [exerciseInfo]);

  const groupTotalDifficultStat = useMemo(() => {
    return exerciseInfo?.difficultStat ?? [];
  }, [exerciseInfo]);

  const activeGroup = useMemo(() => {
    if (!activeGroupId) {
      return undefined;
    }
    return groups.find((item) => item.questionGroupId === Number(activeGroupId));
  }, [groups, activeGroupId]);

  const activeGroupIndex = useMemo(() => {
    if (!activeGroupId) {
      return -1;
    }
    return groups.findIndex((item) => item.questionGroupId === Number(activeGroupId));
  }, [groups, activeGroupId]);

  /**
   * 练习所有题目
   */
  const exerciseQuestions = useMemo(() => {
    const questions = groups
      .map((item) => {
        const { questionStatusInfoList, questionGroupId, questionGroupName } = item;
        return questionStatusInfoList.map((question) => {
          const { questionInfo, addDelStatus } = question;
          const isShowAddButton = addDelStatus !== 2;
          return {
            ...questionInfo,
            ...question,
            questionGroupId,
            questionGroupName,
            isShowAddButton,
          };
        });
      })
      .flat();
    return questions;
  }, [groups]);

  /**
   * 练习所有题目的map
   */
  const exerciseQuestionMaps = useMemo(() => {
    const map = new Map<string, API.ExerciseQuestionListItem>();
    exerciseQuestions.forEach((question) => {
      const { questionId } = question;
      map.set(questionId, question);
    });
    return map;
  }, [exerciseQuestions]);

  /**
   * 获取题组标签
   */
  const exerciseGroupTabs = useMemo(() => {
    return groups.map((item) => ({
      label: item.questionGroupName,
      children: null,
      key: String(item.questionGroupId),
      editable: true,
      closable: false,
    }));
  }, [groups]);

  /**
   * 获取题组插入位置选项
   */
  const exerciseGroupInsertPositionOpts = useMemo(() => {
    const opts = exerciseGroupTabs.map((item) => ({
      name: item.label,
      label: item.label + '后',
      value: item.key,
    }));
    return [
      {
        name: '最前面',
        label: '最前面',
        value: '-1',
      },
      ...opts,
    ];
  }, [exerciseGroupTabs]);

  useEffect(() => {
    // 练习改变：设置默认题组， 没有题组则设置为空， 没有激活题组则设置为第一个题组， 没有找到激活题组则设置为第一个题组
    const firstGroup = exerciseGroupTabs[0];
    if (!firstGroup) {
      setActiveGroupId(undefined);
      return;
    }
    if (!activeGroupId) {
      setActiveGroupId(firstGroup.key);
      return;
    }
    const group = exerciseGroupTabs.find((item) => item.key === activeGroupId);
    if (!group) {
      setActiveGroupId(firstGroup.key);
    }
  }, [exerciseGroupTabs]);

  /**
   * 获取题组难度统计
   */
  const getGroupDifficultStat = useCallback(
    (questionGroupId: number): API.ExerciseDifficultStat[] => {
      const groupItem = groups.find((item) => item.questionGroupId === questionGroupId);
      return groupItem?.difficultStat ?? [];
    },
    [groups],
  );

  /**
   * 获取激活题组难度统计
   */
  const activeGroupDifficultStat = useMemo(() => {
    return getGroupDifficultStat(Number(activeGroupId));
  }, [activeGroupId, getGroupDifficultStat]);

  /**
   * 获取题组题目
   */
  const getGroupQuestions = useCallback(
    (questionGroupId: number): API.ExerciseQuestionListItem[] => {
      const groupItem = groups.find((item) => item.questionGroupId === questionGroupId);
      const questions = groupItem?.questionStatusInfoList ?? [];
      return questions
        .map((item) => exerciseQuestionMaps.get(item.questionId))
        .filter(Boolean) as API.ExerciseQuestionListItem[];
    },
    [groups, exerciseQuestionMaps],
  );

  /**
   * 获取激活题组题目
   */
  const activeGroupQuestions = useMemo(() => {
    return getGroupQuestions(Number(activeGroupId));
  }, [activeGroupId, getGroupQuestions]);

  const exerciseNoQuestionGroups = useMemo(() => {
    return groups.filter((group) => {
      const questions = group.questionStatusInfoList ?? [];
      const unDelQuestions = questions.filter((q) => q.addDelStatus !== 2);
      return unDelQuestions.length === 0;
    });
  }, [groups]);

  const exerciseUnnamedGroup = useMemo(() => {
    return groups.find((group) => group.questionGroupName === '未命名');
  }, [groups]);

  const exerciseGroupNames = useMemo(() => {
    return groups.map((group) => group.questionGroupName);
  }, [groups]);

  return {
    groups,
    groupTotalDifficultStat,
    activeGroup,
    activeGroupIndex,
    exerciseQuestions,
    exerciseQuestionMaps,
    exerciseGroupTabs,
    exerciseGroupInsertPositionOpts,
    getGroupDifficultStat,
    activeGroupDifficultStat,
    getGroupQuestions,
    activeGroupQuestions,
    exerciseNoQuestionGroups,
    exerciseUnnamedGroup,
    exerciseGroupNames,
  };
};
