import EnumManager from '@/utils/enumManager';
import { useModel } from '@umijs/max';
import { useMemo } from 'react';

type EnumManagerMap = {
  [K in keyof API.EnumConstantData]:
    | EnumManager<(API.EnumConstantData[K][number] & { label: string; text: string })[]>
    | undefined;
};

const useEnumManagerMap = () => {
  const { initialState } = useModel('@@initialState');
  const enumConstants = initialState?.globalDictValues?.enumConstants;

  return useMemo(() => {
    const map = {} as EnumManagerMap;

    if (!enumConstants) {
      return map;
    }

    (Object.keys(enumConstants) as Array<keyof API.EnumConstantData>).forEach((key) => {
      const enums = enumConstants[key];

      map[key] = new EnumManager(
        enums.map((item) => ({
          ...item,
          label: item.nameZh,
          text: item.nameZh,
        })),
      );
    });

    return map;
  }, [enumConstants]);
};

export default useEnumManagerMap;
