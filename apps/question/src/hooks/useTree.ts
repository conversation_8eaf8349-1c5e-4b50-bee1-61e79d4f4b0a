import { TreeNodeType } from '@/components/tree';
import { useMemo } from 'react';

/**
 * useTree Hook
 *
 * 该 Hook 用于处理树形数据，提供树形数据的映射和列表形式。
 *
 * @param {TreeNodeType[]} treeData - 输入的树形数据数组
 * @returns {{ treeDataMap: Map<string | number, TreeNodeType>, treeDataList: TreeNodeType[] }}
 * 返回一个对象，包含树形数据的映射和列表
 */
export const useTree = (treeData: TreeNodeType[]) => {
  return useMemo(() => {
    const map = new Map<string | number, TreeNodeType>();
    const list: TreeNodeType[] = [];

    const traverse = (nodes: TreeNodeType[]) => {
      nodes.forEach((node) => {
        map.set(node.key, node);
        list.push(node);
        if (node.children?.length) {
          traverse(node.children);
        }
      });
    };

    traverse(treeData);

    return { treeDataMap: map, treeDataList: list };
  }, [treeData]);
};
