import { useMemo, useState } from 'react';
import useEnumManagerMap from './useEnumManager';

/**
 * phase和学科的映射关系类型
 */
export type PhaseSubjectMapType = Record<string, string[]>;

/**
 * 选项类型
 */
export type OptionType = {
  label: string;
  value: string;
  disabled?: boolean;
};

/**
 * phase和学科关联逻辑的自定义Hook
 */
export const usePhaseSubject = () => {
  const {
    phaseSubjectRelation: phaseSubjectRelationEnumManager,
    phaseList: phaseListEnumManager,
    subjectList: subjectListEnumManager,
  } = useEnumManagerMap();
  // 选中的phase
  const [selectedPhases, setSelectedPhases] = useState<number[]>([]);
  // 选中的学科
  const [selectedSubjects, setSelectedSubjects] = useState<number[]>([]);

  /**
   * 学段选项
   */
  const phaseOptions = useMemo(() => {
    if (!selectedSubjects?.length) {
      return phaseListEnumManager?.enums;
    }

    return phaseSubjectRelationEnumManager?.enums.map(({ subjectList, ...phase }) => {
      const disabled = selectedSubjects.some((subject) =>
        subjectList.every((i) => i.value !== subject),
      );

      return {
        ...phase,
        disabled,
      };
    });
  }, [selectedSubjects]);

  /**
   * 学科选项
   */
  const subjectOptions = useMemo(() => {
    if (!selectedPhases?.length) {
      return subjectListEnumManager?.enums;
    }

    const subjects = new Set<number>();

    selectedPhases.forEach((phase) => {
      const relation = phaseSubjectRelationEnumManager?.enums.find((item) => item.value === phase);

      if (relation) {
        relation.subjectList.forEach((subject) => subjects.add(subject.value));
      }
    });

    return Array.from(subjects.values()).map((item) => {
      const subject = subjectListEnumManager?.enums.find((subject) => subject.value === item);

      return subject;
    });
  }, [selectedPhases]);

  return {
    selectedPhases,
    selectedSubjects,
    phaseOptions,
    subjectOptions,
    setSelectedPhases,
    setSelectedSubjects,
    phaseListEnumManager,
    subjectListEnumManager,
    phaseSubjectRelationEnumManager,
  };
};

export default usePhaseSubject;
