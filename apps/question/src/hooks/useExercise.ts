import { ExerciseContext } from '@/contexts/ExerciseContext';
import { useContext, useMemo } from 'react';

// 常量定义 - 提高代码可维护性
const SHELF_STATUS = {
  ON_SHELF: 1,
  OFF_SHELF: 0,
} as const;

const AUDIT_TASK_STATUS = {
  AUDITING: 1,
  AUDIT_PASSED: 2,
  AUDIT_FAILED: 3,
  AUDIT_CANCELED: 4,
} as const;

const QUESTION_SET_MODIFIED = {
  MODIFIED: 1,
  NOT_MODIFIED: 0,
} as const;

export interface ExerciseHookType {
  isShelf: boolean; // 是否上架
  isAuditing: boolean; // 审核中
  isAuditPassed: boolean; // 审核通过
  isAuditFailed: boolean; // 审核不通过
  isAuditCanceled: boolean; // 撤销审核
  hasContentChanged: boolean; // 内容有变更
  curAuditTaskFailReason: string; // 当前审核任务失败原因
}

export const useExercise = (): ExerciseHookType => {
  const { exerciseInfo } = useContext(ExerciseContext);

  // 使用单个 useMemo 优化性能，避免多次重复计算
  const exerciseStatus = useMemo(() => {
    // 提前返回，避免后续不必要的计算
    if (!exerciseInfo) {
      return {
        isShelf: false,
        isAuditing: false,
        isAuditPassed: false,
        isAuditFailed: false,
        isAuditCanceled: false,
        hasContentChanged: false,
        curAuditTaskFailReason: '',
      };
    }

    const { shelfStatus, curAuditTaskStatus, isQuestionSetModified, curAuditTaskFailReason } =
      exerciseInfo;

    return {
      isShelf: shelfStatus === SHELF_STATUS.ON_SHELF,
      isAuditing: curAuditTaskStatus === AUDIT_TASK_STATUS.AUDITING,
      isAuditPassed: curAuditTaskStatus === AUDIT_TASK_STATUS.AUDIT_PASSED,
      isAuditFailed: curAuditTaskStatus === AUDIT_TASK_STATUS.AUDIT_FAILED,
      isAuditCanceled: curAuditTaskStatus === AUDIT_TASK_STATUS.AUDIT_CANCELED,
      hasContentChanged: isQuestionSetModified === QUESTION_SET_MODIFIED.MODIFIED,
      curAuditTaskFailReason,
    };
  }, [exerciseInfo]);

  return exerciseStatus;
};
