---
description: 
globs: 
alwaysApply: true
---
---
description: 
globs: 
alwaysApply: true
---

# Question Monorepo

# 项目开发规范指南

## 项目描述
本项目是一个基于 React + TypeScript 的内容管理平台，主要功能包括题目管理、题目录入、场景题库、基础数据管理、审核等。

## 技术栈规范

### 核心框架
- React 18+
- TypeScript 4.9+
- Ant Design 5.x
- ProComponents 2.x

### 状态管理
- React Context (全局状态)
- React Hooks (组件状态)

### 工具库
- immer (不可变数据处理)
- await-to-js (异步错误处理)
- less (样式处理)

## 代码规范

### 组件开发规范

1. 组件命名
   - 使用 PascalCase 命名组件
   - 组件文件使用 `.tsx` 扩展名
   - 组件文件夹使用小写字母，多个单词用连字符连接

2. 组件结构
```typescript
// 1. 导入声明
import React from 'react';
import { Space } from 'antd';

// 2. 类型定义
interface Props {
  // ...
}

// 3. 组件定义
const Component: React.FC<Props> = ({ prop1, prop2 }) => {
  // 4. hooks 声明
  const [state, setState] = useState();

  // 5. 副作用
  useEffect(() => {
    // ...
  }, []);

  // 6. 事件处理函数
  const handleEvent = () => {
    // ...
  };

  // 7. 渲染
  return (
    // ...
  );
};

export default Component;
```

3. Props 规范
   - 必须使用 TypeScript 接口定义 Props
   - 可选属性使用 `?` 标记
   - 使用解构赋值获取 props

### 样式编写规范

1. 文件组织
   - 使用 `.module.less` 作为样式文件扩展名
   - 样式文件与组件文件同名

2. 命名规范
   - 使用 camelCase 命名样式类
   - 使用语义化命名
   - 避免使用过于具体的命名

3. 样式结构
```less
.container {
  // 布局属性
  display: flex;
  position: relative;

  // 盒模型属性
  margin: 0;
  padding: 0;

  // 视觉属性
  background: #fff;
  border: 1px solid #ddd;

  // 文字属性
  font-size: 14px;
  color: #333;

  // 其他属性
  cursor: pointer;
}
```

### 状态管理规范

1. 全局状态
   - 使用 Context API 管理全局状态
   - 将相关的状态和操作封装在同一个 Context 中
   - 使用 useReducer 处理复杂状态逻辑

2. 组件状态
   - 使用 useState 管理简单状态
   - 使用 useReducer 管理复杂状态
   - 避免过度使用状态，优先使用 props

3. 状态更新
   - 使用 immer 处理不可变数据
   - 批量更新状态时使用函数式更新

### TypeScript 规范

1. 类型定义
   - 优先使用 interface 定义对象类型
   - 使用 type 定义联合类型和工具类型
   - 为所有 props 定义类型

2. 类型导入
   - 使用 type 关键字导入类型
   - 避免使用 any 类型
   - 使用 unknown 代替 any

3. 泛型使用
   - 合理使用泛型提高代码复用性
   - 为泛型参数提供默认类型

## 项目结构规范

```
src/
├── assets/        # 资源文件
├── components/        # 公共组件
├── contexts/         # 全局状态
├── hooks/           # 自定义 hooks
├── locales/         # 多语言
├── pages/           # 页面组件
├── services/        # API 服务
├── types/           # 类型定义
├── utils/           # 工具函数
└── styles/          # 全局样式
```

## 禁止事项

1. 代码规范
   - 禁止使用 var 声明变量
   - 禁止使用 any 类型（特殊情况除外）
   - 禁止使用 jQuery 或直接操作 DOM
   - 禁止在模板中使用复杂表达式

2. 性能相关
   - 禁止在循环中创建函数
   - 禁止在渲染函数中进行复杂计算
   - 禁止过度使用 useEffect

3. 安全相关
   - 禁止在前端存储敏感信息
   - 禁止使用 eval 等危险函数
   - 禁止直接使用用户输入

## 特殊注意事项

1. 组件开发
   - 使用 React.memo 优化性能
   - 使用 useCallback 缓存函数
   - 使用 useMemo 缓存计算结果

2. 错误处理
   - 使用 try-catch 处理同步错误
   - 使用 await-to-js 处理异步错误
   - 实现错误边界捕获渲染错误

3. 性能优化
   - 使用 React.lazy 实现代码分割
   - 使用 useTransition 优化用户交互
   - 使用虚拟列表处理大量数据

4. 测试规范
   - 编写单元测试
   - 编写集成测试
   - 保持测试覆盖率

## 提交规范

1. 提交信息格式
```
<type>(<scope>): <subject>

<body>
<footer>
```

2. type 类型
   - feat: 新功能
   - fix: 修复
   - docs: 文档
   - style: 格式
   - refactor: 重构
   - test: 测试
   - chore: 构建

3. 分支管理
   - master: 主分支
   - develop: 开发分支
   - feature/*: 功能分支
   - hotfix/*: 修复分支 

## 特殊注意事项
1. 保留 CSS 中原有的 !important 关键字
2. 保留原有的代码注释和 console.log
3. 请勿将文案和注释中的全角引号（“”）改为半角引号（""）
