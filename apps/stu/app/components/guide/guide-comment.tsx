import { AutoHeightInput } from "@/app/components/common/auto-height-input";
import DownArrow from "@/public/icons/ic_back.svg";
import QuoteIcon from "@/public/icons/quote.svg";
import ScopeIcon from "@/public/icons/scope.svg";
import { Signal, useSignal } from "@preact-signals/safe-react";
import { MathContent } from "@repo/core/components/math-content";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@repo/ui/components/dropdown-menu";
import Button from "@repo/ui/components/press-button";
import { cn } from "@repo/ui/lib/utils";
import { FC, MouseEvent } from "react";
import { createPortal } from "react-dom";

export const GuideComment: FC<{
  onSubmit: () => Promise<void> | void;
  scopes: { scope: string; value: number }[];
  comment: Signal<string>;
  scope: Signal<number>;
  reply?: boolean;
  quote?: string;
  onClose: () => void;
}> = ({ onSubmit, scopes, comment, scope, quote, reply, onClose }) => {
  const fetching = useSignal(false);

  const onChangeComment = (value: string) => {
    comment.value = value;
  };

  const handleContainerMouseDown = (e: MouseEvent) => {
    // 阻止容器内所有元素的 mousedown 事件导致失焦
    e.stopPropagation();
    e.preventDefault();
  };

  if (typeof window === "undefined") {
    return null;
  }

  return createPortal(
    <div
      className="z-101 fixed left-0 top-0 h-full w-full bg-black/50"
      onMouseDown={onClose}
      onPointerDown={onClose}
      onClick={(e) => e.preventDefault()}
    >
      <div
        className="absolute bottom-0 left-0 right-0 rounded-tl-xl rounded-tr-xl bg-white px-5"
        onMouseDown={handleContainerMouseDown}
        onPointerDown={handleContainerMouseDown}
      >
        <div className="flex gap-5 py-3">
          <AutoHeightInput
            className="text-text-1 flex-1"
            value={comment.value}
            onChange={onChangeComment}
          />
          <Button
            className="flex-[0_0_auto]"
            color="red"
            loading={fetching.value}
            disabled={comment.value.trim().length === 0}
            onClick={async () => {
              fetching.value = true;
              try {
                await onSubmit();
                onClose();
              } finally {
                fetching.value = false;
              }
            }}
          >
            发表
          </Button>
        </div>
        <div className="bg-divider-2 h-px scale-y-50"></div>
        <div className="text-text-3 flex items-start gap-5 py-3">
          <div className="flex flex-1 overflow-hidden">
            <span className="text-text-5 flex-[0_0_auto]">
              <QuoteIcon className="mr-1 inline" />
              {reply ? "回复" : "引用"}：
            </span>
            <span className="flex-1 overflow-hidden text-ellipsis whitespace-nowrap">
              <MathContent>{quote}</MathContent>
            </span>
          </div>
          {!reply && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <div className="bg-fill-4 flex flex-[0_0_auto] items-center gap-1 rounded-full px-2">
                  <ScopeIcon />
                  {
                    (
                      scopes.find((item) => item.value === scope.value) ??
                      scopes[0]
                    )?.scope
                  }
                  <DownArrow />
                </div>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                style={{ zIndex: 102 }}
                className="bg-white"
                align="end"
              >
                {scopes.map((s) => (
                  <DropdownMenuItem
                    key={s.value}
                    onClick={() => {
                      scope.value = s.value;
                    }}
                    className={cn(
                      s.value === scope.value && "text-red-text bg-[#FF7B5926]"
                    )}
                    onMouseDown={(e) => e.preventDefault()}
                    onPointerDown={(e) => e.preventDefault()}
                  >
                    {s.scope}
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          )}
        </div>
      </div>
    </div>,
    document.body
  );
};
