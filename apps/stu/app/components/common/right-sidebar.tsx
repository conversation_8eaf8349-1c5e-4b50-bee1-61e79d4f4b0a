import icSidebarClose from "@/public/icons/sidebar-close.png";
import { motion } from "motion/react";
import Image from "next/image";
import { FC, ReactElement, useEffect, useRef } from "react";
import { createPortal } from "react-dom";

export const RightSidebar: FC<{
  visible: boolean;
  children: ReactElement;
  onClose: () => void;
  width?: number;
}> = ({ visible, children, onClose, width = 256 }) => {
  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handler = (e: MouseEvent) => {
      if (visible && !ref.current?.contains(e.target as Node)) {
        onClose();
      }
    };
    document.body.addEventListener("click", handler, { capture: true });
    return () => {
      document.body.removeEventListener("click", handler, { capture: true });
    };
  }, [visible, onClose]);

  if (typeof window === "undefined") {
    return null;
  }

  return createPortal(
    <motion.div
      initial={"close"}
      variants={{
        open: { x: 0 },
        close: { x: "150%" },
      }}
      animate={visible ? "open" : "close"}
      transition={{ type: "spring", stiffness: 300, damping: 30 }}
      className="z-100 fixed right-0 top-0 h-full transform-gpu bg-white shadow-[-12px_12px_80px_0px_rgba(64,43,26,0.05)]"
      style={{ width }}
      ref={ref}
    >
      <div
        className="-translate-1/2 absolute left-0 top-1/2 h-12 w-8"
        onClick={onClose}
      >
        <Image
          src={icSidebarClose}
          alt="sidebar-close"
          width={32}
          height={32}
        />
      </div>
      <div className="h-full overflow-y-auto">{children}</div>
    </motion.div>,
    document.body
  );
};
