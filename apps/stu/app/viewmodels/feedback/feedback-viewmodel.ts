"use client";

import { toast } from "@/app/components/common/toast";
import {
  FeedbackType,
  createFeedbackSubmitData,
  useFeedbackConfig,
  useSubmitFeedback,
  type FeedbackSubmitData,
  type FeedbackTag,
  type StudentInfo,
} from "@/app/models/feedback";
import {
  getAppInfo,
  getDeviceInfo,
  getNetworkHeaderParams,
  trackEvent,
} from "@/app/utils/device";
import { useCallback, useEffect, useState } from "react";

export const MAX_CONTENT_LENGTH = 300;

export function useFeedbackViewModel(props?: {
  courseId?: string;
  exerciseId?: string;
  moduleId?: string;
  questionId?: string;
  widgetIndex?: number;
  studentInfo?: StudentInfo;
  feedbackType?: FeedbackType;
  feedbackKnowledgeId?: number;
  feedbackPhaseId?: number;
  feedbackLessonId?: number;
  feedbackWidgetIndex?: number;
  feedbackStudySessionId?: string;
  feedbackSubjectId?: number;
}) {
  const courseId = props?.courseId || "";
  const exerciseId = props?.exerciseId || "";
  const moduleId = props?.moduleId || "";
  const questionId = props?.questionId || "";
  const widgetIndex = props?.widgetIndex || 0;
  const studentInfo = props?.studentInfo;
  const feedbackType = props?.feedbackType || FeedbackType.AI;
  const feedbackKnowledgeId = props?.feedbackKnowledgeId || 0;
  const feedbackPhaseId = props?.feedbackPhaseId;
  const feedbackLessonId = props?.feedbackLessonId || 0;
  const feedbackWidgetIndex = props?.feedbackWidgetIndex || 0;
  const feedbackStudySessionId = props?.feedbackStudySessionId || "";
  const feedbackSubjectId = props?.feedbackSubjectId || 0;
  // 状态管理
  const [isOpen, setIsOpen] = useState(false);
  const [selectedTags, setSelectedTags] = useState<number[]>([]);
  const [feedbackContent, setFeedbackContent] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [hasLoadedConfig, setHasLoadedConfig] = useState(false);
  const { trigger: submitFeedback } = useSubmitFeedback();

  const {
    config: tags,
    refreshConfig: refreshConfig,
    isMutating,
  } = useFeedbackConfig({
    feedbackType: FeedbackType.AI,
  });

  // 当弹窗打开时，只获取一次反馈配置
  useEffect(() => {
    if (isOpen && !hasLoadedConfig && !isMutating) {
      refreshConfig();
      setHasLoadedConfig(true);
    }
  }, [isOpen, hasLoadedConfig, isMutating, refreshConfig]);

  // 当弹窗关闭时，重置加载状态
  useEffect(() => {
    if (!isOpen) {
      setHasLoadedConfig(false);
    }
  }, [isOpen]);

  // 计算属性
  const canSubmit = feedbackContent.trim().length > 0;

  // 方法
  const handleOpen = useCallback(() => {
    setIsOpen(true);
    trackEvent("feedback_entry_click", {
      question_id: questionId,
      widget_index: widgetIndex,
    });
  }, [questionId, widgetIndex]);

  const handleClose = useCallback(() => {
    setIsOpen(false);
    // 重置状态
    setSelectedTags([]);
    setFeedbackContent("");
    setIsSubmitting(false);
  }, []);

  const handleTagToggle = useCallback((tagId: number) => {
    setSelectedTags((prev) => {
      if (prev.includes(tagId)) {
        return prev.filter((id) => id !== tagId);
      } else {
        return [...prev, tagId];
      }
    });
  }, []);

  const handleContentChange = useCallback((content: string) => {
    // 限制100字
    if (content.length <= MAX_CONTENT_LENGTH) {
      setFeedbackContent(content);
    }
  }, []);

  const handleSubmit = useCallback(async () => {
    // if (!canSubmit || !studentInfo) return;

    const deviceInfo = getDeviceInfo();
    const appInfo = getAppInfo();
    const networkType = getNetworkHeaderParams();
    setIsSubmitting(true);
    try {
      // 创建反馈提交数据
      const feedbackData: FeedbackSubmitData = createFeedbackSubmitData({
        feedbackType: feedbackType,
        feedbackLabels: selectedTags,
        feedbackContent: feedbackContent.trim(),
        feedbackKnowledgeId: feedbackKnowledgeId,
        feedbackPhaseId: feedbackPhaseId,
        feedbackLessonId: feedbackLessonId,
        feedbackWidgetIndex: feedbackWidgetIndex,
        feedbackStudySessionId: feedbackStudySessionId,
        feedbackSubjectId: feedbackSubjectId,
        feedbackSourceClientId: 3,
        feedbackTime: Date.now(),
        feedbackDeviceInfo: {
          os: "android",
          deviceModel: deviceInfo?.deviceName || "",
          appVersion: appInfo?.versionName || "",
          networkType: networkType?.["X-Network-Type"] || "",
        },
      });

      // 跟踪提交事件
      trackEvent("feedback_submit", {
        question_id: questionId,
        widget_index: widgetIndex,
        tag_ids: selectedTags,
        content_length: feedbackContent.length,
      });

      await submitFeedback(feedbackData);
      console.log("提交反馈数据:", feedbackData);

      // 提交成功后关闭弹窗
      handleClose();
      setTimeout(() => {
        toast.show("反馈提交成功");
      }, 1000);
    } catch (error) {
      console.error("Failed to submit feedback:", error);
      toast.show("提交失败，请重试", {
        type: "error",
      });
    } finally {
      setIsSubmitting(false);
    }
  }, [
    canSubmit,
    handleClose,
    courseId,
    exerciseId,
    moduleId,
    questionId,
    widgetIndex,
    selectedTags,
    feedbackContent,
    studentInfo,
  ]);

  return {
    isOpen,
    tags: (tags || []) as FeedbackTag[],
    selectedTags,
    feedbackContent,
    isSubmitting,
    canSubmit,

    handleOpen,
    handleClose,
    handleTagToggle,
    handleContentChange,
    handleSubmit,
  };
}
