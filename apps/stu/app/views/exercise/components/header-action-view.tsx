"use client";
import { FeedbackType } from "@/app/models/feedback";
import { useChatViewModel } from "@/app/viewmodels/chat/chat-viewmodel";
import { useFeedbackViewModel } from "@/app/viewmodels/feedback/feedback-viewmodel";
import IconAsk from "@/public/icons/ask.svg";
import IconList from "@/public/icons/list.svg";
import { useSearchParams } from "next/navigation";
import { FC } from "react";
import Chat from "../../chat/chat-view";
import { useCourseViewContext } from "../../course/course-view-context";
import FeedbackView from "../../feedback/feedback-view";

export const ExerciseHeaderActionView: FC<{
  widgetIndex?: number;
  questionId: string;
  showAskButton?: boolean;
  showGuideButton?: boolean;
}> = ({
  questionId,
  showAskButton = true,
  showGuideButton = true,
  widgetIndex,
}) => {
  const { setIsProgressBarOpen } = useCourseViewContext();
  const chatViewModel = useChatViewModel({
    questionId: questionId,
    index: widgetIndex,
  });

  const searchParams = useSearchParams();
  const studyType = searchParams.get("studyType");
  const knowledgeId = searchParams.get("knowledgeId");
  const phaseId = searchParams.get("phaseId");
  const lessonId = searchParams.get("lessonId");
  const studySessionId = searchParams.get("studySessionId");
  const subjectId = searchParams.get("subjectId");

  // 使用反馈ViewModel
  const feedbackViewModel = useFeedbackViewModel({
    feedbackType:
      studyType === "1"
        ? FeedbackType.AI
        : studyType === "2"
          ? FeedbackType.Exercise
          : FeedbackType.AI,
    feedbackKnowledgeId: knowledgeId ? parseInt(knowledgeId) : undefined,
    feedbackPhaseId: phaseId ? parseInt(phaseId) : undefined,
    feedbackLessonId: lessonId ? parseInt(lessonId) : undefined,
    feedbackWidgetIndex: widgetIndex || 0,
    feedbackStudySessionId: studySessionId || "",
    feedbackSubjectId: subjectId ? parseInt(subjectId) : undefined,
  });

  const handleAsk = () => {
    chatViewModel.handleOpen();
  };
  return (
    <div className="flex flex-row items-center gap-1">
      {showAskButton && (
        <>
          <button
            className="inline-flex size-10 items-center justify-center"
            onClick={handleAsk}
          >
            <div className="flex size-6 items-center justify-center">
              <IconAsk />
            </div>
          </button>
          <Chat viewModel={chatViewModel} questionId={questionId} />
        </>
      )}
      {showGuideButton && (
        <button
          className="inline-flex size-10 items-center justify-center"
          onClick={() => setIsProgressBarOpen(true)}
        >
          <div className="flex size-6 items-center justify-center">
            <IconList />
          </div>
        </button>
      )}
      <button
        className="inline-flex size-10 items-center justify-center"
        onClick={() => feedbackViewModel.handleOpen()}
      >
        反馈
      </button>

      {/* 添加反馈弹窗组件 */}
      <FeedbackView viewModel={feedbackViewModel} />
    </div>
  );
};
