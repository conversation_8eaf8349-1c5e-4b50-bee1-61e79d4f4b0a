"use client";

import { FeedbackTag } from "@/app/models/feedback";
import {
  MAX_CONTENT_LENGTH,
  useFeedbackViewModel,
} from "@/app/viewmodels/feedback/feedback-viewmodel";
import IcClose2 from "@/public/icons/ic_close2.svg";
import Button from "@repo/ui/components/press-button";
import { FC, useCallback, useEffect, useRef } from "react";
import { createPortal } from "react-dom";

type FeedbackViewModelType = ReturnType<typeof useFeedbackViewModel>;

// 主组件
const FeedbackView: FC<{ viewModel: FeedbackViewModelType }> = ({
  viewModel,
}) => {
  const {
    isOpen,
    tags,
    selectedTags,
    feedbackContent,
    isSubmitting,
    canSubmit,
    handleClose,
    handleTagToggle,
    handleContentChange,
    handleSubmit,
  } = viewModel;

  if (!isOpen) return null;

  return createPortal(
    <FeedbackWindow
      tags={tags}
      selectedTags={selectedTags}
      feedbackContent={feedbackContent}
      isSubmitting={isSubmitting}
      canSubmit={canSubmit}
      onClose={handleClose}
      onTagToggle={handleTagToggle}
      onContentChange={handleContentChange}
      onSubmit={handleSubmit}
    />,
    document.body
  );
};

export default FeedbackView;

// 子组件
interface FeedbackWindowProps {
  tags: FeedbackTag[] | [];
  selectedTags: number[];
  feedbackContent: string;
  isSubmitting: boolean;
  canSubmit: boolean;
  onClose: () => void;
  onTagToggle: (tagId: number) => void;
  onContentChange: (content: string) => void;
  onSubmit: () => void;
}

const FeedbackWindow = ({
  tags,
  selectedTags,
  feedbackContent,
  isSubmitting,
  canSubmit,
  onClose,
  onTagToggle,
  onContentChange,
  onSubmit,
}: FeedbackWindowProps) => {
  const contentRef = useRef<HTMLDivElement>(null);
  const isFirstScroll = useRef(true);

  // 滚动到底部的函数
  const scrollToBottom = useCallback(() => {
    if (!contentRef.current) return;

    setTimeout(() => {
      const element = contentRef.current;
      if (element) {
        const scrollHeight = element.scrollHeight;
        element.scrollTo({
          top: scrollHeight,
          behavior: isFirstScroll.current ? "auto" : "smooth",
        });
        isFirstScroll.current = false;
      }
    }, 100);
  }, []);

  // 监听内容变化，自动滚动到底部
  useEffect(() => {
    scrollToBottom();
  }, [feedbackContent, scrollToBottom]);

  // 监听窗口大小变化，自动滚动到底部
  useEffect(() => {
    if (!contentRef.current) return;

    const resizeObserver = new ResizeObserver(() => {
      scrollToBottom();
    });

    resizeObserver.observe(contentRef.current);

    return () => {
      resizeObserver.disconnect();
    };
  }, [scrollToBottom]);

  return (
    <div
      className="fixed inset-0 z-50 h-full bg-[rgba(0,0,0,0.5)]"
      onClick={onClose}
    >
      <div
        ref={contentRef}
        className="fixed left-1/2 top-1/2 z-50 max-h-[90vh] w-[90vw] -translate-x-1/2 -translate-y-1/2 overflow-y-auto rounded-2xl bg-[#F9F6F4] p-6"
      >
        <CloseButton onClose={onClose} />
        <div
          className="flex flex-col gap-4"
          onClick={(e) => e.stopPropagation()}
        >
          <h3 className="text-xl font-bold text-[rgba(51,48,45,0.95)]">
            问题反馈
          </h3>

          <div className="flex flex-col gap-2">
            <p className="text-[15px] font-bold text-gray-600">
              问题类型
              <span className="text-[rgba(51,48,45,0.4)]">（选填）</span>
            </p>
            <TagSelector
              tags={tags}
              selectedTags={selectedTags}
              onTagToggle={onTagToggle}
            />
          </div>

          <div className="flex flex-col gap-2">
            <p className="text-[15px] font-bold text-gray-600">填写问题</p>
            <FeedbackTextarea
              value={feedbackContent}
              onChange={onContentChange}
              maxLength={MAX_CONTENT_LENGTH}
            />
          </div>

          <div className="mt-4 flex justify-center">
            <Button
              disabled={!canSubmit}
              loading={isSubmitting}
              onClick={onSubmit}
              className="w-40"
              color="red"
            >
              提交
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

const CloseButton = ({ onClose }: { onClose: () => void }) => (
  <button
    className="absolute right-6 top-6 z-20 flex h-9 w-9 cursor-pointer items-center justify-center rounded-full bg-[rgba(31,29,27,0.05)] text-sm text-[#000]"
    onClick={onClose}
  >
    <IcClose2 />
  </button>
);

const TagSelector = ({
  tags,
  selectedTags,
  onTagToggle,
}: {
  tags: FeedbackTag[];
  selectedTags: number[];
  onTagToggle: (tagId: number) => void;
}) => (
  <div className="grid grid-cols-4 gap-3">
    {tags.map((tag) => (
      <button
        key={tag.id}
        className={`rounded-lg text-[15px] leading-10 transition-colors ${
          selectedTags.includes(tag.id)
            ? "border border-[#FF7B59] bg-[rgba(255,123,89,0.1)] text-[#D1320A]"
            : "bg-[#ffffff] text-[rgba(51,48,45,0.85)]"
        }`}
        onClick={() => onTagToggle(tag.id)}
      >
        {tag.name}
      </button>
    ))}
  </div>
);

const FeedbackTextarea = ({
  value,
  onChange,
  maxLength,
}: {
  value: string;
  onChange: (value: string) => void;
  maxLength: number;
}) => (
  <div className="relative">
    <textarea
      className="h-32 w-full resize-none rounded-xl border border-[#E5E5E5] bg-white pl-6 pr-20 pt-4 text-sm outline-none placeholder:text-[rgba(51,48,45,0.4)]"
      value={value}
      onChange={(e) => onChange(e.target.value)}
      placeholder="请输入不少于 5 个字的内容"
      maxLength={maxLength}
    />
    <div
      className={`absolute right-6 top-4 text-[10px] ${value.length >= maxLength ? "text-[#D1320A]" : "text-[rgba(51,48,45,0.4)]"}`}
    >
      {value.length}/{maxLength}
    </div>
  </div>
);
