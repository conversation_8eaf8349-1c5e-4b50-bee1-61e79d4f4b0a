import { WidgetViewProps } from "@/types/app/ui";
import {
  batch,
  Signal,
  useSignal,
  useSignalEffect,
} from "@preact-signals/safe-react";
import { CallbackListener, PlayerRef } from "@remotion/player";
import { VideoWidgetData } from "@repo/core/types/data/widget-video";
import {
  createContext,
  FC,
  RefObject,
  useCallback,
  useContext,
  useEffect,
  useRef,
} from "react";
import {
  CourseViewContextType,
  useCourseViewContext,
} from "../course/course-view-context";

const FPS = 30;

type VideoViewContextType = CourseViewContextType & {
  title?: string;
  index?: number;
  totalGuideCount: number;
  data: VideoWidgetData;
  refPlayer: RefObject<PlayerRef | null>;
  isPlaying: boolean;
  forwardTo: (seconds: number) => void;
  seekTo: (frame: number) => void;
  togglePlay: () => void;
  showPlayerControls: boolean;
  togglePlayerControls: () => void;
  playRate: Signal<number>;
  set3XPlayRate: () => void;
  resetPlayRate: () => void;
  active: boolean;
  durationInFrames: number;
  currentFrame: Signal<number>;
  fps: number;
};

const VideoViewContext = createContext<VideoViewContextType>(
  {} as VideoViewContextType
);

export const useVideoViewContext = () => {
  return useContext(VideoViewContext);
};

interface VideoViewContextProviderProps extends WidgetViewProps<"video"> {
  children: React.ReactNode;
}

export const VideoViewContextProvider: FC<VideoViewContextProviderProps> = ({
  totalGuideCount,
  content,
  active,
  children,
}) => {
  const courseContexts = useCourseViewContext();
  const {
    next,
    reportCostTime,
    isVersionChanged,
    videoPlayRate: playRate,
  } = courseContexts;
  const { data, name: title, index } = content;
  const { duration } = data;

  const refPlayer = useRef<PlayerRef | null>(null);
  const isPlaying = useSignal(false);
  const isPlayingBeforeDocumentHidden = useSignal(false);
  const showPlayerControls = useSignal(false);
  const playRateBeforeLongPress = useSignal(1);
  const durationInFrames = duration * FPS || 1;
  const currentFrame = useSignal(0);

  const lastTime = useSignal(Date.now());
  const costTime = useSignal(0);

  const forwardTo = useCallback(
    (seconds: number) => {
      if (!refPlayer.current) {
        return;
      }
      const player = refPlayer.current;
      player.seekTo(player.getCurrentFrame() + seconds * FPS);
    },
    [refPlayer]
  );

  const seekTo = useCallback(
    (frame: number) => {
      if (!refPlayer.current) {
        return;
      }
      const player = refPlayer.current;
      player.seekTo(frame);
      isPlaying.value = true;
    },
    [refPlayer, isPlaying]
  );

  const togglePlay = useCallback(() => {
    isPlaying.value = !isPlaying.peek();
  }, [isPlaying]);

  const togglePlayerControls = useCallback(() => {
    showPlayerControls.value = !showPlayerControls.peek();
  }, [showPlayerControls]);

  const set3XPlayRate = useCallback(() => {
    if (playRate.value === 3) return;
    playRateBeforeLongPress.value = playRate.value;
    playRate.value = 3;
  }, [playRate, playRateBeforeLongPress]);

  const resetPlayRate = useCallback(() => {
    if (playRate.value === playRateBeforeLongPress.value) return;
    playRate.value = playRateBeforeLongPress.value;
  }, [playRate, playRateBeforeLongPress]);

  useEffect(() => {
    if (!refPlayer.current) {
      return;
    }
    const player = refPlayer.current;
    const onFrameUpdate: CallbackListener<"frameupdate"> = (e) => {
      currentFrame.value = e.detail.frame;
    };

    player.addEventListener("frameupdate", onFrameUpdate);
    return () => {
      player.removeEventListener("frameupdate", onFrameUpdate);
    };
  }, [refPlayer, currentFrame]);

  useEffect(() => {
    if (!refPlayer.current) {
      return;
    }
    const handleEnded = () => {
      next();
    };
    const player = refPlayer.current;
    player.addEventListener("ended", handleEnded);

    return () => {
      player.removeEventListener("ended", handleEnded);
    };
  }, [refPlayer, next]);

  useEffect(() => {
    const handleVisibilitychange = () => {
      if (document.visibilityState === "hidden") {
        batch(() => {
          isPlayingBeforeDocumentHidden.value = isPlaying.peek();
          isPlaying.value = false;
        });
      } else {
        isPlaying.value = isPlayingBeforeDocumentHidden.peek();
      }
    };

    document.addEventListener("visibilitychange", handleVisibilitychange);

    return () => {
      document.removeEventListener("visibilitychange", handleVisibilitychange);
    };
  }, [isPlaying, isPlayingBeforeDocumentHidden]);

  useEffect(() => {
    if (active) {
      isPlaying.value = true;
    } else {
      isPlaying.value = false;
    }
  }, [active, isPlaying]);

  useSignalEffect(() => {
    if (isPlaying.value) {
      refPlayer.current?.play();
    } else {
      refPlayer.current?.pause();
    }
  });
  useSignalEffect(() => {
    if (isVersionChanged.value) {
      isPlaying.value = false;
    }
  });

  useEffect(() => {
    if (active) {
      lastTime.value = Date.now();
    }
  }, [active, lastTime]);

  useEffect(() => {
    if (!active) return;

    const updateTime = () => {
      const now = Date.now();
      costTime.value += now - lastTime.value;
      lastTime.value = now;
      reportCostTime(costTime.value);
    };
    const timer = setInterval(updateTime, 250);
    return () => {
      clearInterval(timer);
      updateTime();
    };
  }, [active, reportCostTime, lastTime, costTime]);

  const value = {
    ...courseContexts,
    active,
    totalGuideCount,
    title,
    index,
    data,
    refPlayer,
    isPlaying: isPlaying.value,
    showPlayerControls: showPlayerControls.value,
    forwardTo,
    seekTo,
    togglePlay,
    togglePlayerControls,
    set3XPlayRate,
    resetPlayRate,
    playRate,
    currentFrame,
    durationInFrames,
    fps: FPS,
  };

  return <VideoViewContext value={value}>{children}</VideoViewContext>;
};
