/**
 * 图片处理工具函数
 * 主要用于处理 Android content:// URI 格式的图片
 */

/**
 * 支持的图片类型
 */
const SUPPORTED_IMAGE_TYPES = [
  "image/jpeg",
  "image/jpg",
  "image/png",
  "image/webp",
  "image/gif",
];

/**
 * 文件大小限制 (10MB)
 */
const MAX_FILE_SIZE = 10 * 1024 * 1024;

/**
 * 将 Android content:// URI 转换为 File 对象
 * @param imageUri Android 返回的 content:// URI
 * @param fileName 文件名
 * @returns Promise<File> 转换后的 File 对象
 */
export async function convertContentUriToFile(
  imageUri: string,
  fileName: string = "image.jpg"
): Promise<File> {
  try {
    // 如果是普通 URL，先下载再转换
    return await convertUrlToFile(imageUri, fileName);
  } catch (error) {
    console.error("转换图片失败:", error);
    throw new Error("图片转换失败");
  }
}

/**
 * 将 URL 图片转换为 File 对象
 * @param url 图片 URL
 * @param fileName 文件名
 * @returns Promise<File>
 */
async function convertUrlToFile(url: string, fileName: string): Promise<File> {
  try {
    const response = await fetch(url);

    if (!response.ok) {
      throw new Error(
        `下载图片失败: ${response.status} ${response.statusText}`
      );
    }

    const blob = await response.blob();

    // 验证文件大小
    if (blob.size > MAX_FILE_SIZE) {
      throw new Error(
        `图片文件过大，最大支持 ${MAX_FILE_SIZE / 1024 / 1024}MB`
      );
    }

    // 验证文件类型
    if (!SUPPORTED_IMAGE_TYPES.includes(blob.type)) {
      throw new Error(`不支持的图片格式: ${blob.type}`);
    }

    // 根据实际类型确定文件扩展名
    const extension = getFileExtension(blob.type || "image/jpeg");
    const finalFileName = fileName.includes(".")
      ? fileName
      : `${fileName}.${extension}`;

    const file = new File([blob], finalFileName, {
      type: blob.type,
    });

    return file;
  } catch (error) {
    console.error("转换 URL 图片失败:", error);
    if (error instanceof Error) {
      throw error;
    }
    throw new Error("URL 图片转换失败");
  }
}

/**
 * 根据 MIME 类型获取文件扩展名
 * @param mimeType MIME 类型
 * @returns 文件扩展名
 */
function getFileExtension(mimeType: string): string {
  const extensionMap: Record<string, string> = {
    "image/jpeg": "jpg",
    "image/jpg": "jpg",
    "image/png": "png",
    "image/webp": "webp",
    "image/gif": "gif",
  };

  return extensionMap[mimeType] || "jpg";
}

/**
 * 从 content:// URI 创建预览 URL
 * @param imageUri content:// URI 或 base64 数据
 * @returns string 预览 URL
 */
export function createPreviewUrl(imageUri: string): string {
  // 如果是 content:// URI，直接返回作为预览
  if (imageUri.startsWith("content://")) {
    return imageUri;
  }

  // 如果是 base64，直接返回
  if (imageUri.startsWith("data:image/")) {
    return imageUri;
  }

  // 如果是普通 URL，直接返回
  return imageUri;
}

/**
 * 清理预览 URL
 * @param previewUrl 预览 URL
 */
export function revokePreviewUrl(previewUrl: string): void {
  // 只清理 blob URL
  if (previewUrl.startsWith("blob:")) {
    URL.revokeObjectURL(previewUrl);
  }
}

/**
 * 验证图片文件
 * @param file 图片文件
 * @returns 验证结果
 */
export function validateImageFile(file: File): {
  isValid: boolean;
  error?: string;
} {
  // 检查文件大小
  if (file.size > MAX_FILE_SIZE) {
    return {
      isValid: false,
      error: `图片文件过大，最大支持 ${MAX_FILE_SIZE / 1024 / 1024}MB`,
    };
  }

  // 检查文件类型
  if (!SUPPORTED_IMAGE_TYPES.includes(file.type)) {
    return {
      isValid: false,
      error: `不支持的图片格式: ${file.type}`,
    };
  }

  return { isValid: true };
}

/**
 * 压缩图片文件（如果需要）
 * @param file 原始图片文件
 * @param maxWidth 最大宽度
 * @param maxHeight 最大高度
 * @param quality 压缩质量 (0-1)
 * @returns Promise<File> 压缩后的文件
 */
export async function compressImageFile(
  file: File,
  maxWidth: number = 1920,
  maxHeight: number = 1080,
  quality: number = 0.8
): Promise<File> {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement("canvas");
    const ctx = canvas.getContext("2d");
    const img = new Image();

    img.onload = () => {
      try {
        // 计算新的尺寸
        let { width, height } = img;

        if (width > maxWidth || height > maxHeight) {
          const ratio = Math.min(maxWidth / width, maxHeight / height);
          width *= ratio;
          height *= ratio;
        }

        // 设置 canvas 尺寸
        canvas.width = width;
        canvas.height = height;

        // 绘制图片
        ctx?.drawImage(img, 0, 0, width, height);

        // 转换为 blob
        canvas.toBlob(
          (blob) => {
            if (blob) {
              const compressedFile = new File([blob], file.name, {
                type: file.type,
                lastModified: Date.now(),
              });
              resolve(compressedFile);
            } else {
              reject(new Error("图片压缩失败"));
            }
          },
          file.type,
          quality
        );
      } catch (error) {
        reject(error);
      }
    };

    img.onerror = () => {
      reject(new Error("图片加载失败"));
    };

    // 创建 URL 并加载图片
    const url = URL.createObjectURL(file);
    img.src = url;

    // 清理 URL
    img.onload = () => {
      URL.revokeObjectURL(url);
    };
  });
}
