# Models

学生端应用的数据模型层，提供统一的数据获取、转换和管理接口。

## 🎯 架构概述

Model层遵循以下设计原则：
- **只做数据相关操作** - API调用、数据转换、缓存
- **分离校验职责** - 后端数据用Zod，前端传参用TS类型
- **不处理业务逻辑** - 复杂业务逻辑放ViewModel
- **统一网络请求** - 使用项目统一的fetcher

## 📋 可用 Model Hooks

### In-Class 模块 (课中学习)

| Hook 名称 | 描述 | 主要接口 | 依赖 API |
|-----------|------|----------|----------|
| `useStudyReport` | 获取学习报告数据 | `studyReport, isLoading, error, refreshStudyReport` | GET `/api/v1/study/summary` |
| `useCourseInfo` | 获取课程信息 | `courseInfo, isLoading, error, refreshCourseInfo` | GET `/api/v1/study/info` |
| `useWidgetInfo` | 获取课程组件详情 | `widgetInfo, isLoading, error, refreshWidgetInfo` | GET `/api/v1/study/widget/info` |
| `useReportProgress` | 上报学习进度 | `reportProgress, isReporting, reportError` | POST `/api/v1/study/progress/report` |
| `useSubmitAnswer` | 提交答题结果 | `submitAnswer, isSubmitting, submitError` | POST `/api/v1/study/answer` |
| `useFirstQuestion` | 获取第一道习题 | `getFirstQuestion, firstQuestion, isLoading, error` | GET `/api/v1/study/question/first` |
| `useQuestionHistory` | 获取答题历史 | `getQuestionHistory, questionHistory, isLoading, error` | GET `/api/v1/study/question/history` |
| `useNextQuestion` | 获取推题 | `getNextQuestion, nextQuestion, isLoading, error` | GET `/api/v1/study/question/next` |
| `useChatHistory` | 获取AI聊天历史 | `chatHistory, isLoading, error, refreshChatHistory` | GET `/api/v1/ai/history` |
| `useChat` | AI答疑 | `sendMessage, isSending, chatError` | POST `/api/v1/ai/chat` |

### exercise 模块 (练习会话)

| Hook 名称 | 描述 | 主要接口 | 依赖 API |
|-----------|------|----------|----------|
| `useSubmitStudyAnswer` | 提交题目答案并获取反馈 | `submitAnswer, isSubmitting, submitError` | POST `/api/v1/study_session/submit_answer` |
| `useExitStudySession` | 退出练习会话 | `exitSession, isExiting, exitError` | PUT `/api/v1/study_session/exit` |

#### 🗑️ 已删除的 Hooks
- `useEnterStudySession` - 不再需要此功能
- `useRefreshStudySession` - 依赖于已删除的 useEnterStudySession

### wrong-question-bank 模块 (错题本)

| Hook 名称 | 描述 | 主要接口 | 依赖 API |
|-----------|------|----------|----------|
| `useWrongQuestions` | 获取错题本列表 | `wrongQuestions, pageInfo, isLoading, error, refreshWrongQuestions` | GET `/api/v1/study/wrong_questions` |
| `useWrongQuestionsWithRefresh` | 获取错题本列表（带自动刷新） | `wrongQuestions, pageInfo, isLoading, error, refreshWrongQuestions` | GET `/api/v1/study/wrong_questions` |
| `useManualAddWrongQuestion` | 手动添加错题 | `manualAddWrongQuestion, isAdding, addError` | POST `/api/v1/study/wrong_question/add` |
| `useDeleteWrongQuestions` | 删除错题 | `deleteWrongQuestions, isDeleting, deleteError` | DELETE `/api/v1/study/wrong_questions` |

### comments 模块 (评论)

| Hook 名称 | 描述 | 主要接口 | 依赖 API |
|-----------|------|----------|----------|
| `useCommentsList` | 获取一级评论列表 | `data, error, isLoading, refresh` | GET `/api/v1/comments/list` |
| `useSubCommentsList` | 获取二级评论列表 | `data, error, isLoading, refresh` | GET `/api/v1/comments/sub-list` |
| `useAddComment` | 发布评论 | `addComment, isAdding, error` | POST `/api/v1/comments/add` |
| `useDeleteComment` | 删除评论 | `deleteComment, isDeleting, error` | POST `/api/v1/comments/delete` |
| `useReplyComment` | 回复评论 | `replyComment, isReplying, error` | POST `/api/v1/comments/reply` |
| `useLikeComment` | 点赞评论 | `likeComment, isLiking, error` | POST `/api/v1/comment/like` |

### interactive-explanation 模块 (互动解题记录)

| Hook 名称 | 描述 | 主要接口 | 依赖 API |
|-----------|------|----------|----------|
| `useReportInteractiveExplanation` | 上报互动解题记录 | `reportInteractiveExplanation, isReporting, reportError` | POST `/api/v1/study_session/ai_explantion_record/report` |

## 🏗️ 模块结构

### in-class 模块
学生端课中学习会话相关功能，包括：
- 学习报告获取和展示
- 课程信息和组件详情获取
- 学习进度上报
- 答题结果提交和题目获取
- AI聊天历史和答疑功能

详细文档：[in-class/README.md](./in-class/README.md)

### exercise 模块
学生端练习会话管理功能，包括：
- 自动会话创建和恢复
- 答题提交和实时反馈
- 进度保存和会话退出
- 个性化欢迎配置
- 多种反馈类型支持

详细文档：[exercise/README.md](./exercise/README.md)

### wrong-question-bank 模块
学生端错题本管理功能，包括：
- 错题列表获取和分页展示
- 手动添加错题到错题本
- 批量删除错题记录
- 错因标签管理
- 支持按添加方式和课程筛选

详细文档：[wrong-question-bank/README.md](./wrong-question-bank/README.md)

### comments 模块
学生端评论管理功能，包括：
- 评论列表获取和分页展示
- 发布评论（支持文本、图片、引用内容）
- 删除评论
- 回复评论
- 点赞/取消点赞评论
- 支持父评论筛选和根评论展开

详细文档：[comments/README.md](./comments/README.md)

### interactive-explanation 模块
学生端互动解题记录管理功能，包括：
- 互动讲题过程记录上报
- 当前进度跟踪
- 分步指导完成状态记录
- 学习行为数据收集

详细文档：[interactive-explanation/README.md](./interactive-explanation/README.md)

## 📊 核心数据结构

### 学习报告相关
- `StudyReport` - 完整学习报告数据
- `StudyPerformance` - 学习表现数据
- `MasteryRecord` - 掌握度记录
- `CelebrationConfig` - 庆祝动效配置

### 课程信息相关
- `CourseInfo` - 课程信息数据
- `LessonLaunchPage` - 课程启动页信息
- `LessonWidget` - 课程组件信息
- `WidgetInfo` - 组件详情数据

### 答题相关
- `AnswerResponse` - 答题结果响应
- `QuestionInfo` - 题目信息
- `FirstQuestionResponse` - 第一道题响应
- `QuestionHistoryResponse` - 答题历史响应
- `NextQuestionResponse` - 推题响应

### AI聊天相关
- `ChatHistory` - 聊天历史数据
- `ChatHistoryItem` - 聊天记录项

### 错题本相关
- `WrongQuestion` - 错题记录数据
- `PageInfo` - 分页信息
- `GetWrongQuestionsResponse` - 错题列表响应
- `ManualAddWrongQuestionResponse` - 手动添加错题响应
- `DeleteWrongQuestionsResponse` - 删除错题响应

### 评论相关
- `Comment` - 评论信息数据
- `CommentsListData` - 评论列表响应数据
- `ReferencePosition` - 引用位置信息
- `ReferenceCoordinate` - 引用坐标信息
- `AddCommentPayload` - 发布评论请求参数
- `GetCommentsParams` - 获取评论列表参数

### 互动解题记录相关
- `ReportInteractiveExplanationPayload` - 互动解题记录上报参数
- `InteractiveExplanationRecord` - 互动解题记录数据
- `StepByStepGuideItem` - 分步指导项
- `ReportResult` - 上报结果响应

## 🔧 使用示例

### 基础数据获取
```typescript
import { useStudyReport, useCourseInfo } from '@/models';

function MyComponent() {
  const { studyReport, isLoading, error } = useStudyReport({ sessionId: 3001 });
  const { courseInfo } = useCourseInfo({ bizTreeId: 1001 });

  if (isLoading) return <div>加载中...</div>;
  if (error) return <div>加载失败</div>;

  return (
    <div>
      <h2>{courseInfo?.lessonName}</h2>
      <p>正确率: {studyReport?.studyPerformance.accuracyRate}%</p>
    </div>
  );
}
```

### 数据提交操作
```typescript
import { useSubmitAnswer, useReportProgress } from '@/models';

function AnswerComponent() {
  const { submitAnswer, isSubmitting } = useSubmitAnswer();
  const { reportProgress } = useReportProgress();

  const handleSubmit = async () => {
    try {
      await submitAnswer({
        lessonId: 1001,
        widgetIndex: 3,
        questionId: 'q001',
        answer: 'A'
      });
      
      await reportProgress({
        lessonId: 1001,
        widgetIndex: 3,
        status: 'completed',
        costSecond: 300
      });
    } catch (error) {
      console.error('操作失败:', error);
    }
  };

  return (
    <button onClick={handleSubmit} disabled={isSubmitting}>
      {isSubmitting ? '提交中...' : '提交答案'}
    </button>
  );
}
```

## ⚠️ 使用注意事项

1. **类型安全**: 所有Hook都有完整的TypeScript类型定义
2. **错误处理**: 每个Hook都提供error状态用于错误处理
3. **加载状态**: 提供loading状态用于UI展示
4. **数据刷新**: GET类接口提供refresh方法用于手动刷新
5. **参数校验**: 所有数值参数都设置了合理的范围约束

## 🧪 测试覆盖

- ✅ Schema校验测试 - 确保数据结构正确性
- ✅ 数据转换测试 - 验证数据转换逻辑
- ✅ Hook功能测试 - 测试Hook的基本功能
- ✅ 错误处理测试 - 验证异常情况处理

## 📚 开发指南

### 添加新模块
1. 在models目录下创建新的模块目录
2. 按照标准结构创建必需文件：
   - `[module]-model.ts` - 主要Model文件
   - `types.ts` - TypeScript类型定义
   - `schemas.ts` - Zod Schema定义
   - `transformers.ts` - 数据转换函数（可选）
   - `index.ts` - 统一导出
   - `README.md` - 模块文档
   - `__tests__/` - 测试目录

### 更新全局导出
新增模块后需要更新：
1. `models/index.ts` - 添加新模块的导出
2. `models/README.md` - 更新模块列表和Hook表格

## 🔄 数据流转

1. **数据获取**: 使用SWR进行数据缓存和自动重新验证
2. **数据校验**: 使用Zod对后端返回数据进行严格校验
3. **数据转换**: 通过transformer函数处理数据格式转换
4. **状态管理**: 通过Hook返回的状态进行UI状态管理