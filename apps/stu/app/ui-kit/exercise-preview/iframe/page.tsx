"use client";

import { useIframeChild } from "@repo/core/components/iframe-preview/hooks/use-iframe-message";
import { StudyType } from "@repo/core/enums";
import { PreviewQuestionData } from "@repo/core/exercise/model/types";
import { ExercisePreview } from "@repo/core/exercise/preview/view/exercise-view";
import { useState } from "react";

export default function IframePreviewPage() {
  const [questionList, setQuestionList] = useState<PreviewQuestionData[]>([]);
  const [studyType, setStudyType] = useState<StudyType>(
    StudyType.REINFORCEMENT_EXERCISE
  );

  // 使用 iframe 子页面 hooks
  const { requestClose } = useIframeChild({
    PREVIEW_DATA: (data) => {
      setQuestionList(data.questionList || []);
      setStudyType(data.studyType || StudyType.REINFORCEMENT_EXERCISE);
    },
    UPDATE_STUDY_TYPE: (data) => {
      setStudyType(data.studyType);
    },
  });

  // 处理返回事件
  const handleBack = () => {
    requestClose();
  };

  if (questionList.length === 0) {
    return (
      <div className="iframe-loading flex h-full w-full items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="mb-4 text-lg font-medium text-gray-600">
            等待数据加载...
          </div>
          <div className="text-sm text-gray-500">
            iframe 预览模式 (1000×600)
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="h-[600px] w-full">
      <ExercisePreview
        className="h-full w-full"
        progressDisplayMode="progress"
        questionList={questionList}
        studyType={studyType}
        onBack={handleBack}
      />
    </div>
  );
}
