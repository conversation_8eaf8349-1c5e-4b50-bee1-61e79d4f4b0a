输入文件：[ai-generates/raw-docs/prd/PRD - AI课中 - 课程框架文档组件 V1.0_analyzed.md]
输出文件：[ai-generates/ai-docs/ai-prd-AI课中-课程框架文档组件-V1.0.md]

# 前端产品需求文档模板 v1.1 (参照 AI PM 工作指南修订)

## 1. 需求背景与目标

### 1.1 需求目标

- 通过该需求的实现，可以优化AI课中核心学习环节的用户体验，解决前期Demo版本调研中发现的交互不顺畅、信息呈现不清晰等问题，提升基础学习体验质量。
- 通过课程框架升级和文档组件功能增强，旨在使AI课的上课过程更接近1v1真人直播课的沉浸感，并初步展示系统的个性化与智能化能力。
- 该需求上线后，预计核心指标 **用户课程完成率** 可以提升 **5%**，**课程满意度评分（4分及以上比例）** 预计可以达到 **85%**。

### 1.2 用户价值

- 对于 **使用新AI课的学生**，解决了 **板书与讲解不同步、交互方式单一、缺乏即时反馈和个性化指引** 的痛点，带来了 **更流畅、聚焦、可控的学习体验，提升学习效率和参与感**。
- 对于 **使用新AI课的学生**，满足了 **在学习过程中需要清晰了解学习进度、回顾答题情况、获得针对性巩固练习** 的核心诉求，实现了 **更个性化、目标感更强的学习路径，加强学习效果**。

## 2. 功能范围

### 2.1 核心功能

### **[核心功能点1]: 课程框架升级 (开场页、结算页、课程进度、退出续学)**

**用户价值与场景:** 优化课程的开始和结束体验，提供清晰的学习进度导航和中断续学能力，满足学生在完整学习流程中对仪式感、结果反馈、进度掌控和灵活性操作的需求。

**功能详述与前端呈现:**

- **主要交互流程:**
    1. 用户进入课程，首先看到学科定制的开场页，自动播放动效和音频。
    2. 动效播放完毕，自动进入课程第一个学习组件。
    3. 用户可在任意组件中，点击“课程进度”按钮，查看当前及所有组件的解锁/学习中/待解锁状态，并可点击已解锁组件进行跳转。
    4. 用户完成所有组件学习后，进入结算页，展示学习表现、掌握度、答题情况、推荐学习和反馈入口。
    5. 用户在学习过程中可点击左上角退出按钮，系统记录进度，用户下次进入时可从上次中断处继续学习。
- **界面布局与元素:**
    - **开场页:** 全屏展示，包含学科背景图、IP动效、课程章节号、课程序号、课程名称。动效播放时有进度感。
    - **结算页:** 包含庆祝动效与文案、学习表现（用时、答题数、正确率）、知识点掌握度（含提升值）、答题情况概览（含查看详情入口）、推荐学习模块、课程反馈模块、底部操作按钮（回看课程、完成）。布局清晰，信息分区明确。
    - **课程进度弹窗:** 弹窗形式展示，列表展示所有组件（文档/练习）的图标、名称和状态（已解锁、学习中、待解锁）。当前组件高亮。状态图标需清晰区分。
    - **退出按钮:** 固定在左上角，图标清晰。
    - **UI元素状态:**
        - 开场页动效：播放中、播放完毕。
        - 结算页按钮（回看课程、完成）：默认、点击。
        - 课程进度组件项：默认（根据状态区分样式）、选中（学习中）、点击（已解锁项）。
        - 推荐学习按钮（去练习）：默认、点击。
        - 反馈图标：默认、选中。提交按钮：禁用（未选反馈）、可用（已选反馈）、点击。
- **用户操作与反馈:**
    - **进入课程:** 自动播放开场页动效和音频，结束后平滑过渡到首个组件。
    - **点击“课程进度”:** 暂停当前讲解，弹出进度列表；点击列表外区域或再次点击按钮，收起列表，恢复讲解。
    - **点击“已解锁”组件:** 页面内容切换至目标组件，进度弹窗收起。若目标为文档组件，从上次跳出进度播放；若为练习组件，上次未答完则继续答题，已答完则展示历史记录。
    - **点击退出按钮:** 弹出确认框（可选，或直接退出），返回课程入口页，前端提示“学习进度已保存”。
    - **完成课程:** 自动进入结算页，播放庆祝动效（根据表现区分开心/鼓励）。
    - **点击结算页“查看详情”:** 跳转至题目列表页面。
    - **点击“去练习”:** 跳转至对应的练习模块。
    - **点击反馈图标:** 图标高亮，提交按钮变为可用。
    - **点击“提交”反馈:** 发送数据，按钮短暂显示加载中，成功后toast提示“感谢反馈，我们会持续改进！”。
- **数据展示与更新:**
    - 开场页：动态加载课程元信息（章节、序号、名称）。
    - 结算页：动态加载学习统计数据、掌握度数据、答题概况、推荐学习内容、IP及反馈文案（根据规则随机或匹配）。
    - 课程进度弹窗：根据用户学习进度实时更新各组件状态。

**优先级：** 高
**依赖关系：** 依赖课程配置信息、用户学习状态记录服务、掌握度服务、题目服务。

### 用户场景与故事 (针对此核心功能点)

### [场景1: 学生首次进入新AI课程]

作为一个 **初次体验新AI课的学生**，我希望能够 **在课程开始时有一个明确的、有仪式感的开场**，这样我就可以 **更好地进入学习状态，了解本节课的主题**。目前的痛点是 **直接进入内容可能感觉突兀，缺乏准备**，如果能够 **有一个清晰的开场页，包含课程信息和有趣的动效**，对我的学习专注度会有很大帮助。

**前端界面与交互关键步骤：**

1. **[用户操作1]:** 点击进入一节新课程。
    - **界面变化:** 屏幕展示该学科对应的开场页，包含背景、课程章节号（如“第一章”）、课程序号（如“2.1.1”）、课程名称（如“复数的概念”），并开始自动播放IP动效和对应音频。
    - **即时反馈:** 视觉上看到动效播放，听到开场音频。
2. **[用户操作2]:** 等待开场动效和音频播放完毕。
    - **界面变化:** 开场页消失，平滑过渡到该课程的第一个学习组件界面（如文档组件）。
    - **即时反馈:** 课程内容开始呈现，进入正式学习环节。

**场景细节补充：**

- **前置条件:** 用户已登录，并选择了要学习的课程。
- **期望结果:** 用户顺利进入课程的第一个学习环节，对课程主题有初步了解，并感受到一定的学习仪式感。
- **异常与边界情况:**
    - 网络加载慢：开场页动效/音频加载时应有loading提示，加载失败应有重试机制或提示。
    - 用户中途退出App：下次进入应能恢复到开场页或第一个组件。

**优先级：** 高
**依赖关系：** 依赖课程配置服务提供课程元信息和开场资源。

```mermaid
%% 开场页流程
flowchart TD
    A[用户点击进入课程] --> B{加载开场页资源};
    B -- 成功 --> C[展示开场页界面（章节号、序号、名称）];
    C --> D[自动播放IP动效和音频];
    D --> E{动效和音频播放完毕?};
    E -- 是 --> F[平滑过渡到第一个学习组件];
    F --> G[开始学习];
    E -- 否 --> D; 
    %% 循环播放或等待
    B -- 失败 --> H[显示加载失败提示或重试按钮];

    subgraph 开场页UI元素
        direction LR
        UI1[学科背景图]
        UI2[IP动效]
        UI3[章节号]
        UI4[课程序号]
        UI5[课程名称]
    end

    C --> UI1 & UI2 & UI3 & UI4 & UI5;

```

### [场景2: 学生在学习中途想查看或切换进度]

作为一个 **正在AI课中学习的学生**，我希望能够 **随时方便地查看整节课的结构和我的学习进度，并且能够跳转到之前学过的部分进行复习**，这样我就可以 **更好地掌控学习节奏，回顾重要知识点**。目前的痛点是 **不知道后面还有多少内容，或者想回头看某个知识点但操作不便**，如果能够 **通过一个简单的按钮调出课程进度列表，并支持跳转**，对我的学习效率和自主性会有很大帮助。

**前端界面与交互关键步骤：**

1. **[用户操作1]:** 在学习界面（如文档组件或练习组件），点击右上角的“课程进度”按钮。
    - **界面变化:** 当前学习内容（如视频、板书动画）暂停播放。屏幕中央或侧边弹出“课程进度”弹窗。弹窗内以列表形式展示本节课所有组件（如：课程引入-文档、知识点1-文档、练习1-练习...），每个组件带有图标、名称，并根据学习状态显示不同样式（如已解锁√、学习中>、待解锁锁）。当前所在的组件在列表中高亮显示。
    - **即时反馈:** 学习内容暂停，进度弹窗出现。
2. **[用户操作2]:** 查看进度列表，然后点击某个标记为“已解锁”的组件项（如“知识点1-文档”）。
    - **界面变化:** “课程进度”弹窗收起。主界面内容切换到被点击的“知识点1-文档”组件。如果上次离开该组件时有播放进度，则从该进度开始播放；如果是练习组件且已完成，则展示答题记录。
    - **即时反馈:** 界面内容更新，用户成功跳转到想复习的组件。
3. **[用户操作3]:** (可选) 在“课程进度”弹窗打开时，点击弹窗以外的屏幕区域。
    - **界面变化:** “课程进度”弹窗收起。
    - **即时反馈:** 恢复到之前的学习界面，内容从暂停处继续播放。

**场景细节补充：**

- **前置条件:** 用户正在AI课的某个学习组件中。至少有一个组件是“已解锁”状态。
- **期望结果:** 用户能够清晰了解课程结构和进度，并能方便地在已学过的组件间跳转。
- **异常与边界情况:**
    - 只有一个组件：课程进度按钮可能置灰或不显示。
    - 网络问题：跳转时加载内容失败应有提示。
    - 组件状态更新延迟：应确保组件状态（已解锁/学习中/待解锁）及时准确反映。

**优先级：** 高
**依赖关系：** 依赖用户学习状态记录服务。

```mermaid
stateDiagram-v2
    [*] --> InLesson: 用户进入课程

    InLesson --> ProgressPopup: 点击"课程进度"按钮
    ProgressPopup --> InLesson: 点击外部区域/关闭按钮
    ProgressPopup --> TargetComponent: 选择已解锁组件
    TargetComponent --> InLesson: 加载完成，开始学习

    state InLesson {
        [*] --> Playing: 播放/交互中
        Playing --> Paused: 打开进度弹窗
        Paused --> Playing: 关闭进度弹窗
        Playing --> Switched: 选择新组件
        Switched --> Loading: 加载新内容
    }

    state ProgressPopup {
        [*] --> ShowingList: 显示课程进度
        ShowingList --> ShowingList: 高亮当前组件
    }

    state TargetComponent {
        [*] --> Loading: 加载目标组件
        Loading --> Playing: 加载成功
        Loading --> Error: 加载失败
        Error --> InLesson: 返回当前课程
    }

```

### [场景3: 学生完成一节课，查看结算页]

作为一个 **完成了AI课一节课所有内容的学生**，我希望能够 **在课程结束后看到一个全面的学习总结，包括我的表现、知识掌握情况以及下一步建议**，这样我就可以 **了解自己的学习效果，并获得持续学习的动力和方向**。目前的痛点是 **学完就结束了，缺乏反馈和成就感**，如果能够 **有一个包含庆祝、数据总结、掌握度反馈、错题回顾入口和个性化推荐的结算页**，对我的学习体验和效果评估会有很大帮助。

**前端界面与交互关键步骤：**

1. **[用户操作1]:** 完成最后一个学习组件的学习或练习。
    - **界面变化:** 自动跳转到结算页。页面顶部播放庆祝动效（根据学习表现判断是“开心”还是“鼓励”类型），并显示对应的IP和反馈文案（如“成绩优秀！不错啊，”）。
    - **即时反馈:** 获得完成课程的积极情绪反馈。
2. **[用户操作2]:** 在结算页查看各项数据。
    - **界面变化:** 页面清晰展示：学习用时（如“45分钟”）、答题数（如“27题”）、正确率（如“55%”）；知识点掌握度（如“35%”，含本次提升值“+10%”）；答题情况概览（用不同样式标记对错的题号，如[√][x][√]...），旁边有“查看详情 >”链接；推荐学习模块（如“巩固练习 约12题”，旁边有“去练习”按钮）；课程反馈模块（“本节课感受如何?”及评价图标）。
    - **即时反馈:** 用户获得关于本次学习的量化和质化反馈信息。
3. **[用户操作3]:** 点击“答题情况”旁边的“查看详情 >”。
    - **界面变化:** 跳转到“题目列表”页面，展示所有题目的详细列表（题号、类型、题干、作答结果）。
    - **即时反馈:** 用户进入错题回顾和分析的流程。
4. **[用户操作4]:** (返回结算页后) 点击推荐学习模块的“去练习”按钮。
    - **界面变化:** 跳转到系统推荐的练习模块。
    - **即时反馈:** 用户根据系统建议开始针对性练习。
5. **[用户操作5]:** (返回结算页后) 点击底部的“完成”按钮。
    - **界面变化:** 退出结算页，返回到课程入口或其他指定页面。
    - **即时反馈:** 用户结束本次学习流程。

**场景细节补充：**

- **前置条件:** 用户已完成当前课程的所有必学组件。
- **期望结果:** 用户清晰了解本次学习成果，获得成就感或改进方向，并能方便地进行错题回顾或推荐练习。
- **异常与边界情况:**
    - 数据加载失败：结算页各模块数据加载失败时应有提示。
    - 无推荐内容：推荐学习模块应显示“掌握度已达成，暂无推荐学习内容”。
    - 反馈提交失败：应有提示。

**优先级：** 高
**依赖关系：** 依赖学习统计服务、掌握度服务、题目服务、推荐服务、反馈服务。

```mermaid
flowchart TD
    A[完成最后一个学习组件] --> B{加载结算页数据}
    B -->|成功| C[展示结算页]
    B -->|失败| P[显示加载失败提示]
    
    subgraph 结算页UI元素
        direction TB
        D[庆祝动效+文案]
        E[学习表现数据]
        F[掌握度数据]
        G[答题情况概览]
        I[推荐学习模块]
        K[课程反馈模块]
        M[底部按钮区域]
    end
    
    C --> D
    C --> E
    C --> F
    C --> G
    C --> I
    C --> K
    C --> M
    
    G --> G_Action{点击'查看详情>'}
    G_Action -->|是| H[跳转到题目列表页]
    H --> C
    
    I --> I_Action{点击'去练习'}
    I_Action -->|是| J[跳转到推荐练习模块]
    J --> C
    
    K --> K_Action{点击反馈图标}
    K_Action -->|是| L[处理反馈提交]
    
    M --> M_Action_Finish{点击'完成'}
    M_Action_Finish -->|是| N[返回课程入口页]
    
    M --> M_Action_Review{点击'回看课程'}
    M_Action_Review -->|是| O[重新进入课程]

```

### [场景4: 学生中途退出课程后重新进入]

作为一个 **学习比较零散的学生**，我有时可能 **无法一次性完成整节AI课，需要在中途退出**，我希望 **下次回来时能够从上次离开的地方无缝继续学习**，这样我就可以 **利用碎片时间学习，而不用担心进度丢失或重复学习**。目前的痛点是 **担心退出后进度丢失，需要重新找回学习位置**，如果能够 **自动保存我的进度，并在我重新进入时恢复到上次的状态**，对我的学习连贯性会非常有帮助。

**前端界面与交互关键步骤：**

1. **[用户操作1]:** 在学习界面（如文档组件播放到一半，或练习组件答题到第3题），点击左上角的“退出”按钮。
    - **界面变化:** (可能有确认弹窗，或直接) 界面返回到课程列表/入口页。
    - **即时反馈:** (可能有) Toast提示“学习进度已保存”。前端需将当前学习状态（组件ID、文档播放时间点/练习题号及已答选项）上报给后端。
2. **[用户操作2]:** （一段时间后）用户再次从课程列表/入口页点击进入同一节课。
    - **界面变化:**
        - 如果上次退出时在**文档组件**：直接进入该文档组件，界面加载完成后，自动从上次退出的时间点开始播放视频、同步勾画轨迹和JS动画，并默认进入“跟随模式”。
        - 如果上次退出时在**练习组件**：直接进入该练习组件，界面加载完成后，自动定位到上次退出时正在作答的那道题目，并恢复用户退出前已选择但未提交的选项（如果设计如此）。
        - 如果上次退出时在**答疑组件**（挂载在文档/练习下）：则进入调起答疑的那个文档/练习组件，默认不自动打开答疑界面。
    - **即时反馈:** 用户看到熟悉的界面，内容从上次中断处开始，无需手动定位。

**场景细节补充：**

- **前置条件:** 用户之前学习过该课程并中途退出。系统已成功保存用户的学习进度。
- **期望结果:** 用户能够无缝衔接上次的学习进度，继续学习。
- **异常与边界情况:**
    - 进度保存失败：下次进入时可能从头开始或从上一个检查点开始，需要有明确提示。
    - 课程内容更新：若课程结构或内容在用户退出期间发生变化，需要有策略处理（如提示用户，或从最近的有效节点开始）。
    - 多端登录：需要考虑多端进度同步问题（本期可能不涉及，但需注意）。

**优先级：** 高
**依赖关系：** 依赖用户学习状态记录服务（精确到播放时间点/题目状态）。

```mermaid
%% 退出续学流程
sequenceDiagram
    participant User as 用户
    participant Frontend as 前端应用
    participant Backend as 后端服务

    User->>Frontend: 在学习中点击'退出'按钮
    Frontend->>Backend: 上报当前学习进度 (组件ID, 播放时间/题号, 已选答案等)
    Backend-->>Frontend: 确认收到/保存成功
    Frontend->>User: (可选Toast: 进度已保存) 返回课程入口页

    %% 一段时间后...

    User->>Frontend: 再次点击进入同一课程
    Frontend->>Backend: 请求用户该课程的最后学习进度
    Backend-->>Frontend: 返回最后学习进度信息
    Frontend->>Frontend: 根据进度信息判断恢复点
    alt 上次退出在文档组件
        Frontend->>Frontend: 加载文档组件, 定位到特定时间点
        Frontend->>User: 显示文档组件, 从该时间点自动播放 (跟随模式)
    else 上次退出在练习组件
        Frontend->>Frontend: 加载练习组件, 定位到特定题目
        Frontend->>Frontend: (可选) 恢复用户已选答案
        Frontend->>User: 显示练习组件, 呈现该题目
    else 上次退出在答疑组件 (挂载)
        Frontend->>Frontend: 加载挂载答疑的父组件 (文档/练习)
        Frontend->>User: 显示父组件, 不自动打开答疑
    end

```

### **[核心功能点2]: 文档组件增强 (内容播放与交互优化)**

**用户价值与场景:** 提升文档类型组件（包含JS动画、勾画轨迹、数字人视频、字幕）的学习体验，通过同步播放、聚焦效果、快捷交互等方式，让学生学习更流畅、专注，并能方便地控制播放和进行交互。

**功能详述与前端呈现:**

- **主要交互流程:**
    1. **进入文档组件:** 默认进入“跟随模式”，数字人视频、勾画轨迹、JS动画内容、字幕同步播放。当前讲解句子下方有进度线。未播放区域视觉上弱化/模糊处理。
    2. **用户滑动屏幕:** 切换到“自由模式”，视频继续播放，但板书不再自动滚动，勾画轨迹暂停。用户可自由浏览板书。右上角模式提示变为“自由模式”。
    3. **自由模式下交互:**
        - 单击无评论内容：句子高亮，出现“从这里学”按钮，点击后从此句时间点开始同步播放，并切回“跟随模式”。
        - 单击屏幕空白区域：唤起底部操作面板。
        - 长按空白区域：视频/音频/动画内容以3倍速播放，松手恢复原速。
        - 双击空白区域：切换播放/暂停状态。
    4. **跟随/自由模式下快捷操作:**
        - 单击屏幕空白区域：唤起底部操作面板。
        - 底部操作面板：包含字幕开关、播放/暂停按钮、倍速选择（0.75-2.0共6档）、前进10s、后退10s按钮。
- **界面布局与元素:**
    - **文档区域:** 展示JS动画内容（板书）。
    - **数字人视频区域:** 通常位于角落或一侧。
    - **勾画轨迹:** 叠加在文档区域之上，与讲解同步。
    - **字幕区域:** 通常位于视频下方或屏幕底部。
    - **进度线:** 在当前讲解句子下方动态移动。
    - **未播放区域效果:** 背景模糊或蒙层弱化。
    - **模式切换提示:** 右上角显示当前模式（跟随/自由），切换时有短暂提示。
    - **“从这里学”按钮:** 在自由模式下单击句子时出现，样式醒目。
    - **操作面板:** 底部弹出，半透明背景，包含清晰的图标按钮（字幕、播放/暂停、倍速、快退、快进）。倍速选择提供下拉或弹窗列表。
    - **UI元素状态:**
        - 播放/暂停按钮：播放中图标、暂停中图标。
        - 字幕按钮：开图标、关图标。
        - 倍速按钮：显示当前倍速（如1.0x），点击后弹出选项列表，选中项高亮。
        - 快进/快退按钮：默认、点击（触发动作）。
        - 长按3倍速：触发时有视觉/听觉提示，松手恢复。
        - “从这里学”按钮：显示、隐藏、点击。
- **用户操作与反馈:**
    - **滑动屏幕:** 切换模式时，右上角提示文字变化（如“切换到自由模式”toast），板书滚动停止/开始。
    - **单击句子（自由模式）:** 句子高亮，出现按钮；点击“从这里学”，内容跳转播放，模式切换回跟随。
    - **单击空白区:** 操作面板平滑弹出/收起。
    - **双击空白区:** 播放/暂停状态切换，对应按钮图标变化。
    - **长按空白区:** 播放速度变为3倍，松手恢复，期间可能有速度提示。
    - **操作面板交互:**
        - 点击字幕开关：字幕显示/隐藏，按钮状态变化。
        - 点击播放/暂停：播放状态切换，按钮图标变化。
        - 点击倍速：弹出倍速列表，选择后列表收起，按钮显示新倍速，播放速度变化（视频、勾画、动画同步调整）。Toast提示“已切换至X倍速”。
        - 点击快进/快退：播放进度向前/后跳转10s，进度线和内容同步更新。
- **数据展示与更新:**
    - 字幕内容根据播放进度实时滚动或更新。
    - 进度线根据播放进度实时移动。
    - 倍速按钮实时显示当前生效的倍速。
    - 未播放区域的模糊/弱化效果根据播放进度动态调整。

**优先级：** 高
**依赖关系：** 依赖JS动画引擎、视频播放器、勾画轨迹数据、字幕数据、时间戳同步机制。

### 用户场景与故事 (针对此核心功能点)

### [场景1: 学生希望学习时注意力更集中]

作为一个 **容易分心的学生**，我希望 **在观看AI课件时，系统能帮我聚焦在老师当前讲解的部分，避免被后面或前面的内容干扰**，这样我就可以 **更有效地跟上老师的思路，提高学习效率**。目前的痛点是 **满屏的板书内容有时会让我眼花缭乱，不知道该看哪里**，如果能够 **自动将非当前讲解区域弱化，并有清晰的进度指示**，对我的专注学习会非常有帮助。

**前端界面与交互关键步骤：**

1. **[用户操作1]:** 进入一个文档组件，系统默认处于“跟随模式”。
    - **界面变化:** 数字人视频、勾画轨迹、JS动画板书开始同步播放。当前老师讲解到的句子下方出现一条动态进度线。还未讲到或已经讲过的板书区域呈现模糊或半透明的弱化效果。字幕在底部同步显示。
    - **即时反馈:** 视觉焦点自然集中在当前讲解的句子和进度线上，其他内容干扰减少。
2. **[用户操作2]:** 老师讲解进度向前推进。
    - **界面变化:** 进度线下移到新的句子。之前被弱化的下一句内容变得清晰，而刚刚讲完的句子开始被弱化。JS动画和勾画轨迹也同步演进。
    - **即时反馈:** 学习焦点区域随着讲解自动平滑移动，保持学习的连贯性和专注度。

**场景细节补充：**

- **前置条件:** 用户在文档组件中，处于“跟随模式”。课程内容已配置好时间戳、勾画轨迹和字幕。
- **期望结果:** 用户能够在干扰较小的环境下，专注于当前讲解内容，学习体验更流畅。
- **异常与边界情况:**
    - 时间戳配置错误：可能导致进度线、弱化区域与讲解不同步。
    - 性能问题：弱化/模糊效果的渲染不能影响页面流畅度。

**优先级：** 高
**依赖关系：** 依赖精确的时间戳同步机制和前端渲染性能。

```mermaid
stateDiagram-v2
    [*] --> FollowingMode: 进入文档组件

    state FollowingMode {
        [*] --> Playing: 开始播放
        
        state Playing {
            [*] --> Progressing: 同步播放
            Progressing --> Progressing: 进度推进
        }
        
        Playing --> FreeMode: 用户滑动屏幕
    }

    FollowingMode --> FreeMode: 用户切换模式
    FreeMode --> FollowingMode: 用户返回跟随

```

### [场景2: 学生想要快速控制播放状态或调整速度]

作为一个 **追求高效学习的学生**，我希望 **在观看AI课件时，能够像看普通视频一样方便地暂停、调整倍速或快进快退**，这样我就可以 **根据自己的理解速度调整节奏，或者跳过已掌握的部分**。目前的痛点是 **缺乏快捷的播放控制，需要点开菜单层层查找**，如果能够 **通过简单的手势（如双击、长按）或一个常驻的操作面板快速控制播放**，对我的学习自主性和效率会有很大提升。

**前端界面与交互关键步骤：**

1. **[用户操作1]:** 在文档组件学习过程中，双击屏幕空白区域。
    - **界面变化:** 所有播放内容（视频、勾画、动画）暂停。如果底部操作面板未显示，则自动唤起，且面板上的播放/暂停按钮变为“播放”图标。
    - **即时反馈:** 播放立即停止，用户可以通过图标确认当前为暂停状态。
2. **[用户操作2]:** 再次双击屏幕空白区域。
    - **界面变化:** 所有内容从暂停处恢复播放。操作面板上的按钮变回“暂停”图标。
    - **即时反馈:** 播放恢复。
3. **[用户操作3]:** 单击屏幕空白区域，唤起底部操作面板。
    - **界面变化:** 底部弹出操作面板，包含字幕、播放/暂停、倍速、快退10s、快进10s按钮。
    - **即时反馈:** 用户获得完整的播放控制选项。
4. **[用户操作4]:** 在操作面板上点击“倍速”按钮（当前显示1.0x）。
    - **界面变化:** 按钮上方或旁边弹出倍速选项列表（0.75x, 1.0x, 1.25x, 1.5x, 1.75x, 2.0x）。
    - **即时反馈:** 用户看到可用的倍速选项。
5. **[用户操作5]:** 在倍速列表中选择“1.5x”。
    - **界面变化:** 倍速列表收起。操作面板上的倍速按钮显示变为“1.5x”。视频、勾画、动画的播放速度同步提升至1.5倍。
    - **即时反馈:** Toast提示“已切换至1.5倍速”。用户感知到播放速度加快。
6. **[用户操作6]:** 在操作面板上点击“快进10s”按钮。
    - **界面变化:** 播放进度（视频、勾画、动画、进度线）向前跳转10秒。
    - **即时反馈:** 内容发生跳跃，用户快速前进。
7. **[用户操作7]:** 长按屏幕空白区域。
    - **界面变化:** 播放速度临时提升至3倍。
    - **即时反馈:** 可能有视觉或声音提示表明处于3倍速状态。用户感知到极快的播放速度。
8. **[用户操作8]:** 松开长按的手指。
    - **界面变化:** 播放速度恢复到之前的设定值（如1.5x）。
    - **即时反馈:** 播放速度恢复正常（相对于临时3倍速）。

**场景细节补充：**

- **前置条件:** 用户在文档组件中学习。
- **期望结果:** 用户能够通过快捷手势和操作面板方便地控制播放状态、速度和进度。
- **异常与边界情况:**
    - 快速连续操作：系统应能正确响应，避免状态混乱。
    - 倍速调整对性能的影响：高倍速播放不能导致卡顿或音画不同步。
    - 边界跳转：快进/快退到视频开头或结尾的处理（例如，停在开头/结尾）。

**优先级：** 高
**依赖关系：** 依赖播放器能力支持倍速、精确定位、暂停/播放控制。

```mermaid
%% 快捷交互与操作面板流程
flowchart TD
    A[用户在文档组件学习] --> B{用户操作?}
    
    subgraph 操作面板UI
        direction LR
        UI_Sub[字幕开关]
        UI_PlayPause[播放/暂停]
        UI_Speed[倍速按钮]
        UI_Rewind[快退10s]
        UI_Forward[快进10s]
    end
    
    B -->|双击空白区域| C{当前播放状态?}
    C -->|播放中| D[暂停播放]
    C -->|已暂停| E[恢复播放]
    D --> A
    E --> A
    
    B -->|单击空白区域| F{操作面板显示?}
    F -->|否| G[显示操作面板]
    F -->|是| H[收起操作面板]
    G --> UI_Sub & UI_PlayPause & UI_Speed & UI_Rewind & UI_Forward
    G --> A
    H --> A
    
    B -->|点击倍速按钮| I[显示倍速列表]
    I --> J{选择新倍速?}
    J -->|是| K[更新倍速设置]
    K --> A
    
    B -->|点击播放/暂停| C
    B -->|点击快进10s| L[进度+10s]
    B -->|点击快退10s| M[进度-10s]
    B -->|点击字幕开关| N[切换字幕显示]
    L --> A
    M --> A
    N --> A
    
    B -->|长按空白区域| O[临时3倍速]
    O --> P{用户松手?}
    P -->|否| O
    P -->|是| Q[恢复原倍速]
    Q --> A

```

### [场景3: 学生在自由浏览模式下想从特定位置重新跟随学习]

作为一个 **喜欢先快速浏览一遍内容的学生**，我有时会 **滑动屏幕进入自由模式，快速看看后面的知识点**，但我希望 **当我找到一个想仔细听讲的地方时，能够方便地让老师从那里开始讲，并重新进入跟随模式**，这样我就可以 **兼顾自主浏览和同步学习的需求**。目前的痛点是 **自由浏览后，想回到同步状态比较麻烦，可能需要手动拖动进度条**，如果能够 **在自由模式下，点击我想学的那句话，就能直接从那里开始同步播放**，对我的学习灵活性会非常有帮助。

**前端界面与交互关键步骤：**

1. **[用户操作1]:** 在文档组件学习时，向上或向下滑动屏幕，使板书滚动。
    - **界面变化:** 右上角模式提示变为“自由模式”。板书停止自动滚动，勾画轨迹暂停。数字人视频继续播放（或根据设计也可暂停）。
    - **即时反馈:** 用户获得自由浏览板书的权限。
2. **[用户操作2]:** 用户浏览板书，找到感兴趣的一句话，然后单击这句话。
    - **界面变化:** 被单击的句子高亮显示，并在其旁边或下方出现一个“从这里学”的按钮。
    - **即时反馈:** 系统明确响应了用户的点击，并提供了下一步操作的入口。
3. **[用户操作3]:** 用户点击“从这里学”按钮。
    - **界面变化:** “从这里学”按钮消失。数字人视频、勾画轨迹、JS动画板书立即跳转到与该句关联的时间点，并开始同步播放。板书也可能滚动定位到该句附近。右上角模式提示切换回“跟随模式”。未播放区域重新开始应用模糊/弱化效果。
    - **即时反馈:** 用户从选定的位置无缝切换回同步学习状态。

**场景细节补充：**

- **前置条件:** 用户在文档组件中，已通过滑动进入“自由模式”。课程内容已配置好句子与时间戳的关联。
- **期望结果:** 用户能够在自由浏览后，方便地从任意句子开始恢复同步学习。
- **异常与边界情况:**
    - 点击区域非句子：可能无响应，或按单击空白区域处理（唤起操作面板）。
    - 句子无关联时间戳：点击后“从这里学”按钮不出现或置灰，或提示无法从此学起。
    - 跳转失败：内容无法正确定位到时间点，应有提示。

**优先级：** 高
**依赖关系：** 依赖句子与时间戳的精确映射关系。

```mermaid
%% 自由模式下“从这里学”流程
stateDiagram-v2
    [*] --> FollowingMode: 进入文档组件

    FollowingMode --> FreeMode: 用户滑动屏幕 (切换至自由模式)

    state FreeMode {
        description: 自由模式 - 用户自主浏览
        [*] --> Browse: 用户自由滑动浏览板书
        Browse --> SentenceClicked: 用户单击某句子
        SentenceClicked --> ShowLearnHereButton: 句子高亮, 显示'从这里学'按钮
        ShowLearnHereButton --> JumpToSync: 用户点击'从这里学'按钮
        Browse --> TogglePanel: 用户单击空白区域 (唤起/收起面板)
        %% 其他自由模式交互...
    }

    JumpToSync --> FollowingMode: 跳转到句子对应时间点, 开始同步播放, 切换回跟随模式

    state FollowingMode {
        description: 跟随模式 - 同步播放与聚焦
        %% ... (跟随模式逻辑)
    }

```

### 2.2 辅助功能

- **[辅助功能点1]:** 答题情况查看与题目详情，为 [核心功能点1 - 结算页] 提供支持
    - **功能详述与前端呈现:**
        - **题目列表页:** 结算页点击“查看详情”进入。顶部有“仅看错题”开关。列表展示每道题：题号、类型、题干（最多2行，超出...）、作答结果（正确/错误/部分正确）、“查看解析”入口。支持上划加载更多（如果题目多）。
        - **题目详情页:** 题目列表页点击“查看解析”进入。展示题号（如3/5）、用时、题干、选项、用户答案、正确答案、题目解析、考察知识点、猜你想问（若有）、“加入错题本”按钮（未加入/已加入状态区分）、“上一题”/“下一题”导航按钮（首尾题相应按钮隐藏）。
        - **交互:** “仅看错题”开关切换列表内容。“加入错题本”点击后状态变为“已加入”（不可点），并toast提示“已加入错题本”。上下题按钮切换题目详情。
    - **Mermaid图示 (题目列表与详情跳转):**
        
```mermaid
flowchart TD
    A[结算页] -->|点击'查看详情'| B[题目列表页]
    B -->|点击'仅看错题'| C{筛选状态?}
    C -->|开启| B1[显示错题]
    C -->|关闭| B2[显示全部]
    B1 --> B
    B2 --> B
    
    B -->|点击题目| D[题目详情页]
    
    subgraph 题目列表页UI
        direction TB
        UI_List_Toggle[仅看错题开关]
        UI_List_Items[题目卡片列表]
    end
    
    subgraph 题目详情页UI
        direction TB
        UI_Detail_Info[题号/用时]
        UI_Detail_Content[题干/选项/答案]
        UI_Detail_Analysis[解析/知识点]
        UI_Detail_Actions[错题本/上下题按钮]
    end
    
    D -->|点击'上一题'| D1[加载上一题]
    D -->|点击'下一题'| D2[加载下一题]
    D1 --> D
    D2 --> D
    
    D -->|点击'加入错题本'| E[更新状态+提示]
    E --> D
    
    D -->|导航返回| B
    B -->|导航返回| A
    
    B --> UI_List_Toggle & UI_List_Items
    D --> UI_Detail_Info & UI_Detail_Content & UI_Detail_Analysis & UI_Detail_Actions
        
```
        
- **[辅助功能点2]:** 个性化学习推荐，为 [核心功能点1 - 结算页] 提供支持
    - **功能详述与前端呈现:**
        - 根据后端策略（基于掌握度、练习完成情况等），在结算页展示推荐模块。
        - 可能展示“巩固练习”、“巩固练习-加练”或“拓展练习”的标题，并附带预估题量（如“约12题”）。
        - 提供“去练习”按钮，点击后跳转到对应的练习模块。
        - 若无推荐内容（如掌握度已达标），则显示“掌握度已达成，暂无推荐学习内容”。
    - **Mermaid图示 (推荐逻辑触发):**
        
```mermaid
graph TD
    A[进入结算页] --> B{获取推荐策略};
    B -- 策略:推荐巩固 --> C[显示'巩固练习'+题数+按钮];
    B -- 策略:推荐加练 --> D[显示'巩固练习-加练'+题数+按钮];
    B -- 策略:推荐拓展 --> E[显示'拓展练习'+题数+按钮];
    B -- 策略:无推荐 --> F[显示'暂无推荐内容'];
    C -- 点击'去练习' --> G[跳转到巩固练习];
    D -- 点击'去练习' --> H[跳转到加练练习];
    E -- 点击'去练习' --> I[跳转到拓展练习];
        
```
        

### 2.3 非本期功能

- **[功能点1]:** 框架中加入积分体系和小组战队，[原因]：V1.0聚焦基础体验打磨，积分和社交玩法复杂度高，[建议]：后续迭代考虑。
- **[功能点2]:** 支持互动讲题（自动化逐步讲解+互动问题），[原因]：涉及复杂AI交互和内容制作，[建议]：后续迭代考虑。
- **[功能点3]:** 文档中支持长按评论，[原因]：V1.0优先解决核心播放和交互问题，评论功能优先级较低，[建议]：后续迭代考虑。
- **[功能点4]:** 增加费曼组件、互动组件，[原因]：属于更丰富的教学互动形式，[建议]：后续迭代考虑。
- **[功能点5]:** 单击有评论的文档内容展示评论，[原因]：同评论功能，[建议]：后续迭代考虑。

## 3. 业务流程图 (可选，用于整体业务流)

```mermaid
flowchart TD
    A[用户选择课程进入] --> B[开场页]
    B --> C{课程组件序列}
    
    C -->|文档组件| D[文档学习]
    D -->|自然结束/滑动| C
    D -->|进度跳转| C
    D -->|点击退出| ExitPoint
    
    C -->|练习组件| E[练习答题]
    E -->|答题完成/滑动| C
    E -->|进度跳转| C
    E -->|点击退出| ExitPoint
    
    C -->|全部完成| F[结算页]
    F -->|点击完成| ExitPoint
    F -->|点击回看| B
    F -->|查看详情| G[题目列表/详情]
    G --> F
    F -->|去练习| H[推荐练习]
    H --> F
    
    ExitPoint[返回课程入口页]
    
    subgraph 学习中可操作
        direction LR
        P[进度查看/跳转]
        Q[退出课程]
    end
    
    D --> P & Q
    E --> P & Q

```

## 4. 性能与安全需求

### 4.1 性能需求

- **响应时间：**
    - 文档组件加载（JS动画、视频、勾画数据）完成并可交互时间不超过 **3** 秒 (95th percentile，模拟普通WiFi)。
    - 模式切换（跟随/自由）、播放/暂停、倍速切换、快进/快退操作的响应时间应小于 **200** 毫秒。
    - 结算页数据加载完成时间不超过 **2** 秒。
    - 页面主要内容加载时间（LCP）应在 **2.5** 秒内。
    - 首次交互延迟（FID）或交互至下次绘制时间（INP）应小于 **100** 毫秒。
- **并发用户：** 系统需支持 **1000** 个并发用户同时在线学习，核心功能（播放、交互）性能不显著衰减。 (注：此指标更多是后端考量，前端需保证自身渲染和交互在高负载下依然流畅)
- **数据量：**
    - JS动画和勾画轨迹数据量较大时，应采用流式加载或分段加载策略，避免一次性加载阻塞渲染。
    - 结算页答题情况概览，即使题目数量较多（如50+），渲染和滚动也应流畅。
- **前端性能感知：**
    - **[文档组件加载]**: 应使用骨架屏或loading动画，清晰指示加载状态。视频首帧、JS动画首帧应尽快展现。
    - **[高倍速播放]**: 需确保高倍速（如2x, 3x）下音视频、动画、勾画轨迹同步依然准确，无明显卡顿或掉帧。
    - **[弱化/模糊效果]**: 非当前讲解区域的弱化效果渲染开销需控制，避免影响滚动或播放性能。

### 4.2 安全需求

- **权限控制：** 用户只能访问其已购买或授权的课程内容。结算页显示的个人学习数据（掌握度、答题情况）仅对用户本人可见。
- **数据加密：** 用户身份信息、学习进度、答题记录等敏感数据在前后端传输过程中必须使用 HTTPS。前端避免在本地（如localStorage）存储明文敏感信息（如答案）。
- **操作审计：** 对用户完成课程、提交反馈等关键行为，后端需记录日志（用户ID、课程ID、时间、操作详情）。前端需确保相关触发信息正确传递。
- **输入校验:** 课程反馈的文本输入框，前端需进行基础长度限制和特殊字符过滤，防止XSS等注入风险，后端必须进行二次严格校验。

## 5. 验收标准

### 5.1 功能验收

- **[核心功能点1 - 课程框架升级]：**
    - **用户场景：** 当用户首次进入课程时。
    - **界面与交互：**
        - 开场页按学科正确显示背景图、IP动效、课程元信息。
        - 动效和音频自动播放，结束后自动跳转到第一个组件。
    - **期望结果：** 开场流程顺畅，信息展示正确，符合场景1描述。
    - **用户场景：** 用户在学习中点击“课程进度”按钮。
    - **界面与交互：**
        - 学习内容暂停，弹出课程进度列表。
        - 列表正确显示各组件名称、图标和当前状态（已解锁/学习中/待解锁）。
        - 点击已解锁组件能成功跳转，点击外部区域能收起弹窗并恢复播放。
    - **期望结果：** 课程进度功能符合场景2描述。
    - **用户场景：** 用户完成所有组件学习。
    - **界面与交互：**
        - 自动进入结算页，根据表现播放正确的庆祝动效和文案。
        - 页面正确展示学习用时、答题数、正确率、掌握度（含提升）、答题概览、推荐学习（或无推荐提示）、反馈模块、操作按钮。
        - 点击“查看详情”能跳转到题目列表页。
        - 点击“去练习”能跳转到推荐练习。
        - 点击“完成”能返回课程入口。
    - **期望结果：** 结算页功能符合场景3描述。
    - **用户场景：** 用户中途退出课程后再次进入。
    - **界面与交互：**
        - 系统能恢复到上次退出的文档播放时间点或练习题目。
        - 恢复播放/交互流畅。
    - **期望结果：** 退出续学功能符合场景4描述。
- **[核心功能点2 - 文档组件增强]：**
    - **用户场景：** 用户在“跟随模式”下学习。
    - **界面与交互：**
        - 视频、勾画、动画、字幕同步播放。
        - 当前句子有进度线，非当前区域被弱化/模糊。
    - **期望结果：** 聚焦效果和同步播放符合场景1描述。
    - **用户场景：** 用户需要控制播放。
    - **界面与交互:**
        - 双击空白区能切换播放/暂停状态。
        - 单击空白区能唤起/收起操作面板。
        - 操作面板各按钮（字幕、播放/暂停、倍速、快进/快退）功能正常，反馈及时。
        - 长按空白区能临时3倍速播放，松手恢复。
    - **期望结果：** 快捷交互和操作面板功能符合场景2描述。
    - **用户场景：** 用户在“自由模式”下点击句子并选择“从这里学”。
    - **界面与交互:**
        - 点击句子后出现“从这里学”按钮。
        - 点击按钮后，系统从该句时间点开始同步播放，并切换回“跟随模式”。
    - **期望结果：** “从这里学”功能符合场景3描述。

### 5.2 性能验收

- **响应时间：**
    - **测试用例1:** 使用浏览器开发者工具网络节流（模拟普通WiFi），多次加载文档组件，95%的情况下，从开始加载到内容可交互时间 < 3秒。
    - **测试用例2:** 在文档组件中，连续执行模式切换、播放/暂停、倍速选择、快进/快退操作，使用性能分析工具测量，每次操作的界面响应时间 < 200ms。
    - **测试用例3:** 多次进入结算页，95%的情况下，数据加载完成时间 < 2秒。
- **前端性能感知验收：**
    - **测试用例1 (文档加载):** 加载大型文档组件时，骨架屏或loading动画清晰可见且不卡顿。视频/动画首帧快速出现。
    - **测试用例2 (高倍速):** 在2倍速和3倍速（长按）播放时，音画同步，勾画轨迹无明显延迟或卡顿。浏览器性能监控（如任务管理器）显示CPU占用平稳。
    - **测试用例3 (弱化效果):** 在内容滚动或播放时，弱化/模糊效果的渲染对帧率（FPS）影响<10%。

### 5.3 安全验收

- **权限控制：**
    - **测试用例1:** 使用未购买该课程的用户账号登录，尝试访问课程URL，期望结果：应被拒绝访问或导向购买页。
    - **测试用例2:** 尝试直接访问其他用户的结算页数据接口，期望结果：后端应拒绝请求或返回无权限错误。
- **数据加密：**
    - **测试用例1:** 通过抓包工具检查网络请求，确认所有与用户信息、学习进度、答题数据相关的请求均使用HTTPS。
    - **测试用例2:** 检查浏览器本地存储（localStorage, sessionStorage, cookies），确认没有明文存储用户答案或其他敏感信息。
- **输入校验：**
    - **测试用例1 (XSS):** 在课程反馈的文本输入框中输入 `<script>alert('xss')</script>`，提交反馈，期望结果：脚本未执行，输入内容被正确转义或过滤后存储/显示。
    - **测试用例2 (长度):** 在反馈文本框输入超过设定长度限制（若有）的文本，期望结果：前端应有提示，无法输入更多字符或无法提交。

## 6. 其他需求

### 6.1 可用性与体验需求

- **界面友好与直观性：**
    - 核心操作（如播放控制、模式切换、进度查看）应易于发现和理解。图标含义清晰。
    - 模式切换（跟随/自由）应有明确的视觉指示和短暂的toast提示。
    - 结算页信息布局合理，重要数据（如掌握度、正确率）突出显示。
- **容错处理：**
    - 当 [网络请求失败，如加载课程数据、提交反馈失败] 发生时，系统应提供清晰、友好的错误提示（如toast提示“网络开小差了，请稍后重试”），避免页面崩溃。
    - 当 [用户操作异常，如快速点击按钮] 时，应避免状态错乱，如按钮应有防抖或禁用处理。
- **兼容性：**
    - **浏览器：** 需支持最新版本的 Chrome, Safari (iOS/macOS), Edge 浏览器，以及主流移动端浏览器内核（WebView），保证核心功能和界面显示正常。
    - **响应式布局：** （假设为移动端优先或纯移动端产品，根据原型图判断）
        - **移动端 (<768px):** 布局适配主流手机屏幕尺寸，确保按钮点击区域足够大（符合移动端设计规范），文字大小适宜阅读，横屏/竖屏切换时布局稳定（若支持横屏）。
- **可访问性 (A11y)：**
    - 核心交互元素（按钮、开关）应可通过辅助技术（如屏幕阅读器）访问和操作。
    - 图标按钮应有对应的文本标签（aria-label）。
    - 颜色对比度应满足WCAG AA标准，特别是文本与背景、重要状态指示。
    - 视频内容应提供字幕，且字幕开关易于操作。
    - 状态变更（如模式切换、加载完成、错误提示）应有ARIA live regions支持。
- **交互一致性：**
    - 播放/暂停、快进/快退等标准播放控制图标和行为应与通用视频播放器保持一致。
    - 弹窗（如课程进度、倍速选择）的打开和关闭交互方式应统一。
    - 全局的Toast提示样式和位置应统一。

### 6.2 维护性需求

- **配置管理：** [结算页反馈文案、IP动效触发规则（如正确率阈值）] 可由 [运营人员/产品经理] 在 [后台管理界面/配置文件] 进行调整，无需修改前端代码。
- **监控告警：** 前端应集成错误监控系统（如Sentry）。当 [JS执行错误率突增、核心接口请求成功率骤降、视频/动画加载失败率异常] 时，应能捕获错误详情并自动告警，通知 [前端开发团队]。
- **日志管理：** 前端应记录 [关键用户行为（进入/退出课程、完成组件、模式切换、重要交互操作）、核心接口请求与响应（脱敏）、JS错误详情]，日志可通过监控平台查询，辅助问题排查。日志应包含用户标识、设备信息、时间戳等。

## 7. 用户反馈和迭代计划

### 7.1 用户反馈机制

- 用户可以通过 [结算页的反馈模块] 提交对课程的即时评价和建议。
- 用户可以通过 [App内的通用反馈入口或客服渠道] 提交更全面的产品使用问题和意见。
- 定期（如每周）由 [产品/运营团队] 汇总分析结算页反馈数据和通用反馈渠道信息，识别高频问题和改进点。

### 7.2 迭代计划

- **迭代周期：** 初定每 **2** 周为一个迭代周期。
- **迭代流程：** 每个迭代包含 [需求评审 -> UI/UX设计 -> 开发与联调 -> 测试 -> 发布上线 -> 效果跟踪与用户反馈收集]。
- **优先级调整：** 基于V1.0上线后的数据表现（用户完成率、满意度、各功能使用率）、用户反馈、以及业务目标，产品团队会评估后续迭代内容优先级，如考虑V1.1加入评论、积分等功能（参照2.3节）。

## 8. 需求检查清单

| 原始需求 (来自原始PRD的详细条目) | 对应优化后需求点/章节 (在本PRD中) | 完整性 | 正确性 | 一致性 | 可验证性 (是否有验收标准) | 可跟踪性 (是否有优先级/依赖) | UI/UX明确性 | 备注 |
| --- | --- | --- | --- | --- | --- | --- | --- | --- |
| 需求背景：解决demo调研问题（板书过多、高亮时机、无双击暂停、模式切换难理解） | 1. 需求背景与目标 | ✅ | ✅ | ✅ | N/A | N/A | N/A | 背景已融入 |
| 项目收益：优化课中体验，提升学习效果及满意度 | 1.1 需求目标 | ✅ | ✅ | ✅ | ✅ | N/A | N/A | 目标已量化 |
| 方案简述：框架升级（开场/结算）、文档增强（同步勾画、快捷交互） | 1.2 用户价值 / 2.1 核心功能点1 & 2 | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ | 方案已拆解 |
| 业务流程：单节课由文档/练习/答疑组件构成 | 3. 业务流程图 / 图分析 | ✅ | ✅ | ✅ | N/A | N/A | ✅ | 流程图体现 |
| 需求概览：开场/结算页优化 | 2.1 核心功能点1 / 5.1 功能验收 | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ |  |
| 需求概览：课程进度，自由切换 | 2.1 核心功能点1 / 5.1 功能验收 | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ |  |
| 需求概览：退出/继续课程 | 2.1 核心功能点1 / 5.1 功能验收 | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ |  |
| 需求概览：文档内容播放 - 勾画轨迹同步 | 2.1 核心功能点2 / 5.1 功能验收 | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ |  |
| 需求概览：文档内容播放 - 未播放区模糊/弱化 | 2.1 核心功能点2 / 5.1 功能验收 | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ |  |
| 需求概览：文档内容播放 - 默认展示字幕 | 2.1 核心功能点2 (操作面板) / 5.1 功能验收 | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ |  |
| 需求概览：交互 - 增加双击、长按 | 2.1 核心功能点2 / 5.1 功能验收 | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ |  |
| 需求概览：交互 - 增加1.75和3倍速 | 2.1 核心功能点2 (操作面板&长按) / 5.1 功能验收 | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ |  |
| 需求概览：交互 - 增加前进/后退10s | 2.1 核心功能点2 (操作面板) / 5.1 功能验收 | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ |  |
| 详细方案：开场页设计（学科定制、动效、自动进入） | 2.1 核心功能点1 / 5.1 功能验收 / 图分析 | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ |  |
| 详细方案：结算页设计（动效策略、文案策略、学习表现、掌握度、答题情况、推荐学习、反馈） | 2.1 核心功能点1 / 2.2 辅助功能点1&2 / 5.1 功能验收 / 图分析 | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ |  |
| 详细方案：课程进度交互（按钮触发、列表展示、状态区分、跳转逻辑） | 2.1 核心功能点1 / 5.1 功能验收 / 图分析 | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ |  |
| 详细方案：退出/继续课程逻辑（记录进度、恢复状态） | 2.1 核心功能点1 / 5.1 功能验收 | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ |  |
| 详细方案：文档组件 - 模式切换（跟随/自由、触发条件、提示） | 2.1 核心功能点2 / 5.1 功能验收 / 图分析 | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ |  |
| 详细方案：文档组件 - 跟随模式（同步播放、进度线、弱化效果） | 2.1 核心功能点2 / 5.1 功能验收 | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ |  |
| 详细方案：文档组件 - 自由模式（视频播放、板书静止、进度线保留、从这里学） | 2.1 核心功能点2 / 5.1 功能验收 / 图分析 | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ |  |
| 详细方案：文档组件 - 快捷交互（双击、长按3倍速） | 2.1 核心功能点2 / 5.1 功能验收 | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ |  |
| 详细方案：文档组件 - 操作面板（唤起方式、包含按钮及功能） | 2.1 核心功能点2 / 5.1 功能验收 / 图分析 | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ |  |
| 详细方案：文档组件 - 进入下一组件逻辑（自然结束、滑动切换） | 2.1 核心功能点1 (流程) / 5.1 功能验收 | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ |  |
| 数据需求：埋点需求 | 6.2 维护性需求 (日志) / (隐式要求) | ✅ | ✅ | ✅ | N/A | N/A | N/A | 提及日志 |
| 数据需求：存储需求 | (后端需求，本PRD不直接体现) | N/A | N/A | N/A | N/A | N/A | N/A | 前端关注展示和交互所需数据 |
- **完整性：** 原始需求的核心功能点和交互逻辑均已覆盖。✅
- **正确性：** 优化后需求准确表达了原始需求的意图。✅
- **一致性：** 各需求点之间逻辑一致，无明显冲突。✅
- **可验证性：** 大部分需求点在5.验收标准中有对应条目。✅
- **可跟踪性：** 核心功能均已标注优先级。✅
- **UI/UX明确性：** 通过功能详述、用户场景、图示分析和验收标准，对UI/UX进行了详细描述。✅

## 9. 附录

### 9.1 原型图/设计稿链接

- 视觉稿：待补充 (来自原始PRD)
- (原型图链接可在此处补充)

### 9.2 术语表

- **跟随模式:** 文档组件的一种状态，数字人视频、勾画轨迹、JS动画内容同步自动播放，界面焦点随讲解移动。
- **自由模式:** 文档组件的一种状态，用户可通过滑动屏幕触发，此时可自由浏览板书内容，播放状态和进度控制相对独立。
- **JS动画:** 指课程内容中用JavaScript实现的动态板书、效果演示等。
- **勾画轨迹:** 与老师讲解同步的、在板书上模拟书写或标记的视觉轨迹。
- **IP:** 指课程中使用的虚拟形象或角色 (Intelligent Personal Assistant / Intellectual Property)。
- **掌握度:** 系统根据用户答题情况、题目难度等因素计算出的对知识点掌握程度的量化指标。

### 9.3 参考资料

- [原始需求文档：PRD - AI课中 - 课程框架+文档组件 V1.0] (ai-generates/raw-docs/prd/PRD - AI课中 - 课程框架文档组件 V1.0_analyzed.md)
- [关联需求文档：DemoV1.0 PRD] (链接待补充)
- [关联需求文档：DemoV1.1 PRD] (链接待补充)
- [关联需求文档：课程配置 PRD] (链接待补充)
- [关联需求文档：掌握度 V1.0 PRD] (https://wcng60ba718p.feishu.cn/wiki/J3d0wrEaDiWRy6kj2uVcQKLfnmh)
- [关联需求文档：课中-练习组件 PRD] (链接待补充)
- [相关文档：AI课中需求盘点] (https://wcng60ba718p.feishu.cn/wiki/Htv0wNEAxis9dAkBjTTcjcWlnTb)