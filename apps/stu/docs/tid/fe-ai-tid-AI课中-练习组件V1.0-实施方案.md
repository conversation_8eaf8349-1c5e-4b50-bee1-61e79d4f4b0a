# 前端技术实施方案 - AI课中练习组件 V1.0

**项目名称**: `AI课中-练习组件V1.0`
**文档版本**: `1.0`
**架构师**: `前端架构师`
**创建日期**: `2024-08-01`

## 1. 架构概览

### 1.1 业务目标与技术愿景
本项目旨在优化AI课中练习环节的用户体验，重点提升作答流程的流畅度和即时反馈的有效性与情感化。通过打造高效、流畅、情感丰富的练习组件，提高练习题完成率和用户满意度，实现增强学习参与感和自信心，提升知识巩固效果。

技术实现愿景是构建一个响应迅速、交互流畅、用户体验卓越的练习组件，通过精心设计的转场动效、即时反馈机制和用户友好的交互界面，让学习过程更加愉悦，同时确保代码模块化、可测试性和可维护性。

### 1.2 核心技术栈
* 基础框架：React (v19) / Next.js (v15) (App Router)
* 状态管理：@preact-signals/safe-react 
* 数据获取：SWR
* 数据验证：Zod
* 动画实现：Framer Motion
* UI框架：基于radix-ui
* 样式方案：Tailwind CSS

### 1.3 架构核心原则
1. **MVVM严格分层**: 严格遵循Model-ViewModel-View分离，保持各层职责清晰
2. **页面驱动设计**: 从用户体验的页面视角出发，明确各业务视图的功能边界和组合方式
3. **高内聚低耦合**: 业务视图内实现高内聚，视图间保持低耦合
4. **性能优先**: 确保流畅的转场体验和即时反馈，避免不必要的重渲染
5. **复用至上**: 优先设计可复用的业务视图和MVVM组件，充分利用共享库

## 2. 目录结构和代码组织

基于 Next.js App Router 设计的目录结构：

```
app/
├── views/                  # 业务视图组件
│   ├── exercise/           # 练习相关业务视图
│   │   ├── transition-view.tsx    # 练习转场视图
│   │   ├── question-view.tsx      # 练习答题视图
│   │   ├── feedback-view.tsx      # 即时反馈视图
│   │   └── exercise-page.tsx      # 整合所有视图的完整练习页面
│   ├── shared/            # 可复用的业务视图
│   │   ├── markup-view.tsx       # 勾画视图
│   │   ├── qa-view.tsx           # 答疑视图
│   │   └── error-book-view.tsx   # 错题本视图
│   └── ... 
├── components/            # UI组件
│   ├── question/          # 题目相关组件
│   │   ├── header/            # 题目头部组件
│   │   │   ├── back-button.tsx     # 返回按钮
│   │   │   ├── progress-bar.tsx    # 进度条组件
│   │   │   ├── timer-display.tsx   # 计时器显示
│   │   │   └── question-header.tsx # 整合的头部组件
│   │   ├── content/           # 题目内容组件
│   │   │   ├── type-label.tsx      # 题型标签组件
│   │   │   ├── question-description.tsx # 题干描述组件
│   │   │   ├── single-choice.tsx    # 单选题内容组件
│   │   │   ├── multiple-choice.tsx  # 多选题内容组件
│   │   │   ├── fill-blank.tsx       # 填空题内容组件
│   │   │   ├── true-false.tsx       # 判断题内容组件
│   │   │   └── solution.tsx         # 解答题内容组件
│   │   ├── actions/           # 题目操作组件
│   │   │   ├── floating-button.tsx  # 浮动操作按钮
│   │   │   ├── wrong-book-button.tsx # 错题本按钮
│   │   │   ├── uncertain-button.tsx # 不确定按钮
│   │   │   ├── submit-button.tsx    # 提交按钮
│   │   │   └── continue-button.tsx  # 继续按钮
│   │   ├── answer/            # 答案及解析组件
│   │   │   ├── options-list.tsx     # 选项列表组件
│   │   │   ├── option-item.tsx      # 选项项组件
│   │   │   ├── blank-input.tsx      # 填空输入框组件
│   │   │   ├── correct-answer.tsx   # 正确答案组件
│   │   │   └── question-analysis.tsx # 题目解析组件
│   │   ├── option-item.tsx     # 选项组件
│   │   ├── blank-input.tsx     # 填空输入框组件
│   │   └── question.tsx        # 综合题目组件
│   ├── feedback/          # 反馈相关组件
│   │   ├── correct-feedback.tsx  # 正确反馈组件
│   │   ├── incorrect-feedback.tsx # 错误反馈组件
│   │   ├── streak-feedback.tsx   # 连对反馈组件
│   │   └── feedback-container.tsx # 反馈容器组件
│   └── transition/        # 转场相关组件
│       ├── enter-transition.tsx  # 进入练习转场组件
│       ├── exit-transition.tsx   # 退出练习转场组件
│       └── question-transition.tsx # 题目间转场组件
├── models/               # 数据模型与API交互
│   ├── exercise/           # 练习相关模型
│   │   ├── question.ts         # 题目数据模型与API
│   │   ├── user-answer.ts      # 用户答案数据模型与API
│   │   ├── exercise-session.ts # 练习会话数据模型与API
│   │   └── feedback.ts         # 反馈数据模型与API
│   └── ...
├── viewmodels/           # ViewModel 实现
│   ├── exercise/
│   │   ├── transition-view-model.ts   # 转场视图模型
│   │   ├── question-view-model.ts     # 答题视图模型
│   │   ├── feedback-view-model.ts     # 反馈视图模型
│   │   └── exercise-session-model.ts  # 整个练习会话的全局模型
│   └── ...
└── utils/               # 工具函数
    ├── format.ts          # 格式化工具
    ├── validation.ts      # 数据验证工具
    ├── preload.ts         # 资源预加载工具
    ├── api-error-handler.ts # API错误处理
    └── ...
```

### 2.1 命名规范

* **文件命名**: 使用kebab-case（如`question-view.tsx`, `user-answer.ts`）
* **组件命名**: 使用PascalCase（如`QuestionView`, `OptionItem`）
* **函数/变量命名**: 使用camelCase（如`useQuestionViewModel`, `submitAnswer`）
* **常量命名**: 使用UPPER_SNAKE_CASE（如`MAX_RETRY_COUNT`）
* **类型/接口命名**: 使用PascalCase（如`QuestionData`, `UserAnswer`）

### 2.2 代码风格指南

* 遵循项目已有的ESLint和Prettier配置
* React组件优先使用函数组件和Hooks
* 状态管理优先使用@preact-signals/safe-react
* 组件props应明确定义类型，并使用解构赋值
* 避免内联样式，优先使用Tailwind CSS类名
* 复杂逻辑添加适当注释，尤其是ViewModel中的业务逻辑

## 3. 数据模型和API接口

### 3.1 核心数据模型
结合PRD和TD中的需求，以下是练习组件所需的核心数据模型，使用Zod进行类型验证：

#### 3.1.1 题目数据模型

```typescript
// models/question.ts
import { z } from 'zod';

// 题型枚举
export enum QuestionType {
  SINGLE_CHOICE = 'single_choice',   // 单选题
  MULTIPLE_CHOICE = 'multiple_choice', // 多选题
  FILL_BLANK = 'fill_blank',         // 填空题
  TRUE_FALSE = 'true_false',         // 判断题
  SOLUTION = 'solution'              // 解答题
}

// 选项数据模型
export const QuestionOptionSchema = z.object({
  id: z.string(),                // 选项ID
  content: z.string(),           // 选项内容
  isCorrect: z.boolean(),        // 是否是正确选项
  explanation: z.string().optional(), // 选项解析（可选）
  imageUrl: z.string().optional() // 选项图片（可选）
});

// 填空题答案模型
export const FillBlankAnswerSchema = z.object({
  id: z.string(),                 // 填空项ID
  correctAnswer: z.string(),      // 正确答案
  alternatives: z.array(z.string()).optional() // 替代答案（同义词等）
});

// 基础题目数据模型
export const BaseQuestionSchema = z.object({
  id: z.string(),                // 题目ID
  type: z.nativeEnum(QuestionType), // 题目类型
  content: z.string(),           // 题干内容
  knowledgePoint: z.string(),    // 知识点
  difficulty: z.number().min(1).max(5), // 难度等级
  explanation: z.string(),       // 题目解析
  tags: z.array(z.string()).optional() // 标签（可选）
});

// 单选题模型
export const SingleChoiceQuestionSchema = BaseQuestionSchema.extend({
  type: z.literal(QuestionType.SINGLE_CHOICE),
  options: z.array(QuestionOptionSchema)
});

// 多选题模型
export const MultipleChoiceQuestionSchema = BaseQuestionSchema.extend({
  type: z.literal(QuestionType.MULTIPLE_CHOICE),
  options: z.array(QuestionOptionSchema)
});

// 填空题模型
export const FillBlankQuestionSchema = BaseQuestionSchema.extend({
  type: z.literal(QuestionType.FILL_BLANK),
  blanks: z.array(FillBlankAnswerSchema)
});

// 判断题模型
export const TrueFalseQuestionSchema = BaseQuestionSchema.extend({
  type: z.literal(QuestionType.TRUE_FALSE),
  correctAnswer: z.boolean()
});

// 解答题模型
export const SolutionQuestionSchema = BaseQuestionSchema.extend({
  type: z.literal(QuestionType.SOLUTION),
  referenceAnswer: z.string()
});

// 统一题目模型（联合类型）
export const QuestionSchema = z.discriminatedUnion('type', [
  SingleChoiceQuestionSchema,
  MultipleChoiceQuestionSchema,
  FillBlankQuestionSchema,
  TrueFalseQuestionSchema,
  SolutionQuestionSchema
]);

export type QuestionOption = z.infer<typeof QuestionOptionSchema>;
export type FillBlankAnswer = z.infer<typeof FillBlankAnswerSchema>;
export type BaseQuestion = z.infer<typeof BaseQuestionSchema>;
export type SingleChoiceQuestion = z.infer<typeof SingleChoiceQuestionSchema>;
export type MultipleChoiceQuestion = z.infer<typeof MultipleChoiceQuestionSchema>;
export type FillBlankQuestion = z.infer<typeof FillBlankQuestionSchema>;
export type TrueFalseQuestion = z.infer<typeof TrueFalseQuestionSchema>;
export type SolutionQuestion = z.infer<typeof SolutionQuestionSchema>;
export type Question = z.infer<typeof QuestionSchema>;
```

#### 3.1.2 用户答案数据模型

```typescript
// models/user-answer.ts
import { z } from 'zod';
import { QuestionType } from './question';

// 基础用户答案模型
export const BaseUserAnswerSchema = z.object({
  questionId: z.string(),       // 题目ID
  questionType: z.nativeEnum(QuestionType), // 题目类型
  answeredAt: z.date(),         // 回答时间
  timeSpent: z.number(),        // 耗时（秒）
  isCorrect: z.boolean(),       // 是否正确
  isUncertain: z.boolean().optional(), // 是否有信心（"不确定"功能）
  hasAskedForHelp: z.boolean().default(false) // 是否请求过帮助
});

// 单选题答案
export const SingleChoiceAnswerSchema = BaseUserAnswerSchema.extend({
  questionType: z.literal(QuestionType.SINGLE_CHOICE),
  selectedOptionId: z.string()
});

// 多选题答案
export const MultipleChoiceAnswerSchema = BaseUserAnswerSchema.extend({
  questionType: z.literal(QuestionType.MULTIPLE_CHOICE),
  selectedOptionIds: z.array(z.string())
});

// 填空题答案
export const FillBlankAnswerSchema = BaseUserAnswerSchema.extend({
  questionType: z.literal(QuestionType.FILL_BLANK),
  answers: z.record(z.string(), z.string()) // 映射填空ID到用户输入
});

// 判断题答案
export const TrueFalseAnswerSchema = BaseUserAnswerSchema.extend({
  questionType: z.literal(QuestionType.TRUE_FALSE),
  answer: z.boolean()
});

// 解答题答案
export const SolutionAnswerSchema = BaseUserAnswerSchema.extend({
  questionType: z.literal(QuestionType.SOLUTION),
  answer: z.string(),
  evaluationStatus: z.enum(['pending', 'evaluated', 'failed']).optional(),
  score: z.number().optional()
});

// 统一用户答案模型
export const UserAnswerSchema = z.discriminatedUnion('questionType', [
  SingleChoiceAnswerSchema,
  MultipleChoiceAnswerSchema,
  FillBlankAnswerSchema,
  TrueFalseAnswerSchema,
  SolutionAnswerSchema
]);

export type BaseUserAnswer = z.infer<typeof BaseUserAnswerSchema>;
export type SingleChoiceAnswer = z.infer<typeof SingleChoiceAnswerSchema>;
export type MultipleChoiceAnswer = z.infer<typeof MultipleChoiceAnswerSchema>;
export type FillBlankUserAnswer = z.infer<typeof FillBlankAnswerSchema>;
export type TrueFalseAnswer = z.infer<typeof TrueFalseAnswerSchema>;
export type SolutionAnswer = z.infer<typeof SolutionAnswerSchema>;
export type UserAnswer = z.infer<typeof UserAnswerSchema>;
```

#### 3.1.3 练习会话数据模型

```typescript
// models/exercise-session.ts
import { z } from 'zod';
import { UserAnswerSchema } from './user-answer';

// 练习会话状态
export enum ExerciseSessionStatus {
  NOT_STARTED = 'not_started',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  PAUSED = 'paused'
}

// 练习会话数据模型
export const ExerciseSessionSchema = z.object({
  id: z.string(),              // 会话ID
  userId: z.string(),          // 用户ID
  courseId: z.string(),        // 课程ID
  lessonId: z.string(),        // 课时ID
  exerciseType: z.enum(['in_class', 'reinforcement', 'extension']), // 练习类型：课中/巩固/拓展
  status: z.nativeEnum(ExerciseSessionStatus), // 会话状态
  startedAt: z.date().optional(), // 开始时间
  completedAt: z.date().optional(), // 完成时间
  currentQuestionIndex: z.number().default(0), // 当前题目索引
  userAnswers: z.array(UserAnswerSchema), // 用户答案列表
  streakCount: z.number().default(0), // 连续答对题数
  totalTimeSpent: z.number().default(0), // 总耗时（秒）
  continueToken: z.string().optional() // 用于恢复会话的令牌
});

export type ExerciseSession = z.infer<typeof ExerciseSessionSchema>;
```

#### 3.1.4 反馈数据模型

```typescript
// models/feedback.ts
import { z } from 'zod';

// 反馈类型
export enum FeedbackType {
  CORRECT = 'correct',          // 正确
  INCORRECT = 'incorrect',      // 错误
  PARTIAL = 'partial',          // 部分正确
  STREAK = 'streak',            // 连续答对
  COMPLETION = 'completion'     // 完成练习
}

// 奖励类型
export enum RewardType {
  XP = 'xp',                    // 经验值
  BADGE = 'badge',              // 徽章
  ITEM = 'item',                // 道具
  ANIMATION = 'animation'       // 特殊动画
}

// 基础反馈数据模型
export const BaseFeedbackSchema = z.object({
  type: z.nativeEnum(FeedbackType),  // 反馈类型
  title: z.string(),                 // 标题
  message: z.string(),               // 消息内容
  animationUrl: z.string().optional(), // 动画资源URL
  audioUrl: z.string().optional(),   // 音频资源URL
  duration: z.number().default(3000), // 显示持续时间（毫秒）
});

// 奖励数据模型
export const RewardSchema = z.object({
  type: z.nativeEnum(RewardType),    // 奖励类型
  value: z.number().optional(),      // 奖励值（如XP点数）
  name: z.string().optional(),       // 奖励名称
  iconUrl: z.string().optional(),    // 图标URL
  animationUrl: z.string().optional() // 动画URL
});

// 完整反馈数据模型
export const FeedbackSchema = BaseFeedbackSchema.extend({
  explanation: z.string().optional(), // 解释（如错误原因）
  rewards: z.array(RewardSchema).optional(), // 奖励列表
  nextActionDelay: z.number().default(1000), // 下一步操作延迟（毫秒）
  showCorrectAnswer: z.boolean().default(true) // 是否显示正确答案
});

export type BaseFeedback = z.infer<typeof BaseFeedbackSchema>;
export type Reward = z.infer<typeof RewardSchema>;
export type Feedback = z.infer<typeof FeedbackSchema>;
```

### 3.2 API接口设计

基于SWR和项目统一的fetcher工具实现数据获取和缓存策略，以下是核心API接口：

#### 3.2.1 题目数据API

```typescript
// models/question.ts (继续)
import useSWR from 'swr';
import { z } from 'zod';
import fetcher, { get } from '@repo/lib/utils/fetcher';
import { Question, QuestionSchema } from './question-types';

// API接口响应类型
type QuestionApiResponse = Record<string, unknown>;

// SWR请求验证和解析包装器
const fetchAndValidate = async (url: string) => {
  const data = await fetcher<QuestionApiResponse>(url);
  return QuestionSchema.parse(data);
};

const fetchAndValidateArray = async (url: string) => {
  const data = await fetcher<QuestionApiResponse[]>(url);
  return z.array(QuestionSchema).parse(data);
};

/**
 * 获取练习题列表
 * @param exerciseId 练习ID
 */
export function useExerciseQuestions(exerciseId: string | null) {
  return useSWR<Question[]>(
    exerciseId ? `/exercises/${exerciseId}/questions` : null,
    fetchAndValidateArray,
    {
      revalidateOnFocus: false,
      revalidateIfStale: false
    }
  );
}

/**
 * 获取单个题目
 * @param questionId 题目ID
 */
export function useQuestion(questionId: string | null) {
  return useSWR<Question>(
    questionId ? `/questions/${questionId}` : null,
    fetchAndValidate,
    {
      revalidateOnFocus: false
    }
  );
}

/**
 * 预加载下一题
 * @param questionId 下一题ID
 */
export function prefetchNextQuestion(questionId: string) {
  const prefetchUrl = `/questions/${questionId}`;
  return get<QuestionApiResponse>(prefetchUrl, { query: {} });
}
```

#### 3.2.2 用户答案API

```typescript
// models/user-answer.ts (继续)
import useSWR, { mutate } from 'swr';
import { z } from 'zod';
import fetcher, { post } from '@repo/lib/utils/fetcher';
import { UserAnswer, UserAnswerSchema } from './user-answer-types';

// 为避免循环依赖，定义一个简化版的Feedback类型
export interface Feedback {
  type: string;
  title: string;
  message: string;
  [key: string]: unknown;
}

// API接口响应类型
type AnswerFeedbackResponse = {
  feedback: Feedback;
};

type AnswerHistoryResponse = Record<string, unknown>[];

// SWR请求验证和解析包装器
const fetchAndValidateAnswerHistory = async (url: string) => {
  const data = await fetcher<AnswerHistoryResponse>(url);
  return z.array(UserAnswerSchema).parse(data);
};

/**
 * 提交用户答案
 * @param sessionId 会话ID
 * @param answer 用户答案
 * @returns 提交结果，包含反馈信息
 */
export async function submitUserAnswer(
  sessionId: string,
  answer: UserAnswer
): Promise<{feedback: Feedback}> {
  const validatedAnswer = UserAnswerSchema.parse(answer);
  
  const result = await post<AnswerFeedbackResponse>(
    `/exercise-sessions/${sessionId}/answers`, 
    { arg: validatedAnswer }
  );

  // 提交成功后更新SWR缓存
  await mutate(`/exercise-sessions/${sessionId}`);
  
  return result;
}

/**
 * 获取用户在特定练习中的答案历史
 * @param sessionId 会话ID
 */
export function useUserAnswersHistory(sessionId: string | null) {
  return useSWR<UserAnswer[]>(
    sessionId ? `/exercise-sessions/${sessionId}/answers` : null,
    fetchAndValidateAnswerHistory
  );
}
```

#### 3.2.3 练习会话API

```typescript
// models/exercise-session.ts (继续)
import useSWR, { mutate } from 'swr';
import { z } from 'zod';
import fetcher, { post } from '@repo/lib/utils/fetcher';
import { ExerciseSession, ExerciseSessionSchema } from './exercise-session-types';

// API接口响应类型
type ExerciseSessionResponse = Record<string, unknown>;

// SWR请求验证和解析包装器
const fetchAndValidateSession = async (url: string) => {
  const data = await fetcher<ExerciseSessionResponse>(url);
  return ExerciseSessionSchema.parse(data);
};

/**
 * 获取当前练习会话
 * @param sessionId 会话ID
 */
export function useExerciseSession(sessionId: string | null) {
  return useSWR<ExerciseSession>(
    sessionId ? `/exercise-sessions/${sessionId}` : null,
    fetchAndValidateSession,
    {
      refreshInterval: 60000, // 每分钟自动刷新
      revalidateOnFocus: true
    }
  );
}

/**
 * 创建新练习会话
 * @param courseId 课程ID
 * @param lessonId 课时ID
 * @param exerciseType 练习类型
 */
export async function useCreateExerciseSession(
  courseId: string,
  lessonId: string,
  exerciseType: ExerciseType
): Promise<ExerciseSession> {
  const data = await post<ExerciseSessionResponse>(
    '/exercise-sessions',
    { 
      arg: {
        courseId,
        lessonId,
        exerciseType
      }
    }
  );

  return ExerciseSessionSchema.parse(data);
}

/**
 * 更新练习会话状态
 * @param sessionId 会话ID
 * @param status 新状态
 */
export async function updateExerciseSessionStatus(
  sessionId: string,
  status: ExerciseSessionStatus
): Promise<ExerciseSession> {
  const data = await post<ExerciseSessionResponse>(
    `/exercise-sessions/${sessionId}/status`,
    { arg: { status } }
  );

  // 更新SWR缓存
  await mutate(`/exercise-sessions/${sessionId}`);
  
  return ExerciseSessionSchema.parse(data);
}
```

#### 3.2.4 反馈配置API

```typescript
// models/feedback.ts (继续)
import useSWR from 'swr';
import fetcher from '@repo/lib/utils/fetcher';
import { FeedbackSchema } from './feedback-types';

// API接口响应类型
type FeedbackConfigResponse = {
  correct: Feedback;
  incorrect: Feedback;
  partial: Feedback;
  streak: Feedback;
  completion: Feedback;
};

// SWR请求验证和解析包装器
const fetchAndValidateFeedbackConfig = async (url: string) => {
  const data = await fetcher<Record<string, unknown>>(url);
  
  // 验证每种反馈类型的配置
  return {
    correct: FeedbackSchema.parse(data.correct),
    incorrect: FeedbackSchema.parse(data.incorrect),
    partial: FeedbackSchema.parse(data.partial),
    streak: FeedbackSchema.parse(data.streak),
    completion: FeedbackSchema.parse(data.completion)
  };
};

/**
 * 获取当前课程的反馈配置
 * @param courseId 课程ID
 */
export function useFeedbackConfig(courseId: string | null) {
  return useSWR<FeedbackConfigResponse>(
    courseId ? `/courses/${courseId}/feedback-config` : null,
    fetchAndValidateFeedbackConfig
  );
}
```

### 3.3 数据验证和错误处理

#### 3.3.1 数据验证策略

所有数据模型都使用Zod进行验证，确保类型安全。通常通过包装函数集成到SWR的数据获取过程中：

```typescript
// 通用模式：验证和转换数据
const fetchAndValidate = async (url: string) => {
  // 使用项目统一的fetcher工具获取数据
  const data = await fetcher<RawDataType>(url);
  // 使用Zod验证并可能转换数据
  return MyDataSchema.parse(data);
};

// 使用方式
function useMyData(id: string | null) {
  return useSWR(id ? `/endpoint/${id}` : null, fetchAndValidate);
}
```

#### 3.3.2 错误处理

项目使用统一的错误处理模式，通过 `@repo/lib/utils/fetcher` 工具内置的错误处理机制：

1. **统一错误格式**: fetcher 工具确保所有 API 错误返回标准格式
2. **错误分类**: 根据不同类型的错误 (网络、认证、服务器等) 进行不同处理
3. **用户友好提示**: 提供对用户友好的错误信息，通过 toast 等方式展示
4. **日志记录**: 在开发环境记录详细错误信息，方便调试

对于特定业务场景的错误处理，可以在 SWR 的配置中添加 `onError` 处理函数：

```typescript
function useMyData(id: string | null) {
  return useSWR(id ? `/endpoint/${id}` : null, fetchAndValidate, {
    onError: (error) => {
      // 处理特定业务场景的错误
      console.error('Data fetch error:', error);
      // 可以触发特定的UI反馈
    }
  });
}
```

### 3.4 数据 Mocking 策略

根据项目规范，API Mocking 主要通过两种方式实现：

1. **API 中心化 Mock (ApiFox)**: 
   - 优先使用 ApiFox 等工具提供统一的 Mock 接口
   - 前端应用直接请求 ApiFox 提供的 Mock URL
   
2. **前端自定义 Mock (Next.js Route Handlers)**:
   - 在 `app/api/` 目录下创建对应的 Route Handlers
   - 这些 Route Handlers 模拟实际 API 的行为和数据结构
   - 通过环境变量 `NEXT_PUBLIC_API_HOST` 控制使用 Mock API 还是真实 API

例如，创建题目相关的 Mock API：

```typescript
// app/api/questions/[id]/route.ts
import { NextResponse } from 'next/server';
import { QuestionType } from '../../../../models/exercise/question';

// Mock 单个题目数据
export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 300));
  
  const mockQuestion = {
    id: params.id,
    type: QuestionType.SINGLE_CHOICE,
    content: '这是一道模拟的单选题目题干',
    options: [
      {
        id: 'option-1',
        content: '选项A',
        isCorrect: true
      },
      {
        id: 'option-2',
        content: '选项B',
        isCorrect: false
      }
      // ...更多选项
    ],
    knowledgePoint: '模拟知识点',
    difficulty: 3,
    explanation: '这是题目解析'
  };
  
  return NextResponse.json(mockQuestion);
} 