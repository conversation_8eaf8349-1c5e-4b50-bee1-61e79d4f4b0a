---
title: study-api
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# study-api

Base URLs:

# Authentication

# 课中接口

<a id="opIdgetStudyReport"></a>

## GET 获取学习报告

GET /api/v1/study/summary

获取指定学习会话的完整学习报告，包括学习表现、掌握度记录、答题详情等信息。

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|sessionId|query|integer(int64)| 是 |会话ID|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "message": "Success",
  "data": {
    "sessionId": 3001,
    "userId": 1001,
    "courseId": 1001,
    "studyPerformance": {
      "totalQuestions": 20,
      "correctAnswers": 16,
      "accuracyRate": 80,
      "studyDuration": 3600,
      "completionRate": 100
    },
    "masteryRecords": [
      {
        "knowledgePointId": "[",
        "knowledgePointName": "[",
        "currentMastery": "[",
        "targetMastery": "[",
        "masteryProgress": "[",
        "energyGained": "["
      }
    ],
    "celebrationConfig": {
      "animationType": "happy",
      "animationUrl": "https://cdn.example.com/animations/celebration.mp4",
      "encouragementText": "恭喜你完成了本次学习！"
    }
  },
  "pageInfo": {}
}
```

> 400 Response

```json
{
  "code": 4000001,
  "message": "请求参数无效",
  "detail": "参数格式错误或缺少必需参数"
}
```

> 401 Response

```json
{
  "code": 4010001,
  "message": "需要认证或认证失败",
  "detail": "请提供有效的认证令牌"
}
```

> 404 Response

```json
{
  "code": 4040001,
  "message": "请求的资源不存在",
  "detail": "指定的资源ID无效或已被删除"
}
```

> 500 Response

```json
{
  "code": 5000001,
  "message": "服务器内部错误",
  "detail": "请稍后重试或联系技术支持"
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|成功获取学习报告|Inline|
|400|[Bad Request](https://tools.ietf.org/html/rfc7231#section-6.5.1)|请求参数无效|[GenericError](#schemagenericerror)|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|未认证或认证令牌无效|[GenericError](#schemagenericerror)|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|请求的资源未找到|[GenericError](#schemagenericerror)|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|服务器内部错误|[GenericError](#schemagenericerror)|

### 返回数据结构

#### 枚举值

|属性|值|
|---|---|
|animationType|happy|
|animationType|encourage|

状态码 **400**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||业务错误码|
|» message|string|false|none||用户可读的错误信息|
|» details|string¦null|false|none||可选的详细错误信息或调试信息|
|» data|object¦null|false|none||可选的附加数据|

状态码 **401**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||业务错误码|
|» message|string|false|none||用户可读的错误信息|
|» details|string¦null|false|none||可选的详细错误信息或调试信息|
|» data|object¦null|false|none||可选的附加数据|

状态码 **404**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||业务错误码|
|» message|string|false|none||用户可读的错误信息|
|» details|string¦null|false|none||可选的详细错误信息或调试信息|
|» data|object¦null|false|none||可选的附加数据|

状态码 **500**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||业务错误码|
|» message|string|false|none||用户可读的错误信息|
|» details|string¦null|false|none||可选的详细错误信息或调试信息|
|» data|object¦null|false|none||可选的附加数据|

## GET 课程信息

GET /api/v1/study/info

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|bizTreeId|query|integer| 否 |业务树id|
|bizTreeNodeId|query|integer| 否 |知识点id|

> 返回示例

> 200 Response

```json
{
  "status": 0,
  "code": 0,
  "message": "string",
  "response_time": 0,
  "data": {
    "lessonId": 0,
    "theme": "string",
    "lessonLaunchPage": {
      "chapterText": "string",
      "lessonIndex": "string",
      "lessonName": "string"
    },
    "lessonName": "string",
    "lessonWebUrl": "string",
    "totalWidgetNum": "string",
    "lessonWidgets": [
      {
        "widgetIndex": 0,
        "widgetName": "string",
        "widgetType": "string"
      }
    ]
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» status|integer|true|none||none|
|» code|integer|true|none||none|
|» message|string|true|none||none|
|» response_time|integer|true|none||none|
|» data|object|true|none||none|
|»» lessonId|integer|true|none||none|
|»» theme|string|true|none|主题|none|
|»» lessonLaunchPage|object|true|none|欢迎页|none|
|»»» chapterText|string|true|none|章节文案|none|
|»»» lessonIndex|string|true|none|课程编号|none|
|»»» lessonName|string|true|none|课程名称|none|
|»» lessonName|string|true|none|课程名称|none|
|»» lessonWebUrl|string|true|none|前端地址|none|
|»» totalWidgetNum|integer|true|none|总课程数|none|
|»» currentWidgetIndex|integer|true|none|当前进度|none|
|»» lessonWidgets|[object]|true|none|课程组件列表|none|
|»»» widgetIndex|integer|false|none|组件下标|none|
|»»» widgetName|string|false|none|组件名称|none|
|»»» widgetType|string|false|none|组件类型|none|

#### 枚举值

|属性|值|
|---|---|
|widgetType|guide|
|widgetType|exercise|
|widgetType|video|

## GET 课程组件详情

GET /api/v1/study/widget/info

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|bizTreeId|query|integer| 否 |业务树id|
|bizTreeNodeId|query|integer| 否 |知识点id|
|widgetIndex|query|integer| 否 |组件下标|

> 返回示例

> 200 Response

```json
{
  "status": 200,
  "code": 0,
  "message": "SUCCESS",
  "response_time": 1748406140,
  "data": {
    "widgetIndex": 3,
    "widgetType": "exercise",
    "widgetName": "练习-2",
    "data": "q1irmrans600rt\nq1irmrans900ru|q1irmransc00rv\nq1irmransf00s0"
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» status|integer|true|none||none|
|» code|integer|true|none||none|
|» message|string|true|none||none|
|» response_time|integer|true|none||none|
|» data|object|true|none||none|
|»» lessonId|integer|true|none||none|
|»» widgetIndex|integer|true|none||none|
|»» widgetType|string|true|none||none|
|»» widgetName|string|true|none||none|
|»» data|string|true|none|文稿组件是videoJson  视频组件是视频信息|none|

## POST 进度上报

POST /api/v1/study/progress/report

主要用于文稿组件和视频组件

> Body 请求参数

```json
{
  "lessonId": 0,
  "widgetIndex": 0,
  "status": "string",
  "costSecond": 0
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|body|body|object| 否 ||none|
|» lessonId|body|integer| 是 | 课程id|none|
|» widgetIndex|body|integer| 是 | 组件下标|none|
|» status|body|string| 是 | "locked" | "unlocked" | "completed"|none|
|» costSecond|body|integer| 是 | 学习时长 秒|none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||none|
|» msg|string|true|none||none|
|» data|object|true|none||none|

## POST 答题结果

POST /api/v1/study/answer

> Body 请求参数

```json
{
  "lessonId": 0,
  "widgetIndex": 0,
  "questionId": "string",
  "answer": "string"
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|body|body|object| 否 ||none|
|» lessonId|body|integer| 是 | 课程id|none|
|» widgetIndex|body|integer| 是 | 组件下标|none|
|» questionId|body|string| 是 | 习题id|none|
|» answer|body|string| 是 | 答案|none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "isLast": 0,
    "nextQuestion": {}
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||none|
|» msg|string|true|none||none|
|» data|object|true|none||none|
|»» isLast|integer|false|none|是否是最后一题 1：是 2：否|none|
|»» nextQuestion|object|true|none|下一题信息|none|

## GET 第一道习题

GET /api/v1/study/question/first

> Body 请求参数

```json
{
  "lessonId": 0,
  "widgetIndex": 0
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|body|body|object| 否 ||none|
|» lessonId|body|integer| 是 | 课程id|none|
|» widgetIndex|body|integer| 是 | 组件下标|none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "questionInfo": {}
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||none|
|» msg|string|true|none||none|
|» data|object|true|none||none|
|»» questionInfo|object|true|none|题目信息|none|

## GET 答题历史

GET /api/v1/study/question/history

> Body 请求参数

```json
{
  "lessonId": 0
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|body|body|object| 否 ||none|
|» lessonId|body|integer| 是 | 课程id|none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "questionInfo": {}
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||none|
|» msg|string|true|none||none|
|» data|object|true|none||none|
|»» questionInfo|object|true|none|题目信息|none|

## GET 推题

GET /api/v1/study/question/next

> Body 请求参数

```json
{
  "lessonId": 0,
  "widgetIndex": 0
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|body|body|object| 否 ||none|
|» lessonId|body|integer| 是 | 课程id|none|
|» widgetIndex|body|integer| 是 | 组件下标|none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "questionInfo": {}
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||none|
|» msg|string|true|none||none|
|» data|object|true|none||none|
|»» questionInfo|object|true|none|题目信息|none|

## GET 获取历史聊天记录

GET /api/v1/ai/history

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|lessonId|query|integer| 否 ||none|
|widgetIndex|query|integer| 否 ||none|

> 返回示例

> 200 Response

```json
{
  "status": 0,
  "code": 0,
  "message": "string",
  "response_time": 0,
  "data": {
    "list": [
      {
        "type": "string",
        "data": "string",
        "time": 0,
        "method": 0
      }
    ]
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» status|integer|true|none||none|
|» code|integer|true|none||none|
|» message|string|true|none||none|
|» response_time|integer|true|none||none|
|» data|object|true|none||none|
|»» list|[object]|true|none||none|
|»»» type|string|false|none||none|
|»»» data|string|false|none||none|
|»»» time|integer|false|none||none|
|»»» method|integer|false|none||none|

## POST 答疑

POST /api/v1/ai/chat

> Body 请求参数

```json
{
  "lessonId": 0,
  "widgetIndex": 0,
  "currentFrame": 0,
  "questionId": 0,
  "message": "string"
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|body|body|object| 否 ||none|
|» lessonId|body|integer| 是 ||none|
|» widgetIndex|body|integer| 是 ||none|
|» currentFrame|body|integer| 是 ||none|
|» questionId|body|integer| 是 ||none|
|» message|body|string| 是 ||none|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

# 数据模型

<h2 id="tocS_GenericError">GenericError</h2>

<a id="schemagenericerror"></a>
<a id="schema_GenericError"></a>
<a id="tocSgenericerror"></a>
<a id="tocsgenericerror"></a>

```json
{
  "code": 4000001,
  "message": "无效的参数",
  "details": "字段 [studentId] 不能为空",
  "data": {}
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||业务错误码|
|message|string|false|none||用户可读的错误信息|
|details|string¦null|false|none||可选的详细错误信息或调试信息|
|data|object¦null|false|none||可选的附加数据|

<h2 id="tocS_StudyReport">StudyReport</h2>

<a id="schemastudyreport"></a>
<a id="schema_StudyReport"></a>
<a id="tocSstudyreport"></a>
<a id="tocsstudyreport"></a>

```json
{
  "sessionId": 3001,
  "userId": 1001,
  "courseId": 1001,
  "studyPerformance": {
    "totalQuestions": 20,
    "correctAnswers": 16,
    "accuracyRate": 80,
    "studyDuration": 3600,
    "completionRate": 100
  },
  "masteryRecords": [
    {
      "knowledgePointId": 5001,
      "knowledgePointName": "人工智能基础概念",
      "currentMastery": 0.85,
      "targetMastery": 0.8,
      "masteryProgress": 1,
      "energyGained": 50
    }
  ],
  "celebrationConfig": {
    "animationType": "happy",
    "animationUrl": "https://cdn.example.com/animations/celebration.mp4",
    "encouragementText": "恭喜你完成了本次学习！"
  }
}

```

完整学习报告

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|sessionId|integer(int64)|true|none||会话ID|
|userId|integer(int64)|true|none||用户ID|
|courseId|integer(int64)|true|none||课程ID|
|studyPerformance|object|true|none||学习表现数据|
|» totalQuestions|integer|false|none||总答题数量|
|» correctAnswers|integer|false|none||正确答题数量|
|» accuracyRate|number|false|none||正确率（百分比）|
|» studyDuration|integer|false|none||学习用时（秒）|
|» completionRate|number|false|none||完成率（百分比）|
|masteryRecords|[object]|true|none||掌握度记录|
|» knowledgePointId|integer(int64)|false|none||知识点ID|
|» knowledgePointName|string|false|none||知识点名称|
|» currentMastery|number|false|none||当前掌握度值 (0.00-1.00)|
|» targetMastery|number|false|none||目标掌握度值 (0.00-1.00)|
|» masteryProgress|number|false|none||掌握度完成进度 (0.00-1.00)|
|» energyGained|integer|false|none||获得的能量值|
|celebrationConfig|object|false|none||庆祝动效配置|
|» animationType|string|false|none||动效类型|
|» animationUrl|string|false|none||动效资源URL|
|» encouragementText|string|false|none||鼓励文案|

#### 枚举值

|属性|值|
|---|---|
|animationType|happy|
|animationType|encourage|

<h2 id="tocS_GenericSuccess">GenericSuccess</h2>

<a id="schemagenericsuccess"></a>
<a id="schema_GenericSuccess"></a>
<a id="tocSgenericsuccess"></a>
<a id="tocsgenericsuccess"></a>

```json
{
  "code": 0,
  "message": "Success",
  "data": {},
  "pageInfo": {}
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||业务状态码，0表示成功|
|message|string|false|none||成功信息|
|data|object|false|none||返回的业务数据|
|pageInfo|object¦null|false|none||可选的分页信息|

