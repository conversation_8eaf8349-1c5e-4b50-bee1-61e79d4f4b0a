---
title: study-api
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# study-api

Base URLs:

# Authentication

# 课中

## GET 获取历史聊天记录

GET /api/v1/lesson/qa/history

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|lessonId|query|integer| 否 |none|
|knowledgeId|query|integer| 否 |知识点|
|widgetIndex|query|integer| 否 |none|
|Authorization|header|string| 否 |none|
|userTypeId|header|string| 否 |none|
|organizationId|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "status": 0,
  "code": 0,
  "message": "string",
  "response_time": 0,
  "data": {
    "list": [
      {
        "type": "string",
        "data": "string",
        "time": 0,
        "method": 0
      }
    ]
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» status|integer|true|none||none|
|» code|integer|true|none||none|
|» message|string|true|none||none|
|» response_time|integer|true|none||none|
|» data|object|true|none||none|
|»» list|[object]|true|none||none|
|»»» type|string|false|none||none|
|»»» data|string|false|none||none|
|»»» time|integer|false|none||none|
|»»» method|integer|false|none||none|

## POST 答疑

POST /api/v1/lesson/qa/chat

> Body 请求参数

```json
{
  "lessonId": 0,
  "knowledgeId": 0,
  "widgetIndex": 0,
  "currentFrame": 0,
  "questionId": 0,
  "message": "string"
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|userTypeId|header|string| 否 ||none|
|organizationId|header|string| 否 ||none|
|body|body|object| 否 ||none|
|» lessonId|body|integer| 是 ||none|
|» knowledgeId|body|integer| 是 ||none|
|» widgetIndex|body|integer| 是 ||none|
|» currentFrame|body|integer| 是 | 视频当前帧|none|
|» questionId|body|integer| 是 | 习题id|none|
|» message|body|string| 是 ||none|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

# 练习

<a id="opIdgetNextQuestion"></a>

## GET 获取下一题

GET /api/v1/study_session/next_question

基于掌握度的智能推题，获取当前或下一道题目。

1. 巩固练习进入练习后获取第一题，作答后获取下一题。
2. AI课拉取题目会创建获取拉取历史练习会话 id，并返回第一题，作答后获取下一题。

当返回"hasNextQuestion"为false时表示练习结束，可调用学习报告接口。

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|studySessionId|query|integer(int64)| 否 ||会话ID，巩固练习是练习会话 ID，AI课则是课程会话 ID|
|knowledgeId|query|integer| 是 ||知识点ID，必须|
|studyType|query|integer| 是 ||学习类型，必须(1:AI课, 2:巩固练习, 3:拓展练习, 4:普通作业, 5:测试任务, 6:资源任务, 7:课中练习, 99:其他)|
|subjectId|query|integer| 否 ||科目ID，可选|
|phaseId|query|integer| 否 ||学段ID，可选|
|lessonId|query|integer| 否 ||课程ID，可选|
|widgetIndex|query|integer| 否 ||组件序号，AI课必须|
|questionSetId|query|integer| 否 ||题集ID，巩固练习需提供|
|textBookId|query|integer| 否 ||教材ID，巩固练习必须|
|Authorization|header|string| 否 ||none|
|userTypeId|header|string| 否 ||none|
|organizationId|header|string| 否 ||none|

#### 枚举值

|属性|值|
|---|---|
|studyType|1|
|studyType|2|
|studyType|3|
|studyType|4|
|studyType|5|
|studyType|6|
|studyType|7|
|studyType|99|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "studySessionId": 0,
    "questionInfo": {
      "questionId": "string",
      "lastAnswerDuration": 0,
      "questionAnswer": {
        "answerOptionList": null
      },
      "questionContent": {
        "questionOrder": null,
        "questionScore": null,
        "questionOriginName": null,
        "questionStem": null,
        "questionOptionList": null
      },
      "questionExplanation": "string",
      "questionAnswerMode": 0,
      "questionType": 1,
      "questionDifficult": 3,
      "questionExtra": "string",
      "questionTopic": "下列哪个选项是正确的？",
      "questionTags": [
        "string"
      ]
    },
    "progressInfo": {
      "currentProgress": 0,
      "completedQuestions": 0,
      "totalQuestions": 0,
      "questionGroups": [
        {}
      ]
    },
    "isResume": true,
    "hasNextQuestion": true,
    "hasAgain": "string",
    "studentAnswer": {
      "questionId": "string",
      "answerContents": {
        "index": null,
        "questionId": null,
        "type": null,
        "content": null,
        "selfEvaluations": null
      },
      "answerType": 0,
      "answerDuration": 0,
      "answerResult": 0,
      "evaluationType": 0,
      "answerStats": [
        {}
      ]
    }
  }
}
```

> 400 Response

```json
{
  "code": 4000001,
  "message": "请求参数错误"
}
```

> 401 Response

```json
{
  "code": 4010001,
  "message": "需要认证或认证失败"
}
```

> 403 Response

```json
{
  "code": 4030001,
  "message": "无权限执行此操作"
}
```

> 404 Response

```json
{
  "code": 4040001,
  "message": "请求的资源不存在"
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|成功获取题目信息|Inline|
|400|[Bad Request](https://tools.ietf.org/html/rfc7231#section-6.5.1)|请求参数错误|[GenericError](#schemagenericerror)|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|未认证或认证令牌无效|[GenericError](#schemagenericerror)|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|无权限访问|[GenericError](#schemagenericerror)|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|请求的资源未找到|[GenericError](#schemagenericerror)|

### 返回数据结构

#### 枚举值

|属性|值|
|---|---|
|type|1|
|type|2|
|type|3|
|type|4|

状态码 **400**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||业务错误码|
|» message|string|false|none||用户可读的错误信息|
|» detail|string¦null|false|none||错误详情，可选|
|» data|object¦null|false|none||错误时通常为空|
|» pageInfo|object¦null|false|none||错误时通常为空|

状态码 **401**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||业务错误码|
|» message|string|false|none||用户可读的错误信息|
|» detail|string¦null|false|none||错误详情，可选|
|» data|object¦null|false|none||错误时通常为空|
|» pageInfo|object¦null|false|none||错误时通常为空|

状态码 **403**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||业务错误码|
|» message|string|false|none||用户可读的错误信息|
|» detail|string¦null|false|none||错误详情，可选|
|» data|object¦null|false|none||错误时通常为空|
|» pageInfo|object¦null|false|none||错误时通常为空|

状态码 **404**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||业务错误码|
|» message|string|false|none||用户可读的错误信息|
|» detail|string¦null|false|none||错误详情，可选|
|» data|object¦null|false|none||错误时通常为空|
|» pageInfo|object¦null|false|none||错误时通常为空|

<a id="opIdsubmitAnswer"></a>

## POST 提交题目作答结果并获取反馈

POST /api/v1/study_session/submit_answer

学生提交题目答案，系统判断答案正确性，记录作答详情，提供即时的情感化反馈。
包含正确/错误/连胜状态反馈，并自动推进到下一题或完成练习。
如果答错，系统内部自动添加错题到错题本。
支持防重复提交机制。

> Body 请求参数

```json
{
  "sessionId": 1001,
  "questionId": 3001,
  "studentAnswer": "A",
  "answerDuration": 30
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|userTypeId|header|string| 否 ||none|
|organizationId|header|string| 否 ||none|
|body|body|[SubmitAnswerRequest](#schemasubmitanswerrequest)| 否 ||none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "answerResult": [
      {
        "index": 0,
        "questionId": "string",
        "answerVerify": 0
      }
    ],
    "answerCount": 0,
    "correctComboCount": 3,
    "answerStats": [
      {
        "questionId": "string",
        "answerStat": [
          null
        ]
      }
    ],
    "feedback": {
      "ipRole": "role_good",
      "type": "correct",
      "title": "string",
      "content": "string",
      "soundFX": "string",
      "animation": "string"
    },
    "specialFeedbacks": [
      {
        "ipRole": "[",
        "type": "[",
        "title": "string",
        "content": "string",
        "soundFX": "string",
        "animation": "string"
      }
    ],
    "nextQuestionInfo": {
      "questionId": "string",
      "lastAnswerDuration": 0,
      "questionAnswer": {
        "answerOptionList": null
      },
      "questionContent": {
        "questionOrder": null,
        "questionScore": null,
        "questionOriginName": null,
        "questionStem": null,
        "questionOptionList": null
      },
      "questionExplanation": "string",
      "questionAnswerMode": 0,
      "questionType": 1,
      "questionDifficult": 3,
      "questionExtra": "string",
      "questionTopic": "下列哪个选项是正确的？",
      "questionTags": [
        "string"
      ]
    },
    "exerciseCompleted": false,
    "progressInfo": {
      "currentProgress": 0,
      "completedQuestions": 0,
      "totalQuestions": 0,
      "questionGroups": [
        {}
      ]
    },
    "hasNextQuestion": true,
    "nextQuestionGroupInfo": {
      "groupCount": 0,
      "groupIndex": 0,
      "groupName": "string",
      "groupQuestionCount": 0
    }
  }
}
```

> 400 Response

```json
{
  "code": 4000001,
  "message": "请求参数错误"
}
```

> 401 Response

```json
{
  "code": 4010001,
  "message": "需要认证或认证失败"
}
```

> 403 Response

```json
{
  "code": 4030001,
  "message": "无权限执行此操作"
}
```

> 404 Response

```json
{
  "code": 4040001,
  "message": "请求的资源不存在"
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|成功提交答案并获取反馈|Inline|
|400|[Bad Request](https://tools.ietf.org/html/rfc7231#section-6.5.1)|请求参数错误|[GenericError](#schemagenericerror)|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|未认证或认证令牌无效|[GenericError](#schemagenericerror)|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|无权限访问|[GenericError](#schemagenericerror)|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|请求的资源未找到|[GenericError](#schemagenericerror)|

### 返回数据结构

#### 枚举值

|属性|值|
|---|---|
|ipRole|role_good|
|ipRole|role_bad|
|type|correct|
|type|incorrect|
|type|continuous_correct|
|type|difficulty_up|
|type|difficulty_down|
|type|answer_carelessly|
|ipRole|role_good|
|ipRole|role_bad|
|type|correct|
|type|incorrect|
|type|continuous_correct|
|type|difficulty_up|
|type|difficulty_down|
|type|answer_carelessly|

状态码 **400**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||业务错误码|
|» message|string|false|none||用户可读的错误信息|
|» detail|string¦null|false|none||错误详情，可选|
|» data|object¦null|false|none||错误时通常为空|
|» pageInfo|object¦null|false|none||错误时通常为空|

状态码 **401**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||业务错误码|
|» message|string|false|none||用户可读的错误信息|
|» detail|string¦null|false|none||错误详情，可选|
|» data|object¦null|false|none||错误时通常为空|
|» pageInfo|object¦null|false|none||错误时通常为空|

状态码 **403**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||业务错误码|
|» message|string|false|none||用户可读的错误信息|
|» detail|string¦null|false|none||错误详情，可选|
|» data|object¦null|false|none||错误时通常为空|
|» pageInfo|object¦null|false|none||错误时通常为空|

状态码 **404**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||业务错误码|
|» message|string|false|none||用户可读的错误信息|
|» detail|string¦null|false|none||错误详情，可选|
|» data|object¦null|false|none||错误时通常为空|
|» pageInfo|object¦null|false|none||错误时通常为空|

<a id="opIdexitExerciseSession"></a>

## POST 退出练习会话

POST /api/v1/study_session/exit

学生中途退出练习时调用此接口，系统自动保存当前进度。
支持缓存和数据库双重保障，确保进度不丢失。
后续可通过进入练习接口自动恢复会话。

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|studySessionId|query|integer(int64)| 是 ||学习会话ID|
|questionId|query|string| 否 ||当前题目 id|
|answerDuration|query|integer| 否 ||当前题目作答时长，只对答错或未作答的题目有效，毫秒|
|Authorization|header|string| 否 ||none|
|userTypeId|header|string| 否 ||none|
|organizationId|header|string| 否 ||none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "exited": true
  }
}
```

> 400 Response

```json
{
  "code": 4000001,
  "message": "请求参数错误"
}
```

> 401 Response

```json
{
  "code": 4010001,
  "message": "需要认证或认证失败"
}
```

> 403 Response

```json
{
  "code": 4030001,
  "message": "无权限执行此操作"
}
```

> 404 Response

```json
{
  "code": 4040001,
  "message": "请求的资源不存在"
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|成功退出练习会话|Inline|
|400|[Bad Request](https://tools.ietf.org/html/rfc7231#section-6.5.1)|请求参数错误|[GenericError](#schemagenericerror)|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|未认证或认证令牌无效|[GenericError](#schemagenericerror)|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|无权限访问|[GenericError](#schemagenericerror)|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|请求的资源未找到|[GenericError](#schemagenericerror)|

### 返回数据结构

状态码 **400**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||业务错误码|
|» message|string|false|none||用户可读的错误信息|
|» detail|string¦null|false|none||错误详情，可选|
|» data|object¦null|false|none||错误时通常为空|
|» pageInfo|object¦null|false|none||错误时通常为空|

状态码 **401**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||业务错误码|
|» message|string|false|none||用户可读的错误信息|
|» detail|string¦null|false|none||错误详情，可选|
|» data|object¦null|false|none||错误时通常为空|
|» pageInfo|object¦null|false|none||错误时通常为空|

状态码 **403**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||业务错误码|
|» message|string|false|none||用户可读的错误信息|
|» detail|string¦null|false|none||错误详情，可选|
|» data|object¦null|false|none||错误时通常为空|
|» pageInfo|object¦null|false|none||错误时通常为空|

状态码 **404**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||业务错误码|
|» message|string|false|none||用户可读的错误信息|
|» detail|string¦null|false|none||错误详情，可选|
|» data|object¦null|false|none||错误时通常为空|
|» pageInfo|object¦null|false|none||错误时通常为空|

<a id="opIdgetExerciseHistory"></a>

## GET 获取练习会话答题详情

GET /api/v1/study_session/answer_detail

查询学生学习会话的详细信息，包括AI 课和巩固练习，会基于 sessionId 来识别

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|studySessionId|query|integer| 否 ||练习会话 ID，AI 课练习可以是AI课会话ID|
|questionIds|query|string| 否 ||题目 id，多个题目用','连接，如果指定题目，则查询指定题目的作答信息|
|onlyWrong|query|boolean| 否 ||是否只看错题，默认查看全部|
|Authorization|header|string| 否 ||none|
|userTypeId|header|string| 否 ||none|
|organizationId|header|string| 否 ||none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "studySessionId": 0,
    "studyType": 0,
    "questions": [
      {
        "questionId": "string",
        "lastAnswerDuration": 0,
        "questionAnswer": {},
        "questionContent": {},
        "questionExplanation": "string",
        "questionAnswerMode": 0,
        "questionType": "[",
        "questionDifficult": "[",
        "questionExtra": "string",
        "questionTopic": "[",
        "questionTags": [
          null
        ]
      }
    ],
    "studentAnswers": [
      {
        "questionId": "string",
        "answerContents": {},
        "answerType": 0,
        "answerDuration": 0,
        "answerResult": 0,
        "evaluationType": 0,
        "answerStats": [
          null
        ]
      }
    ]
  }
}
```

> 400 Response

```json
{
  "code": 4000001,
  "message": "请求参数错误"
}
```

> 401 Response

```json
{
  "code": 4010001,
  "message": "需要认证或认证失败"
}
```

> 403 Response

```json
{
  "code": 4030001,
  "message": "无权限执行此操作"
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|成功获取练习历史记录|Inline|
|400|[Bad Request](https://tools.ietf.org/html/rfc7231#section-6.5.1)|请求参数错误|[GenericError](#schemagenericerror)|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|未认证或认证令牌无效|[GenericError](#schemagenericerror)|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|无权限访问|[GenericError](#schemagenericerror)|

### 返回数据结构

#### 枚举值

|属性|值|
|---|---|
|type|1|
|type|2|
|type|3|
|type|4|

状态码 **400**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||业务错误码|
|» message|string|false|none||用户可读的错误信息|
|» detail|string¦null|false|none||错误详情，可选|
|» data|object¦null|false|none||错误时通常为空|
|» pageInfo|object¦null|false|none||错误时通常为空|

状态码 **401**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||业务错误码|
|» message|string|false|none||用户可读的错误信息|
|» detail|string¦null|false|none||错误详情，可选|
|» data|object¦null|false|none||错误时通常为空|
|» pageInfo|object¦null|false|none||错误时通常为空|

状态码 **403**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||业务错误码|
|» message|string|false|none||用户可读的错误信息|
|» detail|string¦null|false|none||错误详情，可选|
|» data|object¦null|false|none||错误时通常为空|
|» pageInfo|object¦null|false|none||错误时通常为空|

# 公共

<a id="opIddeleteWrongQuestions"></a>

## DELETE 从错题本删除错题

DELETE /api/v1/study/wrong_questions

从学生错题本中删除指定的错题记录。
支持批量删除，通过错题记录ID列表进行删除操作。
删除后不可恢复，请谨慎操作。

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|questionIds|query|string| 是 ||错题记录ID列表，多个ID用逗号分隔|
|Authorization|header|string| 否 ||none|
|userTypeId|header|string| 否 ||none|
|organizationId|header|string| 否 ||none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "deletedCount": 3,
    "failedIds": []
  }
}
```

> 400 Response

```json
{
  "code": 4000001,
  "message": "请求参数错误"
}
```

> 401 Response

```json
{
  "code": 4010001,
  "message": "需要认证或认证失败"
}
```

> 403 Response

```json
{
  "code": 4030001,
  "message": "无权限执行此操作"
}
```

> 404 Response

```json
{
  "code": 4040001,
  "message": "请求的资源不存在"
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|成功删除错题|Inline|
|400|[Bad Request](https://tools.ietf.org/html/rfc7231#section-6.5.1)|请求参数错误|[GenericError](#schemagenericerror)|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|未认证或认证令牌无效|[GenericError](#schemagenericerror)|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|无权限访问|[GenericError](#schemagenericerror)|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|请求的资源未找到|[GenericError](#schemagenericerror)|

### 返回数据结构

状态码 **400**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||业务错误码|
|» message|string|false|none||用户可读的错误信息|
|» detail|string¦null|false|none||错误详情，可选|
|» data|object¦null|false|none||错误时通常为空|
|» pageInfo|object¦null|false|none||错误时通常为空|

状态码 **401**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||业务错误码|
|» message|string|false|none||用户可读的错误信息|
|» detail|string¦null|false|none||错误详情，可选|
|» data|object¦null|false|none||错误时通常为空|
|» pageInfo|object¦null|false|none||错误时通常为空|

状态码 **403**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||业务错误码|
|» message|string|false|none||用户可读的错误信息|
|» detail|string¦null|false|none||错误详情，可选|
|» data|object¦null|false|none||错误时通常为空|
|» pageInfo|object¦null|false|none||错误时通常为空|

状态码 **404**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||业务错误码|
|» message|string|false|none||用户可读的错误信息|
|» detail|string¦null|false|none||错误详情，可选|
|» data|object¦null|false|none||错误时通常为空|
|» pageInfo|object¦null|false|none||错误时通常为空|

<a id="opIdmanualAddWrongQuestion"></a>

## POST 手动添加错题到错题本

POST /api/v1/study/wrong_question/add

学生手动添加错题到错题本的专用接口。
与自动添加不同，手动添加不需要作答记录ID，但需要学生主动选择错因标签。
支持题目验证和去重处理。

> Body 请求参数

```json
{
  "studentId": 1001,
  "questionId": 3001,
  "errorReasonTags": [
    "需要复习"
  ],
  "notes": "这道题需要重点复习"
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|userTypeId|header|string| 否 ||none|
|organizationId|header|string| 否 ||none|
|body|body|object| 否 ||none|
|» subjectId|body|integer| 是 ||科目ID|
|» questionId|body|string| 是 ||题目ID|
|» errorReasonTags|body|[string]| 是 ||错因标签|
|» notes|body|string¦null| 否 ||学生备注|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "wrongQuestionId": 6001,
    "addMethod": 2
  }
}
```

> 400 Response

```json
{
  "code": 4000001,
  "message": "请求参数错误"
}
```

> 401 Response

```json
{
  "code": 4010001,
  "message": "需要认证或认证失败"
}
```

> 403 Response

```json
{
  "code": 4030001,
  "message": "无权限执行此操作"
}
```

> 404 Response

```json
{
  "code": 4040002,
  "message": "指定的题目不存在"
}
```

> 409 Response

```json
{
  "code": 4090001,
  "message": "该题目已在错题本中"
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|成功手动添加错题|Inline|
|400|[Bad Request](https://tools.ietf.org/html/rfc7231#section-6.5.1)|请求参数错误|[GenericError](#schemagenericerror)|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|未认证或认证令牌无效|[GenericError](#schemagenericerror)|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|无权限访问|[GenericError](#schemagenericerror)|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|题目不存在|[GenericError](#schemagenericerror)|
|409|[Conflict](https://tools.ietf.org/html/rfc7231#section-6.5.8)|错题已存在|[GenericError](#schemagenericerror)|

### 返回数据结构

状态码 **400**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||业务错误码|
|» message|string|false|none||用户可读的错误信息|
|» detail|string¦null|false|none||错误详情，可选|
|» data|object¦null|false|none||错误时通常为空|
|» pageInfo|object¦null|false|none||错误时通常为空|

状态码 **401**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||业务错误码|
|» message|string|false|none||用户可读的错误信息|
|» detail|string¦null|false|none||错误详情，可选|
|» data|object¦null|false|none||错误时通常为空|
|» pageInfo|object¦null|false|none||错误时通常为空|

状态码 **403**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||业务错误码|
|» message|string|false|none||用户可读的错误信息|
|» detail|string¦null|false|none||错误详情，可选|
|» data|object¦null|false|none||错误时通常为空|
|» pageInfo|object¦null|false|none||错误时通常为空|

状态码 **404**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||业务错误码|
|» message|string|false|none||用户可读的错误信息|
|» detail|string¦null|false|none||错误详情，可选|
|» data|object¦null|false|none||错误时通常为空|
|» pageInfo|object¦null|false|none||错误时通常为空|

状态码 **409**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|false|none||业务错误码|
|» message|string|false|none||用户可读的错误信息|
|» detail|string¦null|false|none||错误详情，可选|
|» data|object¦null|false|none||错误时通常为空|
|» pageInfo|object¦null|false|none||错误时通常为空|

## POST 答题上传图片异常检测

POST /api/v1/study_session/answer/picture/check

> Body 请求参数

```json
{
  "pictureUrls": [
    "string"
  ]
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|Authorization|header|string| 否 ||none|
|userTypeId|header|string| 否 ||none|
|organizationId|header|string| 否 ||none|
|body|body|object| 否 ||none|
|» pictureUrls|body|[string]| 是 ||none|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

# 数据模型

<h2 id="tocS_Feedback">Feedback</h2>

<a id="schemafeedback"></a>
<a id="schema_Feedback"></a>
<a id="tocSfeedback"></a>
<a id="tocsfeedback"></a>

```json
{
  "ipRole": "role_good",
  "type": "correct",
  "title": "string",
  "content": "string",
  "soundFX": "string",
  "animation": "string"
}

```

反馈数据

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|ipRole|string|true|none||IP角色，学霸、学渣等|
|type|string|false|none||反馈类型|
|title|string|false|none||消息标题|
|content|string|true|none||消息内容|
|soundFX|string|true|none||音效，url 地址，暂时不用|
|animation|string|true|none||动效，url 地址，暂时不用|

#### 枚举值

|属性|值|
|---|---|
|ipRole|role_good|
|ipRole|role_bad|
|type|correct|
|type|incorrect|
|type|continuous_correct|
|type|difficulty_up|
|type|difficulty_down|
|type|answer_carelessly|

<h2 id="tocS_StudyProgressInfo">StudyProgressInfo</h2>

<a id="schemastudyprogressinfo"></a>
<a id="schema_StudyProgressInfo"></a>
<a id="tocSstudyprogressinfo"></a>
<a id="tocsstudyprogressinfo"></a>

```json
{
  "currentProgress": 0,
  "completedQuestions": 0,
  "totalQuestions": 0,
  "questionGroups": [
    {
      "groupIndex": 0,
      "groupId": 0,
      "groupName": "string",
      "groupQuestionCount": 0
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|currentProgress|number|true|none||当前进度百分比|
|completedQuestions|integer|true|none||已完成题目数|
|totalQuestions|integer|true|none||预估题目总数|
|questionGroups|[object]|true|none||题组信息|
|» groupIndex|integer|true|none||题组序号|
|» groupId|integer|true|none||题组 ID|
|» groupName|string|true|none||题组名称|
|» groupQuestionCount|integer|true|none||题组题目数量|

<h2 id="tocS_StudySessionDetail">StudySessionDetail</h2>

<a id="schemastudysessiondetail"></a>
<a id="schema_StudySessionDetail"></a>
<a id="tocSstudysessiondetail"></a>
<a id="tocsstudysessiondetail"></a>

```json
{
  "studySessionId": 0,
  "studyType": 0,
  "questions": [
    {
      "questionId": "string",
      "lastAnswerDuration": 0,
      "questionAnswer": {
        "answerOptionList": [
          {
            "optionVal": null,
            "optionKey": null
          }
        ]
      },
      "questionContent": {
        "questionOrder": 0,
        "questionScore": 0,
        "questionOriginName": "string",
        "questionStem": "string",
        "questionOptionList": [
          {
            "optionKey": null,
            "optionVal": null
          }
        ]
      },
      "questionExplanation": "string",
      "questionAnswerMode": 0,
      "questionType": 1,
      "questionDifficult": 3,
      "questionExtra": "string",
      "questionTopic": "下列哪个选项是正确的？",
      "questionTags": [
        "string"
      ]
    }
  ],
  "studentAnswers": [
    {
      "questionId": "string",
      "answerContents": {
        "index": 0,
        "questionId": "string",
        "type": 1,
        "content": [
          "string"
        ],
        "selfEvaluations": [
          0
        ]
      },
      "answerType": 0,
      "answerDuration": 0,
      "answerResult": 0,
      "evaluationType": 0,
      "answerStats": [
        {
          "questionId": "string",
          "answerStat": [
            {}
          ]
        }
      ]
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|studySessionId|integer|true|none||学习会话 id|
|studyType|integer|true|none||学习类型 (1:AI课, 2:巩固练习, 3:拓展练习, 4:普通作业, 5:测试任务, 6:资源任务, 7:课中练习, 99:其他)|
|questions|[[QuestionInfo](#schemaquestioninfo)]|true|none||题目列表，按出题顺序提供|
|studentAnswers|[[studentAnswersResponse](#schemastudentanswersresponse)]|true|none||学生答题记录|

<h2 id="tocS_studentAnswersResponse">studentAnswersResponse</h2>

<a id="schemastudentanswersresponse"></a>
<a id="schema_studentAnswersResponse"></a>
<a id="tocSstudentanswersresponse"></a>
<a id="tocsstudentanswersresponse"></a>

```json
{
  "questionId": "string",
  "answerContents": {
    "index": 0,
    "questionId": "string",
    "type": 1,
    "content": [
      "string"
    ],
    "selfEvaluations": [
      0
    ]
  },
  "answerType": 0,
  "answerDuration": 0,
  "answerResult": 0,
  "evaluationType": 0,
  "answerStats": [
    {
      "questionId": "string",
      "answerStat": [
        {
          "optionKey": "string",
          "rate": "string"
        }
      ]
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|questionId|string|true|none||题目 id|
|answerContents|[studentAnswerSubmit](#schemastudentanswersubmit)|true|none||答题详情，数组，支持母子题|
|answerType|integer|true|none||回答类型, 1:文本(默认), 2:图片, 3:视频|
|answerDuration|integer|true|none||答题用时，毫秒|
|answerResult|integer|true|none||判题结果 (0:未作答, 1:正确, 2:错误, 3:部分正确)|
|evaluationType|integer|true|none||判题类型，1:系统判题, 2:自评判题|
|answerStats|[[AnswerStats](#schemaanswerstats)]|true|none||答题统计，支持母子题|

<h2 id="tocS_AnswerStats">AnswerStats</h2>

<a id="schemaanswerstats"></a>
<a id="schema_AnswerStats"></a>
<a id="tocSanswerstats"></a>
<a id="tocsanswerstats"></a>

```json
{
  "questionId": "string",
  "answerStat": [
    {
      "optionKey": "string",
      "rate": "string"
    }
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|questionId|string|true|none||题目 ID|
|answerStat|[object]|true|none||答题统计，只客观题有统计|
|» optionKey|string|true|none||选项|
|» rate|string|true|none||选择比例|

<h2 id="tocS_studentAnswerSubmit">studentAnswerSubmit</h2>

<a id="schemastudentanswersubmit"></a>
<a id="schema_studentAnswerSubmit"></a>
<a id="tocSstudentanswersubmit"></a>
<a id="tocsstudentanswersubmit"></a>

```json
{
  "index": 0,
  "questionId": "string",
  "type": 1,
  "content": [
    "string"
  ],
  "selfEvaluations": [
    0
  ]
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|index|integer|true|none||答题序号，从0开始|
|questionId|string|true|none||题目 ID，支持母子题|
|type|integer|true|none||回答类型, 1:文本(默认), 2:图片, 3:视频|
|content|[string]|true|none||作答内容，数组格式，支持多选题，多填空题等类型题目，支持多图片地址|
|selfEvaluations|[integer]|true|none||作答内容自评，和作答内容按次序对应|

#### 枚举值

|属性|值|
|---|---|
|type|1|
|type|2|
|type|3|
|type|4|

<h2 id="tocS_QuestionInfo">QuestionInfo</h2>

<a id="schemaquestioninfo"></a>
<a id="schema_QuestionInfo"></a>
<a id="tocSquestioninfo"></a>
<a id="tocsquestioninfo"></a>

```json
{
  "questionId": "string",
  "lastAnswerDuration": 0,
  "questionAnswer": {
    "answerOptionList": [
      {
        "optionVal": "string",
        "optionKey": "string"
      }
    ]
  },
  "questionContent": {
    "questionOrder": 0,
    "questionScore": 0,
    "questionOriginName": "string",
    "questionStem": "string",
    "questionOptionList": [
      {
        "optionKey": "string",
        "optionVal": "string"
      }
    ]
  },
  "questionExplanation": "string",
  "questionAnswerMode": 0,
  "questionType": 1,
  "questionDifficult": 3,
  "questionExtra": "string",
  "questionTopic": "下列哪个选项是正确的？",
  "questionTags": [
    "string"
  ]
}

```

题目信息

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|questionId|string|false|none||题目唯一ID|
|lastAnswerDuration|integer|true|none||上次作答时间，毫秒|
|questionAnswer|object¦null|false|none||标准答案|
|» answerOptionList|[object]|true|none||none|
|»» optionVal|string|true|none||选项值|
|»» optionKey|string|true|none||选项key|
|questionContent|object|true|none||none|
|» questionOrder|integer|true|none||题目对应试卷的题号|
|» questionScore|number|true|none||题目对应试卷的分数|
|» questionOriginName|string|true|none||题目来源名称 如 2022秋•房山区期末|
|» questionStem|string|true|none||题干部分，选择题不包含选项, 填空题需要使用<blank/>区分空|
|» questionOptionList|[object]|true|none||none|
|»» optionKey|string|true|none||选项key|
|»» optionVal|string|true|none||选项值|
|questionExplanation|string|true|none||题目解析|
|questionAnswerMode|integer|true|none||none|
|questionType|integer|false|none||题型类型 (1:单选题, 2:多选题, 3:填空题, 4:解答题)|
|questionDifficult|integer|false|none||难度等级|
|questionExtra|string|false|none||学科ID|
|questionTopic|string|false|none||题目内容|
|questionTags|[string]|true|none||题目标签|

<h2 id="tocS_answerResult">answerResult</h2>

<a id="schemaanswerresult"></a>
<a id="schema_answerResult"></a>
<a id="tocSanswerresult"></a>
<a id="tocsanswerresult"></a>

```json
{
  "index": 0,
  "questionId": "string",
  "answerVerify": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|index|integer|true|none||序号|
|questionId|string|true|none||题目 id，支持母子题|
|answerVerify|integer|true|none||自动判题结果 (0:未作答, 1:正确, 2:错误, 3:部分正确)|

<h2 id="tocS_GenericResponse">GenericResponse</h2>

<a id="schemagenericresponse"></a>
<a id="schema_GenericResponse"></a>
<a id="tocSgenericresponse"></a>
<a id="tocsgenericresponse"></a>

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {}
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||业务错误码，0表示成功，非0表示错误|
|message|string|false|none||用户可读的响应信息|
|data|object¦null|true|none||响应数据，具体结构根据接口而定|

<h2 id="tocS_GenericError">GenericError</h2>

<a id="schemagenericerror"></a>
<a id="schema_GenericError"></a>
<a id="tocSgenericerror"></a>
<a id="tocsgenericerror"></a>

```json
{
  "code": 1001,
  "message": "参数错误",
  "detail": "字段 [userId] 不能为空",
  "data": {},
  "pageInfo": {}
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||业务错误码|
|message|string|false|none||用户可读的错误信息|
|detail|string¦null|false|none||错误详情，可选|
|data|object¦null|false|none||错误时通常为空|
|pageInfo|object¦null|false|none||错误时通常为空|

<h2 id="tocS_SubmitAnswerRequest">SubmitAnswerRequest</h2>

<a id="schemasubmitanswerrequest"></a>
<a id="schema_SubmitAnswerRequest"></a>
<a id="tocSsubmitanswerrequest"></a>
<a id="tocssubmitanswerrequest"></a>

```json
{
  "studySessionId": 1001,
  "questionId": "string",
  "studentAnswers": [
    {
      "index": 0,
      "questionId": "string",
      "type": 1,
      "content": [
        "string"
      ],
      "selfEvaluations": [
        0
      ]
    }
  ],
  "answerDuration": 30000,
  "isGiveup": true
}

```

提交答案请求体

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|studySessionId|integer(int64)|true|none||练习会话ID|
|questionId|string|true|none||题目ID，如果是母子题，则为母题 ID|
|studentAnswers|[[studentAnswerSubmit](#schemastudentanswersubmit)]|true|none||学生作答，包括母子题|
|answerDuration|integer|true|none||作答时长（毫秒）|
|isGiveup|boolean|false|none||是否放弃作答|

<h2 id="tocS_SubmitAnswerResponse">SubmitAnswerResponse</h2>

<a id="schemasubmitanswerresponse"></a>
<a id="schema_SubmitAnswerResponse"></a>
<a id="tocSsubmitanswerresponse"></a>
<a id="tocssubmitanswerresponse"></a>

```json
{
  "answerResult": [
    {
      "index": 0,
      "questionId": "string",
      "answerVerify": 0
    }
  ],
  "answerCount": 0,
  "correctComboCount": 3,
  "answerStats": [
    {
      "questionId": "string",
      "answerStat": [
        {
          "optionKey": "string",
          "rate": "string"
        }
      ]
    }
  ],
  "feedback": {
    "ipRole": "role_good",
    "type": "correct",
    "title": "string",
    "content": "string",
    "soundFX": "string",
    "animation": "string"
  },
  "specialFeedbacks": [
    {
      "ipRole": "role_good",
      "type": "correct",
      "title": "string",
      "content": "string",
      "soundFX": "string",
      "animation": "string"
    }
  ],
  "nextQuestionInfo": {
    "questionId": "string",
    "lastAnswerDuration": 0,
    "questionAnswer": {
      "answerOptionList": [
        {
          "optionVal": "string",
          "optionKey": "string"
        }
      ]
    },
    "questionContent": {
      "questionOrder": 0,
      "questionScore": 0,
      "questionOriginName": "string",
      "questionStem": "string",
      "questionOptionList": [
        {
          "optionKey": "string",
          "optionVal": "string"
        }
      ]
    },
    "questionExplanation": "string",
    "questionAnswerMode": 0,
    "questionType": 1,
    "questionDifficult": 3,
    "questionExtra": "string",
    "questionTopic": "下列哪个选项是正确的？",
    "questionTags": [
      "string"
    ]
  },
  "exerciseCompleted": false,
  "progressInfo": {
    "currentProgress": 0,
    "completedQuestions": 0,
    "totalQuestions": 0,
    "questionGroups": [
      {
        "groupIndex": 0,
        "groupId": 0,
        "groupName": "string",
        "groupQuestionCount": 0
      }
    ]
  },
  "hasNextQuestion": true,
  "nextQuestionGroupInfo": {
    "groupCount": 0,
    "groupIndex": 0,
    "groupName": "string",
    "groupQuestionCount": 0
  }
}

```

提交答案响应数据

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|answerResult|[[answerResult](#schemaanswerresult)]|false|none||判题结果 (0:未作答, 1:正确, 2:错误, 3:部分正确)|
|answerCount|integer|true|none||本次会话答题总数|
|correctComboCount|integer¦null|false|none||连续正确次数|
|answerStats|[[AnswerStats](#schemaanswerstats)]|true|none||答案选择分布统计|
|feedback|[Feedback](#schemafeedback)|true|none||反馈数据，主观题第一次提交不返回（除放弃作答）|
|specialFeedbacks|[[Feedback](#schemafeedback)]¦null|false|none||特殊反馈数据，主观题第一次提交不返回（除放弃作答）|
|nextQuestionInfo|[QuestionInfo](#schemaquestioninfo)|false|none||下一题数据（只有答对、放弃，或者 2 次作答之后才有）|
|exerciseCompleted|boolean|false|none||练习是否完成|
|progressInfo|[StudyProgressInfo](#schemastudyprogressinfo)|true|none||进度信息|
|hasNextQuestion|boolean|true|none||是否有下一题|
|nextQuestionGroupInfo|[QuestionGroupInfo](#schemaquestiongroupinfo)|true|none||下一题题组信息|

<h2 id="tocS_QuestionGroupInfo">QuestionGroupInfo</h2>

<a id="schemaquestiongroupinfo"></a>
<a id="schema_QuestionGroupInfo"></a>
<a id="tocSquestiongroupinfo"></a>
<a id="tocsquestiongroupinfo"></a>

```json
{
  "groupCount": 0,
  "groupIndex": 0,
  "groupName": "string",
  "groupQuestionCount": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|groupCount|integer|true|none||题组数量|
|groupIndex|integer|true|none||题组序号|
|groupName|string|true|none||题组名称|
|groupQuestionCount|integer|true|none||题组题目数量|

<h2 id="tocS_NextQuestionResponse">NextQuestionResponse</h2>

<a id="schemanextquestionresponse"></a>
<a id="schema_NextQuestionResponse"></a>
<a id="tocSnextquestionresponse"></a>
<a id="tocsnextquestionresponse"></a>

```json
{
  "studySessionId": 0,
  "questionInfo": {
    "questionId": "string",
    "lastAnswerDuration": 0,
    "questionAnswer": {
      "answerOptionList": [
        {
          "optionVal": "string",
          "optionKey": "string"
        }
      ]
    },
    "questionContent": {
      "questionOrder": 0,
      "questionScore": 0,
      "questionOriginName": "string",
      "questionStem": "string",
      "questionOptionList": [
        {
          "optionKey": "string",
          "optionVal": "string"
        }
      ]
    },
    "questionExplanation": "string",
    "questionAnswerMode": 0,
    "questionType": 1,
    "questionDifficult": 3,
    "questionExtra": "string",
    "questionTopic": "下列哪个选项是正确的？",
    "questionTags": [
      "string"
    ]
  },
  "progressInfo": {
    "currentProgress": 0,
    "completedQuestions": 0,
    "totalQuestions": 0,
    "questionGroups": [
      {
        "groupIndex": 0,
        "groupId": 0,
        "groupName": "string",
        "groupQuestionCount": 0
      }
    ]
  },
  "isResume": true,
  "hasNextQuestion": true,
  "hasAgain": "string",
  "studentAnswer": {
    "questionId": "string",
    "answerContents": {
      "index": 0,
      "questionId": "string",
      "type": 1,
      "content": [
        "string"
      ],
      "selfEvaluations": [
        0
      ]
    },
    "answerType": 0,
    "answerDuration": 0,
    "answerResult": 0,
    "evaluationType": 0,
    "answerStats": [
      {
        "questionId": "string",
        "answerStat": [
          {
            "optionKey": null,
            "rate": null
          }
        ]
      }
    ]
  }
}

```

获取下一题响应数据

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|studySessionId|integer|true|none||学习会话 id，AI课通过这个接口获取，巩固练习需要从欢迎页获取|
|questionInfo|[QuestionInfo](#schemaquestioninfo)|false|none||题目ID|
|progressInfo|[StudyProgressInfo](#schemastudyprogressinfo)|false|none||进度信息|
|isResume|boolean|true|none||是否为继续上次的答题|
|hasNextQuestion|boolean|false|none||是否有下一题，true 表述本次练习结束，但可能还有下次练习|
|hasAgain|string|true|none||是否可以再练一次，巩固练习有效|
|studentAnswer|[studentAnswersResponse](#schemastudentanswersresponse)|false|none||最后一题的答题信息，只在 hasNextQuestion=false 时有数据|

