# 前端产品需求文档 - PRD - 学生端 - 题型组件 - 一期
文档说明：
  - 标注⚠️的是 AI 基于用户角度补充的点，可能并未在原始 PRD 中体现

## 1. 需求背景与目标

### 1.1 需求目标
通过该需求的实现，可以：
- 覆盖校内教学环节的核心功能，满足用户在日常学习过程中，对于练习、测验等场景下所需高频题型（单选题、多选题、填空题、解答题）的要求。
- 提供精准的题目匹配、清晰的作答流程（初始状态、作答状态、解析状态）以及适配各题型的作答方式（键盘输入、拍照上传），以提升用户体验。

### 1.2 用户价值
- 对于 **新初一、初二及新高一、高二用户**，解决了在校内学习场景下，缺乏统一、易用的多题型练习与测验工具的痛点，带来了便捷高效的在线做题与即时反馈体验，有助于巩固知识、提升学习效果。

## 2. 功能范围

### 2.1 核心功能
#### **[核心功能点1]: 单选题**

##### **[核心功能点1.1]: 单选题 - 初始状态**
###### 界面布局与核心UI元素
- **布局参考：**
    - `[单选题初始状态-整体布局](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_ZMHLbhLb3oLnPgx1baccZegqnRH.png) - 仅关注布局`
    - `[单选题初始状态-含图题干](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_XyfubkidNoqIObxbiiOcnuDdn3o.png) - 仅关注布局`
    - `[题干图片排版参考](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_OQ9NbPQ5foqF1Gxc3Trc1FXMnfc.png) - 仅关注布局`
    - `[选项文字超长折行参考](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_LHNtbkAjxoyPGLxdKnWcqouWnRc.png) - 仅关注布局`
    - `[选项含图参考](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_GOEWbMoOBo6fLExCJeScQRctnJf.png) - 仅关注布局`
    - `[勾画组件入口参考](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_XKUNb9JLhoHRiRxX6OIc4vJDnVg.png) - 仅关注布局`
- **布局原则：**
    - **整体界面布局 ([`单选题初始状态-整体布局`](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_ZMHLbhLb3oLnPgx1baccZegqnRH.png) 解析, [`单选题初始状态-含图题干`](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_XyfubkidNoqIObxbiiOcnuDdn3o.png) 解析):** 页面采用全屏展示。顶部栏包含返回按钮、计时器和答题进度条。内容区域分为左侧题干区和右侧选项区。底部操作栏包含勾画按钮、不确定按钮和提交按钮。
    - **题干区 ([`单选题初始状态-整体布局`](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_ZMHLbhLb3oLnPgx1baccZegqnRH.png) 解析):** 左侧展示题型标签、题目来源及题干内容。题干支持文字和图片，图片右下角有放大镜图标。
    - **选项区域 ([`单选题初始状态-整体布局`](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_ZMHLbhLb3oLnPgx1baccZegqnRH.png) 解析):** 右侧展示各选项。选项内容超长时折行显示 ([`选项文字超长折行参考`](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_LHNtbkAjxoyPGLxdKnWcqouWnRc.png) 解析)。选项中可包含图片，图片右下角有放大镜图标 ([`选项含图参考`](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_GOEWbMoOBo6fLExCJeScQRctnJf.png) 解析)。
    - **底部操作栏 ([`单选题初始状态-整体布局`](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_ZMHLbhLb3oLnPgx1baccZegqnRH.png) 解析):** 左下角为勾画按钮，中间为不确定按钮，右下角为提交按钮。
    - **勾画组件 ([`勾画组件入口参考`](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_XKUNb9JLhoHRiRxX6OIc4vJDnVg.png) 解析):** 勾画组件悬浮显示，包含颜色调整、粗细调整、橡皮擦、撤回、恢复、一键清空和完成按钮。
- **核心UI元素清单：**
    - 退出学习按钮
    - 作答计时器 (格式 00:00)
    - 答题进度条
    - 题型标签 (文字：单选题)
    - 题目来源标签
    - 题干内容区 (支持文字、图片)
    - 图片放大查看按钮
    - 选项 (A, B, C, D等，支持文字、图片)
    - “不确定”按钮
    - “提交”按钮 (初始状态置灰)
    - “勾画”悬浮按钮
    - 勾画工具栏 (画笔颜色选择、粗细选择、橡皮擦、撤回、恢复、清空、完成按钮)

###### 功能需求与交互原则
- **功能描述：**
    - **状态判断：** 当用户进入题目且未选择任何选项时，为初始状态。
    - **题型校验：** 仅适用于语文、数学、英语、物理、化学、生物学科的非母子单选题。
    - **页面框架：** 仅支持全屏展示。
    - **退出学习：** 点击“退出学习”按钮，退出至指定页面。
    - **作答计时：**
        - 每题独立正计时，格式为 `00:00`，上限 `59:59`。
        - 用户进入题目时开始计时，提交后停止计时。
        - 若学生未作答退出，计时暂停；下次进入该题时，在上次计时基础上继续计时。
    - **题板结构：** 界面固定为左侧题干区、右侧作答区。
    - **题干区展示：**
        - 显示“单选题”题型标签。
        - 显示题目来源 (数据源自题库)。
        - 显示题干内容 (数据源自题库)，支持文字和图片。图片支持点击放大查看。
        - 题干图片排版需遵循 [`题干图片排版参考`](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_OQ9NbPQ5foqF1Gxc3Trc1FXMnfc.png) 中的布局。
    - **作答区展示：**
        - 显示各选项内容 (数据源自题库)。
        - 选项内容超出一行时，折行展示，需遵循 [`选项文字超长折行参考`](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_LHNtbkAjxoyPGLxdKnWcqouWnRc.png) 中的布局。
        - 选项中可包含图片，需遵循 [`选项含图参考`](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_GOEWbMoOBo6fLExCJeScQRctnJf.png) 中的布局。
    - **勾画组件：**
        - 作答界面常驻悬浮“勾画”按钮。
        - 点击“勾画”按钮可唤起勾画组件，支持用户在题目上进行勾画和标记。
        - 勾画工具提供：笔迹颜色调整（蓝、红、黑）、笔迹粗细调整（细、中、粗）、橡皮擦（细、中、粗）、撤回、恢复、一键清空。
        - 点击勾画组件的“完成”按钮，收起组件，并将勾画内容自动保存并展示在题目上。
- **交互模式：**
    - **“不确定”按钮：** 初始状态可点击。点击后，按钮文字变为“放弃作答”。
    - **“提交”按钮：** 初始状态不可点击，按钮样式置灰。
    - **选项选择：** 用户点击选项进行选择，此时为进入作答状态的前置操作。
- **反馈原则：**
    - 初始状态下，主要为静态内容展示，无特殊即时反馈，等待用户操作。

**优先级：** P0
**依赖关系：** 无

##### **[核心功能点1.2]: 单选题 - 作答状态**
###### 界面布局与核心UI元素
- **布局参考：** `[单选题作答状态-选中B选项](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_AFLQbFyjFocz6PxFnrkcleXKnhd.png) - 仅关注布局`
- **布局原则：**
    - **整体布局 ([`单选题作答状态-选中B选项`](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_AFLQbFyjFocz6PxFnrkcleXKnhd.png) 解析):** 整体布局与初始状态一致。主要区别在于选项区有选项被选中高亮，底部“提交”按钮变为可点击状态。
    - **选项区域 ([`单选题作答状态-选中B选项`](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_AFLQbFyjFocz6PxFnrkcleXKnhd.png) 解析):** 用户选中的选项有明确的视觉高亮（如背景色变化）。
    - **底部操作栏 ([`单选题作答状态-选中B选项`](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_AFLQbFyjFocz6PxFnrkcleXKnhd.png) 解析):** “提交”按钮变为可操作状态（如颜色变化，不再置灰）。
- **核心UI元素清单：**
    - (同初始状态的核心UI元素)
    - 选中状态的选项 (有视觉高亮)
    - “提交”按钮 (可点击状态)
    - “不确定”/“放弃作答”按钮

###### 功能需求与交互原则
- **功能描述：**
    - **状态判断：** 当用户进入题目并选中了唯一一个选项时，为作答状态。
    - **批改方式：** 单选题采用自动批改。
    - **作答方式：**
        - 用户通过点击选择唯一一个选项，被选中的选项应有明确的选中状态视觉反馈。
        - 如果用户已选中一个选项，再点击其他选项，则取消原选项的选中状态，并选中新点击的选项。
- **交互模式：**
    - **“不确定”按钮：**
        - 按钮状态：可点击。
        - 交互：点击后按钮文字变为“放弃作答”。
            - 若此时用户点击“放弃作答”，题目将进入解析状态，并展示该状态下的内容。
            - 若用户在“放弃作答”状态下点击了任一选项，则“放弃作答”按钮回退至“不确定”状态，同时“提交”按钮变为可点击。
    - **“提交”按钮：**
        - 按钮状态：当有选项被选中时，此按钮变为可点击状态。
        - **第一次点击提交：**
            - 系统提交作答数据，并校验作答结果是否正确。
            - **若回答正确：** 显示反馈弹窗（具体样式参考“巩固练习”PRD），随后题目进入解析状态，展示该状态下的内容。
            - **若回答错误：** 显示反馈弹窗（具体样式参考“巩固练习”PRD），题目停留在作答状态。此时仅展示“猜你想问”入口（用户可自主点击），不展示答案、解析等。用户的作答选项被重置（取消选中状态），允许用户重新作答并进行第二次提交。
        - **第二次点击提交：**
            - 系统再次提交作答数据，并校验作答结果是否正确。
            - **若回答正确：** 显示反馈弹窗，随后题目进入解析状态。
            - **若回答错误：** 显示反馈弹窗，随后题目进入解析状态。
- **反馈原则：**
    - 选中选项时，选项有即时的视觉状态变化。
    - 点击“提交”后，根据作答结果（正确/错误）显示相应的反馈弹窗。

**优先级：** P0
**依赖关系：** [核心功能点1.1]: 单选题 - 初始状态

##### **[核心功能点1.3]: 单选题 - 解析状态**
###### 界面布局与核心UI元素
- **布局参考：**
    - `[单选题解析状态-答案正确](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_VEZlblon3oPiDKxrcv1cUPwcnTe.png) - 仅关注布局`
    - `[单选题解析状态-答案错误](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_R8HwbvPb9o4741xn2gfcxSe6nO1.png) - 仅关注布局`
    - `[单选题解析状态-问一问弹窗](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_C1S1brNyUoAK20xiVlmcgg3InAf.png) - 仅关注布局`
- **布局原则：**
    - **整体布局 ([`单选题解析状态-答案正确`](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_VEZlblon3oPiDKxrcv1cUPwcnTe.png) 解析, [`单选题解析状态-答案错误`](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_R8HwbvPb9o4741xn2gfcxSe6nO1.png) 解析):** 顶部栏计时器停止。题干区保持不变。作答区（原选项区）增加选项占比、答案展示（正确选项标记绿色，用户错误选项标记红色）、题目解析内容。底部操作栏变为“继续”按钮，并可能出现“加入错题本”按钮。右上角出现“问一问”图标入口。
    - **选项占比与答案展示 ([`单选题解析状态-答案正确`](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_VEZlblon3oPiDKxrcv1cUPwcnTe.png) 解析, [`单选题解析状态-答案错误`](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_R8HwbvPb9o4741xn2gfcxSe6nO1.png) 解析):** 每个选项下方显示选择该选项的用户占比。正确答案选项以绿色高亮或标记。若用户选择错误，其选择的错误选项以红色高亮或标记。
    - **题目解析区 ([`单选题解析状态-答案正确`](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_VEZlblon3oPiDKxrcv1cUPwcnTe.png) 解析):** 在答案下方展示详细的题目解析文本。
    - **问一问组件 ([`单选题解析状态-问一问弹窗`](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_C1S1brNyUoAK20xiVlmcgg3InAf.png) 解析):** 点击“问一问”图标后，弹出居中模态对话框，包含对话区域（用户提问气泡、AI回复气泡）、输入区域（文本输入框、发送按钮）。
- **核心UI元素清单：**
    - (题干区元素同初始状态)
    - 停止的作答计时器
    - 选项占比标签 (格式 xx.x %)
    - 正确答案标识 (如绿色标记)
    - 错误答案标识 (如红色标记)
    - 题目解析文本区
    - “问一问”入口图标
    - 答疑组件弹窗 (包含对话记录、输入框、发送按钮、关闭按钮)
    - “继续”按钮
    - (可选) “加入错题本”按钮

###### 功能需求与交互原则
- **功能描述：**
    - **状态判断：**
        - 当用户在作答状态下提交答案后，若回答正确，则进入解析状态。
        - 当用户在作答状态下第二次提交答案后（无论对错），则进入解析状态。
        - 当用户在初始或作答状态下点击“不确定”后，再点击“放弃作答”，则进入解析状态。
    - **作答计时：** 计时器停止，时间定格在用户提交答案或放弃作答的时刻。
    - **反馈弹窗：** 进入解析状态前，可能会显示作答结果的反馈弹窗（具体样式参考“巩固练习”PRD）。
    - **选项占比显示：**
        - 在每个选项下方展示已作答用户的选择占比，格式为 `xx.x %`。
        - 数据取自该题目历史回答数据。
        - **冷启动策略：** 当题目累计作答次数大于20次后，开始向用户展示占比数字。
        - **更新策略：** 每日更新累计作答次数大于20次的题目的作答占比数据。
    - **答案展示：**
        - 清晰展示正确的选项文本。
        - 在选项上进行标记：正确选项标记为绿色；用户选择的错误选项标记为红色。
    - **题目解析展示：** 显示该题目的详细文字解析内容（数据源自题库中对应字段“题目解析”）。
    - **问一问功能：**
        - **入口：** 界面右上角显示“问一问”图标，仅当题目进入解析状态后展示此入口。
        - **交互：** 点击“问一问”图标，弹出答疑组件。
        - **展示：** 答疑组件支持用户自由提问，并可展示由模型生成的“猜你想问”问题（最多3个，可以为0个）。
        - **对话发起：** 由用户发起首轮对话。
- **交互模式：**
    - **“继续”按钮：**
        - **出现时机：** 题目进入解析状态后显示。
        - **按钮状态：** 可点击。
        - **交互：** 点击后进入下一题或下一节学习内容。
- **反馈原则：**
    - 正确和错误选项有明确的视觉区分。
    - “问一问”提供即时交互的答疑体验。

**优先级：** P0
**依赖关系：** [核心功能点1.2]: 单选题 - 作答状态

##### 用户场景与故事 (针对此核心功能点: 单选题)

###### [场景1: 学生首次作答单选题，回答正确并查看解析]
作为**学生**，我需要**在单选题中选择我认为正确的答案并提交**以解决**检验我对知识点的掌握程度**，从而**获得即时反馈，如果正确则巩固认知，并可查看详细解析加深理解**。

**前端界面与交互关键步骤：**
1.  **[用户操作1: 查看题目]** 学生进入单选题界面，查看题干和选项（初始状态）。
    *   **界面变化:** 题干、选项正常显示，“提交”按钮置灰，“不确定”按钮可点击。计时器开始计时。
    *   **即时反馈:** 无特殊反馈。
2.  **[用户操作2: 选择选项A]** 学生点击选项A。
    *   **界面变化:** 选项A高亮显示为选中状态，“提交”按钮变为可点击。
    *   **即时反馈:** 选项A视觉上表示已选中。
3.  **[用户操作3: 点击提交按钮]** 学生确认选择，点击“提交”按钮。
    *   **界面变化:** 系统处理提交，显示“回答正确”反馈弹窗。计时器停止。弹窗关闭后，界面更新至解析状态：显示选项占比，选项A标记为绿色（正确），显示题目解析内容，“继续”按钮出现，“问一问”入口出现。
    *   **即时反馈:** “回答正确”弹窗提示。
4.  **[用户操作4: 查看解析与答案]** 学生查看各选项占比、正确答案标识和题目解析文本。
    *   **界面变化:** 无主动变化，内容供用户阅读。
    *   **即时反馈:** 无。
5.  **[用户操作5: 点击继续按钮]** 学生完成查看，点击“继续”按钮。
    *   **界面变化:** 跳转到下一题或下一学习内容。
    *   **即时反馈:** 界面跳转。

**场景细节补充：**
*   **前置条件:** 用户已登录并进入包含单选题的练习或测验。
*   **期望结果:** 用户成功提交答案，获得正确反馈，并能查看题目解析。
*   **异常与边界情况:**
    *   网络请求失败：提交时若网络异常，应有友好提示，并允许用户重试。

**优先级：** 高
**依赖关系：** 无

```mermaid
flowchart TD
    A[用户进入单选题界面（初始状态）] --> B{选择答案};
    B -- 选择选项A --> C[选项A高亮，提交按钮激活];
    C --> D{点击提交};
    D -- 校验答案 --> E{答案正确?};
    E -- 是 --> F[显示'回答正确'弹窗];
    F --> G[进入解析状态：显示答案（A绿色）、解析、选项占比、'问一问'、'继续'按钮];
    G --> H[用户查看解析];
    H --> I{点击'继续'};
    I --> J[进入下一题/内容];
```

###### [场景2: 学生首次作答单选题错误，二次作答正确]
作为**学生**，我需要**在单选题首次回答错误后能重新选择答案并提交**以解决**修正我的错误理解**，从而**通过再次尝试来学习并最终掌握该知识点**。

**前端界面与交互关键步骤：**
1.  **[用户操作1: 查看题目并选择错误选项B]** 学生进入单选题，选择选项B。
    *   **界面变化:** 选项B高亮，“提交”按钮激活。
    *   **即时反馈:** 选项B视觉选中。
2.  **[用户操作2: 点击提交按钮（首次）]** 学生提交选项B。
    *   **界面变化:** 显示“回答错误”反馈弹窗。弹窗关闭后，题目停留在作答状态，选项B的选中状态被取消（或所有选项重置），仅显示“猜你想问”入口。
    *   **即时反馈:** “回答错误”弹窗提示。
3.  **[用户操作3: 重新选择正确选项A]** 学生思考后，重新选择选项A。
    *   **界面变化:** 选项A高亮，“提交”按钮激活。
    *   **即时反馈:** 选项A视觉选中。
4.  **[用户操作4: 点击提交按钮（第二次）]** 学生再次提交。
    *   **界面变化:** 显示“回答正确”反馈弹窗。计时器停止。弹窗关闭后，界面更新至解析状态：显示选项占比，选项A标记为绿色，显示题目解析，“继续”按钮和“问一问”入口出现。
    *   **即时反馈:** “回答正确”弹窗提示。

**场景细节补充：**
*   **前置条件:** 用户在单选题作答状态。
*   **期望结果:** 用户在首次错误后，能成功进行第二次作答并获得正确反馈和解析。
*   **异常与边界情况:**
    *   第二次提交仍错误：按场景3处理。

**优先级：** 高
**依赖关系：** 无

```mermaid
flowchart TD
    A[用户进入单选题界面] --> B{选择错误选项B};
    B --> C[选项B高亮，提交按钮激活];
    C --> D{点击提交（首次）};
    D -- 校验答案 --> E{答案错误?};
    E -- 是 --> F[显示'回答错误'弹窗，停留在作答状态，重置选项];
    F --> G{重新选择正确选项A};
    G --> H[选项A高亮，提交按钮激活];
    H --> I{点击提交（第二次）};
    I -- 校验答案 --> J{答案正确?};
    J -- 是 --> K[显示'回答正确'弹窗];
    K --> L[进入解析状态：显示答案（A绿色）、解析等];
```

###### [场景3: 学生两次作答单选题均错误]
作为**学生**，我需要**在单选题两次回答均错误后能看到正确答案和解析**以解决**了解我的知识盲区**，从而**学习正确解法，避免未来再犯同类错误**。

**前端界面与交互关键步骤：**
1.  **[用户操作1: 查看题目并选择错误选项B]** 学生进入单选题，选择选项B。
    *   **界面变化:** 选项B高亮，“提交”按钮激活。
    *   **即时反馈:** 选项B视觉选中。
2.  **[用户操作2: 点击提交按钮（首次）]** 学生提交选项B。
    *   **界面变化:** 显示“回答错误”反馈弹窗。题目停留在作答状态，重置选项。
    *   **即时反馈:** “回答错误”弹窗提示。
3.  **[用户操作3: 重新选择错误选项C]** 学生思考后，重新选择另一错误选项C。
    *   **界面变化:** 选项C高亮，“提交”按钮激活。
    *   **即时反馈:** 选项C视觉选中。
4.  **[用户操作4: 点击提交按钮（第二次）]** 学生再次提交。
    *   **界面变化:** 显示“回答错误”反馈弹窗。计时器停止。弹窗关闭后，界面更新至解析状态：显示选项占比，正确答案（如A）标记为绿色，用户选择的C标记为红色，显示题目解析，“继续”按钮和“问一问”入口出现。
    *   **即时反馈:** “回答错误”弹窗提示。

**场景细节补充：**
*   **前置条件:** 用户在单选题作答状态，且首次作答错误。
*   **期望结果:** 用户两次作答错误后，系统能展示正确答案和解析。
*   **异常与边界情况:** 无。

**优先级：** 高
**依赖关系：** 无

```mermaid
flowchart TD
    A[用户首次作答错误后，停留在作答状态] --> B{选择另一错误选项C};
    B --> C1[选项C高亮，提交按钮激活];
    C1 --> D{点击提交（第二次）};
    D -- 校验答案 --> E{答案错误?};
    E -- 是 --> F[显示'回答错误'弹窗];
    F --> G[进入解析状态：显示正确答案（如A绿色）、用户答案（C红色）、解析等];
```

###### [场景4: 学生对单选题不确定，选择放弃作答]
作为**学生**，我需要**在对单选题答案不确定时可以选择放弃作答并直接查看解析**以解决**避免浪费时间或盲目猜测**，从而**快速获取正确答案和解题思路进行学习**。

**前端界面与交互关键步骤：**
1.  **[用户操作1: 查看题目]** 学生进入单选题界面。
    *   **界面变化:** 题干、选项正常显示，“提交”按钮置灰。
    *   **即时反馈:** 无。
2.  **[用户操作2: 点击“不确定”按钮]** 学生对答案不确定，点击“不确定”按钮。
    *   **界面变化:** “不确定”按钮文字变为“放弃作答”。
    *   **即时反馈:** 按钮文字变化。
3.  **[用户操作3: 点击“放弃作答”按钮]** 学生决定放弃作答。
    *   **界面变化:** 计时器停止。界面直接进入解析状态：显示选项占比（若有数据），标记正确答案，显示题目解析，“继续”按钮和“问一问”入口出现。用户答案标记为“未作答”。
    *   **即时反馈:** 界面跳转至解析状态。

**场景细节补充：**
*   **前置条件:** 用户在单选题初始状态或已选择选项但未提交的状态。
*   **期望结果:** 用户成功放弃作答并能查看题目解析。
*   **异常与边界情况:**
    *   若在“放弃作答”状态下，用户又点击了某个选项，则按钮应恢复为“不确定”，提交按钮根据选项是否选中而激活/置灰。

**优先级：** 中
**依赖关系：** 无

```mermaid
flowchart TD
    A[用户进入单选题界面] --> B{点击'不确定'按钮};
    B --> C['不确定'按钮变为'放弃作答'];
    C --> D{点击'放弃作答'按钮};
    D --> E[进入解析状态：标记正确答案、显示解析、用户答案为'未作答'];
```

###### [场景5: 学生使用勾画功能]
作为**学生**，我需要**在做单选题时使用勾画工具在题干上进行标记和辅助思考**以解决**复杂题干信息梳理不清的问题**，从而**更有效地分析题目，提高解题准确率**。

**前端界面与交互关键步骤：**
1.  **[用户操作1: 查看题目]** 学生进入单选题界面。
    *   **界面变化:** 界面显示悬浮的“勾画”按钮。
    *   **即时反馈:** 无。
2.  **[用户操作2: 点击“勾画”按钮]** 学生需要辅助思考，点击“勾画”按钮。
    *   **界面变化:** 勾画工具栏展开，显示颜色、粗细、橡皮擦等工具。
    *   **即时反馈:** 工具栏出现。
3.  **[用户操作3: 使用勾画工具]** 学生选择颜色、粗细，在题干区域进行勾画。
    *   **界面变化:** 题干上实时显示用户的勾画笔迹。
    *   **即时反馈:** 笔迹实时呈现。
4.  **[用户操作4: 点击“完成”勾画]** 学生完成勾画，点击勾画工具栏的“完成”按钮。
    *   **界面变化:** 勾画工具栏收起，勾画内容保留在题干上。
    *   **即时反馈:** 工具栏消失。
5.  **[用户操作5: 继续答题]** 学生基于勾画辅助，选择答案并提交。
    *   **界面变化:** (同正常答题流程)
    *   **即时反馈:** (同正常答题流程)

**场景细节补充：**
*   **前置条件:** 用户在单选题的初始状态或作答状态。
*   **期望结果:** 用户能顺利使用勾画功能，并在完成勾画后，勾画内容能保留显示。
*   **异常与边界情况:**
    *   勾画内容过多：应能正常显示，不影响题目阅读。

**优先级：** 中
**依赖关系：** 无

```mermaid
flowchart TD
    A[用户进入单选题界面] --> B{点击'勾画'按钮};
    B --> C[勾画工具栏展开];
    C --> D[用户选择工具并在题干上勾画];
    D --> E[题干实时显示笔迹];
    E --> F{点击勾画'完成'按钮};
    F --> G[勾画工具栏收起，笔迹保留];
    G --> H[用户继续答题流程];
```
**Mermaid图示 (单选题整体主要流程):**
```mermaid
flowchart LR
    subgraph 初始状态
        direction LR
        A["查看题目<br/>（题干+作答区）"] --> B{"选择选项<br/>（仅可选择1个）"}
        A --> C["不确定"]
        C -.-> B
    end

    subgraph 作答状态
        direction LR
        B --> D["提交"]
        C --> E["放弃作答<br/>（二次确认）"]
        
        subgraph 作答反馈
            direction TB
            F["回答正确"]
            G["回答错误"]
            H["未作答"]
        end
        
        D -- 第一次提交 --> F
        D -- 第一次提交 --> G_first_error["回答错误 (首次)"]
        G_first_error --> B_reanswer["重置选项，允许二次作答"]
        B_reanswer --> D_second_submit["提交 (第二次)"]
        D_second_submit --> F_second_correct["回答正确"]
        D_second_submit --> G_second_error["回答错误 (二次)"]
        E --> H
    end

    subgraph 解析状态
        direction LR
        I["查看答案、题目解析"]
        J["猜你想问"]
        K["点击推荐问题"]
        L["点击自由提问"]
        M["答疑 chat"]
        
        F --> I
        F_second_correct --> I
        G_second_error --> I
        H --> I
        I --> J
        J --> K
        J --> L
        K --> M
        L --> M
    end
```

---
#### **[核心功能点2]: 多选题**

##### **[核心功能点2.1]: 多选题 - 初始状态**
###### 界面布局与核心UI元素
- **布局参考：**
    - `[多选题初始状态-整体布局](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_OD1Zbr8o7oZVuwxZJSic0gvtnRh.png) - 仅关注布局`
    - `[题干图片排版参考](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_WcCTbFpfIoG2suxgKFTcSibenIf.png) - 仅关注布局`
    - `[选项文字超长折行参考](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_OssBbq2HhojYHSxhgbZcH2L0nye.png) - 仅关注布局`
    - `[选项含图参考](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_OSIIb8blyo9dXRxLIyTckAR0nsb.png) - 仅关注布局`
    - `[勾画组件入口参考](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_SBribFrWVodELRxfTJvcZ6Tmnbh.png) - 仅关注布局`
- **布局原则：**
    - **整体界面布局 ([`多选题初始状态-整体布局`](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_OD1Zbr8o7oZVuwxZJSic0gvtnRh.png) 解析):** 页面采用全屏展示。顶部栏包含返回按钮、计时器和答题进度条。内容区域分为左侧题干区和右侧选项区。底部操作栏包含勾画按钮、不确定按钮和提交按钮。
    - **题干区 ([`多选题初始状态-整体布局`](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_OD1Zbr8o7oZVuwxZJSic0gvtnRh.png) 解析):** 左侧展示题型标签（多选）、题目来源及题干内容。题干支持文字和图片，图片右下角有放大镜图标。
    - **选项区域 ([`多选题初始状态-整体布局`](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_OD1Zbr8o7oZVuwxZJSic0gvtnRh.png) 解析):** 右侧展示各选项。选项内容超长时折行显示。选项中可包含图片。
    - **底部操作栏 ([`多选题初始状态-整体布局`](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_OD1Zbr8o7oZVuwxZJSic0gvtnRh.png) 解析):** 左下角为勾画按钮，中间为不确定按钮，右下角为提交按钮。
    - **勾画组件 ([`勾画组件入口参考`](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_SBribFrWVodELRxfTJvcZ6Tmnbh.png) 解析):** 勾画组件悬浮显示，包含颜色调整、粗细调整、橡皮擦、撤回、恢复、一键清空和完成按钮。
- **核心UI元素清单：**
    - 退出学习按钮
    - 作答计时器 (格式 00:00)
    - 答题进度条
    - 题型标签 (文字：多选)
    - 题目来源标签
    - 题干内容区 (支持文字、图片)
    - 图片放大查看按钮
    - 选项 (A, B, C, D等，支持文字、图片，可多选)
    - “不确定”按钮
    - “提交”按钮 (初始状态置灰)
    - “勾画”悬浮按钮
    - 勾画工具栏

###### 功能需求与交互原则
- **功能描述：**
    - **状态判断：** 当用户进入题目且未选择任何选项时，为初始状态。
    - **题型校验：** 仅适用于语文、数学、英语、物理、化学、生物学科的非母子多选题。
    - **页面框架、退出学习、作答计时、题板结构、题干区展示、作答区展示（选项内容、折行、含图）、勾画组件：** （同单选题初始状态，仅题型标签为“多选”）
- **交互模式：**
    - **“不确定”按钮：** 初始状态可点击。点击后，按钮文字变为“放弃作答”。
    - **“提交”按钮：** 初始状态不可点击，按钮样式置灰。
    - **选项选择：** 用户点击选项进行选择或取消选择。
- **反馈原则：**
    - 初始状态下，主要为静态内容展示，等待用户操作。

**优先级：** P0
**依赖关系：** 无

##### **[核心功能点2.2]: 多选题 - 作答状态**
###### 界面布局与核心UI元素
- **布局参考：** `[多选题作答状态-选中多个选项](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_JjnbbhnpwogcHfxn8cucByuIn5d.png) - 仅关注布局`
- **布局原则：**
    - **整体布局 ([`多选题作答状态-选中多个选项`](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_JjnbbhnpwogcHfxn8cucByuIn5d.png) 解析):** 整体布局与初始状态一致。主要区别在于选项区有选项被选中高亮，底部“提交”按钮变为可点击状态。
    - **选项区域 ([`多选题作答状态-选中多个选项`](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_JjnbbhnpwogcHfxn8cucByuIn5d.png) 解析):** 用户选中的一个或多个选项有明确的视觉高亮。
    - **底部操作栏 ([`多选题作答状态-选中多个选项`](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_JjnbbhnpwogcHfxn8cucByuIn5d.png) 解析):** “提交”按钮变为可操作状态。
- **核心UI元素清单：**
    - (同初始状态的核心UI元素)
    - 选中状态的选项 (一个或多个，有视觉高亮)
    - “提交”按钮 (可点击状态)
    - “不确定”/“放弃作答”按钮
    - (特殊校验时) 二次确认弹窗 (“多选题答案有多个”，包含“我再想想”、“继续提交”按钮)

###### 功能需求与交互原则
- **功能描述：**
    - **状态判断：** 当用户进入题目并选中了至少一个选项时，为作答状态。
    - **批改方式：** 多选题采用自动批改。
    - **作答方式：**
        - 用户通过点击选择一个或多个选项，被选中的选项应有明确的选中状态视觉反馈。
        - 当有已选中选项时，再次点击该选项，则取消其选中状态。点击其他未选中选项，则选中该选项。
- **交互模式：**
    - **“不确定”按钮：** （同单选题作答状态）
    - **“提交”按钮：**
        - **按钮状态：** 当至少有一个选项被选中时，此按钮变为可点击状态。
        - **特殊校验（首次提交和第二次提交时）：** 当用户提交时，若选中的选项仅为1个，则弹窗进行二次确认。
            - **弹窗内容：** “多选题答案有多个。”
            - **操作按钮：**
                - “我再想想”：点击后关闭弹窗，用户返回作答界面继续选择。
                - “继续提交”：点击后提交当前作答，进入后续的批改和反馈流程。
        - **第一次点击提交（或二次确认后继续提交）：**
            - 系统提交作答数据，并校验作答结果是否正确。
            - **若回答正确：** 显示反馈弹窗（IP + 积分反馈 + 文案），随后题目进入解析状态。
            - **若回答错误（包括部分正确、全错、多选了错误选项）：** 显示反馈弹窗（IP + 文案），题目停留在作答状态。仅展示“猜你想问”入口，不展示答案、解析等。用户的作答选项被重置（取消选中状态），允许用户重新作答并进行第二次提交。
        - **第二次点击提交（或二次确认后继续提交）：**
            - 系统再次提交作答数据，并校验作答结果是否正确。
            - **若回答正确：** 显示反馈弹窗，随后题目进入解析状态。
            - **若回答错误：** 显示反馈弹窗，随后题目进入解析状态。
- **反馈原则：**
    - 选中/取消选中选项时，选项有即时的视觉状态变化。
    - 提交时若只选一项，有二次确认弹窗。
    - 点击“提交”后，根据作答结果显示相应的反馈弹窗。

**优先级：** P0
**依赖关系：** [核心功能点2.1]: 多选题 - 初始状态

##### **[核心功能点2.3]: 多选题 - 解析状态**
###### 界面布局与核心UI元素
- **布局参考：**
    - `[多选题解析状态-全对](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_ExPobyMKgof0mVxHRglcLpCVnFf.png) - 仅关注布局`
    - `[多选题解析状态-部分正确或全错](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_XaGGbFPFcoRya1xzU0Ucr13PnOc.png) - 仅关注布局`
    - `[多选题解析状态-漏选](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_WZ5Obcouco1ajex01pocVqSPnRc.png) - 仅关注布局`
- **布局原则：**
    - **整体布局 ([`多选题解析状态-全对`](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_ExPobyMKgof0mVxHRglcLpCVnFf.png) 解析等):** 与单选题解析状态类似。顶部栏计时器停止。题干区保持不变。作答区增加答案展示（根据多选特性标记正确、错误、漏选）、题目解析内容。底部操作栏变为“继续”按钮，并可能出现“加入错题本”按钮。右上角出现“问一问”图标入口。
    - **答案展示 ([`多选题解析状态-全对`](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_ExPobyMKgof0mVxHRglcLpCVnFf.png) 解析, [`多选题解析状态-部分正确或全错`](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_XaGGbFPFcoRya1xzU0Ucr13PnOc.png) 解析, [`多选题解析状态-漏选`](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_WZ5Obcouco1ajex01pocVqSPnRc.png) 解析):**
        - **回答正确时：** 用户选中的所有正确选项标记为绿色。
        - **回答部分正确（漏选）时：** 用户选中的正确选项标记为绿色，未选中的正确选项也标记为绿色并显示“漏选”标签。
        - **回答错误时：**
            - **全错时：** 用户选中的所有错误选项标记为红色，所有正确选项标记为绿色。
            - **部分错（选了部分正确，也选了错误）时：** 用户选中的正确选项标记为绿色，选中的错误选项标记为红色，未选中的正确选项标记为绿色并显示“漏选”标签。
            - **多选（选了所有正确，还多选了错误）时：** 所有正确选项标记为绿色，用户多选的错误选项标记为红色。
        - **不确定 - 放弃作答时：** 所有正确选项标记为绿色。
- **核心UI元素清单：**
    - (同单选题解析状态的核心UI元素)
    - “漏选”标签

###### 功能需求与交互原则
- **功能描述：**
    - **状态判断：**
        - 当用户在作答状态下提交答案后（第二次提交，或第一次提交正确，或第一次提交选中一项并确认提交），进入解析状态。
        - 当用户在初始或作答状态下点击“不确定”后，再点击“放弃作答”，则进入解析状态。
    - **作答计时、反馈弹窗、题目解析展示、问一问功能：** （同单选题解析状态）
    - **答案展示：**
        - **回答正确时：** 选中的正确选项颜色变更为绿色。
        - **回答部分正确（漏选）时：** 正确选项变更为绿色，未选中的正确选项展示“漏选”标签并标记为绿色。
        - **回答错误时：**
            - **全错时：** 选错的选项颜色变更为红色，并将正确选项标记为绿色。
            - **部分错时：** 选错的选项颜色变更为红色，未选中的正确选项标记为绿色并展示“漏选”标签。
            - **多选了错误选项时：** 正确选项标记为绿色，多选中的错误选项颜色变更为红色。
        - **不确定 - 放弃作答时：** 正确选项标记为绿色。
- **交互模式：**
    - **“继续”按钮：** （同单选题解析状态）
- **反馈原则：**
    - 正确、错误、漏选选项有明确的视觉区分。

**优先级：** P0
**依赖关系：** [核心功能点2.2]: 多选题 - 作答状态

##### 用户场景与故事 (针对此核心功能点: 多选题)

###### [场景1: 学生作答多选题，回答完全正确]
作为**学生**，我需要**在多选题中选择所有我认为正确的答案并提交**以解决**检验我对知识点的全面掌握程度**，从而**获得即时反馈，巩固认知，并可查看详细解析**。

**前端界面与交互关键步骤：**
1.  **[用户操作1: 查看题目并选择多个正确选项A, C]** 学生进入多选题，选择选项A和C。
    *   **界面变化:** 选项A、C高亮，“提交”按钮激活。
    *   **即时反馈:** 选项A、C视觉选中。
2.  **[用户操作2: 点击提交按钮]** 学生提交。
    *   **界面变化:** （若仅选一项，则先有二次确认弹窗）系统处理提交，显示“回答正确”反馈弹窗。计时器停止。弹窗关闭后，界面更新至解析状态：选项A、C标记为绿色，显示题目解析，“继续”和“问一问”入口出现。
    *   **即时反馈:** “回答正确”弹窗提示。

**场景细节补充：**
*   **前置条件:** 用户在多选题作答状态。
*   **期望结果:** 用户成功提交答案，获得正确反馈和解析。
*   **异常与边界情况:**
    *   提交时只选一项：触发二次确认弹窗。

**优先级：** 高
**依赖关系：** 无

```mermaid
flowchart TD
    A[用户进入多选题界面] --> B{选择多个正确选项 A, C};
    B --> C1[选项A, C高亮，提交按钮激活];
    C1 --> D{点击提交};
    D -- 校验答案 --> E{答案完全正确?};
    E -- 是 --> F[显示'回答正确'弹窗];
    F --> G[进入解析状态：显示答案（A,C绿色）、解析等];
```

###### [场景2: 学生作答多选题，漏选部分正确答案]
作为**学生**，我需要**在多选题漏选部分正确答案后，能看到哪些是漏选的**以解决**了解自己掌握不全面的地方**，从而**补充学习，完整掌握知识点**。

**前端界面与交互关键步骤：**
1.  **[用户操作1: 查看题目并选择部分正确选项A (正确答案为A,C)]** 学生选择选项A。
    *   **界面变化:** 选项A高亮，“提交”按钮激活。
    *   **即时反馈:** 选项A视觉选中。
2.  **[用户操作2: 点击提交按钮（并确认“继续提交”）]** 学生提交。
    *   **界面变化:** （因只选一项，先二次确认）显示“回答部分正确”或类似反馈弹窗。计时器停止。弹窗关闭后，界面更新至解析状态：选项A标记为绿色，选项C标记为绿色并带“漏选”提示，显示题目解析等。
    *   **即时反馈:** 反馈弹窗提示。

**场景细节补充：**
*   **前置条件:** 用户在多选题作答状态。
*   **期望结果:** 用户提交答案后，能清晰看到漏选的正确答案。
*   **异常与边界情况:** 无。

**优先级：** 高
**依赖关系：** 无

```mermaid
flowchart TD
    A[用户进入多选题界面] --> B{选择部分正确选项 A （正确答案 A,C）};
    B --> C1[选项A高亮，提交按钮激活];
    C1 --> D{点击提交（处理二次确认）};
    D -- 校验答案 --> E{答案部分正确（漏选C）?};
    E -- 是 --> F[显示反馈弹窗];
    F --> G[进入解析状态：显示答案（A绿色，C绿色+漏选）、解析等];
```

###### [场景3: 学生作答多选题，选择了错误选项]
作为**学生**，我需要**在多选题选择了错误答案后，能看到哪些是选错的，哪些是正确的**以解决**纠正错误认知**，从而**学习正确答案，避免混淆**。

**前端界面与交互关键步骤：**
1.  **[用户操作1: 查看题目并选择选项A(正确), B(错误) (正确答案为A,C)]** 学生选择选项A和B。
    *   **界面变化:** 选项A, B高亮，“提交”按钮激活。
    *   **即时反馈:** 选项A, B视觉选中。
2.  **[用户操作2: 点击提交按钮]** 学生提交。
    *   **界面变化:** 显示“回答错误”或类似反馈弹窗。计时器停止。弹窗关闭后，界面更新至解析状态：选项A标记为绿色，选项B标记为红色，选项C标记为绿色并带“漏选”提示（如果适用，或仅标记正确答案C为绿色），显示题目解析等。
    *   **即时反馈:** 反馈弹窗提示。

**场景细节补充：**
*   **前置条件:** 用户在多选题作答状态。
*   **期望结果:** 用户提交答案后，能清晰看到选错的、选对的以及未选的正确答案。
*   **异常与边界情况:**
    *   首次提交错误后，应允许二次作答。此场景为最终解析。

**优先级：** 高
**依赖关系：** 无

```mermaid
flowchart TD
    subgraph 初始状态
        A[查看题目\n（题干＋作答区）]
    end

    subgraph 作答状态
        B[选择选项\n（可选择1个或多个）]
        C[不确定]
        D[提交]
        E[放弃作答\n（二次确认）]
        D_confirm[二次确认弹窗\n（当仅选1项时）]

        subgraph 作答反馈
            F[回答正确]
            G[回答部分正确\n（漏选）]
            H[回答错误\n（全错、部分错、多选了错误项）]
            I[未作答]
        end
    end

    subgraph 解析状态
        J[查看答案、题目解析]
        K[猜你想问]
        L[点击推荐问题]
        M[点击自由提问]
        N[答疑 chat]
    end

    A --> B
    A --> C
    C -.-> B
    C --> E
    E -.-> B
    E --> I

    B --> D_check_single{仅选一项？}
    D_check_single -- 是 --> D_confirm
    D_check_single -- 否 --> D_submit_action[执行提交]
    D_confirm -- 继续提交 --> D_submit_action
    D_confirm -- 我再想想 --> B

    D_submit_action -- 第一次提交 --> F
    D_submit_action -- 第一次提交 --> G
    D_submit_action -- 第一次提交 --> H_first_error[回答错误／部分正确（首次）]

    H_first_error --> B_reanswer[重置选项，允许二次作答]
    B_reanswer --> D_check_single_re{仅选一项？（二次）}
    D_check_single_re -- 是 --> D_confirm_re
    D_check_single_re -- 否 --> D_submit_action_re[执行提交（二次）]
    D_confirm_re -- 继续提交 --> D_submit_action_re
    D_confirm_re -- 我再想想 --> B_reanswer

    D_submit_action_re --> F_second[回答正确（二次）]
    D_submit_action_re --> G_second[回答部分正确（二次）]
    D_submit_action_re --> H_second[回答错误（二次）]

    F --> J
    G --> J

    %% 首次错误后停留在作答，二次错误才到解析
    H_second --> J

    F_second --> J
    G_second --> J
    I --> J

    J --> K
    K --> L
    K --> M
    L --> N
    M --> N

```

---
#### **[核心功能点3]: 填空题 - 其他学科（自评）**

##### **[核心功能点3.1]: 填空题（自评） - 初始状态**
###### 界面布局与核心UI元素
- **布局参考：**
    - `[填空题初始状态-题干内填空](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_OX5hbeeyEoyqkbxDAj5c6MbInuc.png) - 仅关注布局`
    - `[填空题初始状态-拍照作答入口](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_VjJhbuoEqoTsALxEl2Mc0MkGnEc.png) - 仅关注布局`
    - `[勾画组件入口参考](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_AMqAbcFa3oYtQBxBQKNcVBeHnEh.png) - 仅关注布局`
- **布局原则：**
    - **整体界面布局 ([`填空题初始状态-题干内填空`](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_OX5hbeeyEoyqkbxDAj5c6MbInuc.png) 解析, [`填空题初始状态-拍照作答入口`](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_VjJhbuoEqoTsALxEl2Mc0MkGnEc.png) 解析):** 全屏展示。顶部包含返回、计时、进度。主体左侧为题干区，右侧为作答区（包含作答方式切换、题号/空号导航、具体作答区域）。底部为操作按钮。
    - **题干区 ([`填空题初始状态-题干内填空`](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_OX5hbeeyEoyqkbxDAj5c6MbInuc.png) 解析):** 显示题型标签“填空”、题目来源。题干内容支持文字、图片，并直接嵌入作答区（填空框）。若有多个作答区，需标记顺序（1,2,3...）。
    - **作答区 ([`填空题初始状态-题干内填空`](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_OX5hbeeyEoyqkbxDAj5c6MbInuc.png) 解析, [`填空题初始状态-拍照作答入口`](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_VjJhbuoEqoTsALxEl2Mc0MkGnEc.png) 解析):**
        - **作答区选项卡/导航：** 若题干有多个填空，右侧作答区显示对应数量和顺序的选项卡（如数字按钮1,2,3...）。单个填空时不展示。
        - **键盘作答模式：** 右侧显示“点击输入答案”的输入框。
        - **拍照作答模式：** 右侧显示“拍照上传”入口及提示（如“最多可以传4张”）。
    - **勾画组件 ([`勾画组件入口参考`](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_AMqAbcFa3oYtQBxBQKNcVBeHnEh.png) 解析):** 同单选题。
- **核心UI元素清单：**
    - (同单选题初始状态的核心UI元素，题型标签为“填空”)
    - 题干内嵌的填空框 (标记序号)
    - 作答区选项卡 (多空时)
    - 作答方式切换按钮 (键盘输入/拍照作答)
    - 键盘作答输入框 / 拍照上传入口

###### 功能需求与交互原则
- **功能描述：**
    - **状态判断：** 进入题目且所有填空均未作答时为初始状态。
    - **题型校验：** 适用于语文、数学、物理、化学、生物学科的非母子填空题。
    - **页面框架、退出学习、作答计时、题板结构、勾画组件：** （同单选题初始状态）
    - **题干区展示：**
        - 显示“填空”题型标签、题目来源。
        - 题干内容支持文字、图片，并在文中直接嵌入作答区（填空框）。
        - 若题干存在多个作答区，作答区需从左到右、从上到下标记顺序（1, 2, 3...）。
    - **作答区交互：**
        - **填空区域：** 以输入框的形式插入题干中，长度以答案长度为准。
        - **作答区选项卡：** 若题干有多个填空，右侧作答区显示对应数量和顺序的选项卡。点击选项卡，左侧题干区自动定位到对应序号的填空框。单个填空时不展示选项卡。
        - **点击题干作答区标记：** 点击题干上嵌入的作答区标记（如①），右侧作答区自动定位到对应选项卡（若有）和输入区域。
- **交互模式：**
    - **“不确定”按钮、 “提交”按钮：** （同单选题初始状态）
- **反馈原则：**
    - 点击题干填空标记或右侧选项卡时，对应填空区域应有视觉反馈（如高亮、滚动到视图）。

**优先级：** P0
**依赖关系：** 无

##### **[核心功能点3.2]: 填空题（自评） - 作答状态**
###### 界面布局与核心UI元素
- **布局参考：**
    - `[填空题作答状态-键盘输入](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_BnRUbC553og3KZxcBRvcxjsUnGg.png) - 仅关注布局`
    - `[填空题作答状态-键盘输入提示](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_YxHrbOcU6oqZT0xqByEc7trInNe.png) - 仅关注布局`
    - `[填空题作答状态-多空导航](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_VZF3bsNjQot6Pxxk2kvcRIF0nX7.png) - 仅关注布局`
    - `[填空题作答状态-输入内容超长](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_Ne0Gb7W5voQfHrxhdO6cVe6Vnub.png) - 仅关注布局`
    - `[填空题作答状态-拍照上传入口](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_RIdZbg5cRoilSkxc0SAcjh2kn8b.png) - 仅关注布局`
    - `[填空题作答状态-已上传图片](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_EKFDbnuipoQDyNxtKK2cD2Hbnhg.png) - 仅关注布局`
    - `[拍照作答-图片处理参考1](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_GUhdbGw2HoYAktxdBOWcQMCQnVd.png) - 仅关注布局` (数学题示例，仅参考图片处理)
    - `[拍照作答-图片处理参考2](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_Zvd7bYTEbofuV5x2KLacKUgmn8f.png) - 仅关注布局` (数学题示例，仅参考图片处理)
    - `[拍照作答-图片旋转/框选](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_BkKIbv3lrofEY9xsFeqcjyBVnHf.png) - 仅关注布局` (数学题示例，仅参考图片处理)
    - `[拍照作答-上传状态](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_G967bbzgsoddEJxIow8cNa44nBg.png) - 仅关注布局`
    - `[提交前确认弹窗](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_BKbZbJQxdoQ7GaxW7hqc96ymnVc.png) - 仅关注布局`
- **布局原则：**
    - **键盘作答模式 ([`填空题作答状态-键盘输入`](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_BnRUbC553og3KZxcBRvcxjsUnGg.png) 解析):** 当选择键盘作答时，点击题干中的填空框或右侧对应的作答区，会调起虚拟键盘。键盘上方可能有一个临时输入展示区域。
    - **拍照作答模式 ([`填空题作答状态-已上传图片`](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_EKFDbnuipoQDyNxtKK2cD2Hbnhg.png) 解析):** 当选择拍照作答时，右侧作答区会显示已上传的图片缩略图（带删除按钮）和继续拍照上传的入口。
    - **图片处理界面 ([`拍照作答-图片旋转/框选`](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_BkKIbv3lrofEY9xsFeqcjyBVnHf.png) 解析):** 拍摄完成后，进入图片编辑界面，支持框选、旋转。
    - **上传状态提示 ([`拍照作答-上传状态`](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_G967bbzgsoddEJxIow8cNa44nBg.png) 解析):** 图片上传时显示“上传中”动画，失败时显示“上传失败 点击重试”。
    - **提交确认弹窗 ([`提交前确认弹窗`](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_BKbZbJQxdoQ7GaxW7hqc96ymnVc.png) 解析):** 若有未作答的空，提交时会弹出确认对话框。
- **核心UI元素清单：**
    - (同初始状态的核心UI元素)
    - 虚拟键盘
    - 键盘上方临时输入展示区 (键盘作答时)
    - 已上传图片缩略图 (拍照作答时)
    - 图片删除按钮 (拍照作答时)
    - 图片编辑界面 (拍照后，包含框选工具、旋转按钮、上传/返回按钮)
    - 上传状态指示器 (上传中动画、上传失败提示)
    - 提交确认弹窗 (包含提示文字、“我再想想”按钮、“继续提交”按钮)

###### 功能需求与交互原则
- **功能描述：**
    - **状态判断：** 当进入题目且至少有一个填空处有作答内容（文字或图片）时，为作答状态。
    - **作答方式切换：**
        - 用户可在键盘作答和拍照作答之间切换（单题内仅可选择一种最终提交方式，但切换时不应丢失另一种方式已输入的内容）。
        - 若当前作答方式下已有内容，切换时，界面展示新选择的作答方式的UI。
        - 键盘作答切换到拍照作答：题干上已填写的文字内容清空（视觉上），但不实际删除数据，以便用户切回键盘作答时恢复。
        - 拍照作答切换到键盘作答：已上传的图片不删除，以便用户切回拍照作答时恢复。
    - **键盘作答（默认）：**
        - **入口：** 选择“键盘作答”方式。右侧作答区提示“点击输入答案”。
        - **交互：**
            - 点击左侧题干区域的作答区标记或右侧作答区域的“点击输入答案”，该区域在题干和右侧导航均展示选中状态，并自动调起键盘。
            - 输入文字时，作答区（题干内嵌框）不直接展示输入内容，而是在键盘上方新增一个临时展示区域用于实时呈现输入的文字。此区域最多展示3行文字，超长时自动换行。
            - 点击临时输入区的“确定”按钮，或点击键盘区域外的其他区域（不包括其他填空作答区），键盘收起，填空区域取消选中状态，系统自动保存刚输入的文字到对应的题干内嵌框。
            - 若点击键盘外其他区域时，恰好点击到某一个题干内嵌作答区，则键盘不收起，键盘上方的临时输入框展示该对应作答区已输入的内容。
    - **拍照作答：**
        - **入口：** 选择“拍照作答”方式。
        - **最大上传数量：** 3张。
        - **交互：**
            - 点击拍照作答功能后，调起设备摄像头并进入拍摄界面。
            - 拍摄界面右上角有“x”关闭按钮。拍摄界面增加16:9的长方形辅助线帮助用户构图。
            - 点击拍摄键完成拍摄后，进入图片编辑界面，用户可调整图片框选位置和大小，确认后上传。
            - 图片编辑界面操作：点击“返回”回到拍摄界面；点击“旋转”顺时针旋转图片90度；点击“上传”将处理后的图片上传至作答区。
            - 上传过程在对应图片位置显示“上传中”状态；上传失败时，显示“上传失败 点击重试”，点击后执行重新上传。
            - 已上传的图片缩略图支持点击放大全屏查看，并支持删除。
        - **已上传内容检测：** 接入大模型进行识别。若识别内容为异常（如与题目无关），则不保存图片，并弹窗提示“请上传和题目相关的内容”（3秒后自动消失）。
- **交互模式：**
    - **“不确定”按钮：** （同单选题作答状态，输入内容后，“放弃作答”按钮会回退至“不确定”）
    - **“提交”按钮：**
        - **按钮状态：** 当有作答内容（文字或图片）时，此按钮变为可点击状态。
        - **交互（需校验）：**
            - **若所有空均有作答：** 点击后提交作答数据，题目进入解析状态。
            - **若有空未作答：**
                - **全部空均为空时：** 弹窗提示“本题未作答，确认提交吗？”。操作按钮：“我再想想”（关闭弹窗），“继续提交”（提交作答，进入解析状态）。
                - **部分空为空时：** 弹窗提示“还有填空未作答，确认提交吗？”。操作按钮：“我再想想”（关闭弹窗），“继续提交”（提交作答，进入解析状态）。
- **反馈原则：**
    - 作答方式切换时，界面清晰反映当前作答模式。
    - 键盘输入时，有临时输入预览。
    - 图片上传有明确的状态反馈（上传中、成功、失败、异常内容）。
    - 提交未完成的作答时，有确认弹窗。

**优先级：** P0
**依赖关系：** [核心功能点3.1]: 填空题（自评） - 初始状态

##### **[核心功能点3.3]: 填空题（自评） - 解析状态**
###### 界面布局与核心UI元素
- **布局参考：**
    - `[填空题解析状态-自评](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_LrOQbLpKyo8y2Axm6a4cbZgYnmh.png) - 仅关注布局`
    - `[自评异常行为提示](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_SD3zblHhDoNnHDxG6a0cFKXTnRd.png) - 仅关注布局`
    - `[自评后选项卡状态](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_G0CRbeT6YoeD0gxcg9ncZerynGk.png) - 仅关注布局` (此图为按钮样式，用于示意选项卡状态)
- **布局原则：**
    - **整体布局 ([`填空题解析状态-自评`](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_LrOQbLpKyo8y2Axm6a4cbZgYnmh.png) 解析):** 左侧题干区展示用户作答内容（文字或图片）。右侧为自评区域，包含“核对答案”标题、提示语、作答区选项卡（用于导航多空）、每个空对应的标准答案、用户答案以及自评按钮（我答对了、部分答对、我答错了）。下方为题目解析和“问一问”入口。底部为“继续”按钮。
    - **自评区域 ([`填空题解析状态-自评`](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_LrOQbLpKyo8y2Axm6a4cbZgYnmh.png) 解析):** 针对每个填空，并列展示标准答案和“我的答案”。下方提供三个自评按钮。
    - **作答区选项卡状态 ([`自评后选项卡状态`](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_G0CRbeT6YoeD0gxcg9ncZerynGk.png) 参考):** 自评后，选项卡上应显示对应空的自评结果（如对号、错号、部分对符号）。
    - **异常行为提示 ([`自评异常行为提示`](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_SD3zblHhDoNnHDxG6a0cFKXTnRd.png) 解析):** 若用户自评过快，会弹出toast提示。
- **核心UI元素清单：**
    - (题干区元素同初始状态，展示用户答案)
    - “核对答案”标题及提示语
    - 作答区选项卡 (显示各空自评状态：待自评、答对、答错、部分答对)
    - 标准答案展示区 (针对每个空)
    - “我的答案”展示区 (针对每个空，文字或图片)
    - 自评按钮组 (“✔ 我答对了”, “✖ 部分答对”, “✖ 我答错了”)
    - 题目解析文本区
    - “问一问”入口图标
    - “继续”按钮
    - Toast提示组件 (用于异常行为监控)

###### 功能需求与交互原则
- **功能描述：**
    - **状态判断：**
        - 当用户在作答状态下提交答案后，进入解析状态。
        - 当用户在初始或作答状态下点击“不确定”后，再点击“放弃作答”，则进入解析状态。
    - **批改方式：** 自评。
    - **自评模块展示：**
        - **标题：** “核对答案”。
        - **提示语：** “对待错误是进步的第一步，相信你会认真对待!”。
        - **掌握度与积分影响：** 自评题目的掌握度和积分赋予会相应减少。
        - **异常行为监控：** 若用户连续3题的批改时长（从进入解析到点击“继续”或完成所有自评的时间）均小于2秒，系统会toast提示“认真核对哦～”（提示时长2秒）。
    - **自评选项与交互：**
        - **作答区选项卡：** 对应题干中的作答区数量和顺序。单个填空时不展示。点击选项卡可定位到左侧题干对应空及右侧该空的自评区域。自评后，选项卡上展示对应的自评结果图标（如：对号代表“我答对了”，部分对符号代表“部分答对”，错号代表“我答错了”）。
        - **自评按钮：** 每个填空下方提供“我答对了”、“部分答对”、“我答错了”三个按钮。用户点击其一进行自评，按钮显示选中状态。此为单选逻辑，选择一个后，另两个取消选中。
        - **自动跳转：** 用户点击自评选项后，自动进入下一个待自评的填空（按选项卡顺序）。如果是最后一个填空自评完成，系统判断是否有前置的作答区未自评，若有，则自动跳转到第一个未自评的作答区。
    - **提交自评与反馈：**
        - 当所有填空都已自评后，“提交”按钮（或此处的“继续”按钮逻辑上需要所有自评完成）变为可点击。
        - 原始PRD描述“提交后展示自评结果，此时自评结果仍然可修改”，然后“提交后进行作答反馈”。这里理解为用户完成所有空的自评后，系统会根据自评结果给出整体的作答反馈（参考“巩固练习”PRD）。自评结果在点击“继续”离开本题前应可修改。
    - **题目解析展示、问一问功能：** （同单选题解析状态）
- **交互模式：**
    - **“继续”按钮：**
        - **出现时机：** 题目进入解析状态后显示。
        - **按钮状态：** 必须所有填空都已完成自评后，此按钮才可点击。若有未自评的空，点击“继续”按钮时，第一个未自评的作答区选项卡（或对应自评区域）应有抖动等提示效果。
        - **交互：** 点击后进入下一题或下一节学习内容。
- **反馈原则：**
    - 自评选项有明确的选中状态。
    - 作答区选项卡实时反映各空的自评状态。
    - 未完成所有自评时，对“继续”操作有明确的引导和限制。
    - 快速自评时有toast提示。

**优先级：** P0
**依赖关系：** [核心功能点3.2]: 填空题（自评） - 作答状态

##### 用户场景与故事 (针对此核心功能点: 填空题 - 其他学科（自评）)

###### [场景1: 学生使用键盘作答填空题，部分填写后提交，并完成自评]
作为**学生**，我需要**使用键盘填写填空题的答案，即使部分未填写也能提交，并在查看答案后进行自我评价**以解决**检验我对知识点的回忆和应用能力，并对自己的作答进行反思**，从而**通过对照答案和解析学习，并培养自我订正的能力**。

**前端界面与交互关键步骤：**
1.  **[用户操作1: 查看题目并选择键盘作答]** 学生进入填空题，默认为键盘作答模式。界面显示多个填空框。
    *   **界面变化:** 右侧作答区显示各填空对应的输入区域和选项卡（若多于一个空）。
    *   **即时反馈:** 无。
2.  **[用户操作2: 填写部分答案]** 学生点击第一个填空框，键盘弹出，输入答案。然后点击第二个填空框，输入答案。第三个空未填写。
    *   **界面变化:** 键盘弹出和收起，对应填空框显示输入的文字。右侧选项卡可能高亮当前编辑的空。
    *   **即时反馈:** 输入文字实时显示在键盘上方的临时输入区，确认后显示在题干的填空框内。
3.  **[用户操作3: 点击提交按钮]** 学生提交部分填写的答案。
    *   **界面变化:** 弹出“还有填空未作答，确认提交吗？”确认弹窗。
    *   **即时反馈:** 弹窗提示。
4.  **[用户操作4: 点击“继续提交”]** 学生确认提交。
    *   **界面变化:** 计时器停止。界面进入解析状态。右侧显示“核对答案”区域，列出每个空的标准答案、学生答案（第三个空为“未作答”）和自评按钮。
    *   **即时反馈:** 界面切换到解析和自评模式。
5.  **[用户操作5: 逐个进行自评]** 学生对照标准答案，为第一个空选择“我答对了”，为第二个空选择“部分答对”，为第三个空选择“我答错了”。
    *   **界面变化:** 每个自评按钮被点击后显示选中状态。对应选项卡显示自评结果图标。完成后“继续”按钮激活。
    *   **即时反馈:** 自评结果实时反馈。
6.  **[用户操作6: 点击“继续”按钮]** 学生完成自评，点击“继续”。
    *   **界面变化:** （可能会有整体作答反馈弹窗）跳转到下一题。
    *   **即时反馈:** 界面跳转。

**场景细节补充：**
*   **前置条件:** 用户在填空题（自评）作答流程中。
*   **期望结果:** 用户能顺利提交部分答案，并完成所有空的自评。
*   **异常与边界情况:**
    *   自评过快：触发toast提示“认真核对哦～”。

**优先级：** 高
**依赖关系：** 无

```mermaid
flowchart TD
    A[用户进入填空题（自评，键盘作答模式）] --> B{填写第1、2空答案，第3空不填};
    B --> C[填空框显示输入内容];
    C --> D{点击提交};
    D --> E[弹窗：'还有填空未作答，确认提交吗？'];
    E -- 点击'继续提交' --> F[进入解析状态];
    F --> G[显示标准答案、用户答案（空3为未作答）、自评按钮];
    G --> H{用户逐个自评：空1-对，空2-部分对，空3-错};
    H --> I[自评结果实时显示，'继续'按钮激活];
    I --> J{点击'继续'};
    J --> K[（反馈弹窗），进入下一题];
```

###### [场景2: 学生使用拍照作答填空题，上传图片后提交，并完成自评]
作为**学生**，我需要**通过拍照上传手写答案来完成填空题，并在提交后对照标准答案进行自评**以解决**键盘输入复杂公式或大量文字不便的问题**，从而**更灵活地展示我的解题过程并进行有效学习**。

**前端界面与交互关键步骤：**
1.  **[用户操作1: 查看题目并选择拍照作答]** 学生进入填空题，切换到拍照作答模式。
    *   **界面变化:** 右侧作答区显示拍照上传入口。
    *   **即时反馈:** 无。
2.  **[用户操作2: 点击拍照上传并完成拍摄和图片编辑]** 学生点击拍照入口，调起相机拍摄答案，进行必要的裁剪或旋转后确认上传。
    *   **界面变化:** 显示拍摄界面、图片编辑界面，最后图片缩略图显示在作答区，并有“上传中”提示，成功后提示消失。
    *   **即时反馈:** 各操作步骤有相应界面变化和状态提示。
3.  **[用户操作3: 点击提交按钮]** 学生确认图片无误后提交。
    *   **界面变化:** 计时器停止。界面进入解析状态。右侧显示“核对答案”区域，列出标准答案、学生答案（图片形式）和自评按钮。
    *   **即时反馈:** 界面切换。
4.  **[用户操作4: 进行自评]** 学生查看自己的图片答案和标准答案，选择“我答对了”。
    *   **界面变化:** 自评按钮选中，“继续”按钮激活。
    *   **即时反馈:** 自评结果反馈。
5.  **[用户操作5: 点击“继续”按钮]** 学生完成自评，点击“继续”。
    *   **界面变化:** 跳转到下一题。
    *   **即时反馈:** 界面跳转。

**场景细节补充：**
*   **前置条件:** 用户在填空题（自评）作答流程中。
*   **期望结果:** 用户能顺利通过拍照上传答案并完成自评。
*   **异常与边界情况:**
    *   上传图片内容异常：提示“请上传和题目相关的内容”。
    *   上传失败：提示“上传失败 点击重试”。

**优先级：** 高
**依赖关系：** 无

```mermaid
flowchart TD
    A[用户进入填空题（自评），切换到拍照作答模式] --> B{点击拍照上传};
    B --> C[调起相机 -> 拍摄 -> 编辑图片 -> 确认上传];
    C --> D[图片缩略图显示在作答区，'上传中' -> 上传成功];
    D --> E{点击提交};
    E --> F[进入解析状态];
    F --> G[显示标准答案、用户图片答案、自评按钮];
    G --> H{用户自评：'我答对了'};
    H --> I[自评结果显示，'继续'按钮激活];
    I --> J{点击'继续'};
    J --> K[进入下一题];
```
**Mermaid图示 (填空题-自评整体主要流程):**
```mermaid
flowchart TD
    subgraph 初始状态
        A["查看题目\n（题干+作答区）"]
    end

    subgraph 作答状态
        B["填空作答区\n输入内容/拍照上传"]
        D["不确定"]
        C["提交"]
        E["放弃作答\n（二次确认）"]
        C_confirm_empty["弹窗：未作答/部分未作答\n确认提交？"]
    end

    subgraph 解析状态_自评
        G["查看答案、题目解析"]
        H["自评模块\n（标准答案、我的答案、自评按钮）"]
        I["回答正确"]
        J["部分错误"]
        K["回答错误"]
        L_self_eval_toast["Toast提示：'认真核对哦～'\n（自评过快时）"]
        M_continue_btn_disabled["'继续'按钮（未完成所有自评时禁用）"]
        M_continue_btn_enabled["'继续'按钮（所有自评完成后激活）"]
        N["猜你想问"]
        O["点击推荐问题"]
        P["点击自由提问"]
        Q["答疑 chat"]
    end

    A --> B
    A --> D

    B --> C_check_empty{"有未作答的空?"}
    C_check_empty -- 是 --> C_confirm_empty
    C_check_empty -- 否 --> C_action["执行提交"]
    C_confirm_empty -- 继续提交 --> C_action
    C_confirm_empty -- 我再想想 --> B
    
    C_action --> G
    C_action --> H

    D -.-> B
    D --> E
    E --> G
    E --> H 
    %% 放弃作答也进入自评，我的答案为未作答

    H -- 对空1自评 --> I_1["空1:回答正确"]
    H -- 对空2自评 --> J_2["空2:部分错误"]
    %% ... 其他空的自评

    G --> N
    H -- 完成所有自评 --> M_continue_btn_enabled
    H -- 未完成所有自评 & 用户点击继续 --> M_continue_btn_disabled
    H -- 自评过快 --> L_self_eval_toast

    N --> O
    N --> P
    O --> Q
    P --> Q
```

---
#### **[核心功能点4]: 填空题 - 英语（自动批改）**

##### **[核心功能点4.1]: 英语填空题（自动批改） - 初始状态**
###### 界面布局与核心UI元素
- **布局参考：**
    - `[英语填空题初始状态](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_MQbDb84rGocUN9xlXG3cq4ZVnDz.png) - 仅关注布局`
    - `[勾画组件入口参考](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_W1HJbyVIko755gx2bfXcO1dznjd.png) - 仅关注布局` (此图为单选勾画，仅示意勾画组件存在)
- **布局原则：**
    - **整体界面布局 ([`英语填空题初始状态`](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_MQbDb84rGocUN9xlXG3cq4ZVnDz.png) 解析):** 与其他填空题类似，全屏展示，顶部导航，主体为左右分栏（左题干，右作答区），底部操作按钮。
    - **题干区 ([`英语填空题初始状态`](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_MQbDb84rGocUN9xlXG3cq4ZVnDz.png) 解析):** 显示题型标签“填空”。题干内容为英文，包含带序号的下划线填空。
    - **作答区 ([`英语填空题初始状态`](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_MQbDb84rGocUN9xlXG3cq4ZVnDz.png) 解析):** 右侧显示题号导航（若多空），以及当前选中空的输入提示“点击输入答案”。仅支持键盘作答。
- **核心UI元素清单：**
    - (同其他填空题初始状态的核心UI元素，但无拍照作答相关元素)
    - 题干内嵌的填空框 (标记序号，下划线样式)

###### 功能需求与交互原则
- **功能描述：**
    - **状态判断：** 进入题目且所有填空均未作答时为初始状态。
    - **题型校验：** 适用于英语学科的非母子填空题。
    - **页面框架、退出学习、作答计时、题板结构、勾画组件：** （同单选题初始状态）
    - **题干区展示：**
        - 显示“填空”题型标签。
        - 题干内容为英文文本，支持图片，并在文中直接嵌入作答区（填空框，通常为下划线形式）。
        - 若题干存在多个作答区，作答区需标记顺序。
    - **作答区交互：**
        - **填空区域：** 以输入框的形式插入题干中，长度以答案长度为准。
        - **作答区选项卡：** 若题干有多个填空，右侧作答区显示对应数量和顺序的选项卡。点击选项卡，左侧题干区自动定位到对应序号的填空框。单个填空时不展示。
- **交互模式：**
    - **“不确定”按钮、 “提交”按钮：** （同单选题初始状态）
- **反馈原则：**
    - 点击题干填空标记或右侧选项卡时，对应填空区域应有视觉反馈。

**优先级：** P0
**依赖关系：** 无

##### **[核心功能点4.2]: 英语填空题（自动批改） - 作答状态**
###### 界面布局与核心UI元素
- **布局参考：**
    - `[英语填空题作答状态-键盘输入中](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_OBFUbYqHAoKd5Yxjq7scW5ALn5f.png) - 仅关注布局`
    - `[英语填空题作答状态-输入内容展示](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_FQz8bCpQzoaRrixqwWkccrZlnvc.png) - 仅关注布局` (此图为平板，仅示意输入逻辑)
- **布局原则：**
    - **键盘作答模式 ([`英语填空题作答状态-键盘输入中`](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_OBFUbYqHAoKd5Yxjq7scW5ALn5f.png) 解析):** 点击填空处，调起键盘。键盘上方有临时输入展示区。
- **核心UI元素清单：**
    - (同初始状态的核心UI元素)
    - 虚拟键盘
    - 键盘上方临时输入展示区
    - 提交确认弹窗 (同其他填空题)

###### 功能需求与交互原则
- **功能描述：**
    - **状态判断：** 当进入题目且至少有一个填空处有作答内容时，为作答状态。
    - **批改方式：** 英语学科填空题采用自动批改。
    - **作答方式（仅键盘作答）：**
        - 支持通过键盘输入文字将内容填充到填空的对应位置。
        - 点击左侧题干区域的作答区标记或右侧作答区域的“点击输入答案”，该区域在题干和右侧导航均展示选中状态，并自动调起键盘。
        - 当对应作答区输入内容后，题干的填空处展示已输入状态（如填入文字）。
        - 输入文字时，键盘上方增加一个临时展示输入内容的区域。
        - 点击临时输入区的“确定”按钮或点击键盘区域外的其他区域，键盘收起，取消选中状态，保存刚输入的文字到对应填空。
        - 如果点击键盘外其他区域时刚好点击了某一个题干内嵌作答区，则不收起键盘，键盘上方的临时输入框展示对应作答区已输入的内容。
- **交互模式：**
    - **“不确定”按钮：** （同单选题作答状态）
    - **“提交”按钮：** （同其他填空题作答状态的提交校验逻辑，包括空未作答的确认弹窗）
- **反馈原则：**
    - 输入时有临时预览。
    - 提交未完成的作答时，有确认弹窗。

**优先级：** P0
**依赖关系：** [核心功能点4.1]: 英语填空题（自动批改） - 初始状态

##### **[核心功能点4.3]: 英语填空题（自动批改） - 解析状态**
###### 界面布局与核心UI元素
- **布局参考：** `[英语填空题解析状态](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_T6xIbv5nAoy3xjxBYuocbx9LnUc.png) - 仅关注布局`
- **布局原则：**
    - **整体布局 ([`英语填空题解析状态`](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_T6xIbv5nAoy3xjxBYuocbx9LnUc.png) 解析):** 左侧题干区展示用户答案及批改标记（对/错）。右侧显示正确答案、用户答案（文本）、题目解析、“问一问”入口。底部为“继续”按钮。
    - **答案展示 ([`英语填空题解析状态`](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_T6xIbv5nAoy3xjxBYuocbx9LnUc.png) 解析):** 题干中的填空处，若用户填写正确，则显示绿色对勾或文字不变色；若错误，则显示红色叉号或文字标红，并在右侧对应展示正确答案。
- **核心UI元素清单：**
    - (题干区元素同初始状态，展示用户答案及对错标记)
    - 正确答案展示区 (针对每个空)
    - “你的答案”展示区 (针对每个空，文本)
    - 题目解析文本区
    - “问一问”入口图标
    - “继续”按钮
    - 反馈弹窗 (进入解析前)

###### 功能需求与交互原则
- **功能描述：**
    - **状态判断：**
        - 当用户在作答状态下提交答案后，进入解析状态。
        - 当用户在初始或作答状态下点击“不确定”后，再点击“放弃作答”，则进入解析状态。
    - **批改方式：** 自动批改。
    - **反馈弹窗：** 进入解析状态前，显示作答结果的反馈弹窗（具体样式参考“巩固练习”PRD）。
    - **答案展示：**
        - 在右侧区域清晰展示每个空的“正确答案”和“你的答案”（文本）。
        - 在左侧题干的对应填空处，对用户的答案进行标记：回答正确则标记为绿色（或不标记，仅右侧显示正确），回答错误则标记为红色。
        - 若用户通过“不确定”->“放弃作答”进入此状态，则“你的答案”显示为“未作答”，题干对应填空处可标记为不确定或提示未作答，并在右侧展示正确答案。
    - **题目解析展示、问一问功能：** （同单选题解析状态）
- **交互模式：**
    - **“继续”按钮：** （同单选题解析状态）
- **反馈原则：**
    - 自动批改结果在题干和右侧答案区均有明确视觉反馈。

**优先级：** P0
**依赖关系：** [核心功能点4.2]: 英语填空题（自动批改） - 作答状态

##### 用户场景与故事 (针对此核心功能点: 英语填空题 - 自动批改)

###### [场景1: 学生作答英语填空题，部分正确部分错误]
作为**学生**，我需要**在提交英语填空题答案后，系统能自动批改并明确指出我对错的空**以解决**快速了解自己的作答情况**，从而**针对性地查看错误部分的解析，高效学习**。

**前端界面与交互关键步骤：**
1.  **[用户操作1: 查看题目并填写答案]** 学生进入英语填空题，在多个填空处输入答案。
    *   **界面变化:** 填空处显示输入内容。
    *   **即时反馈:** 输入实时显示。
2.  **[用户操作2: 点击提交按钮]** 学生提交答案。
    *   **界面变化:** 显示反馈弹窗（如“部分正确”）。计时器停止。弹窗关闭后，界面进入解析状态：题干中错误的空标记红色，正确的空标记绿色（或无特殊标记）。右侧显示每个空的正确答案和学生答案。显示题目解析、“继续”和“问一问”入口。
    *   **即时反馈:** 反馈弹窗提示，自动批改结果在界面上清晰展示。

**场景细节补充：**
*   **前置条件:** 用户在英语填空题作答流程中。
*   **期望结果:** 用户能清晰看到自动批改的结果，并能查看解析。
*   **异常与边界情况:**
    *   提交未完成的作答：按提交校验逻辑处理。

**优先级：** 高
**依赖关系：** 无

```mermaid
flowchart TD
    A[用户进入英语填空题] --> B{在各填空处输入答案};
    B --> C[填空处显示输入内容，提交按钮激活];
    C --> D{点击提交};
    D -- 自动批改 --> E[显示反馈弹窗（如'部分正确'）];
    E --> F[进入解析状态：题干中标注对错，右侧显示正确/用户答案、解析等];
    F --> G{用户查看批改结果和解析};
    G --> H{点击'继续'};
    H --> I[进入下一题];
```
**Mermaid图示 (英语填空题-自动批改整体主要流程):**
```mermaid
flowchart LR
    subgraph 初始状态
        B_eng["查看题目<br/>（题干+作答区）"]
    end

    subgraph 作答状态_eng
        C_eng["填空作答区<br/>输入内容"]
        D_eng["不确定"]
        E_eng["提交"]
        F_eng["放弃作答<br/>（二次确认）"]
        E_eng_confirm_empty["弹窗：未作答/部分未作答<br/>确认提交？"]
        
        subgraph 自动批改_feedback
            direction LR
            G_eng["回答正确"]
            H_eng["回答错误"]
            I_eng["未作答"]
        end
    end

    subgraph 解析状态_eng
        J_eng["查看答案、题目<br/>解析"]
        K_eng["猜你想问<br/>（仅回答错误后）"]
        L_eng["点击推荐问题"]
        M_eng["点击自由提问"]
        N_eng["答疑 chat"]
    end

    A_eng_title["英语（自动批改）"] --> B_eng

    B_eng --> C_eng
    B_eng --> D_eng

    C_eng --> E_eng_check_empty{"有未作答的空?"}
    E_eng_check_empty -- 是 --> E_eng_confirm_empty
    E_eng_check_empty -- 否 --> E_eng_action["执行提交"]
    E_eng_confirm_empty -- 继续提交 --> E_eng_action
    E_eng_confirm_empty -- 我再想想 --> C_eng
    
    D_eng --> F_eng
    F_eng --> I_eng 
    %% 放弃作答直接到未作答状态

    E_eng_action -- 自动批改 --> G_eng
    E_eng_action -- 自动批改 --> H_eng
    E_eng_action -- 自动批改 --> I_eng 
    %% 也可能提交了空答案

    G_eng --> J_eng
    H_eng --> J_eng
    I_eng --> J_eng
    
    H_eng --> K_eng 
    %% 只有回答错误才有猜你想问
    K_eng --> L_eng
    K_eng --> M_eng
    L_eng --> N_eng
    M_eng --> N_eng
```

---
#### **[核心功能点5]: 解答题**

##### **[核心功能点5.1]: 解答题 - 初始状态**
###### 界面布局与核心UI元素
- **布局参考：**
    - `[解答题初始状态-键盘作答入口](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_EkLgbJkt5oeaKBxfb7Tcj4yenDf.png) - 仅关注布局`
    - `[解答题初始状态-拍照作答入口](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_Q1UZb24j7oWaejxFENCc1XZmn9g.png) - 仅关注布局`
    - `[勾画组件入口参考](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_GWmGbKabgoupdgxVeF0cSdg5nbh.png) - 仅关注布局` (此图为单选勾画，仅示意勾画组件存在)
- **布局原则：**
    - **整体界面布局 ([`解答题初始状态-键盘作答入口`](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_EkLgbJkt5oeaKBxfb7Tcj4yenDf.png) 解析, [`解答题初始状态-拍照作答入口`](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_Q1UZb24j7oWaejxFENCc1XZmn9g.png) 解析):** 全屏展示。顶部导航。主体左侧为题干区（包含题目描述、示意图），右侧为作答区（包含作答方式切换按钮“键盘输入”、“拍照作答”，以及对应方式的作答入口/提示）。底部为操作按钮。
    - **题干区 ([`解答题初始状态-键盘作答入口`](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_EkLgbJkt5oeaKBxfb7Tcj4yenDf.png) 解析):** 显示题型标签“解答题”、题目来源、题干文字描述及辅助理解的图片/图表。
    - **作答区 ([`解答题初始状态-键盘作答入口`](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_EkLgbJkt5oeaKBxfb7Tcj4yenDf.png) 解析, [`解答题初始状态-拍照作答入口`](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_Q1UZb24j7oWaejxFENCc1XZmn9g.png) 解析):**
        - **作答方式切换：** 提供“键盘输入”和“拍照作答”（默认）按钮。
        - **拍照作答模式（默认）：** 显示作答示例（如有）、拍照上传入口（相机图标+文字“拍照上传”，提示“最多可以传4张”）。
        - **键盘作答模式：** 显示文本输入框提示“点击输入答案”。
- **核心UI元素清单：**
    - (同单选题初始状态的核心UI元素，题型标签为“解答题”)
    - 作答方式切换按钮 (“键盘输入”, “拍照作答”)
    - 拍照上传入口 (相机图标, “拍照上传”文字, 上传数量提示)
    - 键盘作答文本输入框 (初始提示“点击输入答案”)

###### 功能需求与交互原则
- **功能描述：**
    - **状态判断：** 进入题目且未进行任何作答（未输入文字或上传图片）时为初始状态。
    - **题型校验：** 适用于语文、数学、英语、物理、化学、生物学科的非母子解答题。
    - **页面框架、退出学习、作答计时、题板结构（左题干右作答）、勾画组件：** （同单选题初始状态）
    - **题干区展示：** 显示“解答题”题型标签、题目来源、题干内容（文字、图片）。
    - **作答区交互（输入方式选择）：**
        - 单题内仅可选择一种最终提交方式作答，但切换时不应丢失另一种方式已输入的内容。
        - **切换作答方式时：**
            - 若当前作答方式下已有内容，切换时，界面展示新选择的作答方式的UI。
            - 键盘作答切换到拍照作答：已输入的文本内容在视觉上清空，但不实际删除，以便用户切回时恢复。
            - 拍照作答切换到键盘作答：已上传的图片不删除，以便用户切回时恢复。
        - **拍照作答（默认）：** 右侧作答区显示拍照作答入口。可展示作答示例图片。
        - **键盘作答：** 右侧作答区显示文本输入框，提示“点击输入答案”。
- **交互模式：**
    - **“不确定”按钮、 “提交”按钮：** （同单选题初始状态）
- **反馈原则：**
    - 切换作答方式时，界面清晰反映当前模式。

**优先级：** P0
**依赖关系：** 无

##### **[核心功能点5.2]: 解答题 - 作答状态**
###### 界面布局与核心UI元素
- **布局参考：**
    - `[解答题作答状态-已上传图片](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_ZupTbomF3oZI4jxuRhzcqQjcnPF.png) - 仅关注布局`
    - `[解答题作答状态-键盘输入内容](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_HlTBbPcf6o4K4vxxGlscpUlenWh.png) - 仅关注布局`
    - `[拍照作答-图片旋转/框选](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_RkOBbwIHzoVq1axoMMXcUPWynZf.png) - 仅关注布局`
    - `[拍照作答-上传状态](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_R6B1bPOmBoLL91xE84GcTmmMnfe.png) - 仅关注布局`
- **布局原则：**
    - **拍照作答模式 ([`解答题作答状态-已上传图片`](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_ZupTbomF3oZI4jxuRhzcqQjcnPF.png) 解析):** 右侧作答区显示已上传的图片缩略图（带删除按钮）和继续拍照上传的入口。
    - **键盘作答模式 ([`解答题作答状态-键盘输入内容`](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_HlTBbPcf6o4K4vxxGlscpUlenWh.png) 解析):** 右侧作答区显示一个可滚动的文本输入区域，用于输入和展示较多文字。调起虚拟键盘。
    - **图片处理与上传状态：** 同填空题（自评）作答状态。
- **核心UI元素清单：**
    - (同初始状态的核心UI元素)
    - 已上传图片缩略图列表 (拍照作答时)
    - 图片删除按钮 (拍照作答时)
    - 多行文本输入区域 (键盘作答时)
    - 虚拟键盘
    - 图片编辑界面 (拍照后)
    - 上传状态指示器
    - 提交确认弹窗 (未作答时)

###### 功能需求与交互原则
- **功能描述：**
    - **状态判断：** 当进入题目并有作答内容（输入了文字或上传了图片）时，为作答状态。
    - **批改方式：** 自评，核对答案。
    - **作答方式（拍照作答 - 默认）：**
        - **入口：** 选择“拍照作答”方式。
        - **最大上传数量：** 4张。
        - **交互（拍摄、编辑、上传、查看、删除）：** （同填空题（自评）的拍照作答交互，但拍摄界面辅助线为横竖九宫格）。
        - **已上传内容检测：** （同填空题（自评）的拍照作答内容检测）。
    - **作答方式（键盘作答）：**
        - **入口：** 选择“键盘作答”方式。右侧作答区提示“点击输入答案”。
        - **交互：**
            - 点击作答区域，该区域获得焦点并显示选中状态，自动调起键盘。
            - 支持用户通过键盘输入文字，内容直接在作答区的文本输入框内展示。当输入文字超过一行时，文本框自动换行以完整展示内容，并支持滚动查看（若内容超出可视区域，需明确最大展示行数或高度）。
            - 点击键盘区域外的其他区域时，键盘收起，作答区域取消选中状态，系统自动保存输入的文字。
- **交互模式：**
    - **“不确定”按钮：** （同单选题作答状态）
    - **“提交”按钮：**
        - **按钮状态：** 当有作答内容（文字或图片）时，此按钮变为可点击状态。
        - **交互（需校验）：**
            - **若有作答内容：** 点击后提交作答数据，题目进入解析状态。
            - **若无作答内容（未输入文字且未上传图片）：** 点击时，弹窗提示“本题未作答，确认提交吗？”。操作按钮：“我再想想”（关闭弹窗），“继续提交”（提交作答，进入解析状态）。
- **反馈原则：**
    - 作答方式切换时，界面清晰反映当前作答模式。
    - 图片上传有明确的状态反馈。
    - 提交未作答的内容时，有确认弹窗。

**优先级：** P0
**依赖关系：** [核心功能点5.1]: 解答题 - 初始状态

##### **[核心功能点5.3]: 解答题 - 解析状态**
###### 界面布局与核心UI元素
- **布局参考：**
    - `[解答题解析状态-自评](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_TcU4bQiQEoeDBsxwGdUc72yPnrb.png) - 仅关注布局`
    - `[自评按钮样式参考](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_GQA1bq8YEoUpTCxLeFbcRT45npd.png) - 仅关注布局`
- **布局原则：**
    - **整体布局 ([`解答题解析状态-自评`](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_TcU4bQiQEoeDBsxwGdUc72yPnrb.png) 解析):** 左侧题干区。右侧上方展示“正确答案”（通常为图片或富文本），下方展示“我的答案”（用户上传的图片或输入的文字），再下方为自评模块（标题、副标题、自评按钮组）。之后是题目解析和“问一问”入口。底部为“继续”按钮。
    - **自评模块 ([`解答题解析状态-自评`](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_TcU4bQiQEoeDBsxwGdUc72yPnrb.png) 解析, [`自评按钮样式参考`](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_GQA1bq8YEoUpTCxLeFbcRT45npd.png) 解析):** 清晰展示自评选项。
- **核心UI元素清单：**
    - (题干区元素同初始状态)
    - 正确答案展示区 (图片或富文本)
    - “我的答案”展示区 (用户上传的图片列表，或用户输入的文本内容)
    - 自评模块标题 (“核对答案”) 及副标题
    - 自评按钮组 (“✔ 我答对了”, “✖ 部分答对”, “✖ 我答错了”)
    - 题目解析文本区
    - “问一问”入口图标
    - “继续”按钮

###### 功能需求与交互原则
- **功能描述：**
    - **状态判断：**
        - 当用户在作答状态下提交答案后，进入解析状态。
        - 当用户在初始或作答状态下点击“不确定”后，再点击“放弃作答”，则进入解析状态。
    - **正确答案展示：** 显示该题目的标准答案（数据来自题库对应字段，通常为图片或富文本）。
    - **我的答案展示：**
        - 若用户通过拍照作答，则显示已上传的图片列表，图片支持点击放大查看。
        - 若用户通过键盘作答，则显示已输入的文字内容。
    - **自评模块：**
        - **标题：** “核对答案”。
        - **副标题：** “对待错误是进步的第一步，相信你会认真对待!”。
        - **选项 ([`自评按钮样式参考`](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_GQA1bq8YEoUpTCxLeFbcRT45npd.png)):** 提供“我答对了”、“部分答对”、“我答错了”三个自评按钮。
        - **交互：** 用户点击不同选项按钮，按钮显示对应的选中效果。此为单选逻辑，用户可以点击自评选项进行自评结果的切换。
        - **提交与反馈：** 用户完成自评（选择一个状态）后，该自评结果被记录。原始PRD描述“提交后展示自评结果，此时自评结果仍然可修改”，然后“点击【继续】进入下一题/下一课”。这里理解为用户选择自评状态后，该状态即为当前自评结果，在点击“继续”离开本题前可修改。
    - **题目解析展示、问一问功能：** （同单选题解析状态）
- **交互模式：**
    - **“继续”按钮：**
        - **出现时机：** 题目进入解析状态后显示。
        - **按钮状态：** 用户必须完成自评（即在自评按钮组中选择一个状态）后，此按钮才变为可点击。若未完成自评，点击“继续”按钮时，自评选项按钮组应有抖动等提示效果。
        - **交互：** 点击后进入下一题或下一节学习内容。
- **反馈原则：**
    - 自评选项有明确的选中状态。
    - 未完成自评时，对“继续”操作有明确的引导和限制。

**优先级：** P0
**依赖关系：** [核心功能点5.2]: 解答题 - 作答状态

##### 用户场景与故事 (针对此核心功能点: 解答题)

###### [场景1: 学生使用拍照作答解答题，并进行自评]
作为**学生**，我需要**通过拍照上传手写的解答过程来完成解答题，并在查看标准答案后对自己的解答进行评价**以解决**复杂解题步骤键盘输入困难的问题，并能主动反思解题质量**，从而**便捷地提交答案，并通过自我评估加深对知识的理解和应用**。

**前端界面与交互关键步骤：**
1.  **[用户操作1: 查看题目并选择拍照作答]** 学生进入解答题，选择拍照作答模式。
    *   **界面变化:** 右侧作答区显示拍照上传入口。
    *   **即时反馈:** 无。
2.  **[用户操作2: 拍照并上传答案图片]** 学生拍摄手写答案，进行编辑（如裁剪、旋转）后上传一张或多张图片。
    *   **界面变化:** 图片缩略图显示在作答区，有上传状态提示。
    *   **即时反馈:** 上传成功或失败的提示。
3.  **[用户操作3: 点击提交按钮]** 学生确认图片无误后提交。
    *   **界面变化:** 计时器停止。界面进入解析状态，显示标准答案、学生上传的图片、自评按钮组。
    *   **即时反馈:** 界面切换。
4.  **[用户操作4: 对照答案进行自评]** 学生查看标准答案和自己的图片答案，选择了“部分答对”。
    *   **界面变化:** “部分答对”按钮高亮选中，“继续”按钮激活。
    *   **即时反馈:** 自评结果实时显示。
5.  **[用户操作5: 点击“继续”按钮]** 学生完成自评，点击“继续”。
    *   **界面变化:** 跳转到下一题。
    *   **即时反馈:** 界面跳转。

**场景细节补充：**
*   **前置条件:** 用户在解答题作答流程中。
*   **期望结果:** 用户能顺利通过拍照上传答案，并完成自评。
*   **异常与边界情况:**
    *   上传图片内容异常：提示“请上传和题目相关的内容”。
    *   未完成自评点击“继续”：自评按钮组抖动提示。

**优先级：** 高
**依赖关系：** 无

```mermaid
flowchart LR
    subgraph 初始状态_ans
        A1_ans[查看题目\n（题干＋作答区）]
    end

    subgraph 作答状态_ans
        B1_ans[作答方式选择]
        B2_ans[拍照上传]
        B3_ans[键盘输入]
        B4_ans[不确定]
        B5_ans[放弃作答\n（二次确认）]
        B6_ans[提交]
        B6_ans_confirm_empty[弹窗：未作答\n确认提交？]
    end

    subgraph 解析状态_ans
        C1_ans[自评模块\n（标准答案、我的答案、自评按钮）]
        C2_ans[回答正确]
        C3_ans[部分答对]
        C4_ans[回答错误]
        C5_ans[查看答案、题目解析\n（此部分内容已包含在自评模块中或作为其前置）]
        C6_ans[猜你想问]
        C7_ans[点击推荐问题]
        C8_ans[点击自由提问]
        C9_ans[答疑 chat]
        C10_ans_continue_btn_disabled[继续按钮（未完成自评时禁用）]
        C10_ans_continue_btn_enabled[继续按钮（自评完成后激活）]
    end

    A1_ans --> B1_ans
    A1_ans --> B4_ans

    B1_ans -- 选择拍照 --> B2_ans
    B1_ans -- 选择键盘 --> B3_ans

    B2_ans --> B6_ans_check_empty{未上传图片？}
    B3_ans --> B6_ans_check_empty{未输入文字？}

    B6_ans_check_empty -- 是 --> B6_ans_confirm_empty
    B6_ans_check_empty -- 否 --> B6_ans_action[执行提交]
    B6_ans_confirm_empty -- 继续提交 --> B6_ans_action
    B6_ans_confirm_empty -- 我再想想 --> B1_ans

    B4_ans -.-> B1_ans
    B4_ans --> B5_ans

    %% 放弃作答也进入自评，我的答案为未作答
    B5_ans --> C1_ans

    B6_ans_action --> C1_ans

    C1_ans -- 对答案自评 --> C2_ans
    C1_ans -- 对答案自评 --> C3_ans
    C1_ans -- 对答案自评 --> C4_ans

    C1_ans -- 完成自评 --> C10_ans_continue_btn_enabled
    C1_ans -- 未完成自评 ＆ 用户点击继续 --> C10_ans_continue_btn_disabled

    C1_ans --> C5_ans_view_parsing[查看题目解析部分]
    C5_ans_view_parsing --> C6_ans

    C6_ans --> C7_ans
    C6_ans --> C8_ans
    C7_ans --> C9_ans
    C8_ans --> C9_ans

```

---
### 2.2 辅助功能
- **[辅助功能点1]: 勾画组件** 为 [核心功能点1: 单选题], [核心功能点2: 多选题], [核心功能点3: 填空题 - 其他学科（自评）], [核心功能点4: 填空题 - 英语（自动批改）], [核心功能点5: 解答题] 提供支持
    - **功能详述与前端呈现:**
        - **入口：** 各题型作答界面（初始状态、作答状态）均常驻悬浮“勾画”按钮。
        - **界面：** 点击“勾画”按钮唤起勾画工具栏。工具栏包含：
            - 笔迹颜色调整：支持蓝色、红色、黑色。
            - 笔迹粗细调整：支持细、中等、粗三种级别。
            - 橡皮擦工具：支持细、中等、粗三种擦除范围。
            - 撤回操作。
            - 恢复操作。
            - 一键清空所有勾画内容。
            - “完成”按钮。
        - **交互：**
            - 用户选择颜色、粗细后，可在题目题干区域进行自由勾画、标记。
            - 勾画内容实时显示在题干上。
            - 点击“完成”按钮，收起勾画工具栏，勾画内容自动保存并持续显示在题干上。
            - 勾画内容随题目存在，在解析状态也应可见。
    - **Mermaid图示 (勾画组件交互流程):**
      ```mermaid
      flowchart TD
          A[用户在答题界面] --> B{点击“勾画”悬浮按钮};
          B --> C[勾画工具栏展开];
          C --> D{选择颜色/粗细/工具};
          D --> E[在题干区域勾画];
          E --> F[笔迹实时显示];
          F --> G{操作撤销/恢复/清空};
          G -- 撤销 --> E;
          G -- 恢复 --> E;
          G -- 清空 --> E; 
          %% 清空后也是在题干操作
          F --> H{点击“完成”按钮};
          H --> I[勾画工具栏收起];
          I --> J[勾画内容保存并显示在题干上];
          J --> A; 
          %% 返回答题界面继续操作
      ```
- **[辅助功能点2]: 问一问（答疑组件）** 为 [核心功能点1: 单选题], [核心功能点2: 多选题], [核心功能点3: 填空题 - 其他学科（自评）], [核心功能点4: 填空题 - 英语（自动批改）], [核心功能点5: 解答题] 的解析状态提供支持
    - **功能详述与前端呈现:**
        - **入口：** 各题型进入解析状态后，在界面右上角展示“问一问”图标入口。英语填空题仅在回答错误后展示此入口。
        - **界面：** 点击“问一问”图标后，以模态弹窗形式展示答疑组件。组件包含：
            - 关闭按钮。
            - 对话记录区域：展示用户提问气泡和AI（Dolli）回复气泡。
            - “猜你想问”区域：可展示由模型生成的与当前题目相关的推荐问题（最多3个，可为0）。
            - 输入区域：包含文本输入框（提示文字如“请输入你的问题”）和发送按钮。
        - **交互：**
            - 用户可点击“猜你想问”中的推荐问题，该问题将作为用户提问内容发送。
            - 用户可在文本输入框中自由输入问题，点击发送按钮提交。
            - 对话由用户发起首轮。
            - AI（Dolli）对用户提问进行回复，对话记录滚动更新。
    - **Mermaid图示 (问一问交互流程):**
      ```mermaid
      flowchart TD
          A[用户在题目解析状态] --> B{点击“问一问”图标};
          B --> C[弹出答疑组件];
          C --> D{选择操作};
          D -- 点击推荐问题 --> E[推荐问题作为用户提问发送];
          D -- 输入自定义问题 --> F[用户在输入框输入问题];
          F --> G{点击发送按钮};
          E --> H[AI（Dolli）回复];
          G --> H;
          H --> I[对话记录更新];
          I --> D; 
          %% 用户可继续提问或关闭
          C -- 点击关闭按钮 --> J[关闭答疑组件];
      ```

### 2.3 非本期功能
- **[功能点1]: 母子题型支持**, 原因：一期优先保障基础高频题型，母子题结构复杂，交互和数据依赖较高，建议 **二期** 再考虑。
- **[功能点2]: 特殊题型（完形填空、阅读理解、七选五等）支持**, 原因：这些题型通常涉及更复杂的篇章内容展示和特定交互逻辑，一期集中资源攻克基础题型，建议 **三期** 再考虑。

## 3. 业务流程图 (题目类型规划及一期范围)

```mermaid
graph TD
    A[题目类型] --> B(选择题<br/>一期);
    A --> C(填空题<br/>一期);
    A --> D(解答题<br/>一期);
    A --> E(子母题<br/>二期);
    A --> F(特殊题型<br/>三期);

    B --> B1(单选题);
    B --> B2(多选题);

    C --> C1(英语学科<br/>支持自动批改);
    C --> C2(其他学科<br/>自评);

    F --> F1(完形填空);
    F --> F2(阅读理解);
    F --> F3(七选五等);
    %% F --> F3(作图题);
    %% F --> F4(连线题);
    %% F --> F5(证明题);
```

## 4. 性能与安全需求

### 4.1 性能需求
-   **响应时间：**
    -   题目加载（包含题干、选项、图片等）主要内容应在 **2秒** 内完成 (95th percentile)。
    -   用户选择选项、点击提交等关键交互操作的界面反馈应在 **200毫秒** 内。
    -   勾画操作应流畅，笔迹无明显延迟。
    -   图片上传（解答题、填空题拍照作答）应有清晰的进度提示，大图片上传不应导致界面长时间卡顿。
-   **前端性能感知：**
    -   **图片懒加载：** 对于题干或选项中包含的多张图片，考虑使用懒加载技术优化首屏加载速度。
    -   **作答计时器：** 计时器更新不应引起不必要的界面重绘或性能损耗。
    -   **勾画内容：** 勾画内容的渲染和保存不应显著影响答题流畅性。

### 4.2 安全需求
-   **数据传输：** 所有与后端交互的数据（题目内容、用户答案、用户信息等）必须使用 HTTPS 加密传输。
-   **输入校验：**
    -   填空题、解答题的键盘输入内容，前端进行基础的长度限制和特殊字符过滤，防止XSS等常见注入风险。后端必须进行二次严格校验。
-   **图片上传安全：**
    -   解答题、填空题的图片上传功能，需对上传文件类型、大小进行限制。
    -   后端需对上传图片进行安全扫描和内容审核（原始PRD中提到“已上传内容检测：接入大模型进行识别。识别内容为异常时，不保存图片”）。
-   **权限控制：** （虽然本文档主要为学生端，但需考虑）用户只能访问和操作授权给其的题目和功能。

## 5. 验收标准

### 5.1 功能验收
-   **[核心功能点1：单选题]：**
    -   **用户场景：** 当用户在单选题初始状态下，选择一个选项，点击提交。
    -   **界面与交互：**
        -   选中选项有高亮状态。
        -   提交按钮在选中选项后变为可点击。
        -   首次提交错误后，选项重置，允许二次作答。
        -   进入解析状态后，正确答案以绿色标记，错误答案以红色标记，显示题目解析和选项占比。
    -   **期望结果：** 系统能正确判断答案对错，并根据首次/二次作答结果流转到正确的状态（作答或解析），解析内容正确显示。
-   **[核心功能点2：多选题]：**
    -   **用户场景：** 用户选择一个或多个选项，点击提交。
    -   **界面与交互：**
        -   选中选项有高亮状态，可多选。
        -   提交时若只选一项，有二次确认弹窗。
        -   进入解析状态后，正确答案、用户错误选项、漏选选项均有明确标记。
    -   **期望结果：** 系统能正确判断答案（全对、部分对、全错），并根据首次/二次作答结果流转，解析内容正确显示。
-   **[核心功能点3：填空题 - 其他学科（自评）]：**
    -   **用户场景：** 用户通过键盘输入或拍照上传方式作答，提交后进入自评。
    -   **界面与交互：**
        -   键盘输入/拍照上传流程顺畅，内容能正确显示。
        -   提交未完全作答的题目时有确认提示。
        -   解析状态下，能看到标准答案和自己的答案，并能顺利完成所有空的自评。
        -   自评结果在选项卡（若有）和“继续”按钮可用性上正确反映。
    -   **期望结果：** 用户作答和自评流程完整，自评结果被正确记录。
-   **[核心功能点4: 填空题 - 英语（自动批改）]：**
    -   **用户场景：** 用户通过键盘输入作答，提交后系统自动批改。
    -   **界面与交互：**
        -   键盘输入流程顺畅。
        -   解析状态下，系统自动批改结果（对/错）在题干和答案区正确标记。
    -   **期望结果：** 系统自动批改准确，用户能清晰看到批改结果和解析。
-   **[核心功能点5: 解答题]：**
    -   **用户场景：** 用户通过键盘输入或拍照上传方式作答，提交后进入自评。
    -   **界面与交互：**
        -   键盘输入/拍照上传流程顺畅。
        -   解析状态下，能看到标准答案和自己的答案，并能顺利完成自评。
        -   “继续”按钮在自评完成后才能点击。
    -   **期望结果：** 用户作答和自评流程完整，自评结果被正确记录。
-   **[辅助功能：勾画组件]：**
    -   **用户场景：** 用户在任意题型作答时使用勾画功能。
    -   **界面与交互：** 勾画工具栏能正常唤起和收起，勾画操作流畅，内容能正确保存和显示。
    -   **期望结果：** 勾画功能可用且不影响正常答题。
-   **[辅助功能：问一问]：**
    -   **用户场景：** 用户在任意题型解析状态下使用问一问功能。
    -   **界面与交互：** 答疑组件能正常弹出，用户能发送问题并收到回复（或看到推荐问题）。
    -   **期望结果：** 问一问功能可用。

### 5.2 性能验收
-   **响应时间：**
    -   **测试用例1:** 题目加载（包含所有文本、图片资源）95%的请求应在2秒内完成。
    -   **测试用例2:** 用户点击选项、提交、切换作答方式等交互操作，界面响应时间应小于200毫秒。
-   **前端性能感知验收：**
    -   **测试用例1 (勾画):** 进行连续勾画操作，笔迹显示无明显卡顿或延迟。
    -   **测试用例2 (图片查看):** 点击放大查看题干或选项中的图片，图片应能快速加载显示。

### 5.3 安全验收
-   **输入校验：**
    -   **测试用例1 (XSS):** 在填空题、解答题的文本输入框中输入 ``<script>alert('xss')</script>``，提交后脚本未执行，特殊字符被转义或正确处理。
-   **图片上传：**
    -   **测试用例1 (类型/大小):** 尝试上传非图片文件或超大图片，前端应有提示并阻止上传。
    -   **测试用例2 (内容安全):** （后端配合）上传包含异常内容的图片，应被拦截或有相应处理。

## 6. 其他需求

### 6.1 可用性与体验需求
-   **界面友好与直观性：**
    -   各题型作答流程清晰，用户引导明确。
    -   按钮、图标、提示信息易于理解。
    -   整体视觉风格保持一致。
-   **容错处理：**
    -   当用户进行非常规操作或遇到网络异常时，系统应提供清晰、友好的错误提示，并引导用户进行后续操作（如重试）。
    -   例如，图片上传失败时，有明确的“上传失败，点击重试”提示。
-   **兼容性：**
    -   **浏览器：** 需支持最新版本的 Chrome, Firefox, Safari, Edge 浏览器（若为Web应用）。（原始PRD未指明平台，此处为通用考虑）
    -   **响应式布局：** 原始PRD提及“仅支持全屏展示”，暗示可能为特定设备或应用环境。若需适配不同屏幕尺寸，应补充具体布局原则。
-   **可访问性 (A11y)：**
    -   关键交互元素（如选项、按钮）应支持键盘操作。
    -   图片内容（题干图、选项图）需提供有意义的 `alt` 文本（若适用）。
    -   颜色对比度应考虑可读性。
-   **交互一致性：**
    -   相似操作（如提交、不确定、继续）在不同题型间应保持统一的交互模式和视觉风格。
    -   勾画组件、问一问组件的体验应一致。

### 6.2 维护性需求
-   **配置管理：** （原始PRD未明确，可考虑）如选项占比的冷启动阈值（20次）、每日更新策略等是否需要后台配置。
-   **监控告警：** 前端应集成错误监控系统（如Sentry），捕获JS执行错误、API请求异常等，并能及时告警。
-   **日志管理：** 前端应记录关键用户行为路径（如进入题目、选择答案、提交、进入解析）、重要API请求与响应（脱敏后）、JS错误详情，辅助问题排查。

## 7. 用户反馈和迭代计划

### 7.1 用户反馈机制
-   （原始PRD未明确，建议补充）应用内应提供用户反馈入口，方便用户提交使用问题、意见和建议。

### 7.2 迭代计划
-   **一期：** 实现单选题、多选题、填空题（英语自动批改、其他学科自评）、解答题的核心作答与解析流程。
-   **二期：** 支持母子题。
-   **三期：** 支持其他特殊题型（完形填空、阅读理解、七选五等）。

## 8. 需求检查清单
使用下面的检查清单，确保原始需求都被正确地包含和体现在整理后的需求文档中。

| 原始需求 (来自原始PRD的详细条目) | 对应优化后需求点/章节 (在本PRD中) | 完整性 | 正确性 | 一致性 | 可验证性 (是否有验收标准) | 可跟踪性 (是否有优先级/依赖) | UI/UX明确性 | 备注 |
| -------------------------------- | ----------------------------------- | ------ | ------ | ------ | ------------------------- | --------------------------- | ----------- | ---- |
| 单选题-初始状态需求 | 2.1-[核心功能点1.1] / 5.1 | [✅]    | [✅]    | [✅]    | [✅]                       | [✅]                         | [✅]         |      |
| 单选题-作答状态需求 | 2.1-[核心功能点1.2] / 5.1 | [✅]    | [✅]    | [✅]    | [✅]                       | [✅]                         | [✅]         |      |
| 单选题-解析状态需求 | 2.1-[核心功能点1.3] / 5.1 | [✅]    | [✅]    | [✅]    | [✅]                       | [✅]                         | [✅]         |      |
| 多选题-初始状态需求 | 2.1-[核心功能点2.1] / 5.1 | [✅]    | [✅]    | [✅]    | [✅]                       | [✅]                         | [✅]         |      |
| 多选题-作答状态需求 | 2.1-[核心功能点2.2] / 5.1 | [✅]    | [✅]    | [✅]    | [✅]                       | [✅]                         | [✅]         |      |
| 多选题-解析状态需求 | 2.1-[核心功能点2.3] / 5.1 | [✅]    | [✅]    | [✅]    | [✅]                       | [✅]                         | [✅]         |      |
| 填空题(自评)-初始状态 | 2.1-[核心功能点3.1] / 5.1 | [✅]    | [✅]    | [✅]    | [✅]                       | [✅]                         | [✅]         |      |
| 填空题(自评)-作答状态 | 2.1-[核心功能点3.2] / 5.1 | [✅]    | [✅]    | [✅]    | [✅]                       | [✅]                         | [✅]         |      |
| 填空题(自评)-解析状态 | 2.1-[核心功能点3.3] / 5.1 | [✅]    | [✅]    | [✅]    | [✅]                       | [✅]                         | [✅]         |      |
| 英语填空题(自动批改)-初始状态 | 2.1-[核心功能点4.1] / 5.1 | [✅]    | [✅]    | [✅]    | [✅]                       | [✅]                         | [✅]         |      |
| 英语填空题(自动批改)-作答状态 | 2.1-[核心功能点4.2] / 5.1 | [✅]    | [✅]    | [✅]    | [✅]                       | [✅]                         | [✅]         |      |
| 英语填空题(自动批改)-解析状态 | 2.1-[核心功能点4.3] / 5.1 | [✅]    | [✅]    | [✅]    | [✅]                       | [✅]                         | [✅]         |      |
| 解答题-初始状态 | 2.1-[核心功能点5.1] / 5.1 | [✅]    | [✅]    | [✅]    | [✅]                       | [✅]                         | [✅]         |      |
| 解答题-作答状态 | 2.1-[核心功能点5.2] / 5.1 | [✅]    | [✅]    | [✅]    | [✅]                       | [✅]                         | [✅]         |      |
| 解答题-解析状态 | 2.1-[核心功能点5.3] / 5.1 | [✅]    | [✅]    | [✅]    | [✅]                       | [✅]                         | [✅]         |      |
| 勾画组件需求 | 2.2-[辅助功能点1] / 5.1 | [✅]    | [✅]    | [✅]    | [✅]                       | [N/A]                       | [✅]         |      |
| 问一问需求 | 2.2-[辅助功能点2] / 5.1 | [✅]    | [✅]    | [✅]    | [✅]                       | [N/A]                       | [✅]         |      |

-   **完整性：** 原始需求是否都有对应的优化后需求点，无遗漏。
-   **正确性：** 优化后需求描述是否准确表达了原始需求的意图。
-   **一致性：** 优化后需求之间是否有冲突或重复。
-   **可验证性：** 优化后需求是否有明确的验收标准（对应5. 验收标准）。
-   **可跟踪性：** 优化后需求是否有明确的优先级和依赖关系。
-   **UI/UX明确性：** 优化后需求是否清晰描述了前端界面、交互和用户体验细节。

✅表示通过；❌表示未通过；⚠️表示描述正确，但UI/UX细节、验收标准或图示等仍需与PM进一步沟通完善；N/A表示不适用。每个需求点都需要填写。

## 9. 附录

### 9.1 原型图/设计稿链接
- 视觉稿：[Figma: AI课体验版](https://www.figma.com/design/j6wNSQxDFuZHscu1SVMuIA/AI%E8%AF%BE%E4%BD%93%E9%AA%8C%E7%89%88?node-id=2922-27245&t=tMaoye5c0E4lU71L-0)
- 交互稿：(原始PRD未提供)
- 动效：(原始PRD未提供)

### 9.2 术语表
-   **PRD:** Product Requirements Document (产品需求文档)
-   **UI:** User Interface (用户界面)
-   **UX:** User Experience (用户体验)
-   **OCR:** Optical Character Recognition (光学字符识别) - 图片解析中提及
-   **LCP:** Largest Contentful Paint (最大内容绘制) - 性能指标
-   **FID:** First Input Delay (首次输入延迟) - 性能指标
-   **INP:** Interaction to Next Paint (交互到下次绘制) - 性能指标
-   **A11y:** Accessibility (可访问性)
-   **WCAG:** Web Content Accessibility Guidelines (Web内容可访问性指南)

### 9.3 参考资料
-   [原始需求文档：PRD - 学生端 - 题型组件 - 一期.md](ai-doc-format/tools/Format-PRD-学生端-题型组件-一期.md)
-   关联需求：[PRD - 学生端 - 巩固练习](https://wcng60ba718p.feishu.cn/wiki/EClyw7T06iRxYpkK7PwcJcLHn4f) (来自原始PRD)

--- 等待您的命令，指挥官