# 前端产品需求文档：AI课中 - 课程框架与文档组件 V1.1

## 1. 需求背景与目标

### 1.1 需求目标
通过本次需求的实现，旨在显著**提升AI课中学习体验的基础质量和流畅性**，解决前期Demo版本中收集到的关键交互与体验问题，为用户提供更接近1v1真人直播课的沉浸式、个性化和智能化课堂感受，并为后续引入更多互动玩法奠定坚实基础。

### 1.2 用户价值
- 对于 **全量使用新AI课的学生用户**，解决了 **板书与讲解不同步、缺乏快捷操作、模式切换不清晰等痛点**，带来了 **更聚焦、更流畅、更自主的学习体验，提升学习效率和课程满意度**。
- 对于 **课程设计和运营团队**，满足了 **迭代优化课中核心体验的诉求**，实现了 **提升课程口碑和用户粘性的潜力**。

## 2. 功能范围

### 2.1 核心功能
#### **核心功能点1: 课程框架升级**

**用户价值与场景:** 满足学生在AI课程开始、进行中、结束等关键节点的体验需求，解决原有框架下仪式感不足、进度切换不便、学习反馈不清晰等痛点，为用户带来更完整、流畅、有激励性的学习闭环。

**功能详述与前端呈现:**
- **主要交互流程:**
    1.  用户进入课程，首先看到开场页动效与信息展示。
    2.  开场页播放完毕后，自动进入课程第一小节。
    3.  用户在学习过程中，可通过课程进度条在不同组件（文档、练习）间自由跳转。
    4.  用户完成所有组件学习后，进入结算页，查看学习报告、反馈课程。
    5.  用户可在学习过程中随时点击退出按钮，系统记录进度后返回课程入口；再次进入时可从上次退出的节点继续。
- **界面布局与元素:**
    *   **开场页:** 全屏展示，包含学科背景图、课程章节号、课程序号、课程名称。IP动效和音频居中或主要区域播放。
        *   状态：加载中（可选，弱网）、播放中、播放完毕自动跳转。
    *   **结算页:** 包含庆祝动效（IP角色）、鼓励文案（主副文案）、学习表现（学习用时、答题数、正确率）、外化掌握度（进度、本次提升）、答题情况概览、推荐学习模块、课程反馈模块、底部操作按钮（回看课程、完成）。
        *   状态：各模块数据加载中、数据显示正常、反馈提交中、反馈提交成功/失败。
    *   **课程进度条:** 通常位于界面顶部或通过特定按钮唤出。以列表或横向滚动条形式展示所有课程组件（文档/练习）。
        *   状态：当前学习中组件高亮、已解锁组件可点击、待解锁组件置灰或不可点击。点击已解锁组件，可跳转。
    *   **退出确认弹窗（若有）:** 居中模态弹窗，提示用户“是否确认退出课程？”，包含“确认退出”和“继续学习”按钮。
- **用户操作与反馈:**
    *   **开场页:** 用户被动接收信息和动效，无需操作。完成后自动转场。
    *   **结算页:**
        *   用户可滑动查看完整结算信息。
        *   点击“查看详情”（答题情况），跳转至题目列表页。
        *   点击“去练习”（推荐学习），进入对应练习模块。
        *   点击课程反馈表情，弹出详细反馈表单；选择标签或输入文字后，“提交”按钮变为可点击；点击提交，toast提示“感谢反馈...”。
        *   点击“回看课程”，返回课程内容，所有组件解锁。
        *   点击“完成”，返回课程入口页。
    *   **课程进度:**
        *   点击课程进度按钮，进度条展示，当前内容播放暂停。
        *   点击已解锁组件图标/名称，跳转至该组件对应内容，进度条收起，内容从上次学习位置或特定规则开始。
        *   点击屏幕其他区域，进度条收起，当前内容继续播放。
    *   **退出课程:** 点击退出按钮，系统保存进度，返回课程列表。
- **数据展示与更新:**
    *   **结算页数据:** 学习用时、答题数、正确率等均为本次学习会话的统计数据。掌握度会根据算法更新并显示提升值。推荐学习内容根据用户表现动态生成。
    *   **课程进度:** 用户切换到已做过的练习模块时，直接展示历史答题记录。切换到文档模块，从上次学习位置开始。

**优先级：** P0
**依赖关系：** 无

##### 用户场景与故事 (针对此核心功能点)

###### 场景1: 新生小明首次进入AI数学课第一章第一节
作为一个 **AI课程初体验的学生（小明）**，我希望能够 **在课程开始时有一个富有仪式感的开场，清晰了解课程标题**，这样我就可以 **快速进入学习状态，并对即将学习的内容有所预期**。目前的痛点是 **直接进入内容可能有些突兀，缺乏引导**，如果能够 **有一个精美的开场动画和明确的课程信息展示**，对我的学习专注度和第一印象会有很大帮助。

**前端界面与交互关键步骤：**
1.  **用户操作1:** 小明点击进入“第一章 第一节 复数的概念”。
    * **界面变化:** 屏幕展示该节课的开场页，背景为数学学科相关的视觉元素，依次显示“第一章”、“1.1”、“复数的概念”等文字信息。一个可爱的IP角色动画（如小兔子）开始播放，并伴有欢迎音效和简短的开场白语音。
    * **即时反馈:** 视觉和听觉的同步呈现，营造正式上课的氛围。
2.  **用户操作2:** 开场IP动效和音频播放完毕。
    * **界面变化:** 开场页自动消失，平滑过渡到该课程的第一个学习组件（例如，一个文档组件）。
    * **即时反馈:** 无缝衔接，小明可以开始正式学习内容。

**场景细节补充：**
* **前置条件:** 小明已登录，并选择了要学习的课程。
* **期望结果:** 小明顺利观看到完整的开场页，并自动进入课程的第一个学习环节。
* **异常与边界情况:**
    *   网络较差导致动效加载缓慢：⚠️应有加载状态提示，或先展示静态信息，动效后加载。
    *   用户在开场页播放时尝试退出：⚠️行为应与课中退出逻辑一致或不允许退出。

**优先级：** 高
**依赖关系：** 无

**Mermaid图示 (开场页流程):**
  ```mermaid
  flowchart TD
    A[用户点击进入课程单元] --> B{加载开场页资源};
    B -- 成功 --> C[展示课程章节/名称信息];
    C --> D[播放IP动效和音频];
    D -- 播放完成 --> E[自动跳转到课程第一个组件];
    B -- 失败/超时 --> F[显示静态开场信息或错误提示];
    F --> E;
  ```

###### 场景2: 学生小红完成一节课的学习，查看学习成果
作为一个 **完成了AI课程一个单元学习的学生（小红）**，我希望能够 **清晰地看到自己本次学习的表现总结，包括用时、答题情况、知识点掌握度的变化，并得到一些鼓励或后续学习建议**，这样我就可以 **了解自己的学习效果，更有针对性地改进，并保持学习动力**。目前的痛点是 **学完就结束了，没有明确的反馈和下一步指引，感觉学习效果不直观**，如果能够 **有一个包含丰富信息的结算页，并能根据我的表现给出个性化反馈**，对我的学习规划和持续投入会有很大帮助。

**前端界面与交互关键步骤：**
1.  **用户操作1:** 小红完成了本节课的最后一个学习组件。
    * **界面变化:** 自动跳转到结算页。页面顶部出现庆祝动效（例如，一个IP角色撒花）和鼓励文案（例如，“太棒了！完成了本次学习！”）。
    * **即时反馈:** 积极的视觉和文字反馈，给予成就感。
2.  **用户操作2:** 小红向下滑动结算页，查看详细内容。
    * **界面变化:** 依次展示“学习表现”（学习用时45分钟，答题27题，正确率85%）、“知识点掌握度”（当前掌握度75%，本次提升15%）、“答题情况”（简要的题目对错概览，如正确题号列表和错误题号列表，并有“查看详情”按钮）、“推荐学习”（例如，“巩固练习：约5题”并有“去练习”按钮）、“课程反馈”（“本节课感受如何？”及五个表情符号）。
    * **即时反馈:** 各项学习数据清晰呈现。
3.  **用户操作3:** 小红点击“答题情况”下的“查看详情”。
    * **界面变化:** 跳转到题目列表页面，展示每道题的题干、作答情况、正确答案和解析入口。
    * **即时反馈:** 提供深入分析答题表现的途径。
4.  **用户操作4:** 小红返回结算页，点击“推荐学习”中的“去练习”。
    * **界面变化:** 进入推荐的练习模块。
    * **即时反馈:** 开始针对性巩固。
5.  **用户操作5:** 小红返回结算页，点击“课程反馈”中的“满意”表情。
    * **界面变化:** 弹出反馈详情弹窗，包含针对“讲解”、“解析”、“其他”等方面的标签选项和文字输入框。
    * **即时反馈:** 提供结构化和开放式的反馈渠道。小红选择标签并提交后，弹窗关闭，toast提示“感谢反馈！”。
6.  **用户操作6:** 小红点击结算页底部的“完成”按钮。
    * **界面变化:** 返回到课程列表页或应用首页。
    * **即时反馈:** 结束本次学习流程。

**场景细节补充：**
* **前置条件:** 小红已完成当前课程单元的所有必学组件。
* **期望结果:** 小红清晰了解本次学习的各项数据，获得个性化推荐，完成课程反馈，并顺利退出结算流程。
* **异常与边界情况:**
    *   结算数据加载失败：⚠️应有加载失败提示和重试机制。
    *   推荐学习内容为空：应展示“您已掌握所有内容，暂无推荐”等友好提示。

**优先级：** 高
**依赖关系：** 依赖学习数据统计模块、掌握度评估模块、题目系统。

**Mermaid图示 (结算页核心交互流程):**
  ```mermaid
  sequenceDiagram
    participant User as 用户小红
    participant System as AI课程系统
    User->>System: 完成最后一个学习组件
    System->>User: 展示结算页（庆祝动效、鼓励文案）
    User->>System: 滑动查看详情
    System->>User: 显示学习表现、掌握度、答题概览、推荐学习、课程反馈模块
    User->>System: 点击“答题情况”的“查看详情”
    System->>User: 跳转至题目列表页
    User->>System: （在题目列表页操作后）返回结算页
    User->>System: 点击推荐学习的“去练习”
    System->>User: 进入推荐练习模块
    User->>System: （在练习模块操作后）返回结算页
    User->>System: 点击课程反馈表情并提交反馈
    System->>User: 弹出反馈表单，处理提交，toast提示
    User->>System: 点击“完成”按钮
    System->>User: 返回课程列表页
  ```

###### 场景3: 学生小华在学习过程中想要回顾之前学过的一个知识点
作为一个 **正在学习AI课程的学生（小华）**，我希望能够 **在学习过程中方便地查看课程的整体进度，并能快速跳转到之前学过的任何一个知识点或练习**，这样我就可以 **灵活地复习和巩固，而不必按照固定顺序一步步返回**。目前的痛点是 **如果想看前面的内容，可能需要多次点击返回，效率较低，也容易打断当前学习思路**，如果能够 **有一个清晰的课程进度导航，允许我自由跳转**，对我的学习灵活性和效率会有很大提升。

**前端界面与交互关键步骤：**
1.  **用户操作1:** 小华正在学习“知识点3”的文档内容，感觉“知识点1”有些模糊，想回去看看。他点击了界面右上角的“课程进度”按钮。
    * **界面变化:** 屏幕下方或侧边滑出课程进度条/列表。进度条清晰展示了本节课的所有组件，如“开场”、“课程引入”、“知识点1”、“知识点2”、“练习1”、“知识点3”（当前）、“知识点4”、“练习2”、“课程总结”、“学习报告”。“知识点1”、“知识点2”、“练习1”显示为“已解锁”状态，“知识点3”显示为“学习中”状态，其余为“待解锁”状态。当前“知识点3”的播放/讲解暂停。
    * **即时反馈:** 学习内容暂停，进度概览清晰呈现。
2.  **用户操作2:** 小华在课程进度条上点击了“知识点1”这个已解锁的组件。
    * **界面变化:** 课程进度条收起，界面内容切换到“知识点1”的文档内容，并从上次小华学习“知识点1”时退出的位置开始播放（或从头开始，如果之前未学完）。
    * **即时反馈:** 快速跳转到目标内容，可以开始复习。
3.  **用户操作3:** 小华复习完“知识点1”后，该组件内容播放完毕（或小华手动点击了进度条上的“知识点3”）。
    * **界面变化:** 界面自动（或手动）切换回“知识点3”，并从之前暂停的位置继续学习。
    * **即时反馈:** 无缝衔接回之前的学习进度。

**场景细节补充：**
* **前置条件:** 小华正在AI课程的学习过程中。
* **期望结果:** 小华能够通过课程进度功能，自由、快速地在已学和当前学习的组件间跳转，完成复习后能顺利返回原学习进度。
* **异常与边界情况:**
    *   点击待解锁组件：⚠️应有提示“请先完成前面的学习内容”或组件为不可点击状态。
    *   跳转到练习组件：应加载该练习的上次作答记录或题目详情。

**优先级：** 高
**依赖关系：** 依赖各组件的状态记录（学习位置、答题记录）。

**Mermaid图示 (课程进度与跳转):**
  ```mermaid
  graph TD
    A[用户学习中<br>(例：知识点3)] -- 点击 --> B(课程进度按钮);
    B --> C{显示课程进度条<br>(知识点3播放暂停)};
    C -- 点击已解锁组件<br>(例：知识点1) --> D[跳转至知识点1<br>(从上次位置播放)];
    D -- 学习完毕/手动跳转 --> E[返回知识点3<br>(从暂停位置继续)];
    C -- 点击屏幕其他区域 --> F[收起进度条<br>(知识点3继续播放)];
    C -- 点击当前学习中组件 --> F;
  ```

###### 场景4: 学生小李学习到一半有急事，需要中途退出课程
作为一个 **正在学习AI课程的学生（小李）**，我希望 **在我中途需要退出课程时，系统能够自动保存我当前的全部学习进度（包括视频播放位置、练习题作答情况等），并在我下次回来时能从中断处无缝继续**，这样我就 **不必担心学习进度丢失，可以安心处理事务，并在方便时高效恢复学习**。目前的痛点是 **如果中途退出，不确定进度是否保存，回来可能要重新找，浪费时间**，如果能够 **实现智能的断点续学**，对我的学习体验和时间管理会非常有帮助。

**前端界面与交互关键步骤：**
1.  **用户操作1:** 小李正在学习文档组件的某一部分，数字人视频播放到1分30秒，板书同步展示。此时他接到一个重要电话，需要立即退出课程。他点击了界面左上角的“退出”按钮。
    * **界面变化:** 界面可能弹出确认对话框：“确定要退出当前学习吗？系统将为您保存进度。”，包含“确认退出”和“继续学习”按钮。
    * **即时反馈:** 清晰的二次确认，告知用户进度会被保存。
2.  **用户操作2:** 小李点击“确认退出”。
    * **界面变化:** 界面返回到课程列表页或应用首页。
    * **即时反馈:** 退出成功。系统在后台默默保存了小李在文档组件的播放时间点（1分30秒），以及任何已完成的练习题的答案和当前正在进行的练习题的进度（如果适用）。
3.  **用户操作3:** （一段时间后）小李处理完事务，重新进入之前学习的AI课程。
    * **界面变化:** 系统直接将小李导航到他上次退出的文档组件，数字人视频从1分30秒开始播放，板书内容也同步到该位置。
    * **即时反馈:** 无缝衔接上次的学习内容，无需手动寻找。如果是从练习组件退出，则会进入上次未答完的题目或已作答的题目列表。

**场景细节补充：**
* **前置条件:** 小李正在AI课程的学习过程中。
* **期望结果:** 小李退出课程时进度被准确保存，再次进入时能从上次中断的地方继续学习，无论是文档组件还是练习组件。
* **异常与边界情况:**
    *   保存进度时网络中断：⚠️应有本地缓存机制，并在网络恢复后尝试同步。若无法保存，应提示用户。
    *   课程内容更新后再次进入：⚠️若原有进度点失效，应引导用户到最近的有效节点或单元开始。

**优先级：** 高
**依赖关系：** 依赖于精确的进度记录系统（视频播放时间、练习作答状态）。

**Mermaid图示 (退出与断点续学):**
  ```mermaid
  sequenceDiagram
    participant User as 用户小李
    participant Client as AI课程客户端
    participant Server as AI课程服务端

    User->>Client: 学习中，点击“退出”按钮
    Client->>Client: (可选)显示退出确认弹窗
    User->>Client: (若有弹窗)点击“确认退出”
    Client->>Server: 请求保存当前学习进度（组件ID, 播放/作答位置等）
    Server->>Server: 持久化存储进度信息
    Server-->>Client: 确认进度已保存 (或异步处理)
    Client->>User: 返回课程列表页

    %% ...时间流逝...

    User->>Client: 重新进入该课程
    Client->>Server: 请求获取上次学习进度
    Server-->>Client: 返回上次进度信息（组件ID, 播放/作答位置等）
    Client->>Client: 根据进度信息加载对应组件和内容
    Client->>User: 从上次中断处继续学习
  ```

#### **核心功能点2: 文档组件增强**

**用户价值与场景:** 满足学生在学习JS文档（板书）内容时对同步性、聚焦度、自主性和操控性的更高要求，解决原有文档体验中板书与讲解可能不同步、重点不突出、交互方式单一等痛点，为用户带来更沉浸、高效、便捷的文档学习体验。

**功能详述与前端呈现:**
- **主要交互流程:**
    1.  **默认跟随模式:** 用户进入文档组件，内容（数字人视频、勾画轨迹、JS板书）自动同步播放。当前讲解的板书区域高亮或有进度条指示，未播放区域模糊/弱化。字幕默认开启。
    2.  **进入自由模式:** 用户在板书区域进行滑动操作，自动切换到自由模式。此时板书不再自动滚动，但进度线依然提示老师讲解位置。数字人视频继续播放。
    3.  **自由模式交互:**
        *   单击板书无评论内容：该内容块高亮，出现“从这里学”按钮。点击按钮后，从该内容对应时间点开始播放，并切换回跟随模式。
        *   双击屏幕：切换播放/暂停状态（对视频、板书、勾画轨迹均生效）。
        *   长按文档以外区域：内容以3倍速播放，松手恢复原速。
        *   单击文档以外区域：唤起操作面板。
    4.  **操作面板交互:** 显示视频播控条（字幕开关、播放/暂停、倍速选择、前进/后退10s）。
        *   倍速选择：支持0.75x至2.0x多种倍速（包含1.75x，但不含3x，3x由长按触发）。倍速全局生效，退出课程重置。
        *   前进/后退10s：视频、板书、勾画轨迹同步跳转。
    5.  **内容播放到底部:** 若在跟随/自由模式下，当前组件内容（板书）滑动或播放到底部，会出现阻尼效果，继续上滑可进入下一个组件。
- **界面布局与元素:**
    *   **整体布局:** 上方或侧边为数字人视频区域，主要区域为JS板书展示区。
    *   **数字人视频区:** 包含视频画面、字幕（可开关）、播控条（通过操作面板或直接显示）。
    *   **JS板书区:**
        *   **内容块:** 文本、公式、图片等。
        *   **勾画轨迹:** 动态绘制在板书内容上，与讲解同步。
        *   **进度线:** 在当前讲解句子下方显示，指示播放位置。
        *   **聚焦效果:** 跟随模式下，当前讲解内容清晰，上下未播放内容模糊或弱化。
        *   **“从这里学”按钮:** 自由模式下点击内容块出现，通常为小浮窗按钮。
    *   **操作面板:** 半透明浮层，包含播控按钮图标和文字。
    *   **Toast提示:** 例如，切换模式时提示“已切换到自由模式”或“已切换到跟随模式”。
- **用户操作与反馈:**
    *   **滑动板书:** 切换到自由模式，toast提示。板书随手指移动。
    *   **单击无评论内容（自由模式）:** 内容高亮，“从这里学”按钮出现。点击按钮，内容从该点开始播放，切换回跟随模式，toast提示。
    *   **双击屏幕:** 内容播放/暂停，按钮状态相应变化。
    *   **长按文档外区域:** 内容3倍速播放，松手恢复。
    *   **单击文档外区域:** 弹出操作面板。再次单击或点击面板外区域，面板收起。
    *   **操作面板按钮点击:**
        *   字幕：开关字幕显示。
        *   播放/暂停：切换状态。
        *   倍速：弹出倍速选项，选择后立即生效。
        *   前进/后退10s：内容同步跳转。
    *   **板书滑动到底/播放到底再上滑:** 进入下一组件，有转场提示或效果。
- **数据展示与更新:**
    *   字幕内容随视频播放动态更新。
    *   板书内容根据播放进度自动滚动（跟随模式）或手动滚动（自由模式）。
    *   勾画轨迹实时绘制。
    *   进度线实时更新位置。

**优先级：** P0
**依赖关系：** 依赖数字人视频、JS板书内容、勾画轨迹数据以及它们之间的时间轴同步信息。

##### 用户场景与故事 (针对此核心功能点)

###### 场景1: 学生小明在学习文档时，觉得老师讲太快，部分内容没跟上
作为一个 **正在学习AI课程中JS文档内容的学生（小明）**，我希望能够 **在我感觉没跟上老师讲解时，可以方便地暂停、回退，或者让板书的播放焦点回到我想要关注的地方，并从那里重新听讲**，这样我就可以 **按照自己的节奏学习，确保理解每一个重要知识点**。目前的痛点是 **纯粹跟随模式下，一旦错过就很难回去，或者回去操作复杂；板书和讲解不同步也让人困扰**，如果能够 **有更灵活的播放控制和“指哪学哪”的功能**，对我的学习效率和体验会有极大提升。

**前端界面与交互关键步骤：**
1.  **用户操作1:** 小明在“跟随模式”下学习，发现老师讲到某个公式时，自己还没完全看懂，老师已经讲到下一段了。此时板书自动滚动，当前讲解的句子有进度线，而小明想看的公式部分已经向上滚出主要视区或变得模糊。
    * **界面变化:** 小明用手指向下滑动JS板书区域。
    * **即时反馈:** 系统立即toast提示“已切换到自由模式”。板书停止自动滚动，所有内容清晰显示。数字人视频和勾画轨迹仍在按原进度播放，但板书下方的老师讲解进度线依然指示老师当前讲到的位置。
2.  **用户操作2:** 小明将板书滑动到他想重新关注的那个公式位置。他点击了一下这个公式。
    * **界面变化:** 该公式所在的内容块（例如，一个包含公式的文本行或图片）高亮，旁边出现一个“从这里学”的小按钮。
    * **即时反馈:** 清晰标示出用户选中的学习起点。
3.  **用户操作3:** 小明点击了“从这里学”按钮。
    * **界面变化:** 数字人视频、勾画轨迹、板书进度线立刻跳转到与该公式对应的时间点开始播放。板书内容也可能随之调整滚动位置，确保公式在焦点区域。系统toast提示“已切换到跟随模式”。未播放区域（如有）再次模糊/弱化。
    * **即时反馈:** 学习内容从用户指定的位置重新开始同步播放，帮助小明跟上节奏。
4.  **用户操作4:** 在重新学习过程中，小明想要暂停思考。他双击了屏幕的任意位置（文档区或视频区）。
    * **界面变化:** 数字人视频、勾画轨迹、板书进度线全部暂停。如果操作面板上有播放/暂停按钮，其状态切换为“播放”。
    * **即时反馈:** 内容播放停止。再次双击，则恢复播放。
5.  **用户操作5:** 小明觉得某一段老师讲解比较慢，他长按了屏幕的空白区域（非板书内容区）。
    * **界面变化:** 数字人视频、勾画轨迹、板书进度线开始以3倍速播放。
    * **即时反馈:** 内容快速播放。小明松手后，播放速度恢复到之前的设定值（例如1倍速）。
6.  **用户操作6:** 小明想调整正常播放的倍速或快退10秒。他单击了屏幕的空白区域。
    * **界面变化:** 屏幕上出现半透明的操作面板，面板上有“字幕”开关、“播放/暂停”按钮、“倍速”按钮、“快退10s”按钮、“快进10s”按钮。
    * **即时反馈:** 提供了一系列快捷控制功能。小明点击“倍速”按钮，选择1.5倍速，面板收起，内容以1.5倍速播放。

**场景细节补充：**
* **前置条件:** 小明正在学习一个包含JS板书、数字人视频和勾画轨迹的文档组件。
* **期望结果:** 小明能够通过滑动、点击、双击、长按等多种交互方式，自由控制学习节奏，实现暂停、回放、变速、从指定位置开始学习等操作。
* **异常与边界情况:**
    *   点击已添加评论的内容区域（本期不做）：⚠️若未来实现，则应有不同交互（如显示评论）。
    *   板书内容非常短，一屏显示完：滑动可能不会触发自由模式，或自由模式意义不大。⚠️应考虑此情况下的模式切换逻辑。
    *   “从这里学”的内容块没有对应的时间戳：⚠️应有预案，如从最近的上一个时间戳开始，或提示无法精确定位。

**优先级：** 高
**依赖关系：** 依赖精确的内容时间戳映射（板书内容块、勾画轨迹与视频时间轴的同步）。

**Mermaid图示 (文档组件核心交互与模式切换):**
  ```mermaid
  stateDiagram-v2
    [*] --> 跟随模式: 进入文档组件
    跟随模式: 内容同步播放 (视频,板书,勾画)
    跟随模式: 当前讲解内容聚焦，其余弱化
    跟随模式: 字幕默认开启

    跟随模式 --> 自由模式: 用户滑动板书
    自由模式: toast提示“已切换到自由模式”
    自由模式: 板书不自动滚动，进度线仍显示
    自由模式: 视频、勾画继续播放

    自由模式 --> 跟随模式: 点击“从这里学”按钮
    跟随模式: toast提示“已切换到跟随模式”
    跟随模式: 从指定位置开始同步播放

    state 跟随模式 {
        播放暂停: 双击屏幕
        唤起操作面板: 单击文档外区域
        三倍速播放: 长按文档外区域 (松手恢复)
    }

    state 自由模式 {
        指点学习: 单击板书内容块 -> 显示“从这里学”
        播放暂停: 双击屏幕
        唤起操作面板: 单击文档外区域
        三倍速播放: 长按文档外区域 (松手恢复)
    }

    操作面板:
    state "操作面板显示" as面板显示
    面板显示: 字幕开关
    面板显示: 播放/暂停按钮
    面板显示: 倍速选择按钮
    面板显示: 前进/后退10s按钮
    面板显示 --> [*]: 点击面板外区域或操作后收起

    [*] --> 面板显示: (跟随/自由模式下)单击文档外区域
  ```

#### **核心功能点3: 练习组件 (基本交互)**
*(注：原始PRD中练习组件的详细需求较少，主要体现在课程框架的结算页、课程进度跳转逻辑等方面。此处基于通用练习组件的交互进行基础场景描述)*

**用户价值与场景:** 为学生提供在AI课程中进行知识点练习和检验学习成果的平台，解决学与练脱节的问题，帮助用户巩
固知识，发现薄弱点。

**功能详述与前端呈现:**
- **主要交互流程:**
    1.  用户从课程框架进入练习组件。
    2.  系统展示题目（例如单选题），包括题干、选项。
    3.  用户选择答案并提交（或自动进入下一题，取决于配置）。
    4.  系统判断对错，并给出即时反馈（如答案正误、解析入口）。
    5.  用户可查看解析，或进入下一题。
    6.  完成所有题目后，可返回课程框架或查看练习总结。
- **界面布局与元素:**
    *   **题目区域:** 显示题型、题号、题干内容。
    *   **选项区域:** 显示各选项（A, B, C, D...）及内容。
    *   **操作区域:** “提交答案”按钮、“查看解析”按钮（作答后）、“下一题”按钮。
    *   **反馈区域:** 显示答案对错提示、正确答案、题目解析文本。
    *   **导航:** 可能有题目列表入口、错题本入口等。
- **用户操作与反馈:**
    *   用户点击选项进行选择。
    *   用户点击“提交答案”，系统判断并显示对错，以及正确答案和解析。
    *   用户点击“查看解析”，展开或跳转到解析内容。
    *   用户点击“下一题”，加载并显示下一道题目。
- **数据展示与更新:**
    *   题目内容动态加载。
    *   用户选择状态实时更新。
    *   作答结果和解析按需展示。

**优先级：** P0 (作为课程框架的组成部分)
**依赖关系：** 题目库、用户作答记录系统

##### 用户场景与故事 (针对此核心功能点)

###### 场景1: 学生小王学完一个知识点后，进入配套练习进行巩固
作为一个 **刚学完某个知识点的学生（小王）**，我希望能够 **立即做几道相关的练习题来检验我是否真的理解了，并能看到答案和解析**，这样我就可以 **及时巩固所学，发现自己的薄弱环节并加以改进**。目前的痛点是 **学完后没有配套练习，或者练习与学习内容关联不紧密，导致学习效果打折扣**，如果能够 **在学完知识点后无缝进入针对性的练习，并获得即时反馈和详细解析**，对我的知识掌握会非常有帮助。

**前端界面与交互关键步骤：**
1.  **用户操作1:** 小王在AI课程中完成了“知识点A”的学习（例如一个文档组件播放完毕），系统自动或提示他进入“知识点A练习”。
    * **界面变化:** 界面切换到练习组件。显示第一道题，题型为“单选题”，题号“1/5”，题干“关于XXX的描述，以下哪个是正确的？”，下方列出A, B, C, D四个选项。
    * **即时反馈:** 清晰呈现题目内容。
2.  **用户操作2:** 小王阅读题干和选项后，点击了选项C。
    * **界面变化:** 选项C被选中（例如背景色变化或出现勾选标记）。“提交答案”按钮变为可点击状态（如果不是自动提交）。
    * **即时反馈:** 用户的选择得到视觉确认。
3.  **用户操作3:** 小王点击“提交答案”按钮。
    * **界面变化:** 系统判断答案。假设选项C错误，选项B正确。界面上选项C标记为红色（错误），选项B标记为绿色（正确）。同时在题目下方出现“回答错误”的提示，并显示“正确答案：B”，以及一个“查看解析”的按钮和“下一题”的按钮。
    * **即时反馈:** 立即获得答案对错反馈和正确答案。
4.  **用户操作4:** 小王点击“查看解析”按钮。
    * **界面变化:** 题目解析内容（文本或图文）在下方展开或在弹窗中显示，解释了为什么选B以及其他选项为什么错。
    * **即时反馈:** 帮助小王理解题目和知识点。
5.  **用户操作5:** 小王看完解析后（或直接），点击“下一题”按钮。
    * **界面变化:** 加载并显示第二道题。
    * **即时反馈:** 继续进行练习。

**场景细节补充：**
* **前置条件:** 小王完成了与练习相关的知识点学习。
* **期望结果:** 小王能够顺利完成练习题，获得即时反馈和解析，有效巩固知识。
* **异常与边界情况:**
    *   题目加载失败：⚠️应有提示并允许重试。
    *   已经是最后一题，点击“下一题”：应提示“已是最后一题”或自动跳转到练习总结/结算页。

**优先级：** 高 (作为P0功能的核心场景)
**依赖关系：** 题目内容及答案解析数据，用户作答判断逻辑。

**Mermaid图示 (练习组件基本答题流程):**
  ```mermaid
  sequenceDiagram
    participant User as 用户小王
    participant System as AI课程系统

    User->>System: 进入练习组件 (或被引导进入)
    System->>User: 显示题目1 (题干、选项)
    User->>System: 选择答案 (例：选项C)
    System->>User: (可选)更新选项选中状态
    User->>System: 点击“提交答案”
    System->>System: 判断答案正误
    System->>User: 显示作答结果 (对/错、正确答案)
    System->>User: 显示“查看解析”、“下一题”按钮
    User->>System: 点击“查看解析”
    System->>User: 显示题目解析内容
    User->>System: 点击“下一题”
    System->>User: 显示题目2 (或练习总结)
  ```

### 2.2 辅助功能
- **答疑组件入口:**
    - **功能详述与前端呈现:** 在文档组件或练习组件界面中，通常在右上角或浮动按钮形式提供“问一问”或相似文本的入口。点击后，可以拉起或跳转到答疑组件界面，允许用户输入问题进行提问。答疑组件本身的功能和交互遵循其独立需求文档。
    - **Mermaid图示 (答疑入口交互):**
      ```mermaid
      graph TD
        A[学习中 (文档/练习组件)] -- 点击 --> B(问一问/答疑入口按钮);
        B --> C{拉起/跳转至答疑组件界面};
        C --> D[用户输入问题并提问];
        D --> E[查看答疑结果];
        E -- 关闭/返回 --> A;
      ```
- **字幕开关:**
    - **功能详述与前端呈现:** 在文档组件的视频播放控制区域（通常在操作面板内），提供一个“字幕”按钮或开关。点击可切换字幕的显示/隐藏状态。默认开启。
- **课程全局倍速:**
    - **功能详述与前端呈现:** 文档组件视频播放的倍速设置（0.75x 至 2.0x）对当前一整节课中的所有视频组件生效。退出课程后，下次进入时恢复为默认的1.0倍速。

### 2.3 非本期功能
- **文档组件长按评论功能:** 长按文档内容，选中文字/图片后唤起评论操作。（原始PRD V1.0中提及本期不做）
- **文档组件单击有评论的文档内容 - 展示评论内容:** （原始PRD V1.0中提及本期不做）
- **结算页IP动效根据掌握度显示“开心”或“鼓励”的具体细分逻辑**：原始PRD V1.0中给出了基于正确率>70%的动效展示策略，但结算页的动效和文案可能更复杂，与多个IP和多种学习表现情况联动，具体细化的丰富动效库和文案库的完整匹配逻辑。
- **积分体系和小组战队**
- **互动讲题，自动化逐步讲解题目，并在讲解过程中加入互动问题**
- **费曼组件、互动组件**

## 3. 业务流程图 (整体课程结构)

```mermaid
graph TD
    A[用户选择并进入一节AI课程] --> B["课程开场页 (功能页面)<br>自动播放IP动效和音频"];
    B -- 播放完成 --> C["课程内容学习 (可包含多个组件)<br>如: 课程引入 (文档组件)"];
    C --> D{"组件类型判断"};
    D -- 文档组件 --> E["文档组件学习<br>含视频、板书、勾画<br>支持跟随/自由模式<br>支持快捷交互、操作面板"];
    D -- 练习组件 --> F["练习组件学习<br>做题、提交、看解析"];
    E --> G{当前组件是否结束?};
    F --> G;
    G -- 是 --> H{是否所有组件完成?};
    G -- 否 --> C; %% 返回处理下一个组件或用户通过课程进度跳转
    H -- 是 --> I["结算页<br>展示学习报告、反馈等"];
    H -- 否 --> C; %% 进入下一组件
    I -- 点击“完成” --> J[返回课程入口页];
    I -- 点击“回看课程” --> C; %% 所有组件解锁，可自由跳转
    subgraph "学习中公共操作"
        direction LR
        X1[课程进度<br>随时可调用，自由跳转已解锁组件]
        X2[退出课程<br>随时可操作，保存进度]
        X3[问一问<br>在文档/练习组件中调用答疑]
    end
    C -.-> X1;
    C -.-> X2;
    E -.-> X3;
    F -.-> X3;

    %% 详细的组件内部流程在各自功能点中描述
```

## 4. 性能与安全需求

### 4.1 性能需求
-   **响应时间：**
    *   文档组件内“从这里学”点击后，内容（视频、板书、勾画）跳转和开始播放的响应时间应不超过 1.5 秒 (95th percentile)。
    *   操作面板唤出时间应小于 0.5 秒。
    *   练习组件题目切换加载时间应小于 1 秒。
    *   页面主要内容加载时间（LCP）在良好网络环境下（如WIFI，快速4G）应在 2.5 秒内。
    *   首次交互延迟（FID）或交互至下次绘制时间（INP）应小于 100 毫秒。
-   **并发用户：** (服务端相关，前端需配合保证体验) 系统需支持至少 [暂定，需后端评估，如1000] 个并发用户同时在线学习，核心交互功能（如模式切换、播控操作、答题）前端响应不应有明显卡顿。
-   **数据量：**
    *   JS板书内容较长时（例如，等效A4页面10页以上），在自由模式下手动滑动应保持流畅，不应有明显掉帧或卡顿。
    *   练习组件题目数量较多时（例如，一个练习包含50题），通过课程进度跳转或题目列表查看时，加载不应导致长时间白屏。
-   **前端性能感知：**
    *   **文档组件内容加载:** 板书、视频、勾画轨迹等资源应优化加载策略（如预加载、分片加载），避免长时间白屏。弱网情况下应有加载状态提示（如骨架屏、loading动画）。
    *   **勾画轨迹播放:** 勾画轨迹的绘制应流畅，与音频和视频精确同步，不应出现漂移或卡顿。
    *   **未播放区域模糊/弱化效果:** 该效果的渲染和切换不应消耗过多性能导致卡顿。
    *   **快捷交互响应:** 双击、长按等操作的反馈必须即时，避免用户误以为无效操作。

### 4.2 安全需求
-   **权限控制：** (主要为服务端责任) 用户只能访问其已购买或授权的课程内容。前端界面不应展示未授权课程的入口或内容。
-   **数据加密：** 用户学习进度、答题记录等敏感数据在前后端传输过程中必须使用 HTTPS。前端避免在本地（如localStorage）存储明文敏感信息。
-   **操作审计：** (主要为服务端责任) 用户关键学习行为（如完成课程、完成重要练习）应有记录。
-   **输入校验:**
    *   课程反馈中用户输入的文本内容，前端应进行基础的长度限制和特殊字符过滤，防止XSS等常见脚本注入。后端必须进行二次严格校验。
-   **API安全：** 前端调用后端API时，需遵循项目的统一认证和授权机制。

## 5. 验收标准

### 5.1 功能验收
-   **核心功能点1: 课程框架升级**
    -   **用户场景：开场页展示**
        -   当用户首次进入一节新课程时。
        -   界面与交互：开场页按设计稿正确显示课程标题、章节号等信息，IP动效和音频自动播放且无卡顿，播放完毕后自动平滑过渡到第一个学习组件。
        -   期望结果：用户体验到富有仪式感的课程开始流程。
    -   **用户场景：结算页展示与交互**
        -   当用户完成一节课所有组件的学习后。
        -   界面与交互：结算页按设计稿正确展示庆祝动效、文案、各项学习数据（用时、答题数、正确率、掌握度及提升）、答题情况概览、推荐学习模块（根据模拟的不同用户表现，推荐内容应有变化）、课程反馈模块。所有按钮（查看详情、去练习、反馈提交、回看课程、完成）交互功能正常。
        -   期望结果：用户能清晰了解学习成果，并顺利完成反馈和后续操作。
    -   **用户场景：课程进度跳转**
        -   用户在学习过程中，点击“课程进度”按钮，然后选择一个已解锁的组件。
        -   界面与交互：课程进度条正确显示所有组件及其状态（学习中、已解锁、待解锁）。点击已解锁组件后，能成功跳转到该组件的正确内容位置（文档从上次播放点，练习从上次作答记录或题目）。
        -   期望结果：用户能通过课程进度功能灵活导航。
    -   **用户场景：退出与断点续学**
        -   用户在学习文档组件中途（例如视频播放到X分钟）点击退出，然后重新进入该课程。
        -   界面与交互：退出时进度被保存。重新进入后，直接从文档组件X分钟处继续播放。
        -   期望结果：用户学习进度被准确记录和恢复。
-   **核心功能点2: 文档组件增强**
    -   **用户场景：内容同步播放与聚焦（跟随模式）**
        -   用户进入文档组件，处于默认的“跟随模式”。
        -   界面与交互：数字人视频、JS板书内容、勾画轨迹三者精确同步播放。当前讲解的板书内容高亮或有进度条，上下未播放区域按设计模糊/弱化。字幕默认显示。
        -   期望结果：用户能聚焦于当前讲解内容，获得良好同步体验。
    -   **用户场景：模式切换与“从这里学”（自由模式）**
        -   用户在跟随模式下，手动滑动板书内容。
        -   界面与交互：系统toast提示切换到“自由模式”，板书停止自动滚动。用户点击某处板书（无评论），该处高亮并出现“从这里学”按钮。点击按钮后，内容从该点对应时间戳开始同步播放，并toast提示切换回“跟随模式”。
        -   期望结果：用户能方便地切换模式并从指定位置开始学习。
    -   **用户场景：快捷交互（双击、长按）**
        -   用户在文档组件播放时，双击屏幕；或长按文档以外区域。
        -   界面与交互：双击后，内容（视频、板书、勾画）播放/暂停状态切换。长按文档外区域，内容以3倍速播放，松手恢复原速。
        -   期望结果：快捷交互功能符合预期，响应灵敏。
    -   **用户场景：操作面板与播控**
        -   用户在文档组件播放时，单击文档以外区域。
        -   界面与交互：操作面板弹出，显示字幕开关、播放/暂停、倍速（含0.75x-2.0x多种选项）、前进/后退10s按钮。各按钮功能均正常，倍速设置全局生效（本节课内）。
        -   期望结果：用户能通过操作面板方便地控制播放。

### 5.2 性能验收
-   **响应时间：**
    *   **测试用例1 (从这里学):** 在包含10个以上时间戳锚点的长文档中，多次（至少20次）点击不同的“从这里学”按钮，95%的情况下，内容跳转和开始播放的延迟时间不超过1.5秒。
    *   **测试用例2 (操作面板):** 连续快速点击唤出和收起操作面板20次，面板响应时间均小于0.5秒，无卡顿。
    *   **测试用例3 (板书滑动):** 在等效A4 10页长度的JS板书中，在自由模式下快速来回滑动，帧率保持在45fps以上（使用性能分析工具如 Chrome DevTools Performance）。
-   **前端性能感知验收：**
    *   **测试用例1 (文档组件加载):** 模拟普通4G网络（如Chrome DevTools限速），首次进入包含视频和复杂JS板书的文档组件，从点击进入到主要内容可交互（LCP完成且视频首帧出现）的时间不超过5秒。加载过程中应有清晰的loading状态或骨架屏。
    *   **测试用例2 (勾画轨迹同步):** 选取一段包含密集勾画和快速讲解的片段，反复观看，勾画轨迹与音频、口型、板书内容保持肉眼可见的精确同步，无明显延迟或提前。

### 5.3 安全验收
-   **输入校验：**
    *   **测试用例1 (XSS - 课程反馈):** 在结算页的课程反馈文本框中输入 ``<script>alert('xss')</script>``，提交后，脚本未执行，页面显示正常，后台记录的反馈内容中特殊字符被转义或过滤。
-   **API调用：**
    *   **测试用例1 (未授权访问):** 尝试构造请求直接访问一个未购买课程的组件内容API，期望结果：API返回403或相应的权限错误码，前端不渲染内容。

## 6. 其他需求

### 6.1 可用性与体验需求
-   **界面友好与直观性：**
    *   核心操作路径（如开始学习、查看进度、进行练习、查看结算）应清晰易懂，用户无需过多思考即可完成。
    *   图标（如播放、暂停、倍速、退出、课程进度）、标签、提示信息（如模式切换toast）应表意明确，符合用户通用认知。
    *   整体视觉风格（颜色、字体、布局）在课程框架和各组件间保持一致性。重要操作（如“从这里学”、提交答案）有明确的视觉引导。
-   **容错处理：**
    *   当发生网络错误导致内容加载失败（如视频、板书、题目）时，应提供清晰、友好的错误提示（例如：“内容加载失败，请检查网络后重试”），并提供重试按钮。
    *   当用户进行不符合预期的操作时（如在未解锁组件上尝试跳转），应有温和的提示。
-   **兼容性：**
    *   **浏览器：** 需支持最新版本的 Chrome, Safari (iOS), 主流安卓系统自带的WebView/浏览器，保证核心功能和界面显示正常。
    *   **响应式布局：** 产品主要为移动端设计，需确保在不同尺寸的手机屏幕上均有良好的布局和操作体验。关键按钮点击区域符合移动端交互规范（例如，不小于44x44dp）。
-   **可访问性 (A11y)：**
    *   核心交互元素（如播放/暂停按钮、选项、提交按钮、模式切换触发区域）应考虑键盘（若外接）或辅助功能（如VoiceOver, TalkBack）的可操作性。
    *   图片等非文本内容需提供有意义的 `alt` 文本或ARIA标签（例如，IP动效的描述）。
    *   颜色对比度应尽量符合 WCAG AA 级别标准，确保低视力用户可辨识。
    *   重要状态变更（如模式切换、播放暂停、加载完成、错误提示）应有ARIA属性支持，方便屏幕阅读器用户感知。
-   **交互一致性：**
    *   相似功能的操作（如播放控制按钮、退出按钮）在不同组件或课程间应使用统一的视觉样式和交互模式。
    *   Toast提示的样式、位置、持续时间应保持一致。

### 6.2 维护性需求
-   **配置管理：**
    *   开场页不同学科的背景图和色值应支持通过后台配置。
    *   课程章节号、课程序号、课程名称（25字以内）应支持后台配置。
    *   结算页IP动效和鼓励文案（主副文案）应支持后台按IP角色及学习表现触发条件进行配置。
    *   推荐学习模块的模块名称、预估题量、触发策略应支持后台配置。
-   **监控告警：** (需与前端监控系统配合)
    *   前端JS执行错误率突增、核心API（如获取课程内容、提交答案、保存进度）请求成功率骤降或耗时突增时，监控系统应能捕获并告警。
-   **日志管理：** (前端需按规范上报日志)
    *   记录用户关键行为路径（如进入课程、开始/结束组件学习、模式切换、重要交互操作、退出课程）。
    *   记录核心API的请求参数、响应（脱敏后）、耗时。
    *   记录前端发生的JS错误详情。

## 7. 用户反馈和迭代计划

### 7.1 用户反馈机制
-   用户可以通过 **结算页的课程反馈模块**（包含5级评价、分类标签、开放式文本输入）提交对单节课程的即时反馈。
-   前端应确保反馈入口（结算页）的易用性，提交过程顺畅，提交成功有明确提示。
-   （建议）产品运营团队应定期（如每周）收集和分析用户通过此渠道提交的反馈，识别高频问题和改进机会，纳入迭代规划。

### 7.2 迭代计划
-   **迭代周期：** 初定每 [2-3] 周为一个迭代周期。
-   **迭代流程：** （标准敏捷流程）需求评审 -> UI/UX设计（若有变更） -> 开发与联调 -> 测试（含QA、UAT） -> 发布上线 -> 效果跟踪与用户反馈收集。
-   **优先级调整：** 每次迭代结束后，根据上个迭代的需求实现情况、数据表现（如用户使用时长、完成率、反馈评分）和用户反馈，产品团队会重新评估Backlog中的需求优先级，并规划下一个迭代的内容。V1.1版本将优先保障P0核心功能的稳定和体验。

## 8. 需求检查清单

| 原始需求 (来自PRD V1.0)                                  | 对应优化后需求点/章节 (在本PRD V1.1中)                                                                                                                               | 完整性 | 正确性 | 一致性 | 可验证性 (是否有验收标准) | 可跟踪性 (是否有优先级/依赖) | UI/UX明确性 | 备注                                                                                           |
| ---------------------------------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------ | ------ | ------ | ------ | ------------------------- | --------------------------- | ----------- | ---------------------------------------------------------------------------------------------- |
| **课程框架**                                               |                                                                                                                                                                    |        |        |        |                           |                             |             |                                                                                                |
| 开场页优化                                                 | 2.1 核心功能点1 (开场页部分), 5.1 功能验收 (开场页场景)                                                                                                                  | ✅      | ✅      | ✅      | ✅                         | ✅ (P0)                      | ✅           | 原始PRD图示已用于描述                                                                                |
| 结算页优化 (含掌握度、答题情况、推荐学习、鼓励文案)        | 2.1 核心功能点1 (结算页部分), 5.1 功能验收 (结算页场景), 相关子模块（如答题情况、推荐学习的流程图在原始PRD图片分析中有）                                                                | ✅      | ✅      | ✅      | ✅                         | ✅ (P0)                      | ✅           | 原始PRD多个图示用于描述UI和流程                                                                        |
| 课程进度，支持用户在一节课的组件中自由切换进度               | 2.1 核心功能点1 (课程进度部分), 5.1 功能验收 (课程进度跳转场景)                                                                                                            | ✅      | ✅      | ✅      | ✅                         | ✅ (P0)                      | ✅           | 原始PRD图示(TlI4bWf...)已用于描述UI                                                                   |
| 退出/继续课程                                              | 2.1 核心功能点1 (退出/继续课程部分), 5.1 功能验收 (退出与断点续学场景)                                                                                                       | ✅      | ✅      | ✅      | ✅                         | ✅ (P0)                      | ✅           |                                                                                                |
| 去掉每个组件之间的串场动效                                   | 体现在交互流程描述中（如进入下一组件无转场动效）                                                                                                                               | ✅      | ✅      | ✅      | N/A (交互细节)              | N/A                         | ✅           |                                                                                                |
| 课程进度：保存用户答题记录，用户切换到已做过的练习模块时直接展示答题记录 | 2.1 核心功能点1 (课程进度部分的功能详述), 5.1 功能验收 (课程进度跳转场景)                                                                                                | ✅      | ✅      | ✅      | ✅                         | ✅ (P0)                      | ✅           |                                                                                                |
| **文档组件**                                               |                                                                                                                                                                    |        |        |        |                           |                             |             |                                                                                                |
| 内容播放：增加勾画轨迹，和JS内容同步播放                     | 2.1 核心功能点2 (功能详述-主要交互流程、界面元素), 5.1 功能验收 (内容同步播放场景)                                                                                             | ✅      | ✅      | ✅      | ✅                         | ✅ (P0)                      | ✅           |                                                                                                |
| 内容播放：未播放的区域呈现模糊/弱化效果，让学生注意力聚焦    | 2.1 核心功能点2 (功能详述-主要交互流程、界面元素), 5.1 功能验收 (内容同步播放场景)                                                                                             | ✅      | ✅      | ✅      | ✅                         | ✅ (P0)                      | ✅           |                                                                                                |
| 内容播放：默认展示字幕                                     | 2.1 核心功能点2 (功能详述-主要交互流程), 2.2 辅助功能 (字幕开关)                                                                                                            | ✅      | ✅      | ✅      | ✅                         | ✅ (P0)                      | ✅           |                                                                                                |
| 交互操作优化：增加双击、长按等快捷交互                     | 2.1 核心功能点2 (功能详述-主要交互流程、用户操作与反馈), 5.1 功能验收 (快捷交互场景)                                                                                              | ✅      | ✅      | ✅      | ✅                         | ✅ (P0)                      | ✅           |                                                                                                |
| 交互操作优化：增加1.75和3倍速                              | 2.1 核心功能点2 (功能详述-操作面板交互、长按3倍速) , 2.2 辅助功能 (全局倍速)                                                                                                  | ✅      | ✅      | ✅      | ✅                         | ✅ (P0)                      | ✅           | 原始PRD在操作面板中也提到了0.75-2.0倍                                                                |
| 交互操作优化：增加前进 / 后退 10s                          | 2.1 核心功能点2 (功能详述-操作面板交互)                                                                                                                                     | ✅      | ✅      | ✅      | ✅                         | ✅ (P0)                      | ✅           |                                                                                                |
| 模式切换 (跟随/自由)                                       | 2.1 核心功能点2 (功能详述-主要交互流程、用户操作与反馈、Mermaid状态图), 5.1 功能验收 (模式切换与“从这里学”场景)                                                                     | ✅      | ✅      | ✅      | ✅                         | ✅ (P0)                      | ✅           | 原始PRD图示(AZEdbJEh..., IrKybk0A...)已用于描述UI                                                      |
| “从这里学”功能                                             | 2.1 核心功能点2 (功能详述-自由模式交互、用户操作与反馈), 5.1 功能验收 (模式切换与“从这里学”场景)                                                                                   | ✅      | ✅      | ✅      | ✅                         | ✅ (P0)                      | ✅           | 原始PRD图示(RDuzbOPU..., KgQob6BN...)已用于描述UI                                                      |
| 操作面板                                                   | 2.1 核心功能点2 (功能详述-操作面板交互、界面元素), 5.1 功能验收 (操作面板与播控场景)                                                                                              | ✅      | ✅      | ✅      | ✅                         | ✅ (P0)                      | ✅           | 原始PRD图示(UEyNbKjI...)已用于描述UI                                                                    |
| 进入下一组件 (文档组件滑动到底部切换)                      | 2.1 核心功能点2 (功能详述-内容播放到底部)                                                                                                                                    | ✅      | ✅      | ✅      | N/A (交互细节)              | ✅ (P0)                      | ✅           |                                                                                                |
| **练习组件 (从课程框架需求中提取)**                        | 2.1 核心功能点3 (基本交互), 5.1 功能验收 (基本答题场景)                                                                                                                        | ✅      | ✅      | ✅      | ✅                         | ✅ (P0)                      | ✅           | 涉及查看历史答题记录、题目列表、题目详情等，原始PRD图示（MGPobJyO..., NdMMbWsS...）可辅助理解UI。 |

-   **完整性：** ✅ 原始需求中的核心功能点均已覆盖。
-   **正确性：** ✅ 优化后需求描述准确表达了原始需求的意图，并根据模板和指南进行了细化。
-   **一致性：** ✅ 各需求点之间逻辑一致，无明显冲突。
-   **可验证性：** ✅ 大部分核心功能均已定义验收场景和标准。
-   **可跟踪性：** ✅ 核心功能均已标记优先级（P0）。
-   **UI/UX明确性：** ✅ 对关键界面元素、交互流程、用户反馈均有较详细描述，并结合原始PRD图示进行理解。

## 9. 附录

### 9.1 原型图/设计稿链接
-   高保真原型链接: [待产品/设计团队提供]
-   UI设计稿链接: [待产品/设计团队提供，原始PRD中“视觉稿：待补充”]
-   (本文档中提及的原始PRD内图片可作为当前阶段的视觉参考，例如开场页 `in_table_image_WVtsbsQ17oojarxxvYQcPWr0nqc`，结算页 `in_table_image_Wb2pba8ILoTQ5dxwpbtcrWt2npb` 等系列图，文档组件操作面板 `in_table_image_UEyNbKjIqoO8cPx4xbqcyAdJnSc` 等)

### 9.2 术语表
-   **JS文档/板书:** 指AI课中以JavaScript技术实现的动态、可交互的课程内容呈现形式，类似于电子板书。
-   **勾画轨迹:** 与教师讲解同步，在JS板书上动态绘制的标记、线条、高亮等，用于辅助强调重点内容。
-   **跟随模式:** 文档组件的一种播放模式，视频、板书、勾画轨迹三者严格同步自动播放，用户被动跟随学习。
-   **自由模式:** 文档组件的一种播放模式，用户可以通过滑动等操作自由浏览板书内容，板书不随视频自动播放，但仍有进度提示。
-   **从这里学:** 在自由模式下，用户点击板书某处内容后出现的按钮，点击后可使课程从该内容对应的时间点开始以“跟随模式”播放。
-   **外化掌握度:** 系统根据用户学习行为（如答题情况）计算并向用户展示的对知识点的掌握程度评估结果。

### 9.3 参考资料
-   原始需求文档：`PRD - AI课中 - 课程框架文档组件 V1.0_analyzed.md`
-   (其他相关竞品分析、用户调研报告等，若有，在此处列出)

