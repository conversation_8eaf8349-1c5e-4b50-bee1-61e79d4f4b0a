# 前端产品需求文档 - AI课中 - 课程框架+文档组件 V0.9

文档说明：
  - 标注⚠️的是 AI 基于用户角度补充的点，可能并未在原始 PRD 中体现

## 1. 需求背景与目标

### 1.1 需求目标
通过该需求的实现，可以优化课中体验，提升学习效果及课程满意度，确保课程基础体验（包括学习与练习环节）的质量。同时，迭代课中体验，让上课过程更接近 1v1 真人直播课，提供沉浸式地课堂体验，展示系统的个性化和智能型。

### 1.2 用户价值
- 对于 使用新AI课的全量用户，解决了 前期demo版本中存在的如板书内容过多、高亮时机与老师讲解不同步、缺乏快捷交互等影响体验的问题，带来了 更流畅、更接近真实课堂的沉浸式学习体验，提升了学习效率和课程满意度。

## 2. 功能范围

### 2.1 核心功能
#### **[核心功能点1]:** 课程框架升级

##### **[核心功能点1.1]:** 课程开场页优化
###### 界面布局与核心UI元素
- **布局参考：** [开场页UI示例](https://static.test.xiaoluxue.cn/demo41/prd_images/image_NdLqbkkfYo9tBdxEBTFcC3S6nWc.png) - 仅关注布局
- **布局原则：** 
    *   **整体卡片容器 (Card Container)**: 作为所有信息和元素的载体。
        *   **章节标签 (Chapter Tab)**: 位于卡片左上角，突出显示。
        *   **内容区域 (Content Area)**: 卡片主体部分，展示具体学习内容。
            *   **小节编号 (Section Number)**: 位于内容区域上方。
            *   **小节标题 (Section Title)**: 位于小节编号下方。
            *   **描述性文本 (Descriptive Text)**: 位于小节标题下方。
        *   **装饰性元素 (Decorative Elements)**:
            *   图钉 (Pin): 位于内容区域右上角。
            *   卡通形象 (Mascot/Character): 位于卡片右下角区域，部分叠加在卡片上。
    *   **背景 (Background)**: 位于卡片之后，为模糊处理的、带有教育相关小图标的图案。
- **核心UI元素清单：**
    - 章节信息（例如：“第一章”）
    - 课程序号（例如：“1.1.1”）
    - 课程名称（例如：“复数的概念”）
    - IP动效
    - 背景图

###### 功能需求与交互原则
- **功能描述：**
    - 开场页用于建立学习仪式感，提高学习专注度。
    - 页面需展示章节信息（后台配置）、课程序号（后台配置）、课程名称（后台配置，取对应业务树末级知识点名称，25个字以内）。
    - 不同学科使用不同的开场页，页面框架不变，主要更换背景图和色值。
- **交互模式：**
    - 进入开场页后自动播放IP动效及对应音频。
    - IP动效和音频播放完成后，自动进入AI课的第一个小节。
- **反馈原则：**
    - 无特定用户操作反馈，主要为自动流程。

**优先级：** P0
**依赖关系：** 无

##### **[核心功能点1.2]:** 课程结算页优化
###### 界面布局与核心UI元素
- **布局参考：** [结算页-完美通关示例](https://static.test.xiaoluxue.cn/demo41/prd_images/image_EchJbsrQSoGCGmxK1JpcMTtkn2d.png) - 仅关注布局
- **布局原则：**
    *   **系统状态栏层**：位于界面最顶部，显示设备基本信息。
    *   **内容展示层**：占据页面的主要区域，进一步可划分为：
        *   **左侧激励区**：
            *   主要激励视觉元素（卡通形象、奖杯、礼盒）
            *   激励标题文字
            *   激励副标题文字
        *   **右侧数据与操作区**：
            *   **学习数据概要模块**：学习用时、答题数、正确率
            *   **掌握度详情模块**：掌握度标签与提示、本次学习提升百分比、当前掌握度进度条、答题详情链接、奖励展示
            *   **后续操作按钮模块**：回看课程按钮、完成按钮
            *   **课程反馈模块**：反馈引导文案、反馈评级选项
- **核心UI元素清单：**
    - 庆祝动效（IP角色、开心/鼓励动效）
    - 庆祝文案（主文案、副文案）
    - 学习表现数据：学习用时、答题数、正确率
    - 掌握度模块：掌握度说明、掌握度完成进度、掌握度变化、能量值（积分）增加
    - 答题详情入口
    - 课程反馈模块（5类评价icon、反馈弹窗）
    - 操作按钮：回看课程、完成

###### 功能需求与交互原则
- **功能描述：**
    - 结算页用于恭喜用户完成课程，展示学习表现，并推荐后续学习路径。
    - 用户完成一节课全部内容学习后进入结算页。
    - 用户在课程列表页点击已学过的课程，若有“查看报告”入口，点击后进入结算页。
    - **庆祝动效及文案：**
        - 动效：
            - 若课程包含多个IP角色，结算页随机选择某个IP。
            - 每个IP有“开心”、“鼓励”两类动效。展示策略依据“动效展示策略”图片（[动效展示策略](https://static.test.xiaoluxue.cn/demo41/prd_images/board_QBEwwqJVOh02Q9bwVXwcSqvmncd.png)）。
                - 掌握度提升：开心动效。
                - 掌握度不变：正确率70%-100%为开心动效，其他情况为鼓励动效。
                - 外化掌握度不会出现下降，当实际知识点掌握下降时，外化掌握度不变。
        - 文案：
            - 文案包括“主文案”和“副文案”两部分。
            - 文案内容和IP相关，每个IP维护一套反馈文案。
            - 根据用户学习表现展示不同反馈文案，依据“反馈文案展示策略”图片（[反馈文案展示策略](https://static.test.xiaoluxue.cn/demo41/prd_images/board_QJQEwg4J2hl7JfbidfIceBcxnQc.png)）。
                - 如果同时命中两种情况，则随机选择其中一种。
                - 一种情况下有多条文案时，随机选择一条展示。
    - **学习表现（同demoV1.0）：**
        - 学习用时：用户完成本节课学习的累计学习时长。
        - 答题数：用户在这节课本次学习中的累计答题数量。
        - 正确率：答对的题目数量 / 答题数量。
            - 正确率 ≤ 50%：红色字体。
            - 50% ＜ 正确率 ＜ 70%：黄色字体。
            - 正确率 ≥ 70%：绿色字体。
        - 已学完的课程又重新学习，答题数量、正确率、时长均不累计。
    - **外化掌握度：**
        - 计算方式详见：[PRD - 掌握度策略 - V1.0](https://wcng60ba718p.feishu.cn/wiki/J3d0wrEaDiWRy6kj2uVcQKLfnmh)
        - **掌握度说明：** 掌握度标题旁展示“i”图标，点击显示说明信息：“掌握度反映了你对本节课的掌握情况，和你的答题正确率、题目难度相关。掌握度完成进度和你设定的学习目标相关，修改目标后会重新计算哦。”
        - **掌握度完成进度：** 用户当前掌握度 / 目标掌握度，保留小数点后两位，范围[0, 1]。若用户未设置目标，默认目标掌握度为0.8。
        - **掌握度变化：** 本次学习后的进度 - 本次学习前的进度。
            - 初始掌握度不用于进度计算，用户首次学习的掌握度变化 = 本次学习后的进度 - 0。
            - 如果用户本次学习后掌握度下降，此处展示的进度不变化。
        - **能量值（积分）增加：**
            - 掌握度进度提升，增加能量值（本期先默认增加20，后续基于积分策略增加）。
            *   **奖励指示：** 以图形化（星星）和数字（例如 x 35）结合的方式，展示用户可能获得的某种形式的奖励或积分数量。
            - 掌握度未提升，不增加能量值。
        - **答题详情：** 点击后进入题目列表页面。
    - **课程反馈：**
        - 展示极差、较差、一般、满意、很棒5类评价icon。
        - 用户点击某个icon后，展示对应的反馈弹窗。
        - 用户可在同一节课多次提交评论。
    - **操作按钮：**
        - 回看课程：点击后重新进入课程。
            - 所有组件都是已解锁状态，用户可以自由学习任何组件。
            - 保留历史答题记录，用户进入练习组件时看到的是答题结果页面。
        - 完成：用户点击后返回课程入口页面。
- **交互模式：**
    - 点击“i”图标显示掌握度说明弹窗。
    - 点击“答题详情”跳转至题目列表页面。
    - 点击评价icon弹出反馈弹窗。
    - 点击“回看课程”按钮，重新进入当前课程。
    - 点击“完成”按钮，退出结算页返回课程入口。
- **反馈原则：**
    - 提交反馈后，toast提示“感谢反馈，我们会持续改进！”。

**优先级：** P0
**依赖关系：** 掌握度计算服务

##### **[核心功能点1.3]:** 结算页-题目列表与详情
###### 界面布局与核心UI元素
- **布局参考：** [题目列表页UI示例](https://static.test.xiaoluxue.cn/demo41/prd_images/image_MGPobJyOlop6YVxdI4kcucIsn2A.png), [题目详情页UI示例](https://static.test.xiaoluxue.cn/demo41/prd_images/image_ONSvbcgWooybvox58b7cR3fVn4f.png) - 仅关注布局
- **布局原则：**
    - **题目列表页:**
        *   **顶部导航栏:** 包含返回按钮、页面标题“题目列表”、筛选控件“仅看错题”。
        *   **题目列表区域:** 垂直滚动列表，每个题目项包含题号、题型、题目描述（超长则省略）、答题状态（图标+文字）、“查看解析”链接。
    - **题目详情页:**
        *   **导航栏:** 包含返回按钮、答题用时、答题进度。
        *   **题目区域:** 包含题型及来源、题干内容、选项列表（含用户选择、正确答案、选择比例）、正确答案显示、题目解析区域。
        *   **操作栏:** 包含“加入错题本”按钮、“上一题”按钮、“下一题”按钮。
- **核心UI元素清单：**
    - **题目列表页：**
        - 返回按钮
        - 页面标题 "题目列表"
        - “仅看错题”筛选勾选框
        - 题目项（题号、题型、题干、作答结果、查看解析按钮）
    - **题目详情页：**
        - 返回按钮
        - 用时显示
        - 题目序号/总数显示
        - 题干内容
        - 选项（含作答结果、选择比例）
        - 正确答案展示
        - 题目解析内容
        - “加入错题本”按钮 (状态可切换为“已加入错题本”)
        - “上一题”按钮
        - “下一题”按钮

###### 功能需求与交互原则
- **功能描述：**
    - **题目列表：**
        - 展示题号、题目类型、题干（默认最多展示2行，超过部分展示...）、作答结果（回答正确、回答错误、部分正确三种情况）。
    - **题目详情：**
        - 展示用时（用户完成这道题目的用时）、序号（当前题目序号 / 列表中的全部题目数量）。
        - “加入错题本”按钮：
            - 如果题目没有加入错题本，按钮展示“加入错题本”。
            - 如果题目已加入错题本，按钮展示“已加入错题本”，不可点击。
        - “上一题”按钮：第一题不展示。
        - “下一题”按钮：最后一题不展示。
- **交互模式：**
    - **题目列表：**
        - 点击“仅看错题”，列表中只展示回答错误和部分正确的题目。
        - 点击“查看解析”，进入对应题目的详情页。
    - **题目详情：**
        - 点击“加入错题本”进行状态切换。
        - 点击“上一题”/“下一题”切换题目详情。
- **反馈原则：**
    - 无特定即时反馈，主要为页面跳转和内容更新。

**优先级：** P0
**依赖关系：** 题目服务，错题本服务

##### **[核心功能点1.4]:** 结算页-课程反馈弹窗
###### 界面布局与核心UI元素
- **布局参考：** [课程反馈弹窗示意图](https://static.test.xiaoluxue.cn/demo41/prd_images/image_IqW3bjKbTogLnVxrMvucpnGMnWf.png) - 仅关注布局 (此图仅展示了评价icon，实际弹窗内容需结合文字描述)
- **布局原则：**
    - 弹窗形式展示。
    - 顶部为标题：“反馈”。
    - 中间为多选选项区域，按评价等级（极差&较差、一般、满意&很棒）分组，每组包含针对不同方面（讲解、题目、解析、问一问）的具体描述选项。
    - 选项下方为文本输入框。
    - 底部为操作按钮：“提交”、“放弃评价”。
- **核心UI元素清单：**
    - 弹窗标题 "反馈"
    - 多选选项组（根据评价等级区分）
        - 讲解相关选项（例如：过于简略、过于啰嗦、枯燥乏味）
        - 题目相关选项（例如：题太多、题太少、太简单、太难了）
        - 解析相关选项（例如：看不懂、过于啰嗦）
        - 问一问相关选项（例如：回答有误、讲解不清楚）
    - 文本输入框 (placeholder: "还有其他想说…")
    - “提交”按钮
    - “放弃评价”按钮

###### 功能需求与交互原则
- **功能描述：**
    - **选项内容：**
        - **极差&较差：**
            - 讲解：过于简略、过于啰嗦、枯燥乏味（“过于简略”和“过于啰嗦”互斥）。
            - 题目：题太多、题太少、太简单、太难了（“题太多”和“题太少”互斥，“太简单”和“太难了”互斥）。
            - 解析：看不懂、过于啰嗦。
            - 问一问：回答有误、讲解不清楚。
        - **一般：**
            - 讲解：有点简略、有点啰嗦、枯燥乏味（“有点简略”和“有点啰嗦”互斥）。
            - 题目：题偏多、题偏少、有点简单、有点难（“题偏多”和“题偏少”互斥，“有点简单”和“有点难”互斥）。
            - 解析：看不懂、有点啰嗦。
            - 问一问：回答有误、讲解不清楚。
        - **满意&很棒：**
            - 讲解：清晰易懂、生动有趣。
            - 题目：题量刚好、难度合适。
            - 解析：清晰易懂。
            - 问一问：回复清晰、对学习有帮助。
    - **输入框：**
        - 标题：反馈
        - placeholder：还有其他想说…
    - **操作按钮：**
        - “提交”按钮默认为不可点击状态，用户选择了某个选项后，切换为可提交状态。
- **交互模式：**
    - 点击选项进行选择/取消选择（互斥选项按规则处理）。
    - 输入框允许用户输入文字。
    - 点击“提交”按钮提交反馈。
    - 点击“放弃评价”按钮关闭弹窗。
- **反馈原则：**
    - 提交后，toast提示“感谢反馈，我们会持续改进！”。

**优先级：** P0
**依赖关系：** 无

##### 用户场景与故事 (针对此核心功能点)

###### [场景1标题：用户完成课程学习查看结算与反馈]
作为**一名学生**，我需要**在完成一节AI课程后查看我的学习表现总结，并对课程进行反馈**以解决**我对学习效果的了解以及对课程的建议无法传达的问题**，从而**更好地了解自己的学习情况并帮助课程改进**。

**前端界面与交互关键步骤：**
1.  **[用户操作1]:** 完成课程所有组件的学习。
    *   **界面变化:** 自动跳转到“结算页”。左侧展示IP形象的庆祝动效（如小鹿从礼盒中跳出并手持奖杯）和激励文案（如“完美通关”，“你是怎么做到这么完美的！”或“别怀疑自己”，“高手也有失手的时候，下次就能反杀回来！”）。右侧展示学习数据概要（学习用时、答题数、正确率）、掌握度详情（提升百分比、当前掌握度进度条、答题详情入口、获得的星星奖励）、操作按钮（“回看课程”、“完成”）以及课程反馈引导（“本节课感受如何？”和五个评价表情icon）。
    *   **即时反馈:** 无特定即时反馈，为页面加载。
2.  **[用户操作2]:** 查看学习表现和掌握度。
    *   **界面变化:** 用户可以清晰看到自己的学习用时、答题总数、正确率。在掌握度模块，可以看到本次学习带来的掌握度提升百分比、当前的掌握度进度条及百分比数值。
    *   **即时反馈:** 无，为信息展示。
3.  **[用户操作3]:** 点击“答题详情 >”链接。
    *   **界面变化:** 页面跳转到“题目列表页”。顶部显示返回按钮、标题“题目列表”和“仅看错题”筛选框。下方列表展示本节课所有做过的题目，包括题号、题型、题干预览、作答结果（如“回答正确”配绿色勾，或“回答错误”配红色叉）和“查看解析”链接。
    *   **即时反馈:** 无特定即时反馈，为页面加载。
4.  **[用户操作4]:** 在题目列表页，点击某道题的“查看解析”链接。
    *   **界面变化:** 页面跳转到该题的“题目详情页”。顶部显示返回按钮、本题用时、题目在列表中的序号/总题数。中间显示题干、所有选项（用户答案和正确答案有明显标识，如背景色和图标，并可能显示各选项的选择比例）、正确答案文字提示、详细的题目解析内容。底部有“加入错题本”、“上一题”、“下一题”操作按钮。
    *   **即时反馈:** 无特定即时反馈，为页面加载。
5.  **[用户操作5]:** 返回结算页，点击“满意”表情的课程反馈icon。
    *   **界面变化:** 弹出“课程反馈”弹窗。弹窗标题为“反馈”。内容区域展示针对“满意&很棒”评价等级的反馈选项，如讲解（清晰易懂、生动有趣）、题目（题量刚好、难度合适）、解析（清晰易懂）、问一问（回复清晰、对学习有帮助）。下方有文本输入框和“提交”、“放弃评价”按钮。“提交”按钮初始为置灰不可点击。
    *   **即时反馈:** 无特定即时反馈，为弹窗加载。
6.  **[用户操作6]:** 在反馈弹窗中，选择“讲解：清晰易懂”和“题目：题量刚好”，并在输入框输入“老师讲得很好！”。
    *   **界面变化:** 选中的选项高亮。“提交”按钮变为可点击状态。
    *   **即时反馈:** 选项选中状态变化，按钮状态变化。
7.  **[用户操作7]:** 点击“提交”按钮。
    *   **界面变化:** 反馈弹窗关闭。结算页出现toast提示“感谢反馈，我们会持续改进！”。
    *   **即时反馈:** toast提示出现并在2秒后消失。
8.  **[用户操作8]:** 点击“完成”按钮。
    *   **界面变化:** 页面返回到课程入口页面。
    *   **即时反馈:** 无特定即时反馈，为页面跳转。

**场景细节补充：**
*   **前置条件:** 用户已登录并进入一节AI课程，且完成了该课程的所有学习组件。
*   **期望结果:** 用户能够清晰了解自己的学习成果，顺利提交课程反馈，并能方便地回看课程或结束学习。
*   **异常与边界情况:**
    *   网络请求失败时，应有友好提示。
    *   用户未设置掌握度目标时，默认目标掌握度为0.8。
    *   已学完课程重新学习，结算页的学习数据（答题数、正确率、时长）不累计。
    *   若掌握度实际下降，外化掌握度不变，掌握度变化显示为0或不提升。

**优先级：** 高
**依赖关系：** 用户登录状态、课程数据服务、掌握度服务、题目服务、反馈提交服务。

**Mermaid图示 (核心功能点主要流程或复杂场景图):**
  ```mermaid
  %% flowchart TD
  %% 目的：清晰展示用户完成课程后的主要流程和关键交互点。
  graph TD
      A[用户完成所有学习组件] --> B{进入结算页};
      B --> C[展示庆祝动效和文案];
      B --> D[展示学习表现数据];
      B --> E[展示掌握度信息];
      B --> F[展示课程反馈入口];
      B --> G[展示操作按钮（回看/完成）];
      D -- 点击答题详情 --> H[题目列表页];
      H -- 点击查看解析 --> I[题目详情页];
      I -- 点击加入错题本 --> J{错题本状态更新};
      I -- 点击上一题/下一题 --> I;
      I -- 点击返回 --> H;
      H -- 点击返回 --> B;
      F -- 点击反馈Icon --> K[弹出反馈弹窗];
      K -- 选择反馈选项/输入内容 --> L{更新提交按钮状态};
      L -- 点击提交 --> M[提交反馈信息];
      M --> N[Toast提示感谢];
      N --> K_Close[关闭反馈弹窗];
      K -- 点击放弃评价 --> K_Close;
      G -- 点击回看课程 --> O[重新进入当前课程（保留记录）];
      G -- 点击完成 --> P[返回课程入口页];
  ```

---
#### **[核心功能点2]:** 课程进度控制与退出机制

##### **[核心功能点2.1]:** 课程进度展示与切换
###### 界面布局与核心UI元素
- **布局参考：** [课程进度弹窗UI示例](https://static.test.xiaoluxue.cn/demo41/prd_images/image_TlI4bWfnFooISex1mYPcUE9Hnuf.png), [文档组件课程进度入口示例](https://static.test.xiaoluxue.cn/demo41/prd_images/image_OS1vbRpk1oBq7lxgd4ycsUH8nxj.png), [练习组件课程进度入口示例](https://static.test.xiaoluxue.cn/demo41/prd_images/image_Wj2mbKm98onhyqxuQOXcdSW3npb.png) - 仅关注布局
- **布局原则：**
    - **入口按钮:** 位于文档组件或练习组件的右上角（通常在“问一问”按钮旁），显示为“课程进度”文字或相应图标。
    - **课程进度弹窗/侧边栏:**
        *   **课程节点列表 (Vertical List of Course Items):** 垂直排列课程的各个组件（文档、练习）。
            *   每个节点显示序号、组件名称、时长（文档）或题数（练习）。
            *   节点状态通过不同视觉方式区分：“已解锁”（如橙色勾号）、“学习中”（如特殊高亮）、“待解锁”（如锁形图标）。
- **核心UI元素清单：**
    - “课程进度”入口按钮
    - 课程进度弹窗/侧边栏
    - 组件列表项（显示组件序号、名称、时长/题数、状态图标：已解锁、学习中、待解锁）

###### 功能需求与交互原则
- **功能描述：**
    - 用户可以在任何组件中，灵活调整学习进度。
    - **展示：**
        - 按照组成一节课程的“文档组件、练习组件”的排序进行展示。
        - 分为“已解锁”、“学习中”、“待解锁”三种状态。
            - 已解锁：用户学习过的组件，全部为已解锁状态。
            - 待解锁：还没有学习的组件为待解锁状态。
            - 学习中：用户当前正在学习的组件。
        - 如果用户“回看课程”（从结算页进入），则所有的组件都为解锁状态。
- **交互模式：**
    - 点击“课程进度”按钮，展示进度弹窗/侧边栏，暂停当前讲解/播放。
    - 点击屏幕其他区域或关闭按钮，收起进度弹窗/侧边栏，继续讲解/播放。
    - 点击“已解锁”状态的组件，可直接跳转到对应组件的内容。
    - **组件跳转逻辑：**
        - 用户点击进入文档组件，从上次跳出的进度开始播放。如果上次已播完，则自动从头开始播放。
        - 用户点击进入练习组件：
            - 如果上次的题目没有答完，从上次跳出的题目开始继续答题。
            - 如果上次的题目已全部答完，从第一题开始展示用户历史答题记录（[历史答题记录UI示例](https://static.test.xiaoluxue.cn/demo41/prd_images/image_HmXSbDdSnoSjajx6iuPcKVOunZc.png)）。用户可点击“下一题”，逐个题目查看后，进入下一个课程组件。
        - 用户跳转到之前学习过的组件，完成后自动进入课程中的下一个组件（文档/练习组件）。
        - 在组件间切换，不影响学习进度的统计，学习进度还是按“已学完的组件数量 / 一节课中文档+练习组件的数量之和”计算。
- **反馈原则：**
    - 跳转到已解锁组件时，界面平滑过渡到目标组件内容。

**优先级：** P0
**依赖关系：** 课程结构配置、用户学习状态记录

##### **[核心功能点2.2]:** 退出/继续课程
###### 界面布局与核心UI元素
- **布局参考：** 左上角退出按钮（参考[文档组件操作面板](https://static.test.xiaoluxue.cn/demo41/prd_images/image_VQpsbi2htooui2xDyXYc3esLn5c.png)中的退出学习按钮）
- **布局原则：** 在课程播放界面的左上角提供一个清晰的“退出学习”或返回图标按钮。
- **核心UI元素清单：**
    - 退出学习按钮（图标或文字）

###### 功能需求与交互原则
- **功能描述：**
    - **退出课程：** 在上课过程中，点击左上角的退出按钮，记录用户的当前进度，返回课程入口页。
    - **退出后再次进入课程的进度策略如下：**
        - 文档组件：从退出的时间点继续播放，默认进入跟随状态。
        - 练习组件：进入用户退出时未答完的题目，且保存退出前的答题结果。
        - 答疑组件（若存在并被调起）：进入调起答疑组件的文档/练习组件，默认不进入答疑组件。当用户再次点击答疑组件时，可查看历史对话记录。
        - ⚠️ 文档中有一条重复的描述：“文档组件：从退出的时间点继续播放，默认进入跟随状态”，及“练习组件：进入用户退出时查看题目解析的页面”，根据上下文，应以第一条为准。
- **交互模式：**
    - 用户点击退出按钮。
    - 系统记录当前学习进度。
    - 页面跳转回课程入口页。
    - 用户再次进入同一课程时，按上述策略恢复学习进度。
- **反馈原则：**
    - 无特定即时反馈，为页面跳转和状态保存。

**优先级：** P0
**依赖关系：** 用户学习状态记录

##### 用户场景与故事 (针对此核心功能点)

###### [场景1标题：用户学习中途调整学习进度]
作为**一名学生**，我需要**在学习过程中方便地跳转到课程的不同部分**以解决**有时我想复习前面内容或预习后面内容的需求**，从而**更灵活地掌握学习节奏**。

**前端界面与交互关键步骤：**
1.  **[用户操作1]:** 在文档组件学习时，点击右上角的“课程进度”按钮。
    *   **界面变化:** 当前文档讲解暂停。屏幕右侧滑出（或居中弹出）课程进度列表。列表显示本节课所有组件的序号、名称、时长/题数，并标记各组件状态（如“01 课程引入 02:44 已解锁√”、“03 复数相等的条件 00:32/02:44 学习中”、“04 随堂练习1 共3道题 待解锁🔒”）。
    *   **即时反馈:** 讲解暂停，进度列表出现。
2.  **[用户操作2]:** 在课程进度列表中，点击一个“已解锁”状态的“01 课程引入”组件。
    *   **界面变化:** 课程进度列表收起。界面跳转到“01 课程引入”文档组件，并从上次离开时的播放点（或从头）开始播放。
    *   **即时反馈:** 界面平滑过渡到所选组件。
3.  **[用户操作3]:** 学习完“01 课程引入”，系统自动进入“02 复数的分类”。此时再次点击“课程进度”按钮。
    *   **界面变化:** 当前文档讲解暂停。课程进度列表出现，此时“01 课程引入”和“02 复数的分类”均显示为“已解锁√”，“03 复数相等的条件”仍为“学习中”。
    *   **即时反馈:** 讲解暂停，进度列表出现。
4.  **[用户操作4]:** 点击一个“已解锁”状态的“随堂练习1 (旧)”组件，该练习上次已做完。
    *   **界面变化:** 课程进度列表收起。界面跳转到“随堂练习1”的第一题，并展示上次的答题记录（如选项、对错情况、解析）。用户可以通过“下一题”按钮回顾所有题目。
    *   **即时反馈:** 界面平滑过渡到练习回顾界面。

**场景细节补充：**
*   **前置条件:** 用户已进入AI课程的学习界面。
*   **期望结果:** 用户可以自由、顺畅地在课程的不同已解锁组件间跳转，并能从正确的断点继续学习或回顾。
*   **异常与边界情况:**
    *   点击“待解锁”组件应无反应或提示需先完成前置学习。

**优先级：** 高
**依赖关系：** 课程组件状态管理，用户学习进度持久化。

**Mermaid图示 (课程进度切换流程):**
  ```mermaid
  flowchart TD
      A[用户在学习组件X中] -- 点击 --> B(课程进度按钮);
      B --> C{展示课程进度列表};
      C -- 点击已解锁组件Y --> D{跳转到组件Y};
      D -- 若为文档组件 --> E[从上次播放点/从头播放];
      D -- 若为练习组件 (未完成) --> F[从上次未答题目开始];
      D -- 若为练习组件 (已完成) --> G[展示历史答题记录];
      C -- 点击屏幕其他区域/关闭 --> H{收起进度列表};
      H --> A_Continue[继续学习组件X];
  ```

###### [场景2标题：用户中途退出课程后重新进入]
作为**一名学生**，我需要**在我中途退出课程后，下次进入时能从上次的进度继续学习**以解决**学习被打断后需要重新找进度的问题**，从而**无缝衔接学习过程**。

**前端界面与交互关键步骤：**
1.  **[用户操作1]:** 在文档组件A学习到一半时，点击左上角的“退出学习”按钮。
    *   **界面变化:** 界面返回到课程列表页或应用首页。
    *   **即时反馈:** 无，直接跳转。
2.  **[用户操作2]:** 一段时间后，重新从课程列表页点击进入同一门课程。
    *   **界面变化:** 课程直接打开到文档组件A，并从上次退出的时间点开始继续播放内容，模式为“跟随模式”。
    *   **即时反馈:** 无，直接加载到上次学习位置。
3.  **[用户操作3]:** (假设上次退出时在练习组件B的第3题，已选但未提交) 点击左上角“退出学习”按钮。
    *   **界面变化:** 返回课程列表页。
    *   **即时反馈:** 无。
4.  **[用户操作4]:** 再次进入该课程。
    *   **界面变化:** 课程直接打开到练习组件B的第3题，之前选择的答案被保留。
    *   **即时反馈:** 无，加载到上次练习题目和状态。

**场景细节补充：**
*   **前置条件:** 用户已登录并至少开始了一节AI课程的学习。
*   **期望结果:** 用户退出课程后，学习进度（包括文档播放位置、练习作答状态）被准确记录，并在下次进入时恢复。
*   **异常与边界情况:**
    *   若记录的组件ID或时间戳无效，应有合理的降级处理（如从该组件开头或课程开头开始）。

**优先级：** 高
**依赖关系：** 用户学习进度持久化服务。

**Mermermaid图示 (退出与继续学习流程):**
  ```mermaid
  sequenceDiagram
      participant User as 用户
      participant Frontend as 前端应用
      participant Backend as 后端服务

      User->>Frontend: 在组件X中点击退出按钮
      Frontend->>Backend: 保存当前学习进度 (课程ID, 组件ID, 进度详情)
      Frontend->>User: 返回课程入口页

      %% 一段时间后
      User->>Frontend: 重新进入同一课程
      Frontend->>Backend: 请求获取上次学习进度 (课程ID)
      Backend-->>Frontend: 返回上次学习进度信息
      Frontend->>User: 加载到组件X的对应进度并继续
  ```

---
#### **[核心功能点3]:** 文档组件内容播放与交互优化

##### **[核心功能点3.1]:** 模式切换（跟随/自由）
###### 界面布局与核心UI元素
- **布局参考：** [跟随模式UI示例](https://static.test.xiaoluxue.cn/demo41/prd_images/image_V3dGbOlzAoWZNaxWj3zc8qzhnhh.png), [自由模式UI示例](https://static.test.xiaoluxue.cn/demo41/prd_images/image_V0m5bACxAoYd7YxhP9ic0tSOnPe.png), [toast提示UI示例](https://static.test.xiaoluxue.cn/demo41/prd_images/image_HQhobUzWco56ohxVeixc3woenZR.png) - 仅关注布局
- **布局原则：**
    - **跟随模式:**
        *   左侧主要区域展示老师板书（JS动画内容）和同步的勾画轨迹。
        *   右侧（或特定区域）播放数字人视频。
        *   底部（或叠加在视频上）展示字幕。
        *   右上角常驻显示“问一问”和“课程进度”按钮。
    - **自由模式:**
        *   当用户滑动课件时触发。
        *   在板书内容区域的显眼位置（通常为底部或浮动）展示“跟随老师”按钮。
        *   老师板书、勾画轨迹、数字人视频均暂停。
- **核心UI元素清单：**
    - 左侧板书内容区（JS动画、勾画轨迹）
    - 右侧数字人视频区
    - 字幕展示区
    - “问一问”按钮
    - “课程进度”按钮
    - “跟随老师”按钮 (自由模式下出现)
    - Toast提示框

###### 功能需求与交互原则
- **功能描述：**
    - 进入课程后，默认进入“跟随模式”。
        - 左侧播放老师板书（JS动画）+ 勾画轨迹。
        - 右侧播放数字人视频。
        - 默认展示字幕。
    - 当用户滑动课件内容时，进入“自由模式”。
        - 老师板书（JS动画）+ 勾画轨迹 + 数字人视频全部暂停播放。
        - 展示“跟随老师”按钮。
        - 页面移动距离小于预设阈值x时，不进入“自由模式”。
    - **返回跟随模式：**
        - 用户点击“跟随老师”按钮。
        - 用户双击屏幕。
- **交互模式：**
    - **进入自由模式：** 用户在板书内容区进行滑动操作（超过阈值x）。
    - **返回跟随模式：**
        - 点击“跟随老师”按钮。
        - 双击屏幕任何区域。
- **反馈原则：**
    - 返回跟随模式时，页面出现toast提示“已定位到老师位置”，2s后消失。

**优先级：** P0
**依赖关系：** 无

##### **[核心功能点3.2]:** 内容同步播放控制
###### 界面布局与核心UI元素
- **布局参考：** 参考[跟随模式UI示例](https://static.test.xiaoluxue.cn/demo41/prd_images/image_V3dGbOlzAoWZNaxWj3zc8qzhnhh.png) - 仅关注布局
- **布局原则：**
    - 在跟随模式下，板书内容块（如句子）下方应有进度线，随讲解动态更新位置。
- **核心UI元素清单：**
    - 板书内容（JS动画）
    - 勾画轨迹
    - 数字人视频
    - 字幕
    - 播放进度线 (在板书句子下方)

###### 功能需求与交互原则
- **功能描述：**
    - **跟随模式：**
        - 数字人视频、勾画轨迹和JS动画内容自动播放，三个内容间的进度保持一致。
        - 播放时，板书对应的内容块（句子）下方展示进度线，表示目前讲到这里了。
        - 如果老师讲解内容在多个句子中上下来回跳跃，进度线始终在最后的一个句子后面，不跟着讲解上下跳跃。
        - 未播放的区域呈现模糊/弱化效果，让学生的注意力聚焦在当前内容块上。(原始PRD表格中提及，但模式切换描述中未提及，此处根据表格补充)
    - **自由模式：**
        - 进入自由模式后老师板书（JS动画）+ 勾画轨迹 + 数字人视频全部暂停播放。
        - 已展示的高亮内容及勾画轨迹仍然展示。
- **交互模式：**
    - 播放控制主要由系统在跟随模式下自动进行，或由用户通过后续定义的快捷交互进行。
- **反馈原则：**
    - 进度线实时更新，与讲解内容同步。
    - 模糊/弱化效果动态应用于未播放区域。

**优先级：** P0
**依赖关系：** 时间轴同步服务

##### **[核心功能点3.3]:** 文档组件快捷交互
###### 界面布局与核心UI元素
- **布局参考：** [单击文档-从这里学-文字示例](https://static.test.xiaoluxue.cn/demo41/prd_images/image_RDuzbOPUYoyDHTxWwdqcQDslnwb.png), [单击文档-从这里学-公式示例](https://static.test.xiaoluxue.cn/demo41/prd_images/image_KgQob6BNFoxTTOxHT5ScxdZWnYf.png), [操作面板UI示例](https://static.test.xiaoluxue.cn/demo41/prd_images/image_VQpsbi2htooui2xDyXYc3esLn5c.png) - 仅关注布局
- **布局原则：**
    - **从这里学按钮:** 单击文档内容（句子或公式）后，在对应内容附近或上下文菜单中出现“从这里学”按钮。
    - **操作面板:** 单击文档以外区域时，以浮层或底部抽屉形式展示操作面板。
        *   左上角展示“退出学习”按钮。
        *   数字人视频区域（或面板中对应视频控制区域）展示字幕开关、播放/暂停按钮、倍速选择、前进/后退10s按钮。
- **核心UI元素清单：**
    - “从这里学”按钮
    - 操作面板
        - “退出学习”按钮
        - 字幕开关按钮
        - 播放/暂停按钮
        - 倍速选择按钮/列表
        - 前进10s按钮
        - 后退10s按钮

###### 功能需求与交互原则
- **功能描述：**
    - 跟随模式和自由模式均支持以下快捷交互：
    - **1. 单击没有评论的文档内容 - 从这里学：**
        - 当前内容所在的句子高亮展示，并出现“从这里学”按钮（同demoV1.1）。
        - 用户点击“从这里学”，数字人视频 & 勾画轨迹从句子对应的时间戳开始播放。
        - 自由模式下点击“从这里学”，自动切换为跟随模式。
    - **2. 单击文档以外区域 - 唤起操作面板。**
    - **3. 单击有评论的文档内容 - 展示评论内容（本期不做）。**
    - **4. 长按文档内容：选中文字/图片内容，唤起问一问、评论操作（本期不做）。**
        - 用户可拖拽高亮条，选择文字。
        - 点击问一问，进入答疑组件。
        - 点击评论，唤起键盘，用户可输入内容进行发表。
    - **5. 长按文档以外区域：3倍速播放内容，松手后恢复原始速度。**
    - **6. 双击 - 切换播放 / 暂停状态。** (此双击功能也用于返回跟随模式，见核心功能点3.1)
- **交互模式：**
    - **单击文档内容（无评论）：** 句子高亮，显示“从这里学”按钮。点击按钮则从该处开始播放并进入跟随模式。
    - **单击文档以外区域：** 弹出/显示操作面板。
    - **长按文档以外区域：** 视频、板书、勾画以3倍速播放。松手恢复原速。
    - **双击屏幕：**
        - 如果当前是播放状态，则暂停所有内容。
        - 如果当前是暂停状态，则恢复播放所有内容。
        - 如果当前是自由模式，则切换回跟随模式并从老师当前进度播放。
- **反馈原则：**
    - “从这里学”后，内容从指定位置开始播放。
    - 长按加速时，播放速度明显加快。
    - 双击切换播放/暂停状态时，播放状态相应改变。

**优先级：** P0
**依赖关系：** 时间轴控制服务

##### **[核心功能点3.4]:** 操作面板功能
###### 界面布局与核心UI元素
- **布局参考：** [操作面板UI示例](https://static.test.xiaoluxue.cn/demo41/prd_images/image_VQpsbi2htooui2xDyXYc3esLn5c.png) - 仅关注布局
- **布局原则：**
    *   **A. 顶部导航栏 (Header Bar)**
        *   A1. 返回按钮 (Back Arrow Icon) (此处指操作面板内的退出学习)
        *   A2. 课程标题 (Text: "等差数列的表示方法") (主界面标题，非面板内)
        *   A3. 辅助功能区 (主界面按钮，非面板内)
            *   A3.1. "问一问"按钮 (Q&A/Help Icon)
            *   A3.2. 目录/列表按钮 (List Icon)
    *   **B. 主要内容区域 (Main Content Area)** (主界面内容)
    *   **C. 视频控制栏 (Video Control Bar / 操作面板核心区域)**
        *   C1. 字幕按钮 (Text: "字幕")
        *   C2. 倍速播放按钮 (Text: "倍速")
        *   C3. 后退10秒按钮 (Rewind Icon)
        *   C4. 播放/暂停按钮 (Play/Pause Icon)
        *   C5. 快进10秒按钮 (Fast Forward Icon)
- **核心UI元素清单：**
    - “退出学习”按钮 (在操作面板左上角)
    - 字幕开关按钮
    - 播放/暂停按钮
    - 倍速选择按钮 (点击后可展开0.75、1.0、1.25、1.5、1.75、2.0倍速选项)
    - 前进10s按钮
    - 后退10s按钮

###### 功能需求与交互原则
- **功能描述：**
    - 跟随模式和自由模式均支持唤起操作面板，单击文档以外区域可唤起。
    - 自由模式下，唤起操作面板后，不展示“跟随老师”按钮。
    - **页面元素：**
        - 左上角展示“退出学习”。
        - 数字人区域展示“视频播控”：包括字幕、播放/暂停、倍速、前进/后退10s。
    - **视频播控：**
        - **字幕：** 点击切换字幕开关。默认展示字幕。
        - **暂停/播放：** 点击暂停按钮，暂停所有内容的播放（数字人、JS板书、勾画），再次点击时恢复播放。
        - **视频倍速：** 倍速需保持数字人、JS板书、勾画内容的播放进度一致。
            - 点击倍速按钮，可选择倍速播放，支持0.75、1.0、1.25、1.5、1.75、2.0六种倍速选择。
            - 全局倍速，设置倍速后对一节课中的所有视频组件生效。退出课程下次重新进入课程，恢复正常速度（1.0倍）。
        - **前进/后退10s：** 点击后，数字人、JS板书、勾画内容进度同步变化10秒。
- **交互模式：**
    - 点击“退出学习”按钮，行为同核心功能点2.2。
    - 点击“字幕”按钮，切换字幕的显示/隐藏状态。
    - 点击“播放/暂停”按钮，切换播放状态。
    - 点击“倍速”按钮，弹出倍速选项列表，选择后应用新倍速。
    - 点击“前进10s”或“后退10s”按钮，播放进度相应跳转。
    - 点击操作面板以外的区域，操作面板收起。
- **反馈原则：**
    - 字幕状态实时切换。
    - 播放/暂停状态实时切换。
    - 倍速应用后，播放速度变化。
    - 播放进度条（若有）和播放内容实时响应前进/后退操作。

**优先级：** P0
**依赖关系：** 时间轴控制服务，用户偏好设置存储（用于全局倍速的临时记忆）

##### **[核心功能点3.5]:** 组件间切换逻辑
###### 界面布局与核心UI元素
- 无特定新增UI元素，依赖现有组件布局。

###### 功能需求与交互原则
- **功能描述：**
    - 分为三种情况：
    - **1. 从文档组件进入文档组件：**
        - 在“跟随模式”下正常学习，数字人视频播完后，自动进入下一个组件，无转场动效。
        - 在“跟随模式”和“自由模式”向上滑动文档，进入下一学习内容。
            - 当前片段的JS动画滑动到底部时，会出现“阻尼效果”，用户继续向上滑动将切换至下一个组件的内容。
            - 进入下一课程组件后，默认为跟随模式。
    - **2. 从文档组件进入练习组件：**
        - “跟随模式”和“自由模式”下均展示切换动效。
    - **3. 从练习组件进入文档组件：**
        - “跟随模式”和“自由模式”下均展示切换动效。
    - (原始PRD表格中去掉了组件间的串场动效，但此处描述中部分保留，此处按最新描述保留动效。)
- **交互模式：**
    - **自动切换：** 文档组件在跟随模式下播放完毕，自动加载并播放下一个文档组件。
    - **手动滑动切换（文档至文档）：** 用户在文档底部继续上滑，触发切换到下一个文档组件，并进入跟随模式。
    - **切换至练习/从练习切换：** 组件切换时伴随动效。
- **反馈原则：**
    - 切换动效平滑自然。
    - 组件加载成功后，内容正确显示。

**优先级：** P0
**依赖关系：** 课程结构配置

##### 用户场景与故事 (针对此核心功能点)

###### [场景1标题：用户在文档组件中进行快捷操作与模式切换]
作为**一名学生**，我需要**在学习AI课件时能方便地控制播放、调整观看模式和速度**以解决**固定播放流程无法满足个性化学习节奏的问题**，从而**更高效地学习和理解课程内容**。

**前端界面与交互关键步骤：**
1.  **[用户操作1]:** 进入文档组件，默认为“跟随模式”，老师讲解与板书同步播放，字幕默认显示。
    *   **界面变化:** 左侧为板书（JS动画+勾画轨迹），右侧为数字人视频，底部有字幕。板书当前讲解句子下方有进度线。右上角有“问一问”、“课程进度”按钮。
    *   **即时反馈:** 内容自动播放。
2.  **[用户操作2]:** 在板书内容区域向下滑动屏幕。
    *   **界面变化:** 视频、板书、勾画轨迹均暂停播放。屏幕上出现“跟随老师”按钮。进入“自由模式”。
    *   **即时反馈:** 播放暂停，出现“跟随老师”按钮。
3.  **[用户操作3]:** 在自由模式下，双击屏幕。
    *   **界面变化:** “跟随老师”按钮消失。视频、板书、勾画轨迹从老师当前讲解的进度（即暂停时的进度）开始继续播放。屏幕出现toast提示“已定位到老师位置”，2秒后消失。切换回“跟随模式”。
    *   **即时反馈:** 播放恢复，toast提示。
4.  **[用户操作4]:** 在跟随模式下，单击板书中的某一句历史文字（非当前讲解处）。
    *   **界面变化:** 被单击的句子高亮，旁边出现“从这里学”按钮。
    *   **即时反馈:** 句子高亮，按钮出现。
5.  **[用户操作5]:** 点击“从这里学”按钮。
    *   **界面变化:** 视频、板书、勾画轨迹立即跳转到该句子对应的时间戳开始播放。模式仍为“跟随模式”。
    *   **即时反馈:** 播放内容跳转。
6.  **[用户操作6]:** 在跟随模式下，单击文档区域以外的空白处。
    *   **界面变化:** 屏幕下方（或特定区域）弹出“操作面板”。面板包含“退出学习”按钮、字幕开关、播放/暂停按钮、倍速按钮、前进10s、后退10s按钮。
    *   **即时反馈:** 操作面板出现。
7.  **[用户操作7]:** 在操作面板中，点击“倍速”按钮，选择“1.5”倍速。
    *   **界面变化:** 倍速按钮显示为“1.5x”。视频、板书、勾画轨迹均以1.5倍速播放。
    *   **即时反馈:** 播放速度加快。
8.  **[用户操作8]:** 在操作面板中，点击“字幕”按钮。
    *   **界面变化:** 字幕消失。字幕按钮状态改变（如变为未选中）。
    *   **即时反馈:** 字幕隐藏。
9.  **[用户操作9]:** 长按文档区域以外的空白处。
    *   **界面变化:** 视频、板书、勾画以3倍速快速播放。
    *   **即时反馈:** 播放速度显著加快。
10. **[用户操作10]:** 松开长按。
    *   **界面变化:** 播放速度恢复到之前的设定速度（如1.5倍速）。
    *   **即时反馈:** 播放速度恢复。
11. **[用户操作11]:** (跟随模式) 向上滑动文档到底部，继续向上滑动。
    *   **界面变化:** 出现阻尼效果，然后切换到下一个文档组件，并从头开始以“跟随模式”播放。
    *   **即时反馈:** 组件切换，新内容开始播放。

**场景细节补充：**
*   **前置条件:** 用户已进入AI课程的文档组件学习界面。
*   **期望结果:** 用户能够通过各种快捷手势和操作面板灵活控制学习过程，模式切换顺畅，播放控制精准。
*   **异常与边界情况:**
    *   快速连续进行互斥操作（如双击后立即长按）时，系统应有合理响应，避免状态混乱。
    *   “从这里学”定位的时间戳不存在或无效时，应有降级处理（如从最近的有效时间点开始）。

**优先级：** 高
**依赖关系：** 时间轴同步与控制服务、手势识别库。

**Mermaid图示 (文档组件交互流程):**
  ```mermaid
  stateDiagram-v2
      [*] --> 跟随模式: 进入文档组件
      跟随模式 --> 自由模式: 滑动课件内容
      自由模式 --> 跟随模式: 点击“跟随老师”按钮
      自由模式 --> 跟随模式: 双击屏幕
      跟随模式 --> 跟随模式: 双击屏幕 (播放/暂停切换)
      自由模式 --> 自由模式: 双击屏幕 (播放/暂停切换)

      state 跟随模式 {
          [*] --> 播放中
          播放中 --> 暂停中: 单击操作面板“暂停” / 双击屏幕
          暂停中 --> 播放中: 单击操作面板“播放” / 双击屏幕
          播放中 --> 从指定点播放: 单击“从这里学”
          播放中 --> 加速播放: 长按非文档区
          加速播放 --> 播放中: 松开长按
          播放中 --> 调整倍速: 操作面板选择倍速
          播放中 --> 内容跳转: 操作面板+/-10s
          播放中 --> 字幕切换: 操作面板切换字幕
          播放中 --> 下一组件: 当前组件播放完毕 / 上滑到底切换
      }
      state 自由模式 {
          [*] --> 已暂停
          已暂停 --> 播放中 (并转至跟随模式): 单击“从这里学”
          已暂停 --> 跟随模式 (从老师位置播放): 点击“跟随老师” / 双击屏幕
          已暂停 --> 调整倍速: 操作面板选择倍速 (不影响当前暂停状态，下次播放生效)
          已暂停 --> 内容跳转: 操作面板+/-10s (跳转后仍暂停)
          已暂停 --> 字幕切换: 操作面板切换字幕
          已暂停 --> 下一组件: 上滑到底切换 (进入后为跟随模式)
      }

      跟随模式 --> 操作面板可见: 单击非文档区
      自由模式 --> 操作面板可见: 单击非文档区
      操作面板可见 --> 跟随模式: 点击面板外区域 (若原为跟随)
      操作面板可见 --> 自由模式: 点击面板外区域 (若原为自由)
  ```

---
### 2.2 辅助功能
- **[辅助功能点1]:** 答疑组件入口
    - **功能详述与前端呈现:** 在文档组件和练习组件的右上角（通常与“课程进度”按钮相邻）常驻显示“问一问”按钮（通常为云朵和笑脸图标或文字）。用户点击该按钮后，会从当前界面（文档或练习）调起答疑组件（通常为弹窗或侧边栏形式），允许用户输入问题并与AI进行对话。答疑组件会保留历史对话记录，即使用户暂时关闭答疑组件或在课程组件间切换，再次打开时也能看到之前的问答内容。
    - **Mermaid图示 (可选，若交互复杂则建议提供):**
      ```mermaid
      sequenceDiagram
          participant User as 用户
          participant CurrentComponent as 当前组件（文档/练习）
          participant QA_Component as 答疑组件

          User->>CurrentComponent: 点击“问一问”按钮
          CurrentComponent->>QA_Component: 请求调起/显示
          QA_Component-->>User: 展示答疑界面（含历史记录）
          User->>QA_Component: 输入问题并发送
          QA_Component->>QA_Component: 处理问题并生成回答
          QA_Component-->>User: 展示回答内容
          User->>CurrentComponent: 点击关闭/其他区域 (隐藏答疑组件)
          CurrentComponent->>QA_Component: 请求隐藏
      ```
- **[辅助功能点2]:** 退出学习确认 (⚠️基于通用体验补充)
    - **功能详述与前端呈现:** 当用户在课程学习过程中（非结算页）点击“退出学习”按钮时，为了防止误操作导致学习进度中断，可以弹出一个简短的确认对话框。例如，提示“确定要退出当前学习吗？您的学习进度将会保存。”提供“确定退出”和“继续学习”两个选项。
    - **Mermaid图示 (可选，若交互复杂则建议提供):**
      ```mermaid
      flowchart TD
          A[用户点击“退出学习”按钮] --> B{弹出确认对话框};
          B -- 点击“确定退出” --> C[保存进度并退出到课程入口页];
          B -- 点击“继续学习”/关闭对话框 --> D[停留在当前学习界面];
      ```

### 2.3 非本期功能
- **[功能点1]:** 文档内容评论功能, 用户单击有评论的文档内容展示评论，长按文档内容唤起评论操作, 原始PRD描述为“本期不做”。
- **[功能点2]:** 长按文档内容选中文字/图片后快捷提问, 原始PRD描述为“本期不做”。
- **[功能点3]:** 框架中加入积分体系和小组战队, 原始PRD描述为“未来会做什么”。
- **[功能点4]:** 支持互动讲题，自动化逐步讲解题目，并在讲解过程中加入互动问题, 原始PRD描述为“未来会做什么”。
- **[功能点5]:** 增加费曼组件、互动组件, 原始PRD描述为“未来会做什么”。

## 3. 业务流程图 (可选，用于整体业务流)

```mermaid
graph TD
  A[用户进入应用] --> B{选择AI课程};
  B --> C[进入课程开场页];
  C -- 动效播放完毕 --> D[进入第一个课程组件（如文档组件1）];
  D -- 学习中 --> E{课程进度控制};
  E -- 点击课程进度 --> F[展示课程组件列表];
  F -- 选择已解锁组件X --> G[跳转到组件X学习];
  G --> D;
  D -- 组件学习中/完成 --> H{判断组件类型};
  H -- 文档组件 --> I{文档组件交互};
  I -- 滑动/双击/单击等 --> I;
  I -- 内容播放完毕/上滑切换 --> J[进入下一组件（文档/练习）];
  J --> D;
  H -- 练习组件 --> K[练习组件交互（答题/查看解析）];
  K -- 练习完成 --> J;
  D -- 所有组件学习完毕 --> L[进入课程结算页];
  L --> M[展示学习总结与反馈];
  M -- 点击回看 --> D;
  M -- 点击完成 --> N[返回课程入口页];
  D -- 学习中点击退出 --> O{保存进度};
  O --> N;

  subgraph "通用交互"
    P[任意学习组件] -- 点击“问一问” --> Q[调起答疑组件];
    Q -- 进行问答 --> Q;
    Q -- 关闭 --> P;
  end
```

## 4. 性能与安全需求

### 4.1 性能需求
-   **响应时间：**
    -   课程开场页IP动效加载及播放启动应在 [2] 秒内。
    -   结算页数据加载与展示（学习用时、答题数、正确率、掌握度）应在 [3] 秒内。
    -   文档组件内容（JS动画、勾画轨迹、数字人视频）切换与加载应流畅，避免明显卡顿，首次进入或跳转后主要内容展示在 [2] 秒内。
    -   快捷交互（如双击暂停/播放、长按3倍速）响应时间应小于 [200] 毫秒。
    -   操作面板弹出时间应小于 [300] 毫秒。
-   **并发用户：** 暂无高并发场景，主要为单用户体验。
-   **数据量：**
    -   课程进度列表支持展示至少 [20] 个组件项，滚动流畅。
    -   题目列表页支持流畅加载和展示至少 [50] 条题目记录。
-   **前端性能感知：**
    -   **[文档组件加载]**: 首次加载或组件间切换时，应有明确的加载状态指示（如骨架屏或loading动画），避免白屏。
    -   **[勾画轨迹与JS动画同步]**: 确保勾画、JS动画与数字人视频、音频时间轴精确同步，避免出现音画不同步或动画卡顿。
    -   **[自由模式与跟随模式切换]**: 模式切换过程应平滑，不应有视觉跳跃或延迟感。

### 4.2 安全需求
-   **权限控制：** 课程内容本身无特殊权限，但依赖于用户对课程的购买或授权状态（由外层系统控制）。
-   **数据加密：** 用户学习进度、答题记录等敏感数据在前后端传输过程中必须使用 HTTPS。前端避免在本地（如localStorage）存储明文的用户答案等敏感信息。
-   **操作审计：** 课程反馈提交时，需记录用户ID、课程ID、反馈内容及时间。
-   **输入校验:** 课程反馈弹窗中的文本输入框，应对输入内容长度进行限制（如500字以内），防止超长文本提交。

## 5. 验收标准

### 5.1 功能验收
-   **[核心功能点1.1：课程开场页优化]：**
    -   **用户场景：** 用户首次进入一节新课程。
    -   **界面与交互：**
        -   开场页按设计展示章节、课程序号、课程名称、IP动效和背景图。
        -   IP动效与音频自动播放，播放完毕后自动跳转到第一个课程组件。
        -   不同学科配置的开场页，背景图和色值符合配置。
    -   **期望结果：** 开场页流程顺畅，信息展示正确，符合仪式感设计。
-   **[核心功能点1.2：课程结算页优化]：**
    -   **用户场景：** 用户完成一节课所有组件的学习。
    -   **界面与交互：**
        -   结算页正确展示随机IP的庆祝动效和对应文案（根据掌握度、正确率策略）。
        -   学习表现（用时、答题数、正确率及颜色）计算和展示准确，符合不累计原则。
        -   掌握度信息（说明、完成进度、变化、积分）展示正确，符合计算规则（包括默认目标、下降时不变等）。
        -   点击“答题详情”能正确跳转到题目列表页。
        -   课程反馈功能正常，点击icon能弹出对应选项的反馈弹窗，提交后有toast提示。
        -   “回看课程”能解锁所有组件并保留答题记录，“完成”能返回课程入口。
    -   **期望结果：** 结算页信息完整准确，交互符合预期，用户能清晰了解学习成果并进行反馈。
-   **[核心功能点1.3：结算页-题目列表与详情]：**
    -   **用户场景：** 用户在结算页点击“答题详情”，并查看具体题目。
    -   **界面与交互：**
        -   题目列表页正确展示题号、题型、题干（超长省略）、作答结果。
        -   “仅看错题”筛选功能有效。
        -   点击“查看解析”能跳转到对应题目详情页。
        -   题目详情页正确展示用时、序号、题干、选项（含用户选择、正确答案、选择比例）、解析。
        -   “加入错题本”按钮状态正确切换，上一题/下一题功能在边界条件下（首/尾题）正确显隐。
    -   **期望结果：** 题目列表和详情展示清晰、准确，筛选和导航功能正常。
-   **[核心功能点2.1：课程进度展示与切换]：**
    -   **用户场景：** 用户在学习中途，希望查看整体进度或跳转到其他组件。
    -   **界面与交互：**
        -   点击“课程进度”按钮能正常展示进度列表，并暂停当前讲解。
        -   进度列表正确显示各组件状态（已解锁、学习中、待解锁）。
        -   点击已解锁组件能正确跳转，并从正确断点开始学习/回顾。
        -   跳转后，学习进度统计不受影响。
    -   **期望结果：** 课程进度功能稳定，用户可以方便地在课程各部分间导航。
-   **[核心功能点2.2：退出/继续课程]：**
    -   **用户场景：** 用户学习中途退出，之后重新进入课程。
    -   **界面与交互：**
        -   点击退出按钮能保存当前进度并返回课程入口页。
        -   再次进入课程时，能准确恢复到上次退出的文档播放时间点（跟随模式）或练习题目（含作答状态）。
    -   **期望结果：** 学习进度保存和恢复功能可靠，用户体验连贯。
-   **[核心功能点3.1 & 3.2：模式切换与内容同步播放]：**
    -   **用户场景：** 用户在文档组件中学习，体验跟随和自由模式。
    -   **界面与交互：**
        -   默认进入跟随模式，板书、勾画、视频、字幕同步播放，进度线准确。
        -   滑动课件内容（超阈值）能切换到自由模式，所有播放暂停，出现“跟随老师”按钮。
        -   点击“跟随老师”或双击屏幕能返回跟随模式，并toast提示。
        -   未播放区域呈现模糊/弱化效果。
    -   **期望结果：** 模式切换顺畅，内容播放同步精确，视觉聚焦效果符合设计。
-   **[核心功能点3.3 & 3.4：文档组件快捷交互与操作面板]：**
    -   **用户场景：** 用户使用各种快捷方式控制文档学习。
    -   **界面与交互：**
        -   单击文档内容显示“从这里学”，点击后能从该处播放并进入跟随模式。
        -   单击非文档区唤起操作面板，面板功能（退出、字幕、播/暂、倍速、±10s）均有效，倍速全局生效。
        -   长按非文档区3倍速播放，松手恢复。
        -   双击屏幕切换播放/暂停状态（同时在自由模式下可返回跟随模式）。
    -   **期望结果：** 所有快捷交互功能响应灵敏准确，操作面板功能齐全且符合预期。
-   **[核心功能点3.5：组件间切换逻辑]：**
    -   **用户场景：** 用户在一个组件学习完毕或手动操作切换到下一个组件。
    -   **界面与交互：**
        -   文档到文档：跟随模式下播完自动切换；手动上滑到底后继续上滑切换到下一文档（跟随模式）。
        -   文档到练习/练习到文档：出现切换动效。
    -   **期望结果：** 组件切换逻辑清晰，过渡自然。

### 5.2 性能验收
-   **响应时间：**
    -   **测试用例1:** 首次进入课程，开场页IP动效加载及播放启动时间 < 2秒。
    -   **测试用例2:** 完成课程进入结算页，学习数据（用时、答题数、正确率、掌握度）加载展示 < 3秒。
    -   **测试用例3:** 在文档组件中，双击屏幕暂停/播放，响应时间 < 200毫秒。
    -   **测试用例4:** 在文档组件中，单击文档外区域，操作面板弹出时间 < 300毫秒。
-   **前端性能感知验收：**
    -   **测试用例1 (文档加载):** 首次进入文档组件或在不同文档组件间切换时，loading状态清晰可见，主要内容（首屏板书）在2秒内渲染完成。
    -   **测试用例2 (同步播放):** 在跟随模式下长时间播放文档组件，观察勾画轨迹、JS动画与数字人视频、音频的同步情况，不应出现肉眼可见的延迟或卡顿。

### 5.3 安全验收
-   **数据加密：**
    -   **测试用例1:** 通过浏览器开发者工具查看与后端交互的网络请求（如保存学习进度、提交反馈），确认传输的数据已通过HTTPS加密。
-   **输入校验：**
    -   **测试用例1:** 在课程反馈弹窗的文本输入框中尝试输入超过500字符的内容，期望结果：无法输入更多字符或提交时有提示。

## 6. 其他需求

### 6.1 可用性与体验需求
-   **界面友好与直观性：**
    -   课程框架各页面（开场、结算、题目列表/详情）信息层级清晰，用户能快速找到所需信息和操作。
    -   文档组件的模式切换逻辑（跟随/自由）易于理解，快捷交互手势符合用户习惯。
    -   图标、标签、提示信息表意明确。
-   **容错处理：**
    -   当网络请求失败（如获取课程数据、提交反馈）时，应有统一的、友好的错误提示（如toast提示“网络开小差了，请稍后重试”），避免页面白屏或崩溃。
    -   无效操作（如点击待解锁组件）应无响应或给出合理解释。
-   **兼容性：**
    -   **浏览器：** 需支持最新版本的 Chrome, Firefox, Safari, Edge 浏览器，保证核心功能和界面显示正常。
    -   **响应式布局：** 主要为移动端设计，需确保在主流手机屏幕尺寸上（如iPhone系列、主流Android机型）显示和交互良好。若需考虑Pad端，关键信息和操作按钮应适当放大，确保点击区域。
-   **交互一致性：**
    -   应用内的返回操作、弹窗样式、按钮风格应保持一致。
    -   课程进度列表、操作面板等复用组件在不同地方调用时，体验应一致。

### 6.2 维护性需求
-   **配置管理：**
    -   开场页不同学科的背景图、色值应可通过后台配置。
    -   结算页IP动效、反馈文案（与IP、学习表现关联）应可通过后台配置。
    -   掌握度默认目标值（0.8）、积分奖励值（默认20）应可配置。
    -   文档组件滑动进入自由模式的阈值x应可配置。
-   **监控告警：**
    -   前端应上报JS执行错误、API请求失败（如获取课程结构、保存进度失败）等关键异常到监控系统（如Sentry）。
    -   当关键API（如课程加载、进度保存）错误率突增时，应能触发告警通知相关开发人员。
-   **日志管理：**
    -   前端应记录关键用户行为路径（如进入课程、完成课程、切换组件、模式切换、使用快捷交互、提交反馈）。
    -   记录重要API的请求参数和响应（脱敏后），辅助排查问题。

## 7. 用户反馈和迭代计划

### 7.1 用户反馈机制
-   用户主要通过结算页的课程反馈功能提交对课程的评价和建议。
-   运营团队定期收集和分析反馈数据，识别高频问题和改进机会，纳入迭代规划。

### 7.2 迭代计划
-   **本期目标：** 完成课程框架升级和文档组件增强，打磨课中体验的底层能力。
-   **后续迭代方向（参考原始PRD）：**
    -   框架中加入积分体系和小组战队。
    -   支持互动讲题，自动化逐步讲解题目，并在讲解过程中加入互动问题。
    -   支持在文档中长按评论。
    -   增加费曼组件、互动组件。
-   迭代周期和流程遵循团队统一规范。

## 8. 需求检查清单
使用下面的检查清单，确保原始需求都被正确地包含和体现在整理后的需求文档中。

| 原始需求 (来自原始PRD的详细条目) | 对应优化后需求点/章节 (在本PRD中) | 完整性 | 正确性 | 一致性 | 可验证性 (是否有验收标准) | 可跟踪性 (是否有优先级/依赖) | UI/UX明确性 | 备注 |
| -------------------------------- | ----------------------------------- | ------ | ------ | ------ | ------------------------- | --------------------------- | ----------- | ---- |
| 开场页优化（后台配置、IP动效、自动进入） | 2.1-[核心功能点1.1] / 5.1 | [✅]    | [✅]    | [✅]    | [✅]                       | [✅]                         | [✅]         |      |
| 结算页优化（庆祝动效文案、学习表现、掌握度、反馈、操作按钮） | 2.1-[核心功能点1.2], [1.3], [1.4] / 5.1 | [✅]    | [✅]    | [✅]    | [✅]                       | [✅]                         | [✅]         |      |
| 课程进度控制（展示状态、跳转逻辑、进度统计） | 2.1-[核心功能点2.1] / 5.1 | [✅]    | [✅]    | [✅]    | [✅]                       | [✅]                         | [✅]         |      |
| 退出/继续课程（保存进度、恢复逻辑） | 2.1-[核心功能点2.2] / 5.1 | [✅]    | [✅]    | [✅]    | [✅]                       | [✅]                         | [✅]         |      |
| 文档组件模式切换（跟随/自由、触发条件、返回方式） | 2.1-[核心功能点3.1] / 5.1 | [✅]    | [✅]    | [✅]    | [✅]                       | [✅]                         | [✅]         |      |
| 文档组件内容播放（同步、进度线、模糊弱化、默认字幕） | 2.1-[核心功能点3.2] / 5.1 | [✅]    | [✅]    | [✅]    | [✅]                       | [✅]                         | [✅]         | 包含表格中但描述中未明确的“未播放区域模糊/弱化”和“默认展示字幕” |
| 文档组件快捷交互（从这里学、唤起面板、长按3倍速、双击播/暂） | 2.1-[核心功能点3.3] / 5.1 | [✅]    | [✅]    | [✅]    | [✅]                       | [✅]                         | [✅]         |      |
| 文档组件操作面板（退出、字幕、播/暂、倍速、±10s） | 2.1-[核心功能点3.4] / 5.1 | [✅]    | [✅]    | [✅]    | [✅]                       | [✅]                         | [✅]         | 倍速增加1.75, 3倍速（长按） |
| 组件间切换逻辑（文档到文档、文档到练习、练习到文档、动效） | 2.1-[核心功能点3.5] / 5.1 | [✅]    | [✅]    | [✅]    | [✅]                       | [✅]                         | [✅]         | 原始PRD表格去掉了串场动效，但描述中部分保留，按描述处理 |
| 答疑组件入口与基础交互 | 2.2-[辅助功能点1] | [✅]    | [✅]    | [✅]    | [ ]                       | [ ]                         | [✅]         | 属于通用组件能力，本PRD仅定义入口和基本预期 |

-   **完整性：** 原始需求是否都有对应的优化后需求点，无遗漏。
-   **正确性：** 优化后需求描述是否准确表达了原始需求的意图。
-   **一致性：** 优化后需求之间是否有冲突或重复。
-   **可验证性：** 优化后需求是否有明确的验收标准（对应5. 验收标准）。
-   **可跟踪性：** 优化后需求是否有明确的优先级和依赖关系。
-   **UI/UX明确性：** 优化后需求是否清晰描述了前端界面、交互和用户体验细节。

✅表示通过；❌表示未通过；⚠️表示描述正确，但UI/UX细节、验收标准或图示等仍需与PM进一步沟通完善；N/A表示不适用。每个需求点都需要填写。

## 9. 附录

### 9.1 原型图/设计稿链接
-   视觉稿：[https://www.figma.com/design/j6wNSQxDFuZHscu1SVMuIA/AI](https://www.figma.com/design/j6wNSQxDFuZHscu1SVMuIA/AI) 课体验版?node-id=2361-2174&p=f&t=on7tbutEPKpuOgUU-0
-   (原始PRD中各功能点附带的图片URL已在对应章节的“布局参考”中列出)

### 9.2 术语表
-   **IP:** Intellectual Property, 此处指课程中使用的卡通形象或角色。
-   **JS动画:** 指课程板书中通过JavaScript实现的动态效果和内容展示。
-   **勾画轨迹:** 指模拟老师在黑板上书写或标记重点时产生的笔迹路径。
-   **掌握度:** 反映用户对课程知识点掌握程度的综合评估指标。
-   **跟随模式:** 课程内容（板书、视频、勾画）按预设时间轴自动同步播放的模式。
-   **自由模式:** 用户可自由滚动和查看课件内容，此时自动播放暂停的模式。

### 9.3 参考资料
-   [原始需求文档：PRD - AI课中 - 课程框架+文档组件 V0.9_analyzed copy.md]
-   [PRD - 掌握度策略 - V1.0](https://wcng60ba718p.feishu.cn/wiki/J3d0wrEaDiWRy6kj2uVcQKLfnmh) (用于掌握度计算逻辑)
-   [AI 课中需求盘点](https://wcng60ba718p.feishu.cn/wiki/Htv0wNEAxis9dAkBjTTcjcWlnTb) (用于整体需求规划参考)

