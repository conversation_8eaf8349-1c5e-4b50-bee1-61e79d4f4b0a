import { cn } from "@/utils/utils";
import { AssignButton } from "./assign-button";
import { AssignTimeButton } from "./assign-time-button";
import { useContext } from "react";
import { AssignCourseContext } from "@/app/assign/[subjectKey]/course/store";
import { AssignSubjectContext } from "@/app/assign/[subjectKey]/store";
import { AssignCourseParams } from "@/types/assign/course";
import { AssignContext } from "@/app/assign/store";
import { AssignTaskTypeEnum } from "@/configs/assign";
import { useComputed, useSignal } from "@preact-signals/safe-react";
import { assignCourse } from "@/services/assign";
import to from "await-to-js";
import { useTchNavigation } from "@/hooks/useTchNavigation";
import { toast } from "@/ui/toast";
import { getEndOfDayAfter, getImmediatePublishTimestamp, umeng, UmengAssignAction, UmengCategory } from "@/utils";
import { AssignCancelAlert } from "./assign-cancel-alert";
import { AssignJoinedResource } from "./assign-joined-resource";
import { useTaskExtraInfo } from "@/app/assign/[subjectKey]/course/hooks"; 

interface AssignFooterProps {
  style?: React.CSSProperties;
  className?: string;
}

export function AssignFooter({
  style = {},
  className = "",
}: AssignFooterProps) {
  const { getTaskExtraInfo } = useTaskExtraInfo();
  const { gotoTchAssignPage } = useTchNavigation();
  const {
    totalCounter,
    treeId,
    bizTreeNodeId,
    resourceList,
    minBizTreeNode,
    totalClassTimes,
  } = useContext(AssignCourseContext);
  const { currentAssignStep, assignTimeRanges, invalidTimeRanges, checkedClasses } =
    useContext(AssignSubjectContext);
  const { currentSubject } = useContext(AssignContext);
  const cancelAssignOpen = useSignal(false);

  const disabledNext = useComputed(() => {
    return totalCounter.value === 0 || checkedClasses.value.length === 0;
  });

  const umengCancelStep = useComputed(() => {
    if (treeId.value && totalCounter.value > 0 && checkedClasses.value.length === 0) {
      // 树id和课程数量都存在,没有选择班级，是复制时取消布置
      return "";
    }
    const ret: string[] = [];
    if (currentAssignStep.value === 'select-target') {
      if (checkedClasses.value.length > 0) {
        ret.push('class_selected');
      }
      if (treeId.value && bizTreeNodeId.value) {
        ret.push('chapter_selected');
      }
      if (totalCounter.value > 0) {
        ret.push('content_selected');
      }
    }
    if (currentAssignStep.value === 'set-time') {
      ret.push('time_set');
    }
    return ret.join('/');
  });

  const taskParams = useComputed<AssignCourseParams>(() => {
    return {
      subject: currentSubject.value.subjectKey,
      taskType: AssignTaskTypeEnum.TASK_TYPE_COURSE,
      taskName: minBizTreeNode.value?.bizTreeNodeName || "",
      bizTreeId: treeId.value || 0,
      teacherComment: "",
      resources: resourceList.value,
      studentGroups: assignTimeRanges.value.map((item) => {
        const { classInfo, startTime, endTime, isImmediate } = item;
        return {
          groupType: 2,
          groupId: classInfo.jobClass,
          studentIds: [],
          startTime: isImmediate ? Math.floor(Date.now() / 1000) : Math.floor(startTime / 1000),
          deadline: Math.floor(endTime / 1000),
        };
      }),
    };
  });

  function updateAssignTimeRanges() {
    const newAssignTimeRanges = assignTimeRanges.value.map((timeRange) => {
      const item = { ...timeRange };
      if (timeRange.isImmediate) {
        item.startTime = getImmediatePublishTimestamp();
      }
      if (timeRange.isRecommend) {
        item.endTime = getEndOfDayAfter(item.startTime, totalClassTimes.value);
      }
      return item;
    });
    console.log("下一步更新时间: ", newAssignTimeRanges);
    assignTimeRanges.value = newAssignTimeRanges;
  }

  const onNext = () => {
    // 更新assignTimeRanges
    updateAssignTimeRanges();
    currentAssignStep.value = "set-time";
  };
  const onComplete = async () => {
    const params = {
      ...taskParams.value,
      taskExtraInfo: getTaskExtraInfo()
    };
    const [err, res] = await to(assignCourse(params));
    if (err) {
      console.error(err);
      if (err.message) {
        toast.error(err.message);
      }
      return;
    }
    console.log("onComplete", res);
    toast.success("布置成功");
    umeng.trackEvent(UmengCategory.ASSIGN, UmengAssignAction.TASK_LIST_COURSE_SETUP_SUBMIT, {});
    gotoTchAssignPage(true);
  };

  const umengCancelTrack = () => {
    umeng.trackEvent(UmengCategory.ASSIGN, UmengAssignAction.TASK_LIST_COURSE_SETUP_CANCEL_CLICK_2, {
      cancel_status: '已取消',
    });
    umeng.trackEvent(UmengCategory.ASSIGN, UmengAssignAction.TASK_LIST_COURSE_SETUP_CANCEL_CLICK_3, {
      unfinish_step: umengCancelStep.value,
    });
  }

  const onCancelAssign = () => {
    umeng.trackEvent(UmengCategory.ASSIGN, UmengAssignAction.TASK_LIST_COURSE_SETUP_CANCEL_CLICK_1, {
      cancel_clicked: '已点击',
    });
    if (totalCounter.value > 0) {
      cancelAssignOpen.value = true;
    } else {
      umengCancelTrack();
      gotoTchAssignPage(true);
    }
  };

  return (
    <div
      className={cn(
        "h-17 flex select-none items-center justify-end gap-2 border-t border-slate-200 bg-white px-8 shadow-[0px_8px_32px_0px_rgba(16,18,25,0.10)]",
        className
      )}
      style={style}
    >
      <AssignTimeButton content="预估时长" />

      <AssignButton
        variant="outline"
        className="px-5 outline-slate-300"
        onClick={onCancelAssign}
      >
        取消布置
      </AssignButton>
      
      <AssignJoinedResource />
      {currentAssignStep.value === "set-time" ? (
        <AssignButton
          className="px-5"
          onClick={onComplete}
          disabled={
            totalCounter.value === 0 || invalidTimeRanges.value.length > 0
          }
        >
          完成并布置
        </AssignButton>
      ) : (
        <AssignButton
          className="px-5"
          onClick={onNext}
          disabled={disabledNext.value}
        >
          下一步
        </AssignButton>
      )}
      <AssignCancelAlert
        open={cancelAssignOpen.value}
        onCancel={() => {
          cancelAssignOpen.value = false;
          umeng.trackEvent(UmengCategory.ASSIGN, UmengAssignAction.TASK_LIST_COURSE_SETUP_CANCEL_CLICK_2, {
            cancel_status: '未取消',
          });
        }}
        onOk={() => {
          cancelAssignOpen.value = false;
          umengCancelTrack();
          gotoTchAssignPage(true);
        }}
      />
    </div>
  );
}
