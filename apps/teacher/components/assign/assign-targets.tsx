"use client";
import { cn } from "@/utils/utils";
import { AssignCheckboxButton } from "./assign-checkbox-button";
// import { AssignButton } from "./assign-button";
import { TchDrawer } from "../../ui/tch-drawer";
import { AssignStudentCard } from "./assign-student-card";
import { TargetJobClass } from "@/types";
import {
  useComputed,
  useSignal,
  useSignalEffect,
} from "@preact-signals/safe-react";
import { useCallback, useContext } from "react";
import to from "await-to-js";
import { getStudentsByClassIds } from "@/services/assign";
import { ClassStudentListItem } from "@/types/assign";
import { AssignSubjectContext } from "@/app/assign/[subjectKey]/store";
import { getEndOfDayAfter, getImmediatePublishTimestamp } from "@/utils";
interface AssignTargetsProps {
  style?: React.CSSProperties;
  className?: string;
  assignRecommendTime?: number;
}

export function AssignTargets({
  style = {},
  className = "",
  assignRecommendTime = 0,
}: AssignTargetsProps) {
  const {
    checkedTargetClassIds,
    targetClassList,
    checkedClasses,
    assignTimeRanges,
  } = useContext(AssignSubjectContext);

  const checkedClassStudentList = useSignal<ClassStudentListItem[]>([]);
  const totalSelectedStudents = useComputed(() => {
    return checkedClassStudentList.value.reduce((prev, item) => {
      return prev + (item.students?.length ?? 0);
    }, 0);
  });

  const disabledClasses = useComputed(() => {
    const len = checkedClasses.value.length;
    if (len === 0) {
      return [];
    }
    const { jobGrade } = checkedClasses.value[0];
    return targetClassList.value
      .filter((item) => item.jobGrade !== jobGrade)
      .map((item) => item.jobClass);
  });

  async function fetchStudents() {
    const classIdsStr = checkedTargetClassIds.value.join(",");
    const [err, res = []] = await to(getStudentsByClassIds(classIdsStr));
    if (err) {
      return;
    }
    checkedClassStudentList.value = res.map((item) => ({
      ...item,
      classIDStr: String(item.classID),
    }));

    console.log("checkedClassStudentList: ", checkedClassStudentList.value);
  }

  const SelectedStudentsTrigger = () => {
    return (
      <div className="inline-block cursor-pointer select-none text-sm font-normal text-indigo-600">
        查看已选学生({totalSelectedStudents.value})
      </div>
    );
  };
  const classItemClick = useCallback(
    (item: TargetJobClass) => {
      if (checkedTargetClassIds.value.includes(item.jobClass)) {
        const classes = checkedClasses.value.filter(
          (checkedClass) => checkedClass.jobClass !== item.jobClass
        );
        checkedClasses.value = classes;
        assignTimeRanges.value = assignTimeRanges.value.filter(
          (range) => range.classInfo.jobClass !== item.jobClass
        );
      } else {
        checkedClasses.value = [...checkedClasses.value, item];
        const startTime = getImmediatePublishTimestamp();
        assignTimeRanges.value = [
          ...assignTimeRanges.value,
          {
            isImmediate: true,
            isRecommend: true,
            startTime,
            endTime: getEndOfDayAfter(startTime, assignRecommendTime),
            classInfo: item,
          },
        ];
      }
    },

    [
      checkedClasses,
      checkedTargetClassIds,
      assignTimeRanges,
      assignRecommendTime,
    ]
  );

  useSignalEffect(() => {
    if (checkedTargetClassIds.value.length) {
      fetchStudents();
      return;
    }
    checkedClassStudentList.value = [];
  });

  return (
    <div
      className={cn("flex flex-wrap items-center gap-3", className)}
      style={style}
    >
      {targetClassList.value.map((item) => (
        <AssignCheckboxButton
          key={item.jobClass}
          content={item.name}
          checked={checkedTargetClassIds.value.includes(item.jobClass)}
          disabled={disabledClasses.value.includes(item.jobClass)}
          onClick={() => classItemClick(item)}
        />
      ))}
      {/* <AssignButton variant="outline">自定义选择</AssignButton> */}
      {checkedClasses.value.length > 0 && (
        <TchDrawer
          title={`已选择的学生(${totalSelectedStudents.value})`}
          trigger={<SelectedStudentsTrigger />}
        >
          <div className="rounded-2xl bg-white px-6 py-3">
            <AssignStudentCard
              classStudentList={checkedClassStudentList.value}
            />
          </div>
        </TchDrawer>
      )}
    </div>
  );
}
