import { cn } from "@/utils/utils";
import { AssignCard } from "./assign-card";
import { AssignHeading } from "./assign-heading";
import { AiCourse, CoursePracticeItem, Practice } from "@/types/assign/course";
import { useContext } from "react";
import { AssignSubjectContext } from "@/app/assign/[subjectKey]/store";
import { useComputed } from "@preact-signals/safe-react";
import {
  AssignCourseContext,
  getTotalMinutes,
} from "@/app/assign/[subjectKey]/course/store";
import { AssignCourseItemOp } from "./assign-course-item-op";
import { calculateClassTime, getEndOfDayAfter, umeng, UmengAssignAction, UmengCategory } from "@/utils";

interface AssignCourseItemProps {
  style?: React.CSSProperties;
  className?: string;
  data: CoursePracticeItem;
  showPreview?: boolean;
}

export function AssignCourseItem({
  style = {},
  className = "",
  data,
  showPreview = false,
}: AssignCourseItemProps) {
  const {
    targetClassMap,
    targetClassList,
    assignTimeRanges,
    currentAssignStep,
  } = useContext(AssignSubjectContext);
  const {
    aiCourseList,
    practiceList,
    bizTreeNodeMap,
    previewPractice,
    previewAiCourse,
    addCourseResource,
    removeCourseResource,
  } = useContext(AssignCourseContext);

  // 获取巩固练习部署信息
  const deployedPracticeInfo = useComputed(() => {
    const assignedClassIds = data.practice.assignedClassIds || [];
    return getClassDeployedInfo(assignedClassIds);
  });

  // 获取课程学习部署信息
  const deployedAiCourseInfo = useComputed(() => {
    const assignedClassIds = data.aiCourse.assignedClassIds || [];
    return getClassDeployedInfo(assignedClassIds);
  });

  const hasDeployedAiCourse = useComputed(() => {
    return aiCourseList.value.some((item) => item.id === data.aiCourse.id);
  });

  const hasDeployedPractice = useComputed(() => {
    return practiceList.value.some((item) => item.id === data.practice.id);
  });

  const treeNodeIndex = useComputed(() => {
    const node = bizTreeNodeMap.value.get(data.bizTreeNodeId);
    return node?.nodeIndex || 0;
  });

  const onAddAiCourse = (aiCourse: AiCourse) => {
    console.log("加入课程", aiCourse);
    if (aiCourseList.value.find((item) => item.id === aiCourse.id)) {
      console.log("已加入课程");
      return;
    }
    const list = [
      ...aiCourseList.value,
      { ...aiCourse, bizTreeNodeIndex: treeNodeIndex.value },
    ];
    aiCourseList.value = list;
    updateRecommendAssignTimeRanges(list, practiceList.value);
    addCourseResource(data, "aiCourse");
  };

  const onRemoveAiCourse = (aiCourse: AiCourse) => {
    console.log("移除课程", aiCourse);
    const list = aiCourseList.value.filter((item) => item.id !== aiCourse.id);
    aiCourseList.value = list;
    updateRecommendAssignTimeRanges(list, practiceList.value);
    removeCourseResource(data, "aiCourse");
  };

  const onAddPractice = (practice: Practice) => {
    if (practiceList.value.find((item) => item.id === practice.id)) {
      return;
    }
    const list = [
      ...practiceList.value,
      { ...practice, bizTreeNodeIndex: treeNodeIndex.value },
    ];
    practiceList.value = list;
    updateRecommendAssignTimeRanges(aiCourseList.value, list);
    addCourseResource(data, "practice");
  };

  const onRemovePractice = (practice: Practice) => {
    const list = practiceList.value.filter((item) => item.id !== practice.id);
    practiceList.value = list;
    updateRecommendAssignTimeRanges(aiCourseList.value, list);
    removeCourseResource(data, "practice");
  };

  const onPreviewAiCourse = (aiCourse: AiCourse) => {
    console.log("预览课程", aiCourse);
    umeng.trackEvent(UmengCategory.ASSIGN, UmengAssignAction.TASK_LIST_COURSE_SETUP_PREVIEW_CLICK, {});
    if (aiCourse.id) {
      currentAssignStep.value = "course-preview";
      previewAiCourse.value = data;
      // gotoPracticePreviewPage(currentSubject.value.subjectKey, practice.questionSetId);
    }
  };

  const onPreviewPractice = (practice: Practice) => {
    umeng.trackEvent(UmengCategory.ASSIGN, UmengAssignAction.TASK_LIST_COURSE_SETUP_PREVIEW_CLICK, {});
    if (practice.questionSetId) {
      currentAssignStep.value = "practice-preview";
      previewPractice.value = data;
      // gotoPracticePreviewPage(currentSubject.value.subjectKey, practice.questionSetId);
    }
  };

  // 更新推荐部署时间段
  function updateRecommendAssignTimeRanges(
    ailist: AiCourse[],
    pList: Practice[]
  ) {
    const totalClassTimes = calculateClassTime(
      getTotalMinutes(ailist) + getTotalMinutes(pList)
    );
    assignTimeRanges.value = assignTimeRanges.value.map((item) => {
      if (item.isRecommend) {
        item.endTime = getEndOfDayAfter(item.startTime, totalClassTimes);
      }
      return item;
    });
  }

  // 获取部署信息
  function getClassDeployedInfo(assignedClassIds: number[]) {
    // 全部未布置
    if (!assignedClassIds || assignedClassIds.length === 0) {
      return {
        deployed: "",
        unDeployed: "全部未布置",
      };
    }
    const validAssignedClassIds = assignedClassIds.filter(
      (id) => !!targetClassMap.value[id]
    );
    // 全部已布置
    const allInAssignedClassIds = targetClassList.value.every((item) =>
      validAssignedClassIds.includes(item.jobClass)
    );
    if (allInAssignedClassIds) {
      return {
        deployed: "全部已布置",
        unDeployed: "",
      };
    }

    const deployedClassIds = targetClassList.value.filter((item) =>
      validAssignedClassIds.includes(item.jobClass)
    );
    const unDeployedClassIds = targetClassList.value.filter(
      (item) => !validAssignedClassIds.includes(item.jobClass)
    );
    return {
      deployed: deployedClassIds.map((item) => item.name).join("、"),
      unDeployed: unDeployedClassIds.map((item) => item.name).join("、"),
    };
  }

  return (
    <AssignCard
      className={cn("flex gap-3 outline-none", className)}
      style={style}
    >
      <AssignHeading
        content={data.bizTreeNodeName || ""}
        showPrefix={data.isRecommend}
        prefix="推荐"
        className="h-6"
      />

      {data?.aiCourse?.id && (
        <AssignCourseItemOp
          type="aiCourse"
          hasJoined={hasDeployedAiCourse.value}
          estimatedTime={data.aiCourse.estimatedTime || 0}
          estimatedTimeStr={data.aiCourse.estimatedTimeStr || ""}
          deployed={deployedAiCourseInfo.value.deployed}
          unDeployed={deployedAiCourseInfo.value.unDeployed}
          showPreview={showPreview}
          onPreview={() => onPreviewAiCourse(data.aiCourse)}
          onAdd={() => onAddAiCourse(data.aiCourse)}
          onRemove={() => onRemoveAiCourse(data.aiCourse)}
        />
      )}

      {data?.practice?.id && (
        <AssignCourseItemOp
          type="practice"
          hasJoined={hasDeployedPractice.value}
          estimatedTime={data.practice.estimatedTime || 0}
          estimatedTimeStr={data.practice.estimatedTimeStr || ""}
          deployed={deployedPracticeInfo.value.deployed}
          unDeployed={deployedPracticeInfo.value.unDeployed}
          showPreview={showPreview}
          onPreview={() => onPreviewPractice(data.practice)}
          onAdd={() => onAddPractice(data.practice)}
          onRemove={() => onRemovePractice(data.practice)}
        />
      )}
    </AssignCard>
  );
}
