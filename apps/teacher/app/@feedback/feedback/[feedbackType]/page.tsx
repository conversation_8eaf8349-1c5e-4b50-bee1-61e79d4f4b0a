"use client";
import React, { useState } from "react";
import { TchSheet } from "@/ui/tch-sheet";
import { useParams, useRouter } from "next/navigation";
import { useFeedback } from "@/hooks";
import Image from "next/image";
import closeIcon from "@/public/icons/ic_close.svg";
import { Button } from "@/ui/tch-button";
import TextArea from "@/ui/tch-textarea";
import { toast } from "@/ui/toast";
import { ScrollArea } from "@/ui/scroll-area";
import { cn } from "@/utils";
import FeedbackImageUpload from "../_components/FeedbackImageUpload";

const MAX_IMAGES = 4;
const TYPE_OPTIONS = [
  "板书文字问题",
  "字幕问题",
  "板书花圈问题",
  "听不懂太难了",
  "黑屏",
  "其他",
  "音画不同步",
  "老师配音问题",
  "讲解超纲",
  "播放卡顿",
];

export default function FeedbackTypePage() {
  const router = useRouter();
  // 反馈类型
  const { feedbackType } = useParams();
  const { submit } = useFeedback();
  const [open, setOpen] = useState(true);
  const [desc, setDesc] = useState("");
  const [images, setImages] = useState<File[]>([]);
  // 问题类型
  const [types, setTypes] = useState<string[]>([]);

  // 选择类型
  const handleTypeClick = (t: string) => {
    setTypes((prev) =>
      prev.includes(t) ? prev.filter((x) => x !== t) : [...prev, t]
    );
  };

  // 提交
  const handleSubmit = async () => {
    if (types.length === 0) {
      toast.warning("请选择问题类型");
      return;
    }
    if (desc.trim().length < 10) {
      toast.warning("请至少输入10个字的问题描述");
      return;
    }

    await submit({
      desc,
      images,
      type: Array.isArray(types) ? types.join(",") : types,
    });
    toast.success("反馈提交成功");
    setOpen(false);
  };

  return (
    <TchSheet
      className="w-130 max-w-130! outline-0"
      open={open}
      onAnimationEnd={() => {
        if (!open) {
          router.back();
        }
      }}
    >
      <div className="feedback_type_container bg-fill-light min-h-150 flex h-full flex-col">
        {/* Header */}
        <div className="feedback_type_header h-18 flex items-center justify-between px-6">
          <div className="text-gray-1 leading-7.5 text-xl font-semibold">
            问题反馈
          </div>
          <Image
            onClick={() => {
              setOpen(false);
            }}
            src={closeIcon}
            alt="关闭"
            className="size-5 cursor-pointer active:opacity-40"
            width={20}
            height={20}
          />
        </div>

        <ScrollArea className="flex-1 overflow-hidden pb-4">
          <div className="space-y-4 px-6">
            {/* 类型选择 */}
            <div className="feedback_type_select border-line-1 pb-7.5 rounded-2xl border bg-white px-5 pt-5">
              <div className="text-gray-1 mb-5 text-base font-semibold">
                <span className="text-danger-1">*</span>{" "}
                请选择问题类型（可多选）
              </div>

              <div className="grid grid-cols-2 gap-x-3 gap-y-4">
                {TYPE_OPTIONS.map((t) => (
                  <button
                    key={t}
                    type="button"
                    className={cn(
                      "feedback_type_btn text-gray-2 h-9 cursor-pointer rounded-md border text-sm outline-0",
                      types.includes(t) &&
                        "border-primary-2 bg-primary-2 text-white"
                    )}
                    onClick={() => handleTypeClick(t)}
                  >
                    {t}
                  </button>
                ))}
              </div>
            </div>

            {/* 截图上传区 */}
            <div className="feedback_type_upload border-line-1 pb-7.5 rounded-2xl border bg-white px-5 pt-5">
              <div className="text-gray-1 mb-5 text-base font-semibold">
                截图反馈：
                <span className="text-gray-2 text-sm">
                  （{images.length}/{MAX_IMAGES}）
                </span>
              </div>

              <FeedbackImageUpload images={images} onChange={setImages} />
            </div>

            {/* 问题描述区 */}
            <div className="feedback_type_desc border-line-1 pb-7.5 rounded-2xl border bg-white px-5 pt-5">
              <div className="text-gray-1 mb-5 text-base font-semibold">
                请描述问题
              </div>
              <TextArea
                value={desc}
                onChange={setDesc}
                placeholder="请输入10个字以上的问题描述，以便我们为您提供更好的帮助"
                maxLength={300}
                maxHeight={120}
                minHeight={120}
                className={cn("text-gray-2 placeholder:text-gray-4")}
              />
            </div>
          </div>
        </ScrollArea>

        {/* 底部按钮区 */}
        <div className="feedback_type_footer bg-fill-white flex items-center justify-end px-6 py-4">
          <Button
            type="primary"
            radius="full"
            className={cn(`h-9 w-full text-sm font-medium`)}
            onClick={() => {}}
          >
            提交反馈
          </Button>
        </div>
      </div>
    </TchSheet>
  );
}
