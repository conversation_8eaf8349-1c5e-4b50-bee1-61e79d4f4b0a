import type { Metadata, Viewport } from "next";
import Script from "next/script";
import AppLayout from "@/app/_layout";
import "./globals.css";
import localFont from "next/font/local";
import { cn } from "@/utils";

const tchFont = localFont({
  src: [
    {
      path: "../public/fonts/AlibabaPuHuiTi-3-55-RegularL3/AlibabaPuHuiTi-3-55-RegularL3.woff2",
      weight: "400",
      style: "normal",
    },
    {
      path: "../public/fonts/AlibabaPuHuiTi-3-55-RegularL3/AlibabaPuHuiTi-3-55-RegularL3.woff",
      weight: "400",
      style: "normal",
    },
    {
      path: "../public/fonts/AlibabaPuHuiTi-3-55-RegularL3/AlibabaPuHuiTi-3-55-RegularL3.eot",
      weight: "400",
      style: "normal",
    },
    {
      path: "../public/fonts/AlibabaPuHuiTi-3-55-RegularL3/AlibabaPuHuiTi-3-55-RegularL3.otf",
      weight: "400",
      style: "normal",
    },

    {
      path: "../public/fonts/AlibabaPuHuiTi-3-55-RegularL3/AlibabaPuHuiTi-3-55-RegularL3.ttf",
      weight: "400",
      style: "normal",
    },
  ],
  display: "swap",
  preload: true,
});

export const metadata: Metadata = {
  title: "教师端",
  description: "教师端",
};

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 1,
  minimumScale: 1,
  userScalable: false,
};

export default function RootLayout({
  children,
  feedback,
}: Readonly<{
  children: React.ReactNode;
  feedback: React.ReactNode;
}>) {
  return (
    <html lang="zh-CN" className={cn(tchFont.className, "h-full")}>
      <head>
        <Script
          id="aliyun-captcha-main"
          src="https://o.alicdn.com/captcha-frontend/aliyunCaptcha/AliyunCaptcha.js"
          strategy="beforeInteractive"
        />
      </head>
      <body>
        <AppLayout>
          {children}
          {feedback}
        </AppLayout>
      </body>
    </html>
  );
}
