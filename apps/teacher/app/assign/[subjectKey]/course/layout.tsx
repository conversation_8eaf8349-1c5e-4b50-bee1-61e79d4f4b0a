"use client";
import { AssignCourseContext, AssignCourseContextType, AssignCourseProvider, createAssignCourseState } from "./store";
import { useParams } from "next/navigation";
import { useEffect, useContext, useState } from "react";
import { fetchTreeList } from "@/services/assign";
import { useTaskExtraInfo } from "./hooks";
import { useApp } from "@/hooks";
import { useSignalEffect } from "@preact-signals/safe-react";

function BizTreeInfo({ children }: { children: React.ReactNode }) {
  const { userInfo } = useApp();
  const { bizTreeList, courseTreeInfo } =
    useContext(AssignCourseContext);
  const { recoverTreeInfoFromLocalStorage } = useTaskExtraInfo();
  const { subjectKey } = useParams();
  const currentSubjectKey = Number(subjectKey);

  const localStorageKey = `assign-course-${userInfo?.userID}-${currentSubjectKey}`;
  recoverTreeInfoFromLocalStorage(localStorageKey);

  useSignalEffect(() => {
    localStorage.setItem(localStorageKey, JSON.stringify(courseTreeInfo.value));
  });

  useEffect(() => {
    fetchTreeList(currentSubjectKey, "bizTree").then((res) => {
      bizTreeList.value = res;
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  return <>{children}</>;
}

export default function AssignCourseLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const [state, setState] = useState<AssignCourseContextType>(
    null as unknown as AssignCourseContextType
  );
  useEffect(() => {
    setState(createAssignCourseState());
  }, []);
  if (!state) {
    return null;
  }
  return (
    <AssignCourseProvider value={state}>
      <BizTreeInfo>{children}</BizTreeInfo>
    </AssignCourseProvider>
  );
}
