"use client";
import React from "react";
import { notFound } from "next/navigation";
import AgreementPage from "../../[agreement]/page";

export default function AgreementModal({
  params,
}: {
  params: Promise<{ agreement: string }>;
}) {
  const { agreement } = React.use(params);
  const allowedAgreements = ["user-agreement", "privacy-policy"];

  if (!allowedAgreements.includes(agreement)) {
    notFound();
  }

  return (
    <div className="fixed inset-0 bg-white">
      <AgreementPage params={params} />
    </div>
  );
}
