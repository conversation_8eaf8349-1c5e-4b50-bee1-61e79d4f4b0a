"use client";

import { useTaskContext } from "@/app/homework/[id]/_context/task-context";
import { umeng, UmengCategory, UmengHomeworkAction } from "@/utils";
import { QuestionList } from "@repo/core/views/tch-question-view";
import { selectQuestion, useAnswerResults } from "../store/answers";
import CustomFooter from "./CustomFooter";

export default function QuestionListWrapper() {
  const { viewMode } = useTaskContext();
  const { filteredAnswers, loading } = useAnswerResults();

  const handleClickView = (questionId: string) => {
    // DONE: 埋点28 => `homework_list_report_student_question_detail` 学生的作业报告页中查看题目详情 view=student
    if (viewMode.value === "student") {
      umeng.trackEvent(
        UmengCategory.HOMEWORK,
        UmengHomeworkAction.REPORT_STUDENT_QUESTION_DETAIL,
        {
          eventName: "学生的作业报告页中查看题目详情",
        }
      );
    }
    selectQuestion(questionId);
  };

  return (
    <QuestionList
      questions={filteredAnswers.value}
      loading={loading.value}
      onClickView={handleClickView}
      customFooter={CustomFooter}
      hasFooterButton={true}
    />
  );
}
