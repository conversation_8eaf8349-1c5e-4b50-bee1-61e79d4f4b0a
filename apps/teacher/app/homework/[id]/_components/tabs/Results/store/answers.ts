"use client";

import { signal, computed, batch } from "@preact-signals/safe-react";
import {
  getTaskReportAnswers,
  getStudentTaskReportAnswers,
  TaskReportAnswersRequest,
  getAnswerPanel,
  PanelItem,
} from "@/services/homework";
import { toast } from "@/ui/toast";
import {
  TaskReportAnswersData,
  StudentTaskReportAnswersData,
  QuestionAnswer,
  StudentQuestionAnswer,
} from "@/types/homeWork";

// 为了兼容现有代码，定义 Answer 和 AnswerDetail 类型
interface Answer {
  questionId: string;
  answer: string;
  answerExplain: string;
  avgCostTime: number;
  content: string;
  questionType: number;
  resourceId: string;
  resourceType: number;
  answerDetails: AnswerDetail[];
}

interface AnswerDetail {
  answer: string;
  costTime: number;
  isCorrect: boolean;
  studentId: number;
}
import { classDataSignal, taskDataSignal, viewModeSignal, studentDataSignal } from "@/app/homework/[id]/_context/task-context";
import { QUESTION_TYPE } from "@/enums";


// 定义答题结果状态
interface AnswerResultsState {
  data: TaskReportAnswersData | StudentTaskReportAnswersData | null;
  panel: PanelItem[] | null;
  loading: boolean;
  selectedQuestionId: string | null;
}

interface AnswerPagination {
  current: number;
  pageSize: number;
}

export interface AnswerFilter {
  keyword: string;
  questionType?: QUESTION_TYPE;
  allQuestions: boolean;
  sortBy?: 'answerCount' | 'incorrectCount' | 'orderByAssign';
  sortType?: string;
}
// 初始状态
export const answerResultsState = signal<AnswerResultsState>({
  panel: null,
  data: null,
  loading: false,
  selectedQuestionId: null
});

export const answerPagination = signal<AnswerPagination>({
  current: 1,
  pageSize: 10
})

export const answerFilter = signal<AnswerFilter>({
  keyword: '',
  questionType: undefined,
  allQuestions: false,
  sortBy: 'answerCount',
  sortType: 'desc'
})


// 计算属性：过滤后的题目列表
export const filteredAnswers = computed(() => {
  if (!answerResultsState.value.data) {
    return [];
  }

  const isClassMode = viewModeSignal.value === 'class';
  const data = answerResultsState.value.data as (TaskReportAnswersData | StudentTaskReportAnswersData);

  return data.questionAnswers.map((qa: QuestionAnswer | StudentQuestionAnswer) => {
    // 共同的字段映射逻辑
    const commonFields = {
      questionTags: qa.question.questionTags,
      questionId: qa.question.questionId,
      answer: JSON.stringify(qa.question.questionAnswer),
      questionAnswer: qa.question.questionAnswer,
      answerExplain: qa.question.questionExplanation,
      avgCostTime: qa.avgCostTime,
      content: qa.question.questionContent.questionStem,
      questionType: qa.question.questionType,
      resourceId: qa.resourceId,
      resourceType: qa.resourceType,
      // 将 questionOptionList 转换为 options 属性
      options: qa.question.questionContent.questionOptionList ?
        qa.question.questionContent.questionOptionList.map((opt: { optionKey: string; optionVal: string }) => ({
          key: opt.optionKey,
          content: opt.optionVal
        })) :
        undefined
    };

    // 根据视图模式处理 answerDetails
    let answerDetails: AnswerDetail[] = [];

    if (isClassMode) {
      // 班级视图: 将新格式的answers转换为旧格式的answerDetails
      const classQA = qa as QuestionAnswer;
      answerDetails = classQA.answers?.map((a) => ({
        answer: a.answer,
        costTime: a.costTime,
        isCorrect: a.isCorrect,
        studentId: a.studentId
      })) || [];
    } else {
      // 学生视图: 只有一个答案，将其转换为数组
      const studentQA = qa as StudentQuestionAnswer;
      answerDetails = studentQA.answer ? [{
        answer: studentQA.answer.answer,
        costTime: studentQA.answer.costTime,
        isCorrect: studentQA.answer.isCorrect,
        studentId: studentQA.answer.studentId
      }] : [];
    }

    return {
      ...commonFields,
      answerDetails
    };
  });
});

// 计算属性：当前选中的题目
export const selectedAnswer = computed(() => {
  if (!answerResultsState.value.selectedQuestionId || !answerResultsState.value.data) {
    return null;
  }

  // 使用filteredAnswers来获取当前选中的题目，因为它已经处理了不同版本的数据格式
  return filteredAnswers.value.find(
    (q: Answer) => q.questionId === answerResultsState.value.selectedQuestionId
  ) || null;
});

// 计算属性：题目统计信息
export const answerStats = computed(() => {
  if (!answerResultsState.value.data) {
    return {
      totalQuestions: 0,
      commonWrongQuestions: 0,
      averageCorrectRate: 0,
      averageCostTime: 0
    };
  }

  // 使用filteredAnswers来计算统计信息，因为它已经处理了不同版本的数据格式
  const answers = filteredAnswers.value;
  const totalQuestions = answers.length;

  // 计算共性错题数量
  const commonWrongQuestions = answers.filter((q: Answer) => {
    const correctCount = q.answerDetails.filter((detail: AnswerDetail) => detail.isCorrect).length;
    const totalCount = q.answerDetails.length;
    const correctRate = totalCount > 0 ? correctCount / totalCount : 0;
    return totalCount > 5 && correctRate < 0.6;
  }).length;

  // 计算平均正确率
  const totalCorrectRate = answers.reduce((sum: number, q: Answer) => {
    const correctCount = q.answerDetails.filter((detail: AnswerDetail) => detail.isCorrect).length;
    const totalCount = q.answerDetails.length;
    return sum + (totalCount > 0 ? correctCount / totalCount : 0);
  }, 0);
  const averageCorrectRate = totalQuestions > 0 ? totalCorrectRate / totalQuestions : 0;

  // 计算平均用时
  const averageCostTime = answers.reduce((sum: number, q: Answer) => sum + q.avgCostTime, 0) / totalQuestions;

  return {
    totalQuestions,
    commonWrongQuestions,
    averageCorrectRate,
    averageCostTime
  };
});

// 新增一个专门用于请求参数的计算属性
export const fetchParams = computed(() => ({
  ...answerFilter.value,
  page: answerPagination.value.current,
  pageSize: answerPagination.value.pageSize,
  assignId: classDataSignal.value.assignId,
  taskId: taskDataSignal.value?.id || 0
}));

// 设置过滤条件
export function setFilter(filter: Partial<AnswerFilter>) {
  answerFilter.value = {
    ...answerFilter.value,
    ...filter
  };
  console.log('fetchParams.value', fetchParams.value);
  fetchAnswerResults(fetchParams.value);
}

// 设置分页
export function setPagination(pagination: Partial<AnswerPagination>) {
  answerPagination.value = {
    ...answerPagination.value,
    ...pagination
  };
  fetchAnswerResults(fetchParams.value);
}

// 选择题目
export function selectQuestion(questionId: string | null) {
  answerResultsState.value = {
    ...answerResultsState.value,
    selectedQuestionId: questionId
  };
}

// 获取答题结果数据
export async function fetchAnswerResults(params: TaskReportAnswersRequest) {
  // 如果正在加载中直接返回
  if (answerResultsState.value.loading) return;
  answerResultsState.value = {
    ...answerResultsState.value,
    loading: true
  };

  try {
    // 根据当前视图模式决定使用哪个接口
    const currentViewMode = viewModeSignal.value;
    const commonParams = {
      ...params,
      page: answerPagination.value.current,
      pageSize: answerPagination.value.pageSize,
      allQuestions: answerFilter.value.allQuestions,
      questionType: answerFilter.value.questionType,
      titleKey: answerFilter.value.keyword,
    }
    const requestParams = {
      ...commonParams,
      sortBy: answerFilter.value.sortBy,
      sortType: answerFilter.value.sortType
    };

    let response;
    let panelResponse: { panel: PanelItem[] } = {
      panel: [],
    };

    if (currentViewMode === 'student') {
      // 学生视图使用学生接口
      const studentParams = {
        ...requestParams,
        studentId: Number(studentDataSignal.value.studentId)
      };
      response = await getStudentTaskReportAnswers(studentParams);
    } else {
      // 班级视图使用班级接口
      response = await getTaskReportAnswers(requestParams);
      panelResponse = await getAnswerPanel(commonParams)
    }

    batch(() => {
      answerResultsState.value = {
        data: response,
        panel: panelResponse.panel,
        loading: false,
        selectedQuestionId: null
      };
      // 如果页码超过总页数，重置到第一页
      if (params.page > Math.ceil(response.pageInfo.total / params.pageSize)) {
        answerPagination.value.current = 1;
      }
    });
  } catch (error) {
    console.error("获取答题结果数据失败:", error);
    toast.error("获取答题结果数据失败，请刷新重试");
    answerResultsState.value = {
      ...answerResultsState.value,
      loading: false
    };
  }
}


// 重置状态
export function resetAnswerResultsState() {
  answerResultsState.value = {
    panel: null,
    data: null,
    loading: false,
    selectedQuestionId: null
  };
  answerFilter.value = {
    keyword: '',
    questionType: undefined,
    allQuestions: true,
    sortBy: 'answerCount',
    sortType: 'desc'
  };
  answerPagination.value = {
    current: 1,
    pageSize: 10
  };
}


const loading = computed(() => answerResultsState.value.loading)
const data = computed(() => answerResultsState.value.data)
const selectedQuestionId = computed(() => answerResultsState.value.selectedQuestionId)
const panel = computed(() => answerResultsState.value.panel)
export function useAnswerResults() {


  return {
    fetchParams,
    filteredAnswers,
    answerResultsState,
    loading,
    data,
    answers: filteredAnswers,
    filter: answerFilter,
    pagination: answerPagination,
    selectedQuestionId,
    setFilter,
    setPagination,
    selectQuestion,
    fetchAnswerResults,
    panel
  };
}