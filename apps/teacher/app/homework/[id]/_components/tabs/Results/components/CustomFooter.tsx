"use client";

import { useTaskContext } from "@/app/homework/[id]/_context/task-context";
import { Separator } from "@/ui/separator";
import { QuestionContext } from "@repo/core/views/tch-question-view";
import ic_correct from "@/public/icons/ic_correct.svg";
import ic_incorrect from "@/public/icons/ic_incorrect.svg";
import Image from "next/image";
interface CustomFooterProps {
  context: Omit<QuestionContext, 'options'>;
}

export default function CustomFooter({ context }: CustomFooterProps) {
  const {
    qaContent,
    correctRatePercent,
    isCommonWrong,
  } = context;

  console.log("context", context,isCommonWrong);


  // 从 task-context 中获取 viewMode
  const { viewMode } = useTaskContext();

  // 计算错误人数
  const totalAnswers = qaContent.answerDetails.length;
  // 计算正确人数
  const correctAnswers = qaContent.answerDetails.filter(detail => detail.isCorrect).length;
  // 计算错误人数
  const incorrectAnswers = totalAnswers - correctAnswers;
  console.log("qaContent", {
    qaContent,
    incorrectAnswers,
    correctAnswers,
    totalAnswers
  });

  // 根据正确率决定颜色
  const rateColorClass = correctRatePercent > 50 ? "text-green-600" : "text-red-600";

  // 只返回左侧内容，不包含查看解析按钮
  return (
    <div className="flex gap-2 items-center">
      {/* 如果是班级视图，显示特定的统计信息 */}
      {viewMode.value === 'class' ? (
        <div className="text-sm flex items-center">
          <div className="flex items-center gap-1">
            <span>正确率</span>
            <span className={`font-medium ${rateColorClass}`}>{correctRatePercent}%</span>
          </div>
          {/* 分割线 */}
          <Separator orientation="vertical" className="!h-4 w-[1px] bg-line-2 mx-2" />

          <div className="flex items-center gap-1">
            <span className="font-medium text-red-600">{incorrectAnswers}</span>
            <span className="text-sm text-gray-500">人错误</span>
          </div>
          <Separator orientation="vertical" className="!h-4 w-[1px] bg-line-2 mx-2" />

          <div className="flex items-center gap-1">
            <span className="font-medium">{totalAnswers}</span>
            <span className="text-sm text-gray-500">人作答</span>
          </div>
        </div>
      ) : (
        <>
          {!totalAnswers ? (
            <div className="text-gray-2 text-sm font-normal leading-[150%]">尚未作答，暂无结果</div>
          ) : (
            <>
              {correctAnswers ? (
                <div className="  flex items-center gap-1">
                  <Image src={ic_correct} alt="correct" width={24} height={24} />
                  <span className="text-[#444963] text-[0.875rem] font-normal leading-[150%]">正确</span>
                </div>
              ) : (
                <div className=" flex items-center gap-1">
                  <Image src={ic_incorrect} alt="incorrect" width={24} height={24} />
                  <span className="text-[#444963] text-[0.875rem] font-normal leading-[150%]">错误</span>

                </div>
              )}
            </>
          )}
        </>
      )}
    </div>
  );
}
