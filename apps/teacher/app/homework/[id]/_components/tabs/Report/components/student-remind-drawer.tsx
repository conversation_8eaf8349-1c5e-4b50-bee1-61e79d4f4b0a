"use client";

import { <PERSON>, ChevronRight } from "lucide-react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, SheetTitle } from "@/ui/sheet";
import { But<PERSON> } from "@/ui/tch-button";
import { Progress } from "@/ui/progress";
import { Textarea } from "@/ui/textarea";
import { useState, useEffect, useCallback } from "react";
import StudentAvatar from "@/public/assign/student-avatar.png";
import SeparationIcon from "@/public/icons/ic_separation.svg";
import { DrawerCard } from "../../../../../../../components/common/drawer-card";
import { OfflineCommunicationDrawer } from "../../../../../../../components/common/offline-communication";
import { useSignal } from "@preact-signals/safe-react";
import Image from "next/image";
import { useApp } from "@/hooks/useApp";
import { useTaskContext } from "@/app/homework/[id]/_context/task-context";
import { umeng, Umeng<PERSON><PERSON><PERSON>y, UmengHomeworkAction } from "@/utils/umeng";
import Avatar from "@/ui/tch-avatar";
import { handleStudentBehavior } from "@/services/homework";
import { toast } from "@/ui/toast";
import to from "await-to-js";
import { getStudentDetail } from "@/services/homework";
import { StudentDetail } from "@/types/homeWork";

// 学生类型定义
type StudentStatus = "good" | "attention";

interface StudentDetailDrawerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  studentId: string | number | null;
  /** 操作成功后的回调，用于更新父组件 */
  onActionSuccess?: () => void;
}

// 学生信息卡片组件
interface StudentInfoCardProps {
  student: StudentDetail;
  onClose: () => void;
  praiseCount: number;
  attentionCount: number;
}

function StudentInfoHeader({
  student,
  onClose,
  praiseCount,
  attentionCount,
}: StudentInfoCardProps) {
  return (
    <div className="flex items-center justify-between py-6">
      <div className="flex items-center gap-3">
        <Avatar
          className="h-9 w-9"
          src={student.avatar || StudentAvatar.src}
          alt={student.name}
        />
        <span className="text-lg font-medium">{student.name}</span>
        {praiseCount > 0 && (
          <div className="bg-green-5 flex h-[1.375rem] items-center gap-[0.125rem] rounded-sm px-[0.375rem] py-[0.125rem]">
            <span className="text-green-0 text-sm">
              已表扬过 {praiseCount} 次
            </span>
          </div>
        )}
        {attentionCount > 0 && (
          <div className="bg-orange-5 flex h-[1.375rem] items-center gap-[0.125rem] rounded-sm px-[0.375rem] py-[0.125rem]">
            <span className="text-orange-0 text-sm">
              已提醒过 {attentionCount} 次
            </span>
          </div>
        )}
      </div>
      <div
        className="flex h-8 w-8 items-center justify-center rounded-full"
        onClick={onClose}
      >
        <X className="h-5 w-5" />
      </div>
    </div>
  );
}

// 学习表现卡片组件
interface PerformanceCardProps {
  performance: StudentDetail["performance"];
  status: StudentStatus;
}

function PerformanceCard({ performance, status }: PerformanceCardProps) {
  const statusInfo =
    status === "good"
      ? {
          title: "值得鼓励的进步",
          bgColor: "bg-green-50",
          textColor: "text-green-700",
        }
      : {
          title: "需要关注的问题",
          bgColor: "bg-red-50",
          textColor: "text-red-700",
        };

  return (
    <DrawerCard title={statusInfo.title}>
      <div className="">
        <div className="mb-6">
          <div className="mb-2 flex justify-between text-sm">
            <span>作业正确率</span>
            <div className="flex items-center gap-1">
              <span className="text-sm font-medium leading-[150%] text-[#646B8A]">
                {performance.homeworkAccuracy}%
              </span>
              <Image
                src={SeparationIcon.src}
                alt="分隔符"
                className="mx-1 h-3 w-1"
                width={4}
                height={12}
              />
              <span className="text-sm leading-[150%] text-[#646B8A]">
                平均正确率 {performance.averageAccuracy}%
              </span>
            </div>
          </div>
          <Progress value={performance.homeworkAccuracy} className="h-2" />
        </div>

        <div className="">
          <div className="mb-2 flex justify-between text-sm">
            <span>完成进度</span>
            <div className="flex items-center gap-1">
              <span className="text-sm font-medium leading-[150%] text-[#646B8A]">
                {performance.completionRate}%
              </span>
              <Image
                src={SeparationIcon.src}
                alt="分隔符"
                className="mx-1 h-3 w-1"
                width={4}
                height={12}
              />
              <span className="text-sm leading-[150%] text-[#646B8A]">
                平均进度 {performance.averageCompletionRate}%
              </span>
            </div>
          </div>
          <Progress value={performance.completionRate} className="h-2" />
        </div>
      </div>
    </DrawerCard>
  );
}

// 干预措施卡片组件
interface InterventionCardProps {
  feedback: StudentDetail["feedback"];
}

function InterventionCard({ feedback }: InterventionCardProps) {
  return (
    <DrawerCard title="建议干预措施">
      <div>
        <p className="text-gray-2 mb-4 text-sm leading-[150%]">
          {feedback.description}
        </p>
        <ul className="flex flex-col gap-y-2">
          {feedback.recommendations.map((recommendation, index) => (
            <li key={index} className="flex items-start gap-2">
              <span className="rounded-xs ml-2 mt-2 h-1.5 w-1.5 flex-shrink-0 bg-gray-600"></span>
              <span className="text-gray-2 text-sm leading-[150%]">
                {recommendation}
              </span>
            </li>
          ))}
        </ul>
      </div>
    </DrawerCard>
  );
}

// Push提醒卡片组件
interface ReminderCardProps {
  onCancel: () => void;
  onSend: () => void;
  onOfflineCommunication: () => void;
  reminderMessage: string;
  setReminderMessage: (message: string) => void;
  isLoading?: boolean;
}

function ReminderCard({
  onSend,
  onOfflineCommunication,
  reminderMessage,
  setReminderMessage,
  isLoading,
}: ReminderCardProps) {
  const TitleAction = (
    <Button
      type="text"
      className="flex h-auto items-center gap-1 p-0 text-blue-500"
      onClick={onOfflineCommunication}
    >
      线下沟通 <ChevronRight className="h-4 w-4" />
    </Button>
  );

  const FooterActions = (
    <div className="flex justify-end gap-3">
      <Button
        className="h-9 rounded-[1.125rem]"
        onClick={onSend}
        disabled={!reminderMessage.trim() || isLoading}
        loading={isLoading}
      >
        发送提醒
      </Button>
    </div>
  );

  return (
    <DrawerCard
      title="push提醒"
      titleAction={TitleAction}
      footer={FooterActions}
    >
      <div className="relative">
        <Textarea
          placeholder="今天答题状态不是很好，是不是课程学习有点难，是否有需要我帮忙的吗？"
          className="min-h-[100px] resize-none"
          value={reminderMessage}
          onChange={(e) => setReminderMessage(e.target.value)}
          maxLength={500}
        />
        <div className="absolute bottom-2 right-2 text-xs text-gray-400">
          {reminderMessage.length}/500
        </div>
      </div>
    </DrawerCard>
  );
}

// 底部操作按钮组件
interface BottomActionButtonProps {
  onClick: () => void;
  isLoading?: boolean;
  praiseCount?: number;
}

function BottomActionButton({
  onClick,
  isLoading,
  praiseCount = 0,
}: BottomActionButtonProps) {
  return (
    <div className="sticky bottom-0 left-0 right-0 mx-[-1.5rem] border-t border-gray-100 bg-white px-6 py-4 shadow-sm">
      <Button
        type="primary"
        className="bg-primary-2 hover:bg-primary-2/90 disabled:bg-primary-3 h-9 w-full rounded-[1.125rem] font-medium text-white"
        onClick={onClick}
        disabled={isLoading || praiseCount > 0}
        loading={isLoading}
      >
        {praiseCount > 0 && (
          <span className="text-sm text-white">今天已点赞</span>
        )}
        {praiseCount === 0 && (
          <span className="text-sm text-white">他今天很棒！点赞吧</span>
        )}
      </Button>
    </div>
  );
}

export function StudentRemindDrawer({
  open,
  onOpenChange,
  studentId,
  onActionSuccess,
}: StudentDetailDrawerProps) {
  const { getRoleSummary, userInfo } = useApp();
  const { taskData } = useTaskContext();
  const [reminderMessage, setReminderMessage] = useState("");
  const offlineCommunicationOpen = useSignal(false);
  const [isLoading, setIsLoading] = useState(false);
  const [studentData, setStudentData] = useState<StudentDetail | null>(null);

  // 获取学生详情数据
  const fetchStudentDetail = useCallback(async () => {
    if (!taskData.value?.id || !studentId || !open) return;
    console.log("Fetching student detail for:", studentId);
    const [err, data] = await to(
      getStudentDetail(
        taskData.value.id,
        taskData.value.classes[0].assignId,
        Number(studentId),
        userInfo?.currentSchoolID || 0
      )
    );
    console.log("Fetched student data:", data);

    if (err) {
      console.error("Failed to fetch student detail:", err);
      toast.error("获取学生信息失败", {
        description: "请稍后重试",
      });
      setStudentData(null); // Clear data on error
      return;
    }

    // 转换API返回的数据为组件所需的格式
    const newStudentData = {
      id: data.studentId,
      name: data.studentName,
      avatar: data.avatar,
      className: "", // API中暂无此字段
      status:
        data.studentAccuracyRate >= data.classAccuracyRate
          ? "good"
          : ("attention" as StudentStatus),
      praiseCount: data.praiseCount,
      attentionCount: data.attentionCount,
      performance: {
        homeworkAccuracy: parseFloat(
          (data.studentAccuracyRate * 100).toFixed(2)
        ),
        averageAccuracy: parseFloat((data.classAccuracyRate * 100).toFixed(2)),
        completionRate: parseFloat(
          (data.studentCompletedProgress * 100).toFixed(2)
        ),
        averageCompletionRate: parseFloat(
          (data.classCompletedProgress * 100).toFixed(2)
        ),
      },
      feedback: {
        description: data.attentionText,
        recommendations: data.attentionTextList,
      },
      pushDefaultText: data.pushDefaultText,
    };
    setStudentData(newStudentData);

    // 只有在首次加载或提醒消息为空时才设置默认提醒消息
    if (!reminderMessage || reminderMessage === studentData?.pushDefaultText) {
      setReminderMessage(data.pushDefaultText || "");
    }
  }, [
    taskData.value?.id,
    studentId,
    open,
    taskData.value?.classes,
    userInfo?.currentSchoolID,
    reminderMessage,
    studentData?.pushDefaultText,
  ]);

  useEffect(() => {
    if (open && studentId) {
      fetchStudentDetail();
    }
  }, [open, studentId, fetchStudentDetail]);

  // 重置表单状态
  const resetForm = () => {
    setReminderMessage("");
    setIsLoading(false);
  };

  // 处理抽屉关闭
  const handleClose = () => {
    onOpenChange(false);
    resetForm();
  };

  // 处理发送提醒
  const handleSendReminder = async () => {
    if (!taskData.value?.id || !studentId) return;
    setIsLoading(true);

    // DONE: 埋点20 => `homework_list_report_single_push` 对单人用户进行的push操作
    umeng.trackEvent(
      UmengCategory.HOMEWORK,
      UmengHomeworkAction.REPORT_SINGLE_PUSH,
      {
        subject: taskData.value?.subject,
        job: getRoleSummary(),
      }
    );

    const [err] = await to(
      handleStudentBehavior({
        taskId: taskData.value.id,
        assignId: taskData.value.classes[0].assignId,
        studentIds: [Number(studentId)],
        behaviorType: "task_attention",
        content: reminderMessage.trim(),
      })
    );

    if (err) {
      console.error("Failed to send reminder:", err);
      toast.error("提醒发送失败", {
        description: "请稍后重试",
      });
    } else {
      toast.success("提醒发送成功", {
        description: "已成功向学生发送提醒",
      });

      // 调用成功回调，用于更新父组件
      onActionSuccess?.();
      fetchStudentDetail(); // 更新当前抽屉内的学生数据
      // 不再自动关闭抽屉
      // handleClose();
    }

    setIsLoading(false);
  };

  // 处理点赞
  const handlePraise = async () => {
    if (!taskData.value?.id || !studentId) return;
    setIsLoading(true);

    // DONE: 埋点21 => `homework_list_report_single_like` 对单人用户进行的点赞操作
    umeng.trackEvent(
      UmengCategory.HOMEWORK,
      UmengHomeworkAction.REPORT_SINGLE_LIKE,
      {
        subject: taskData.value?.subject,
        job: getRoleSummary(),
      }
    );

    const [err] = await to(
      handleStudentBehavior({
        taskId: taskData.value.id,
        assignId: taskData.value.classes[0].assignId,
        studentIds: [Number(studentId)],
        behaviorType: "task_praise",
      })
    );

    if (err) {
      console.error("Failed to praise student:", err);
      toast.error("点赞失败", {
        description: "请稍后重试",
      });
    } else {
      toast.success("点赞成功", {
        description: "已成功给学生点赞",
      });

      // 调用成功回调，用于更新父组件
      onActionSuccess?.();
      fetchStudentDetail(); // 更新当前抽屉内的学生数据
      // 不再自动关闭抽屉
      // handleClose();
    }

    setIsLoading(false);
  };

  // 清理副作用
  useEffect(() => {
    if (!open) {
      resetForm();
    }
  }, [open]);

  if (!studentId || !studentData) return null;

  return (
    <>
      <OfflineCommunicationDrawer
        open={offlineCommunicationOpen.value}
        onOpenChange={() => (offlineCommunicationOpen.value = false)}
      />
      <Sheet open={open} onOpenChange={handleClose}>
        <SheetTitle></SheetTitle>
        <SheetContent
          className="bg-fill-gray-2 w-full overflow-y-auto px-6 sm:max-w-md"
          closeable={false}
        >
          <div className="relative">
            {/* 学生信息卡片 */}
            <StudentInfoHeader
              student={studentData}
              onClose={handleClose}
              praiseCount={studentData.praiseCount}
              attentionCount={studentData.attentionCount}
            />

            {/* 学习表现卡片 */}
            <PerformanceCard
              performance={studentData.performance}
              status={studentData.status}
            />

            {/* 干预措施卡片 */}
            <InterventionCard feedback={studentData.feedback} />

            {/* Push提醒卡片 */}
            <ReminderCard
              onCancel={handleClose}
              onSend={handleSendReminder}
              onOfflineCommunication={() =>
                (offlineCommunicationOpen.value = true)
              }
              reminderMessage={reminderMessage}
              setReminderMessage={setReminderMessage}
              isLoading={isLoading}
            />

            {/* 底部点赞按钮 */}
            <BottomActionButton
              onClick={handlePraise}
              isLoading={isLoading}
              praiseCount={studentData.praiseCount}
            />
          </div>
        </SheetContent>
      </Sheet>
    </>
  );
}
