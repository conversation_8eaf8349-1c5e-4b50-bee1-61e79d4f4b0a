"use client";
import CourseSectionTitle from "./course-section-title";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/ui/table";
import { useRequest } from "ahooks";
import { getStudentLearningStatus } from "@/services";
import Avatar from "@/ui/tch-avatar";
import { StudentInfo } from "@/types/course";
import { Progress } from "@/ui/progress";
import { useState } from "react";
import to from "await-to-js";
import IcCourseClassroomStatus from "@/public/icons/ic_course_classroom_status.svg";

export function LearningStatus({
  classroomId,
  setStudent,
}: {
  classroomId: string;
  setStudent: (student: StudentInfo) => void;
}) {
  const [startIndex, setStartIndex] = useState(0);

  const { data: studentData = [] } = useRequest(
    async () => {
      const [error, data] = await to(
        getStudentLearningStatus({
          classroomId,
        })
      );

      if (error || !data?.length) {
        setStartIndex(0);
        return;
      }

      const endIndex = startIndex + 10;

      const showStudentData = data.slice(startIndex, endIndex);

      setStartIndex(endIndex >= data.length ? 0 : endIndex);

      return showStudentData;
    },
    {
      pollingInterval: 10000, // 每10秒轮询一次
    }
  );

  return (
    <section>
      <CourseSectionTitle title="全班学习状态" icon={IcCourseClassroomStatus} />

      <div className="border-fill-gray-2 overflow-hidden rounded-xl border bg-white p-5">
        <Table className="table-fixed overflow-hidden rounded-lg">
          <TableHeader>
            <TableRow className="bg-fill-gray-2 border-b-0! hover:bg-fill-gray-2 h-11">
              <TableHead className="text-gray-2 w-42 pl-4 font-normal">
                学生
              </TableHead>
              <TableHead className="text-gray-2 w-24.5 text-center font-normal">
                学习能量
              </TableHead>
              <TableHead className="text-gray-2 font-normal">
                当前学习内容
              </TableHead>
              <TableHead className="text-gray-2 w-52.5 font-normal">
                完成进度
              </TableHead>
              <TableHead className="text-gray-2 w-30 text-center font-normal">
                正确率
              </TableHead>
            </TableRow>
          </TableHeader>

          <TableBody className="text-gray-1">
            {studentData.map((student) => (
              <TableRow
                key={student.studentId}
                className="h-13 border-[#E1E5F2] hover:bg-transparent"
              >
                <TableCell className="pl-4">
                  <div className="text-gray-1 flex items-center gap-2.5">
                    <Avatar
                      src={student.avatarUrl}
                      alt="avatar"
                      className="h-6 w-6 active:opacity-80"
                      onClick={() => {
                        setStudent({
                          studentId: student.studentId,
                          studentName: student.studentName,
                          avatarUrl: student.avatarUrl,
                        });
                      }}
                    />
                    <span>{student.studentName}</span>
                  </div>
                </TableCell>

                <TableCell className="text-gray-1 text-center">
                  {student.correctAnswers || 0}
                </TableCell>

                <TableCell className="overflow-hidden text-ellipsis whitespace-nowrap">
                  {student.pageName}
                </TableCell>

                <TableCell>
                  <div className="text-gray-2 flex items-center gap-2 text-xs">
                    <Progress
                      value={student.totalQuestions}
                      max={100}
                      className="> div:bg-primary-2 bg-fill-gray-1 rounded-xs h-1"
                    />
                    {student.totalQuestions}%
                  </div>
                </TableCell>

                <TableCell className="text-gray-1 text-center">
                  {student.accuracyRate || 0}%
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </section>
  );
}
