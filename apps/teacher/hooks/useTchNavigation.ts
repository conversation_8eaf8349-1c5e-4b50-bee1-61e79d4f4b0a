import { useRouter } from "next/navigation";


export interface TchNavigation {
  gotoBack: () => void;
  gotoTchAssignPage: (isReplace?: boolean) => void;
  gotoTaskCreatePage: (subjectKey: number, type: string) => void;
  gotoHomeworkDetailPage: (homeworkId: number, { tab, source, assignId }: { tab: string, source: string, assignId: number }) => void;
  gotoHomeworkPage: (source?: string) => void;
  gotoPracticePreviewPage: (subjectKey: number,questionSetId: number) => void;
}

export function useTchNavigation(): TchNavigation {
  const router = useRouter();

  const gotoBack = () => router.back();
  const gotoTchAssignPage = (isReplace = false) => isReplace ? router.replace("/assign") : router.push("/assign");
  const gotoTaskCreatePage = (subjectKey: number, type: string) => router.push(`/assign/${subjectKey}/${type}`);
  const gotoHomeworkPage = (source?: string) => {
    if (source) {
      router.push(`/homework?source=${source}`);
    } else {
      router.push(`/homework`);
    }
  };
  const gotoHomeworkDetailPage = (homeworkId: number, { tab, source, assignId }: { tab: string, source: string, assignId: number }) => {
    if (source) {
      return router.push(`/homework/${homeworkId}?tab=${tab}&source=${source}&assignId=${assignId}`)
    }
    return router.push(`/homework/${homeworkId}?tab=${tab}&assignId=${assignId}`)
  };

  const gotoPracticePreviewPage = (subjectKey: number,questionSetId: number) => router.push(`/assign/${subjectKey}/course/preview?questionSetId=${questionSetId}`);

  return {
    gotoBack,
    gotoTchAssignPage,
    gotoTaskCreatePage,
    gotoHomeworkDetailPage,
    gotoHomeworkPage,
    gotoPracticePreviewPage,
  };
}


