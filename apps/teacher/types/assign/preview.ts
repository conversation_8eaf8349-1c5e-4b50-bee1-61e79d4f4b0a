import { CommonResponseType, QuestionType } from ".";

export interface QuestionSetDetailData {
    estimatedTime: number;
    list: QuestionSetGroupItem[];
    questionSetId: number;
    questionSetVersionId: number;
    questionTypeCount: number;
}

export interface QuestionSetDifficultStat {
    difficultName: string;
    difficultNeedQuestionCount: number;
    difficultQuestionCount: number;
}

export interface QuestionSetGroupItem {
    questionGroupId?: number;
    questionGroupName?: string;
    questionGroupOrder?: number;
    questionGroupQuestionList?: QuestionGroupQuestionItem[];
}

export interface QuestionGroupQuestionItem {
    questionId: string;
    questionInfo: QuestionType;
}


export type QuestionSetDetailResponse = CommonResponseType<QuestionSetDetailData>;

