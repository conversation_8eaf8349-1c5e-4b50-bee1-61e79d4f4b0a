import { r } from "../libs/axios";
import type {
  GetCourseTableParams,
  GetCourseTableResponse,
  CourseRankingData,
  GetBehaviorCategorysResponse,
  RemindStudentData,
  StudentCourseDetailData,
  IsTeachingData,
  GetStudentLearningStatusResponse,
  GetCourseInfoResponse,
} from "@/types";

/**
 * 获取教师课程表
 */
export const getCourseTable = (params: GetCourseTableParams) => {
  return r.get<GetCourseTableResponse>("/schedule/store/auth", { params });
};

/**
 * 检测是否上课
 */
export const isTeaching = (params: {
  teacher_id: string;
  school_id: string;
}) => {
  return r.get<IsTeachingData>("/schedule/is_teaching", { params });
};

/**
 * 获取课堂信息
 */
export const getCourseInfo = (params: { classroomId: string }) => {
  return r.get<GetCourseInfoResponse>("/schedule/classroom/status", { params });
};

/**
 * 获取课堂学习能量排行
 */
export const getCourseRanking = (params: { classroomId: string }) => {
  return r.get<CourseRankingData>("behavior/classroom/learning-scores", {
    params,
  });
};

/**
 * 课堂表扬关注提醒行为分类列表
 */
export const getBehaviorCategorys = (params: { classroomId: string }) => {
  return r.get<GetBehaviorCategorysResponse>(
    "/behavior/class/behavior-category",
    { params }
  );
};

/**
 * 提醒学生
 */
export const remindStudent = (data: {
  classroomID: number;
  studentIDs: number[];
  teacherID: number;
  teacherName: string;
  attentionMessage?: string;
}) => {
  return r.post<RemindStudentData>("/behavior/attention", data);
};

/**
 * 表扬学生
 */
export const praiseStudent = (data: {
  classroomID: number;
  studentIDs: number[];
  teacherID: number;
  teacherName: string;
  praiseMessage?: string;
}) => {
  return r.post<RemindStudentData>("/behavior/praise", data);
};

/**
 * 获取学生个人课堂详情
 */
export const getStudentCourseDetail = (params: {
  classroomId: string;
  studentId: string;
}) => {
  return r.get<StudentCourseDetailData>("/behavior/student/classroom-detail", {
    params,
  });
};

export const getStudentLearningStatus = (params: { classroomId: string }) => {
  return r.get<GetStudentLearningStatusResponse>(
    "/behavior/class/latest-behaviors",
    { params }
  );
};

/**
 * 发送评价
 */
export const sendStudentEvaluate = (data: {
  classId: number;
  classroomId: number;
  content: string;
  evaluateType: string;
  schoolId: number;
  studentId: number;
  teacherId: number;
}) => {
  return r.post<{
    success: boolean;
    message: string;
    evaluateId: number;
  }>("/behavior/student/evaluate", data);
};
