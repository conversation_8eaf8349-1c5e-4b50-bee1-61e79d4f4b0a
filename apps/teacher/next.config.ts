import type { NextConfig } from "next";

const nextConfig = {
  transpilePackages: ["@repo/ui", "@repo/core"],
  distDir: "dist",
  output: "standalone",
  assetPrefix:
    process.env.NODE_ENV === "production"
      ? "https://static.xiaoluxue.cn/tch"
      : process.env.NODE_ENV === "test"
        ? "https://static.test.xiaoluxue.cn/tch"
        : "",
  experimental: {
    swcPlugins: [
      [
        "@preact-signals/safe-react/swc",
        {
          // you should use `auto` mode to track only components which uses `.value` access.
          // Can be useful to avoid tracking of server side components
          mode: "auto",
        } /* plugin options here */,
      ],
    ],
  },
  // 配置重写规则
  rewrites: async () => {
    return [
      {
        source: "/api/:path*",
        destination: `http://teacher.local.xiaoluxue.cn/api/:path*`,
      },
    ];
  },
  // TODO 配置图片域名
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "**",
      },
      {
        protocol: "http",
        hostname: "**",
      },
    ],
  },
  webpack(config) {
    // 检查 NODE_ENV 并设置为 production
    if (process.env.NODE_ENV === 'test') {
      // @ts-expect-error 这里需要修改环境变量，使测试环境打包也按生产环境打包，否则两个环境的打包产物会不一致
      process.env.NODE_ENV = 'production';
    }

    return config;
  },
} as NextConfig;

module.exports = nextConfig;
