"use client";
import { createContext, FC, ReactNode, useContext } from "react";

import {
  CourseSequenceViewmodel,
  useCourseSequenceViewmodel,
} from "@/app/viewmodels/course-sequence-vm";
import { Signal, useSignal } from "@preact-signals/safe-react";

enum GuideMode {
  free = "free",
  follow = "follow",
}

type CourseViewContextType = CourseSequenceViewmodel & {
  showSubtitle: Signal<boolean>;
  playRate: Signal<number>;
};

const CourseViewContext = createContext<CourseViewContextType>(
  {} as CourseViewContextType
);

const useCourseViewContext = () => useContext(CourseViewContext);

interface CourseViewProviderProps {
  knowledgeId: number;
  children: ReactNode;
  type: "draft" | "official";
}

const CourseViewProvider: FC<CourseViewProviderProps> = ({
  knowledgeId,
  children,
  type
}) => {
  // 获取数据及操作数据的方法
  // TODO: 获取数据及操作数据的方法
  const sequenceVm = useCourseSequenceViewmodel(knowledgeId, type);

  const showSubtitle = useSignal(true);
  const playRate = useSignal(1);

  const value = {
    ...sequenceVm,
    showSubtitle,
    playRate,
  };
  return <CourseViewContext value={value}>{children}</CourseViewContext>;
};

export {
  CourseViewContext,
  CourseViewProvider,
  GuideMode,
  useCourseViewContext,
  type CourseViewContextType,
};
