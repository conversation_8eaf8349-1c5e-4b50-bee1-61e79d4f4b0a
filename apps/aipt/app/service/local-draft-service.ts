import {
  clearStorage,
  getStorageItem,
  removeStorageItem,
  setStorageItem,
} from "@repo/lib/utils/local-storage";

export class LocalDraftService {
  private guideKey: string;

  constructor({
    guideWidgetSetId,
    guideWidgetId,
  }: {
    guideWidgetSetId: string;
    guideWidgetId: string;
  }) {
    this.guideKey = `guide:${guideWidgetSetId}-${guideWidgetId}`;
  }

  load<T>(name: string, defaultValue?: T) {
    return getStorageItem<T>(`${this.guideKey}-${name}`, defaultValue as T);
  }

  save<T>(name: string, content: T) {
    setStorageItem<T>(`${this.guideKey}-${name}`, content);
  }

  clear(name: string) {
    removeStorageItem(`${this.guideKey}-${name}`);
  }

  clearAll() {
    clearStorage();
  }
}
