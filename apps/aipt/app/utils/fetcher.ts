import commonFetcher from "@repo/lib/utils/fetcher";

const apiHost = process.env.NEXT_PUBLIC_API_HOST;
console.info("AIPT:API_HOST", apiHost);

const fetcher = async <T>(url: string, init?: RequestInit) => {
  return commonFetcher<T>(`${apiHost}${url}`, {
    ...init,
    headers: {
      ...init?.headers,
    },
  });
};

export async function get<T>(
  url: string,
  { query }: { query?: Record<string, string> }
) {
  const params = query ? new URLSearchParams(query) : undefined;
  return await fetcher<T>(`${url}?${params?.toString()}`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
    },
  });
}

export async function post<T>(url: string, { arg }: { arg?: object }) {
  return await fetcher<T>(url, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: arg ? JSON.stringify(arg) : undefined,
  });
}

export default fetcher;
