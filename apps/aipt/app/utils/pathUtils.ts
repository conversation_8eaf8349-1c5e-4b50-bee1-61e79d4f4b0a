import { DrawElement } from "@repo/core/types/data/widget-guide";

/**
 * 将SVG路径数据合并到路径对象中
 * @param paths 原始路径对象数组
 * @param svgString SVG字符串
 * @returns 合并了SVG路径数据的路径对象数组
 */
export const mergePathsWithSvg = (
  paths: any[],
  svgString: string
): DrawElement[] => {
  if (paths.length === 0) return [];
  // 解析 SVG 字符串
  const parser = new DOMParser();
  const svgDoc = parser.parseFromString(svgString, "image/svg+xml");
  const pathElements = svgDoc.querySelectorAll("path");
  if (pathElements.length === 0) return [];
  const lastPath = paths.pop();
  let lastPathElement = [].find.call(pathElements, ((ele: SVGPathElement) => ele.getAttribute('id')?.includes(lastPath?.id)));

  if (!lastPath.drawMode) {
    const eraserPathElements = [].filter.call(pathElements, ((c: Element) => c.getAttribute('id')?.includes('eraser')));
    lastPathElement = eraserPathElements[eraserPathElements.length - 1];
  }

  return [...paths, { ...lastPath, svgPath: (lastPathElement as unknown as SVGPathElement)?.getAttribute("d") || "" }]
};
