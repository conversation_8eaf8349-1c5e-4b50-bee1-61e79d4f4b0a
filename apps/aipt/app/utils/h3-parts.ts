import { Line } from "@repo/core/types/data/widget-guide";

/**
 * 按H3标题分组内容
 * @param content 内容数组
 * @returns 按H3分组的内容数组
 */
export function groupByH3(content: Line[]): Line[][] {
  if (!content || content.length === 0) {
    return [];
  }

  const groups: Line[][] = [];
  let currentGroup: Line[] = [];

  for (const line of content) {
    if (line.tag === "h3") {
      // 遇到新的H3，保存当前组并开始新组
      if (currentGroup.length > 0) {
        groups.push(currentGroup);
      }
      currentGroup = [line];
    } else {
      // 将当前行添加到当前组
      currentGroup.push(line);
    }
  }

  // 添加最后一组
  if (currentGroup.length > 0) {
    groups.push(currentGroup);
  }

  return groups;
}
