// 将帧数和 frame 转为 mm:ss:SSS 格式
export function frameToTime(frame: number, fps: number): string {
  // 总秒数
  const totalSeconds = frame / fps;
  const minutes = Math.floor(totalSeconds / 60);
  const seconds = Math.floor(totalSeconds % 60);
  // 毫秒 = 小数部分 * 1000，四舍五入
  const ms = Math.round((totalSeconds - Math.floor(totalSeconds)) * 1000);

  // 补零
  const minStr = String(minutes).padStart(2, "0");
  const secStr = String(seconds).padStart(2, "0");
  const msStr = String(ms).padStart(3, "0");

  return `${minStr}:${secStr}:${msStr}`;
}

// 将 mm:ss:SSS 格式转为帧数
export function timeToFrame(time: string, fps: number): number {
  const [minutesPart = 0, secondsPart = 0, millisecondsPart = 0] = time
    .split(":")
    .map(Number);
  const totalSeconds = minutesPart * 60 + secondsPart + millisecondsPart / 1000;
  return Math.round(totalSeconds * fps);
}

export const formatTime = (time: number): string => {
  // 计算分钟和剩余秒数
  const minutes = Math.floor(time / 60);
  const remainingSeconds = time % 60;
  // 根据分钟数返回格式化字符串
  if (minutes > 0) {
    return `${String(minutes).padStart(2, "0")}分${String(remainingSeconds).padStart(2, "0")}秒`;
  }
  return `${remainingSeconds}秒`;
};
