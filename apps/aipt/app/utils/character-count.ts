import { Line, LineTexture } from "@repo/core/types/data/widget-guide";

/**
 * 计算字符数：四级标题×35 + 其他实际字符数
 * @param lines Line数组
 * @returns 总字符数
 */
export function calculateCharacterCount(lines: Line[]): number {
  let totalCount = 0;

  const processLine = (line: Line) => {
    // 如果是四级标题，按35个字符计算
    if (line.tag === 'h4') {
      totalCount += 35;
    } else {
      // 其他情况计算实际字符数
      if (Array.isArray(line.content)) {
        // 检查第一个元素的类型来判断是Line数组还是LineTexture数组
        if (line.content.length > 0) {
          const firstItem = line.content[0];
          if (firstItem && 'tag' in firstItem && 'level' in firstItem) {
            // 如果是Line数组，递归处理
            (line.content as Line[]).forEach(processLine);
          } else {
            // 如果是LineTexture数组，计算实际字符数
            (line.content as LineTexture[]).forEach(texture => {
              totalCount += texture.content.length;
            });
          }
        }
      }
    }
  };

  lines.forEach(processLine);
  return totalCount;
}

/**
 * 根据字符数获取默认样式
 * @param charCount 字符数
 * @returns 样式值
 */
export function getDefaultStyleByCharCount(charCount: number): string {
  if (charCount <= 35) {
    return 'style1'; // 上文下图，少字配大图
  } else if (charCount <= 70) {
    return 'style2'; // 左文右图(6:2)
  } else if (charCount <= 140) {
    return 'style3'; // 左文右图(5:3)
  } else if (charCount <= 280) {
    return 'style4'; // 左文右图(4.8:3.2)
  } else {
    return 'style5'; // 上文下图，多字配大图
  }
} 