import { useEffect, useState } from "react";

/**
 * 获取"AI产课+当天第一次进入页面的时间戳后4位"水印文本的自定义hook
 * - 当天第一次进入页面时生成时间戳并存储到localStorage
 * - 同一天内多次访问都返回同一个后4位
 * - 可自定义前缀
 * @param prefix 水印前缀，默认"AI产课"
 * @returns 水印文本
 */
export function useDailyWatermarkText(prefix: string = "AI产课") {
  // 存储水印文本
  const [watermarkText, setWatermarkText] = useState("");

  useEffect(() => {
    // 获取今天的日期字符串（yyyy-mm-dd）
    const today = new Date().toISOString().slice(0, 10);
    // 从localStorage读取已存储的时间戳信息
    const stored = localStorage.getItem("firstVisitInfo");
    let ts = "";
    if (stored) {
      try {
        // 解析存储内容，判断日期是否为今天
        const obj = JSON.parse(stored);
        if (obj.date === today && obj.ts) {
          ts = obj.ts;
        }
      } catch (e) {
        console.error(e);
      }
    }
    // 如果没有存储或已过期，则生成新的时间戳并存储
    if (!ts) {
      ts = new Date().toISOString();
      localStorage.setItem(
        "firstVisitInfo",
        JSON.stringify({ date: today, ts })
      );
    }
    // 设置水印文本，格式为"前缀+时间戳后4位"
    setWatermarkText(`${prefix}${ts.slice(-4)}`);
  }, [prefix]);

  return watermarkText;
}
