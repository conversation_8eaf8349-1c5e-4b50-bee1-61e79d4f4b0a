import { commonEnum } from "./enum";
import useSWR from "swr";
import fetcher from "@/lib/course-center-fetcher";
import { toast } from "sonner";

export interface EnumItem {
  value: number;
  nameZh: string;
  nameEn?: string;
  [key: string]: any;
}

interface EnumResponse {
  phaseList: EnumItem[];
  subjectList: EnumItem[];
  lessonTypeList: EnumItem[];
  [key: string]: EnumItem[];
}

const useEnum = () => {
  const { data: enums = commonEnum } = useSWR<EnumResponse>(
    "/api/v1/common/enum",
    fetcher,
    {
      revalidateOnFocus: false,
      revalidateOnReconnect: false,
      revalidateIfStale: false,
      onSuccess: (data) => {
        console.log(data);
      },
      onError(err: Error) {
        // toast.error(err.message);
        console.log( "获取 /api/v1/common/enum错误：", err);
        
      },
    }
  );

  const getByValue = (thisEnums: EnumItem[], val: string) => {
    const item = thisEnums.find((item) => item.value === Number(val));
    return {
      ...item,
      label: item?.nameZh,
      value: item?.value,
    };
  }
  return { enums, getByValue };
};

export default useEnum;
