import { Button } from "@/app/components/common/button";
import { useReadingEditorContext } from "@/app/components/reading-editor/reading-editor-context";
import { useGuideContext } from "@/app/context/guide-context";
import { cn } from "@repo/ui/lib/utils";
import { CloudUpload, GitBranchPlus } from "lucide-react";

const BtnSave = () => {
  const { isContentChanged, save, isSaving } = useReadingEditorContext();
  const disabled = !isContentChanged;
  return (
    <Button
      disabled={disabled}
      type="primary"
      loading={isSaving}
      className={cn(
        "rounded-sm border bg-slate-100 text-xs font-bold text-slate-400 hover:bg-slate-50",
        !disabled && "bg-red-600 text-white hover:bg-red-700"
      )}
      onClick={save}
      icon={<CloudUpload className="size-4" />}
    >
      提交
    </Button>
  );
};

const BtnBatchGenerate = () => {
  const { isFinish, isGenerating } = useGuideContext();
  const { isBatchGenerateDisabled, batchGenerate, batchGenerateLoading } =
    useReadingEditorContext();
  const disabled = isBatchGenerateDisabled || isFinish || isGenerating;
  return (
    <Button
      disabled={disabled}
      type="primary"
      loading={batchGenerateLoading}
      className={cn(
        "rounded-sm border bg-slate-100 text-xs font-bold text-slate-400 hover:bg-slate-50",
        !disabled && "bg-red-600 text-white hover:bg-red-700"
      )}
      onClick={batchGenerate}
      icon={<GitBranchPlus className="size-4" />}
    >
      生成配音
    </Button>
  );
};

const BtnMergeAudio = () => {
  const { isFinish, isGenerating } = useGuideContext();
  const { isMergeAudioDisabled, mergeEntireAudio, isMergeWholeAudio } =
    useReadingEditorContext();
  const disabled = isMergeAudioDisabled || isFinish || isGenerating;
  return (
    <Button
      disabled={disabled}
      type="primary"
      loading={isMergeWholeAudio}
      className={cn(
        "rounded-sm border bg-slate-100 text-xs font-bold text-slate-400 hover:bg-slate-50",
        !disabled && "bg-red-600 text-white hover:bg-red-700"
      )}
      onClick={mergeEntireAudio}
      icon={<CloudUpload className="size-4" />}
    >
      合成整个配音
    </Button>
  );
};
// 近期生成配音提示
const TextGenerateInfo = () => {
  const { generateInfo } = useReadingEditorContext();
  return (
    <div className="text-primary flex items-center text-sm">{generateInfo}</div>
  );
};

export { BtnSave, BtnBatchGenerate, BtnMergeAudio, TextGenerateInfo };
