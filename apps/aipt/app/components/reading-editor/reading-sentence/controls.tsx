import { But<PERSON> } from "@/app/components/common/button";
import {
  AudioSentenceChangeType,
  useReadingEditorContext,
} from "@/app/components/reading-editor/reading-editor-context";
import { useGuideContext } from "@/app/context/guide-context";
import { useGuideSetContext } from "@/app/context/guide-set-context";
import { useSignal, useSignalEffect } from "@preact-signals/safe-react";
import { download } from "@repo/lib/utils/download";
import { cn } from "@repo/ui/lib/utils";
import {
  CircleAlert,
  Download,
  Pause,
  Play,
  Sparkle,
  Upload,
  CircleX,
} from "lucide-react";
import { ComponentProps, FC, useEffect, useMemo, useRef } from "react";
import { Uploader } from "../../uploader/uploader";
import { useReadingSentenceContext } from "./reading-sentence";
type ReadingAudioItemControlsProps = ComponentProps<typeof Button> & {};

const BtnAudio: FC<ReadingAudioItemControlsProps> = () => {
  const { sentence } = useReadingSentenceContext();
  const { audioSentenceUrl } = sentence.value;
  const { playingAudioRef, playbackRate } = useReadingEditorContext();
  const audioRef = useRef<HTMLAudioElement>(null);
  const isPlaying = useSignal(false);

  const handleClick = () => {
    isPlaying.value = !isPlaying.value;
  };
  useSignalEffect(() => {
    if (isPlaying.value) {
      audioRef.current?.play();
      playingAudioRef.value = audioRef.current;
    } else {
      audioRef.current?.pause();
    }
  });

  useSignalEffect(() => {
    if (playingAudioRef.value !== audioRef.current) {
      isPlaying.value = false;
    }
  });

  useSignalEffect(() => {
    if (audioRef.current) {
      audioRef.current.playbackRate = playbackRate.value;
    }
  });

  useEffect(() => {
    if (audioRef.current) {
      const player = audioRef.current;
      player.addEventListener("ended", () => {
        isPlaying.value = false;
      });
    }
  }, [audioRef, isPlaying]);

  return (
    <>
      <audio src={audioSentenceUrl} hidden ref={audioRef} />
      <Button
        type="text"
        onClick={handleClick}
        className="text-primary rounded-sm text-xs font-normal"
        icon={
          isPlaying.value ? (
            <Pause strokeWidth={1.5} size={16} />
          ) : (
            <Play strokeWidth={1.5} size={16} />
          )
        }
      >
        {isPlaying.value ? "暂停" : "播放"}
      </Button>
    </>
  );
};

const BtnGenerate: FC<ReadingAudioItemControlsProps> = (props) => {
  const { isFinish, isGenerating } = useGuideContext();
  const { isTextChanged, generate, isLoading } = useReadingSentenceContext();

  const handleClick = async () => {
    await generate(AudioSentenceChangeType.Regenerate);
  };

  const icon = useMemo(() => {
    if (isTextChanged.value) {
      return <CircleAlert className="size-4 stroke-red-400" />;
    }
    return <Sparkle strokeWidth={1.5} className="size-4" />;
  }, [isTextChanged.value]);

  return (
    <Button
      disabled={isGenerating || isLoading || isFinish}
      onClick={handleClick}
      type="text"
      {...props}
      className={cn(
        "text-primary rounded-sm text-xs font-normal",
        isFinish && "text-muted-foreground"
      )}
      icon={icon}
    >
      生成配音
    </Button>
  );
};

const BtnDownload: FC<ReadingAudioItemControlsProps> = (props) => {
  const { guideWidgetIndex } = useGuideSetContext();
  const { guideSet } = useGuideContext();
  const { sentence } = useReadingSentenceContext();
  const { audioSentenceUrl, index = 0 } = sentence.value;
  const loading = useSignal(false);

  const handleDownload = async () => {
    loading.value = true;
    const fileName = `${guideSet.guideWidgetSetName}_Part${guideWidgetIndex.value + 1}_第${index + 1}句`;
    await download(audioSentenceUrl, fileName);
    loading.value = false;
  };

  return (
    <Button
      type="text"
      disabled={loading.value}
      loading={loading.value}
      onClick={handleDownload}
      {...props}
      className="text-primary rounded-sm text-xs font-normal"
      icon={<Download strokeWidth={1.5} size={16} />}
    >
      下载
    </Button>
  );
};

const BtnUpload: FC<ReadingAudioItemControlsProps> = (props) => {
  const { isFinish, isGenerating } = useGuideContext();
  const { generate, isLoading, changeUrl } = useReadingSentenceContext();
  const isUploading = useSignal(false);

  const beforeUpload = async () => {
    isUploading.value = true;
  };

  const handleUploaded = async (url: string) => {
    changeUrl(url);
    isUploading.value = false;
    await generate(AudioSentenceChangeType.Upload);
  };

  return (
    <Uploader
      accept="audio/*"
      showUploadedFile={false}
      beforeUpload={beforeUpload}
      onUpload={handleUploaded}
    >
      <Button
        disabled={isGenerating || isLoading || isFinish}
        loading={isUploading.value}
        type="text"
        {...props}
        className={cn(
          "text-primary rounded-sm text-xs font-normal",
          isFinish && "text-muted-foreground"
        )}
        icon={<Upload strokeWidth={1.5} size={16} />}
      >
        上传
      </Button>
    </Uploader>
  );
};
// 朗读稿改变提示
const IconGenerate: FC<ReadingAudioItemControlsProps> = () => {
  const { isTextChanged } = useReadingSentenceContext();

  if (isTextChanged.value) {
    return (
      <div className="text-primary flex h-8 items-center rounded-sm px-4 text-xs font-normal">
        <CircleAlert className="size-4 stroke-red-400" />
      </div>
    );
  }
  return null;
};

// 生成配音失败提示
const IconError: FC<ReadingAudioItemControlsProps> = () => {
  const { sentence } = useReadingSentenceContext();
  const { genStatus } = sentence.value;
  if (genStatus === 3) {
    return (
      <div className="text-primary flex h-8 items-center rounded-sm px-4 text-xs font-normal">
        <CircleX className="size-4 stroke-red-400" />
      </div>
    );
  }
  return null;
};

// 生成中提示
const TipLoading: FC<ReadingAudioItemControlsProps> = () => {
  const { sentence } = useReadingSentenceContext();
  const { genStatus } = sentence.value;
  if (genStatus === 1) {
    return (
      <div className="flex flex-row h-8 items-center gap-2 pl-3.5 text-xs text-gray-500">
        <span className="relative flex size-2">
          <span className="absolute inline-flex h-full w-full animate-ping rounded-full bg-blue-400 opacity-75"></span>
          <span className="relative inline-flex size-2 rounded-full bg-blue-500"></span>
        </span>
        生成中...
      </div>
    );
  }
  return null;
};

export {
  BtnAudio,
  BtnDownload,
  BtnGenerate,
  BtnUpload,
  IconGenerate,
  IconError,
  TipLoading,
};
