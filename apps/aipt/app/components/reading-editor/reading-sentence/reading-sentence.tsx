import {
  AudioSentenceChangeType,
  useReadingEditorContext,
} from "@/app/components/reading-editor/reading-editor-context";
import { useGuideContext } from "@/app/context/guide-context";
import { post } from "@/app/utils/fetcher";
import { AudioSentence } from "@/types/guide-widget";
import {
  signal,
  Signal,
  useComputed,
  useSignal,
  useSignalEffect,
} from "@preact-signals/safe-react";
import { cn } from "@repo/ui/lib/utils";
import {
  ComponentProps,
  createContext,
  FC,
  useCallback,
  useContext,
  useMemo,
} from "react";
import useSWRMutation from "swr/mutation";
import { BtnAudio, BtnDownload,/* BtnGenerate,*/ BtnUpload, IconGenerate, IconError, TipLoading } from "./controls";

// 提供上下文
interface ReadingSentenceContextType {
  sentence: Signal<AudioSentence>;
  isTextChanged: Signal<boolean>;
  generate: (changeType: AudioSentenceChangeType) => Promise<void>;
  isLoading: boolean;
  changeText: (text: string) => void;
  changeUrl: (url: string) => void;
}
const ReadingSentenceContext = createContext<ReadingSentenceContextType>({
  sentence: signal<AudioSentence>({} as AudioSentence),
  isTextChanged: signal(false),
  generate: async () => {},
  isLoading: false,
  changeText: () => {},
  changeUrl: () => {},
});
export const useReadingSentenceContext = () =>
  useContext(ReadingSentenceContext);

interface ReadingSentenceProps extends ComponentProps<"li"> {
  data: AudioSentence;
}
export const ReadingSentence: FC<ReadingSentenceProps> = ({ data }) => {
  const { guide, isFinish, refresh } = useGuideContext();

  const { changeContent, isDifferentWithInitial } = useReadingEditorContext();

  const defaultSentence = useMemo(() => {
    return data;
  }, [data]);

  const sentence = useSignal<AudioSentence>(defaultSentence);
  const changeText = (text: string) => {
    sentence.value = { ...sentence.value, audioSentenceText: text, changeType: 1 };
    // changeContent(sentence.value);
  };

  const changeUrl = (url: string) => {
    sentence.value = { ...sentence.value, audioSentenceUrl: url };
    // changeContent(sentence.value);
  };

  const { trigger: triggerGenerate, isMutating: isLoading } = useSWRMutation(
    "/api/v1/guideWidget/produce/audiojson",
    post
  );

  const generate = useCallback(
    async (changeType: AudioSentenceChangeType) => {
      const { guideWidgetSetId, guideWidgetId } = guide;
      const it = sentence.value;
      delete it.isCompleted;
      await triggerGenerate({
        guideWidgetSetId,
        guideWidgetId,
        sentence: {
          ...it,
          changeType,
        },
      });
      sentence.value = { ...sentence.value, isCompleted: false, uploadMark: true };
      // changeContent(sentence.value);
      refresh?.();
    },
    [triggerGenerate, guide, sentence, refresh]
  );

  const isTextChanged = useComputed(() =>
    isDifferentWithInitial(sentence.value)
  );

  useSignalEffect(() => {
    changeContent(sentence.value);
  });

  const value = {
    sentence,
    isTextChanged,
    changeText,
    changeUrl,
    generate,
    isLoading,
  };

  return (
    <li className="flex flex-row items-start gap-2">
      <ReadingSentenceContext value={value}>
        <span className="size-4 pt-2">{data.index ? data.index + 1 : 1}.</span>
        <>
          <textarea
            disabled={isFinish}
            className={cn(
              "flex-1 rounded-sm px-4 py-2 outline-1 outline-gray-300",
              isFinish && "text-muted-foreground"
            )}
            value={sentence.value.audioSentenceText}
            onChange={(e) => changeText(e.target.value)}
          />
        </>
        <div className="max-w-1/3 flex h-full flex-col justify-evenly">
          <menu className="flex flex-row gap-2 text-sm">
            <BtnAudio />
            {/* <BtnGenerate /> */}
            <BtnDownload />
            <BtnUpload />
          </menu>
          <menu className="flex flex-row gap-2 text-sm">
            <IconGenerate />
            <IconError />
            <TipLoading />
          </menu>
        </div>
      </ReadingSentenceContext>
    </li>
  );
};
