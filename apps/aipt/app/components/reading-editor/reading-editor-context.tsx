import { post } from "@/app/utils/fetcher";
import { GuideStatus, GuideTaskType } from "@/types/base";
import { AudioJson, AudioSentence } from "@/types/guide-widget";
import {
  signal,
  Signal,
  useSignal,
  useSignalEffect,
} from "@preact-signals/safe-react";
import { diff } from "json-diff-ts";
import {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from "react";
import useSWRMutation from "swr/mutation";
import { useGuideContext } from "../../context/guide-context";

enum AudioSentenceChangeType {
  None = 0,
  Regenerate = 1,
  Upload = 2,
}

export interface ReadingEditorContextType {
  content: AudioJson;
  reset: () => void;
  save: () => void;
  isSaving: boolean;
  isContentChanged: boolean;
  changeContent: (it: AudioSentence) => void;
  isDifferentWithInitial: (it: AudioSentence) => boolean;
  // 当前正在播放的音频
  playingAudioRef: Signal<HTMLAudioElement | null>;
  playbackRate: Signal<number>;
  generateInfo: string | null;
  batchGenerate: () => void;
  batchGenerateLoading: boolean;
  isBatchGenerateDisabled: boolean;
  mergeEntireAudio: () => void;
  isMergeWholeAudio: boolean;
  isMergeAudioDisabled: boolean;
}

const ReadingEditorContext = createContext<ReadingEditorContextType>({
  content: {} as AudioJson,
  reset: () => {},
  save: () => {},
  isSaving: false,
  isContentChanged: false,
  changeContent: () => {},
  isDifferentWithInitial: () => false,
  playingAudioRef: signal<HTMLAudioElement | null>(null),
  playbackRate: signal(1),
  generateInfo: null,
  batchGenerate: () => {},
  batchGenerateLoading: false,
  isBatchGenerateDisabled: false,
  mergeEntireAudio: () => {},
  isMergeWholeAudio: false,
  isMergeAudioDisabled: false,
});

const useReadingEditorContext = () => useContext(ReadingEditorContext);

interface ReadingEditorProviderProps {
  children: React.ReactNode;
}

const ReadingEditorProvider = ({ children }: ReadingEditorProviderProps) => {
  const {
    guide,
    localDraft,
    hasPanelUnSavedContent,
    isGenerating: isGeneratingGuide,
    refresh,
  } = useGuideContext();

  const { audioJson, audioJsonDraft } = guide;
  // 初始内容
  const initialContent = useMemo(() => {
    if (!audioJson) {
      return null;
    }

    const { audioUrl, sentenceList } = audioJson;
    return {
      audioUrl,
      sentenceList: sentenceList.map((sentence, index) => ({
        ...sentence,
        index,
        changeType: 0,
        isCompleted: true,
      })),
    } as AudioJson;
  }, [audioJson]);
  // 远程草稿
  const remoteDraftContent = useMemo(() => {
    if (!audioJsonDraft) {
      return null;
    }
    const { audioUrl, sentenceList } = audioJsonDraft;

    return {
      audioUrl,
      sentenceList: sentenceList.map((sentence, index) => ({
        ...sentence,
        index,
        changeType: 0,
        isCompleted: sentence.isCompleted === false ? false : true,
      })),
    };
  }, [audioJsonDraft]);

  // // 本地草稿
  // todo: 草稿发生变化没更新
  // const localDraftContent = useMemo(() => {
  //   return localDraft.load<AudioJson>("audioJson");
  // }, [localDraft]);

  const currentContent = useMemo(() => {
    if (!initialContent) {
      return {} as AudioJson;
    }
    // 朗读稿文案内容： 本地草稿 > 合并远程草稿 > 初始内容
    // 其余内容：合并远程草稿 > 本地草稿 > 初始内容
    const localDraftContent = localDraft.load<AudioJson>(
      "audioJson",
      null as unknown as AudioJson
    );
    const remoteAudioUrl = initialContent.audioUrl;
    const localUrl = localDraftContent ? localDraftContent.audioUrl : "";
    if (remoteAudioUrl && remoteAudioUrl !== localUrl) {
      return initialContent;
    }
    const draft = remoteDraftContent || localDraftContent;
    if (!draft || !draft.sentenceList) {
      return initialContent;
    }

    const { audioUrl, sentenceList } = initialContent;
    const list = [];
    for (let i = 0; i < sentenceList.length; i++) {
      const sentence = sentenceList[i];
      const draftSentence = draft.sentenceList.at(i);
      const { audioSentenceText, changeType } =
        localDraftContent?.sentenceList?.at(i) || {};
      if (draftSentence) {
        list.push({
          ...sentence,
          ...draftSentence,
          audioSentenceText:
            draftSentence.genStatus === 2
              ? draftSentence.audioSentenceText
              : audioSentenceText || draftSentence.audioSentenceText,
          changeType: draftSentence.genStatus === 2 ? 0 : changeType,
        });
      } else {
        list.push(sentence);
      }
    }
    return {
      audioUrl: draft.audioUrl || audioUrl,
      sentenceList: [...list] as AudioSentence[],
    };
  }, [initialContent, remoteDraftContent, localDraft]);

  const [content, setContent] = useState<AudioJson>(
    currentContent as AudioJson
  );

  // 添加 useEffect 来同步 currentContent 和 content
  useEffect(() => {
    setContent(currentContent as AudioJson);
  }, [currentContent]);

  const changeContent = useCallback(
    (target: AudioSentence) => {
      const { sentenceList } = content;
      const hasChanged = sentenceList.some(
        (item) => item.index === target.index && item !== target
      );
      if (!hasChanged) {
        return;
      }
      const list = sentenceList.map((item) => {
        if (item.index === target.index) {
          return target;
        }
        return item;
      });
      setContent({ ...content, sentenceList: list });
    },
    [content]
  );

  const playingAudioRef = useSignal<HTMLAudioElement | null>(null);
  const playbackRate = useSignal(1);

  // 朗读稿整体内容变化
  const isContentChanged = useMemo(() => {
    const format = (it: AudioJson | null) => {
      if (!it) {
        return null;
      }
      return {
        // ...it, // audioUrl 不参与对比
        sentenceList: it.sentenceList?.map((item) => {
          const { audioSentenceText, audioSentenceUrl, audioWordsUrl } = item;
          return {
            audioSentenceText,
            audioSentenceUrl,
            audioWordsUrl,
          };
        }),
      };
    };
    const diffs = diff(format(content), format(initialContent));
    // console.log("diffs", diffs);
    return diffs.length > 0;
  }, [content, initialContent]);

  const isDifferentWithInitial = useCallback(
    (target: AudioSentence) => {
      const remote = remoteDraftContent || initialContent;
      if (!remote) {
        return true;
      }
      const initial = remote.sentenceList?.at(target.index!);
      return initial?.audioSentenceText !== target.audioSentenceText;
    },
    [initialContent, remoteDraftContent]
  );

  const { trigger: triggerSave, isMutating: isSaving } = useSWRMutation(
    "/api/v1/guideWidget/save/audiojson",
    post
  );

  const isGenerating = useSignal(
    isGeneratingGuide &&
      (guide.flowRunType === GuideTaskType.GenerateReadingAndWhiteboard ||
        guide.flowRunType === GuideTaskType.GenerateReading)
  );

  useSignalEffect(() => {
    if (isGenerating.value) {
      localDraft.clear("audioJson");
    }
  });

  const save = useCallback(async () => {
    const { guideWidgetSetId, guideWidgetId } = guide;
    if (!guideWidgetSetId || !guideWidgetId) {
      return;
    }
    await triggerSave({
      guideWidgetSetId,
      guideWidgetId,
      audioJson: content,
    });
    localDraft.clear("audioJson");
    refresh?.();
  }, [content, triggerSave, guide, localDraft, refresh]);

  const { trigger: triggerBatchGenerate } = useSWRMutation(
    "/api/v1/guideWidget/produce/audiojson/batch",
    post
  );
  // 批量生成配音按钮是否loading
  const batchGenerateLoading = useMemo(() => {
    const { flowRunType, guideWidgetStatus } = guide;
    return (
      flowRunType === GuideTaskType.GenerateReading &&
      guideWidgetStatus === GuideStatus.Loading
    );
  }, [guide]);

  // 批量生成配音按钮是否禁用
  const isBatchGenerateDisabled = useMemo(() => {
    const format = (it: AudioJson | null) => {
      if (!it) {
        return null;
      }
      return {
        // ...it, // audioUrl 不参与对比
        sentenceList: it.sentenceList?.map((item) => {
          const { audioSentenceText } = item;
          return {
            audioSentenceText,
          };
        }),
      };
    };
    const diffs = diff(format(content), format(initialContent));
    return diffs.length === 0;
  }, [content, initialContent]);

  // 生成配音完成提示
  const generateInfo = useMemo(() => {
    const { sentenceList } = guide.audioJsonDraft || {};
    const indices =
      sentenceList
        ?.map((item, index) => (item.genStatus === 2 ? index + 1 : null))
        .filter((index) => index !== null) || [];
    return indices?.length > 0
      ? `最近生成配音：${indices.join("、")}，请返回单句播放检查`
      : null;
  }, [guide]);

  // 批量生成配音
  const batchGenerate = useCallback(async () => {
    const { guideWidgetSetId, guideWidgetId } = guide;
    if (!guideWidgetSetId || !guideWidgetId) {
      return;
    }
    await triggerBatchGenerate({
      guideWidgetSetId,
      guideWidgetId,
      audioJson: content,
    });
    localDraft.clear("audioJson");
    refresh?.();
  }, [content, triggerBatchGenerate, guide, localDraft, refresh]);

  const { trigger: triggerMergeAudio } = useSWRMutation(
    "/api/v1/guideWidget/merge/audiojson",
    post
  );

  // 合成整个配音按钮是否loading
  const isMergeWholeAudio = useMemo(() => {
    const { flowRunType, guideWidgetStatus } = guide;
    return (
      flowRunType === GuideTaskType.GenerateMergeWholeAudio &&
      guideWidgetStatus === GuideStatus.Loading
    );
  }, [guide]);

  // 合成整个配音按钮是否禁用
  const isMergeAudioDisabled = useMemo(() => {
    const { sentenceList } = guide.audioJsonDraft || {};
    const hasgGenerateAudio = sentenceList?.some(
      (item) => item.genStatus === 2
    );

    return (
      batchGenerateLoading ||
      (!hasgGenerateAudio &&
        !content.sentenceList?.some((item) => item.uploadMark))
    );
  }, [content, guide, batchGenerateLoading]);

  // 合成整个配音
  const mergeEntireAudio = useCallback(async () => {
    const { guideWidgetSetId, guideWidgetId } = guide;
    if (!guideWidgetSetId || !guideWidgetId) {
      return;
    }
    const res = await triggerMergeAudio({
      guideWidgetSetId,
      guideWidgetId,
    });
    // setTimeout(() => {
    localDraft.clear("audioJson");
    refresh?.();
    // }, 500);
    return res;
  }, [triggerMergeAudio, guide, localDraft, refresh]);

  // 计算是否有未保存的朗读内容
  const hasUnsavedReading = useMemo(() => {
    return isContentChanged || !isMergeAudioDisabled;
  }, [isContentChanged, isMergeAudioDisabled]);

  useEffect(() => {
    hasPanelUnSavedContent.value = {
      ...hasPanelUnSavedContent.value,
      reading: hasUnsavedReading,
    };
    if (isGenerating.value) return;
    // 保存本地草稿
    localDraft.save("audioJson", content);
  }, [
    isContentChanged,
    localDraft,
    content,
    hasPanelUnSavedContent,
    hasUnsavedReading,
    isGenerating.value,
  ]);

  const value = {
    content,
    isContentChanged,
    changeContent,
    isDifferentWithInitial,
    playingAudioRef,
    playbackRate,
    reset: () => {},
    save,
    isSaving,
    generateInfo,
    batchGenerate,
    batchGenerateLoading,
    isBatchGenerateDisabled,
    mergeEntireAudio,
    isMergeWholeAudio,
    isMergeAudioDisabled,
  };

  return <ReadingEditorContext value={value}>{children}</ReadingEditorContext>;
};

export {
  AudioSentenceChangeType,
  ReadingEditorProvider,
  useReadingEditorContext,
};
