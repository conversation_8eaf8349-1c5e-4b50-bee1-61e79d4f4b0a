import { AudioPlayer } from "@/app/components/player/audio-player";
import { useReadingEditorContext } from "@/app/components/reading-editor/reading-editor-context";
import { useGuideContext } from "@/app/context/guide-context";
import { useEffect, useMemo } from "react";
import { EmptyTip } from "../panels/empty-tip";
import { ReadingSentence } from "./reading-sentence/reading-sentence";
import { isEqual } from "lodash";

export const ReadingEditor = () => {
  const { isInit, setReadingContext, readingContext } = useGuideContext();
  const ctx = useReadingEditorContext();
  const { content } = ctx;
  useEffect(() => {
    setReadingContext(ctx);
  }, [ctx]);
  const { sentenceList } = content;
  const hasData = useMemo(() => {
    return sentenceList && sentenceList.length > 0;
  }, [sentenceList]);

  if (isInit && !hasData) {
    return <EmptyTip texture="需基于逐字稿生成朗读稿" />;
  }

  return (
    <div className="flex flex-col gap-2">
      <ol className="flex flex-col gap-2">
        {sentenceList?.length > 0 &&
          sentenceList.map((item, index) => (
            <ReadingSentence
              data={item}
              key={`sentence-${item.audioSentenceUrl}-${index}`}
            />
          ))}
      </ol>

      <AudioPlayer />
    </div>
  );
};
