import { useReadingEditorContext } from "@/app/components/reading-editor/reading-editor-context";
import { Button } from "@/app/components/common/aipt-button";
import { cn } from "@repo/ui/lib/utils";
import { CloudUpload } from "lucide-react";

const BtnSave = () => {
  const { isContentChanged, save, isSaving } = useReadingEditorContext();
  const disabled = !isContentChanged;
  return (
    <Button
      disabled={disabled}
      type="primary"
      loading={isSaving}
      className={cn(
        "rounded-sm border bg-slate-100 text-xs font-bold text-slate-400 hover:bg-slate-50",
        !disabled && "bg-red-600 text-white hover:bg-red-700"
      )}
      onClick={save}
      icon={<CloudUpload className="size-4" />}
    >
      提交
    </Button>
  );
};

export { BtnSave };
