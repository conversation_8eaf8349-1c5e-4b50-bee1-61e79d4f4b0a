import {
  AudioSentenceChangeType,
  useReadingEditorContext,
} from "@/app/components/reading-editor/reading-editor-context";
import { useGuideContext } from "@/app/context/guide-context";
import { AudioSentence } from "@/types/guide-widget";
import {
  signal,
  Signal,
  useComputed,
  useSignal,
  useSignalEffect,
} from "@preact-signals/safe-react";
import { post } from "@/app/utils/fetcher";
import { cn } from "@repo/ui/lib/utils";
import {
  ComponentProps,
  createContext,
  FC,
  useCallback,
  useContext,
  useMemo,
} from "react";
import useSWRMutation from "swr/mutation";
import { BtnAudio, BtnDownload, /* BtnGenerate,*/ BtnUpload } from "./controls";

// 提供上下文
interface ReadingSentenceContextType {
  sentence: Signal<AudioSentence>;
  isTextChanged: Signal<boolean>;
  generate: (changeType: AudioSentenceChangeType) => Promise<void>;
  uploadAudio: () => Promise<void>;
  isLoading: boolean;
  changeText: (text: string) => void;
  changeUrl: (url: string) => void;
}
const ReadingSentenceContext = createContext<ReadingSentenceContextType>({
  sentence: signal<AudioSentence>({} as AudioSentence),
  isTextChanged: signal(false),
  generate: async () => {},
  uploadAudio: async () => {},
  isLoading: false,
  changeText: () => {},
  changeUrl: () => {},
});
export const useReadingSentenceContext = () =>
  useContext(ReadingSentenceContext);

interface ReadingSentenceProps extends ComponentProps<"li"> {
  data: AudioSentence;
}
export const ReadingSentence: FC<ReadingSentenceProps> = ({ data }) => {
  const { guide, isFinish, isGenerating, refresh, localDraft } =
    useGuideContext();

  const { changeContent, isDifferentWithInitial } = useReadingEditorContext();

  const defaultSentence = useMemo(() => {
    return data;
  }, [data]);

  const sentence = useSignal<AudioSentence>(defaultSentence);
  const changeText = (text: string) => {
    sentence.value = { ...sentence.value, audioSentenceText: text };
    // changeContent(sentence.value);
  };

  const changeUrl = (url: string) => {
    sentence.value = { ...sentence.value, audioSentenceUrl: url };
    // changeContent(sentence.value);
  };

  const { trigger: triggerGenerate, isMutating: isLoading } = useSWRMutation(
    "/api/v1/guideWidget/bugChange/audioJson",
    post
  );
  // 生成音频
  const generate = useCallback(
    async (changeType: AudioSentenceChangeType) => {
      const { guideWidgetSetId, guideWidgetId } = guide;
      const it = sentence.value;
      delete it.isCompleted;
      await triggerGenerate({
        guideWidgetSetId,
        guideWidgetId,
        sentence: {
          ...it,
          changeType,
        },
      });
      sentence.value = { ...sentence.value, isCompleted: false };
      // changeContent(sentence.value);
      refresh?.();
    },
    [triggerGenerate, guide, sentence, refresh]
  );
  // 上传音频
  const uploadAudio = useCallback(async () => {
    const { guideWidgetSetId, guideWidgetId } = guide;
    const { index, audioSentenceUrl } = sentence.value;
    await triggerGenerate({
      guideWidgetSetId,
      guideWidgetId,
      index,
      audioUrl: audioSentenceUrl,
    });
    sentence.value = { ...sentence.value, isCompleted: false };
    localDraft.clear("audioJson");
    refresh?.();
  }, [triggerGenerate, guide, sentence, refresh, localDraft]);

  const isTextChanged = useComputed(() =>
    isDifferentWithInitial(sentence.value)
  );

  useSignalEffect(() => {
    changeContent(sentence.value);
  });

  const value = {
    sentence,
    isTextChanged,
    changeText,
    changeUrl,
    generate,
    uploadAudio,
    isLoading,
  };

  return (
    <li className="flex flex-row items-start gap-2">
      <ReadingSentenceContext value={value}>
        <span className="size-4 pt-2">{data.index ? data.index + 1 : 1}.</span>
        <>
          <textarea
            disabled={isFinish}
            className={cn(
              "flex-1 rounded-sm px-4 py-2 outline-1 outline-gray-300",
              isFinish && "text-muted-foreground"
            )}
            value={sentence.value.audioSentenceText}
            onChange={(e) => changeText(e.target.value)}
          />
        </>
        <div className="max-w-1/3 flex h-full flex-col justify-evenly">
          <menu className="flex flex-row gap-2 text-sm">
            <BtnAudio />
            {/* <BtnGenerate /> */}
            <BtnDownload />
            <BtnUpload />
          </menu>
          {isGenerating && !sentence.value.isCompleted && (
            <div className="flex flex-row items-center gap-2 pl-3.5 text-xs text-gray-500">
              <span className="relative flex size-2">
                <span className="absolute inline-flex h-full w-full animate-ping rounded-full bg-blue-400 opacity-75"></span>
                <span className="relative inline-flex size-2 rounded-full bg-blue-500"></span>
              </span>
              生成中...
            </div>
          )}
        </div>
      </ReadingSentenceContext>
    </li>
  );
};
