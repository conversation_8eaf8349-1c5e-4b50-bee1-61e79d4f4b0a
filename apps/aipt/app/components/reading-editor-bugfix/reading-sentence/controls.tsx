import {
  AudioSentenceChangeType,
  useReadingEditorContext,
} from "@/app/components/reading-editor/reading-editor-context";
import { useGuideContext } from "@/app/context/guide-context";
import { useGuideSetContext } from "@/app/context/guide-set-context";
import { useSignal, useSignalEffect } from "@preact-signals/safe-react";
import { download } from "@repo/lib/utils/download";
import { Button } from "@/app/components/common/aipt-button";
import { cn } from "@repo/ui/lib/utils";
import {
  CircleAlert,
  Download,
  Pause,
  Play,
  Sparkle,
  Upload,
} from "lucide-react";
import { ComponentProps, FC, useEffect, useMemo, useRef } from "react";
import { Uploader } from "../../uploader/uploader";
import { useReadingSentenceContext } from "./reading-sentence";
type ReadingAudioItemControlsProps = ComponentProps<typeof Button> & {};

const BtnAudio: FC<ReadingAudioItemControlsProps> = () => {
  const { sentence } = useReadingSentenceContext();
  const { audioSentenceUrl } = sentence.value;
  const { playingAudioRef, playbackRate } = useReadingEditorContext();
  const audioRef = useRef<HTMLAudioElement>(null);
  const isPlaying = useSignal(false);

  const handleClick = () => {
    isPlaying.value = !isPlaying.value;
  };
  useSignalEffect(() => {
    if (isPlaying.value) {
      audioRef.current?.play();
      playingAudioRef.value = audioRef.current;
    } else {
      audioRef.current?.pause();
    }
  });

  useSignalEffect(() => {
    if (playingAudioRef.value !== audioRef.current) {
      isPlaying.value = false;
    }
  });

  useSignalEffect(() => {
    if (audioRef.current) {
      audioRef.current.playbackRate = playbackRate.value;
    }
  });

  useEffect(() => {
    if (audioRef.current) {
      const player = audioRef.current;
      player.addEventListener("ended", () => {
        isPlaying.value = false;
      });
    }
  }, [audioRef, isPlaying]);

  return (
    <>
      <audio src={audioSentenceUrl} hidden ref={audioRef} />
      <Button
        type="text"
        onClick={handleClick}
        className="text-primary rounded-sm text-xs font-normal"
        icon={
          isPlaying.value ? (
            <Pause strokeWidth={1.5} size={16} />
          ) : (
            <Play strokeWidth={1.5} size={16} />
          )
        }
      >
        {isPlaying.value ? "暂停" : "播放"}
      </Button>
    </>
  );
};

const BtnGenerate: FC<ReadingAudioItemControlsProps> = (props) => {
  const { isFinish, isGenerating } = useGuideContext();
  const { isTextChanged, generate, isLoading } = useReadingSentenceContext();

  const handleClick = async () => {
    await generate(AudioSentenceChangeType.Regenerate);
  };

  const icon = useMemo(() => {
    if (isTextChanged.value) {
      return <CircleAlert className="size-4 stroke-red-400" />;
    }
    return <Sparkle strokeWidth={1.5} className="size-4" />;
  }, [isTextChanged.value]);

  return (
    <Button
      disabled={isGenerating || isLoading || isFinish}
      onClick={handleClick}
      type="text"
      {...props}
      className={cn(
        "text-primary rounded-sm text-xs font-normal",
        isFinish && "text-muted-foreground"
      )}
      icon={icon}
    >
      生成配音
    </Button>
  );
};

const BtnDownload: FC<ReadingAudioItemControlsProps> = (props) => {
  const { guideWidgetIndex } = useGuideSetContext();
  const { guideSet } = useGuideContext();
  const { sentence } = useReadingSentenceContext();
  const { audioSentenceUrl, index = 0 } = sentence.value;
  const loading = useSignal(false);

  const handleDownload = async () => {
    loading.value = true;
    const fileName = `${guideSet.guideWidgetSetName}_Part${guideWidgetIndex.value + 1}_第${index + 1}句`;
    await download(audioSentenceUrl, fileName);
    loading.value = false;
  };

  return (
    <Button
      type="text"
      disabled={loading.value}
      loading={loading.value}
      onClick={handleDownload}
      {...props}
      className="text-primary rounded-sm text-xs font-normal"
      icon={<Download strokeWidth={1.5} size={16} />}
    >
      下载
    </Button>
  );
};

const BtnUpload: FC<ReadingAudioItemControlsProps> = (props) => {
  const { isFinish, isGenerating } = useGuideContext();
  const { uploadAudio, isLoading, changeUrl } = useReadingSentenceContext();
  const isUploading = useSignal(false);

  const beforeUpload = async () => {
    isUploading.value = true;
  };

  const handleUploaded = async (url: string) => {
    console.log("handleUploaded", url);
    changeUrl(url);
    isUploading.value = false;
    await uploadAudio();
  };

  return (
    <Uploader
      accept="audio/*"
      showUploadedFile={false}
      beforeUpload={beforeUpload}
      onUpload={handleUploaded}
    >
      <Button
        disabled={isGenerating || isLoading || isFinish}
        loading={isUploading.value}
        type="text"
        {...props}
        className={cn(
          "text-primary rounded-sm text-xs font-normal",
          isFinish && "text-muted-foreground"
        )}
        icon={<Upload strokeWidth={1.5} size={16} />}
      >
        上传
      </Button>
    </Uploader>
  );
};

export { BtnAudio, BtnDownload, BtnGenerate, BtnUpload };
