import { AudioPlayer } from "@/app/components/player-bugfix/audio-player";
import { useReadingEditorContext } from "./reading-editor-context";
import { useGuideContext } from "@/app/context/guide-context";
import { useMemo } from "react";
import { EmptyTip } from "../panels/empty-tip";
import { ReadingSentence } from "./reading-sentence/reading-sentence";

export const ReadingEditor = () => {
  const { isInit } = useGuideContext();
  const { content } = useReadingEditorContext();
  const { sentenceList } = content;
  const hasData = useMemo(() => {
    return sentenceList && sentenceList.length > 0;
  }, [sentenceList]);

  if (isInit && !hasData) {
    return <EmptyTip texture="需基于逐字稿生成朗读稿" />;
  }

  return (
    <div className="flex flex-col gap-2">
      <ol className="flex flex-col gap-2">
        {sentenceList?.length > 0 &&
          sentenceList.map((item, index) => (
            <ReadingSentence
              data={item}
              key={`sentence-${item.audioSentenceUrl}-${index}`}
            />
          ))}
      </ol>

      <AudioPlayer />
    </div>
  );
};
