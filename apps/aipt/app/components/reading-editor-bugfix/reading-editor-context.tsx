import { GuideTaskType } from "@/types/base";
import { AudioJson, AudioSentence } from "@/types/guide-widget";
import {
  signal,
  Signal,
  useSignal,
  useSignalEffect,
} from "@preact-signals/safe-react";
import { post } from "@/app/utils/fetcher";
import { diff } from "json-diff-ts";
import {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from "react";
import useSWRMutation from "swr/mutation";
import { useGuideContext } from "../../context/guide-context";

enum AudioSentenceChangeType {
  None = 0,
  Regenerate = 1,
  Upload = 2,
}

interface ReadingEditorContextType {
  content: AudioJson;
  reset: () => void;
  save: () => void;
  isSaving: boolean;
  isContentChanged: boolean;
  changeContent: (it: AudioSentence) => void;
  isDifferentWithInitial: (it: AudioSentence) => boolean;
  // 当前正在播放的音频
  playingAudioRef: Signal<HTMLAudioElement | null>;
  playbackRate: Signal<number>;
}

const ReadingEditorContext = createContext<ReadingEditorContextType>({
  content: {} as AudioJson,
  reset: () => {},
  save: () => {},
  isSaving: false,
  isContentChanged: false,
  changeContent: () => {},
  isDifferentWithInitial: () => false,
  playingAudioRef: signal<HTMLAudioElement | null>(null),
  playbackRate: signal(1),
});

const useReadingEditorContext = () => useContext(ReadingEditorContext);

interface ReadingEditorProviderProps {
  children: React.ReactNode;
}

const ReadingEditorProvider = ({ children }: ReadingEditorProviderProps) => {
  const {
    guide,
    localDraft,
    hasUnSavedContent,
    isGenerating: isGeneratingGuide,
    refresh,
  } = useGuideContext();

  const { audioJson, audioJsonDraft } = guide;
  // 初始内容
  const initialContent = useMemo(() => {
    if (!audioJson) {
      return null;
    }

    const { audioUrl, sentenceList } = audioJson;
    return {
      audioUrl,
      sentenceList: sentenceList.map((sentence, index) => ({
        ...sentence,
        index,
        isCompleted: true,
      })),
    } as AudioJson;
  }, [audioJson]);

  // 远程草稿
  const remoteDraftContent = useMemo(() => {
    if (!audioJsonDraft) {
      return null;
    }
    const { audioUrl, sentenceList } = audioJsonDraft;
    return {
      audioUrl,
      sentenceList: sentenceList.map((sentence, index) => ({
        ...sentence,
        index,
        isCompleted: sentence.isCompleted === false ? false : true,
      })),
    };
  }, [audioJsonDraft]);

  // 本地草稿
  const localDraftContent = useMemo(() => {
    return localDraft.load<AudioJson>("audioJson", {} as AudioJson);
  }, [localDraft]);

  const currentContent = useMemo(() => {
    if (!initialContent) {
      return {} as AudioJson;
    }
    // 合并远程草稿 > 本地草稿 > 初始内容
    const draft = remoteDraftContent || localDraftContent;

    if (!draft || !draft.sentenceList) {
      return initialContent;
    }

    const { audioUrl, sentenceList } = initialContent;

    const list = [];
    for (let i = 0; i < sentenceList.length; i++) {
      const sentence = sentenceList[i];
      const draftSentence = draft.sentenceList.at(i);
      if (draftSentence) {
        list.push({
          ...sentence,
          ...draftSentence,
        });
      } else {
        list.push(sentence);
      }
    }
    return {
      audioUrl: draft.audioUrl || audioUrl,
      sentenceList: [...list] as AudioSentence[],
    };
  }, [initialContent, remoteDraftContent, localDraftContent]);

  const [content, setContent] = useState<AudioJson>(currentContent);

  // 添加 useEffect 来同步 currentContent 和 content
  useEffect(() => {
    setContent(currentContent);
  }, [currentContent]);

  const changeContent = useCallback(
    (target: AudioSentence) => {
      const { sentenceList } = content;
      const hasChanged = sentenceList.some(
        (item) => item.index === target.index && item !== target
      );
      if (!hasChanged) {
        return;
      }
      const list = sentenceList.map((item) => {
        if (item.index === target.index) {
          return target;
        }
        return item;
      });
      setContent({ ...content, sentenceList: list });
    },
    [content]
  );
  const playingAudioRef = useSignal<HTMLAudioElement | null>(null);
  const playbackRate = useSignal(1);

  const isContentChanged = useMemo(() => {
    const format = (it: AudioJson | null) => {
      if (!it) {
        return null;
      }
      return {
        ...it,
        sentenceList: it.sentenceList?.map((item) => {
          const { audioSentenceText, audioSentenceUrl, audioWordsUrl } = item;
          return {
            audioSentenceText,
            audioSentenceUrl,
            audioWordsUrl,
          };
        }),
      };
    };

    const diffs = diff(format(content), format(initialContent));
    // console.log("diffs", diffs);
    return diffs.length > 0;
  }, [content, initialContent]);

  const isDifferentWithInitial = useCallback(
    (target: AudioSentence) => {
      const remote = remoteDraftContent || initialContent;
      if (!remote) {
        return true;
      }
      const initial = remote.sentenceList?.at(target.index!);
      return initial?.audioSentenceText !== target.audioSentenceText;
    },
    [initialContent, remoteDraftContent]
  );

  const { trigger: triggerSave, isMutating: isSaving } = useSWRMutation(
    "/api/v1/guideWidget/save/audiojson",
    post
  );

  const isGenerating = useSignal(
    isGeneratingGuide &&
      (guide.flowRunType === GuideTaskType.GenerateReadingAndWhiteboard ||
        guide.flowRunType === GuideTaskType.GenerateReading)
  );

  useSignalEffect(() => {
    if (isGenerating.value) {
      localDraft.clear("audioJson");
    }
  });

  const save = useCallback(async () => {
    const { guideWidgetSetId, guideWidgetId } = guide;
    if (!guideWidgetSetId || !guideWidgetId) {
      return;
    }
    await triggerSave({
      guideWidgetSetId,
      guideWidgetId,
      audioJson: content,
    });
    localDraft.clear("audioJson");
    refresh?.();
  }, [content, triggerSave, guide, localDraft, refresh]);

  useEffect(() => {
    hasUnSavedContent.value = isContentChanged;
    if (isContentChanged) {
      // 保存本地草稿
      localDraft.save("audioJson", content);
    }
  }, [isContentChanged, localDraft, content, hasUnSavedContent]);

  const value = {
    content,
    isContentChanged,
    changeContent,
    isDifferentWithInitial,
    playingAudioRef,
    playbackRate,
    reset: () => {},
    save,
    isSaving,
  };

  return <ReadingEditorContext value={value}>{children}</ReadingEditorContext>;
};

export {
  AudioSentenceChangeType,
  ReadingEditorProvider,
  useReadingEditorContext,
};
