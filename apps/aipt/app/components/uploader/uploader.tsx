import { Button } from "@/app/components/common/button";
import { useGuideContext } from "@/app/context/guide-context";
import { useSignal } from "@preact-signals/safe-react";
import { upload } from "@repo/lib/utils/oss";
import { Check, Paperclip } from "lucide-react";
import { useRef } from "react";

export const Uploader = ({
  children,
  accept = "image/*",
  beforeUpload,
  onUpload,
  showUploadedFile = true,
}: {
  children: React.ReactNode;
  accept?: string;
  beforeUpload?: () => void;
  onUpload?: (url: string) => void;
  showUploadedFile?: boolean;
}) => {
  const { guide: data } = useGuideContext();
  const { guideWidgetSetId, guideWidgetId } = data;
  const inputRef = useRef<HTMLInputElement>(null);
  const uploaderUrl = useSignal<string>("");
  const copied = useSignal(false);
  const host = process.env.NEXT_PUBLIC_API_HOST;

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] ?? null;
    if (!file) return;
    console.log(file.name);
    beforeUpload?.();
    const { url, error } = await upload({
      file,
      signature: {
        url: `${host}/api/v1/upload/token`,
        params: {
          guideWidgetSetId: guideWidgetSetId.toString(),
          guideWidgetId: guideWidgetId.toString(),
        },
      },
    });
    console.log(url, error);
    if (url) {
      uploaderUrl.value = url;
      onUpload?.(url);
    }
  };

  const handleCopy = () => {
    const tmp = `<em data-src="${uploaderUrl.value}">需替换的文本</em>`;
    navigator.clipboard.writeText(tmp);
    copied.value = true;
    setTimeout(() => {
      copied.value = false;
    }, 1000);
  };

  const icon = copied.value ? (
    <Check strokeWidth={1.5} className="size-4 text-green-500" />
  ) : (
    <Paperclip strokeWidth={1.5} className="size-4" />
  );

  return (
    <div className="flex flex-row items-center gap-2">
      {uploaderUrl.value && showUploadedFile && (
        <Button
          onClick={handleCopy}
          type="text"
          className="text-primary p-0 text-xs"
          icon={icon}
        >
          复制
        </Button>
      )}
      <div onClick={() => inputRef.current?.click()}>
        <input
          type="file"
          hidden
          ref={inputRef}
          onChange={handleFileChange}
          accept={accept}
        />
        {children}
      </div>
    </div>
  );
};
