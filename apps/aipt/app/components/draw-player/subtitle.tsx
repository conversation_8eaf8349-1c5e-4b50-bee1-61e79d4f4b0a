import { MathContent } from "@repo/core/components/math-content";
import { Subtitle as SubtitleData } from "@repo/core/types/data/base";
import React from "react";
import { Sequence } from "remotion";
interface SubtitleProps {
  subtitles?: SubtitleData[];
}

export const Subtitle: React.FC<
  React.HTMLAttributes<HTMLDivElement> & SubtitleProps
> = ({ subtitles }) => {
  if (!subtitles) {
    return null;
  }

  return (
    <div className="bottom-5 flex min-h-[32px] justify-center whitespace-nowrap text-center text-base font-medium leading-8 tracking-[0.52px] text-[#4d3c32] [&>*]:text-center [&>span]:rounded-lg [&>span]:bg-[#f7f2ed] [&>span]:px-2.5">
      {subtitles.map((item, index) => {
        const { inFrame, outFrame, text } = item;
        const durationInFrames = outFrame - inFrame;
        if (durationInFrames <= 0) {
          throw new Error(`<字幕>  ${JSON.stringify(item)}`);
        }
        return (
          <Sequence
            layout="none"
            key={index.toString()}
            from={inFrame}
            durationInFrames={durationInFrames}
          >
            <MathContent>{text}</MathContent>
          </Sequence>
        );
      })}
    </div>
  );
};
