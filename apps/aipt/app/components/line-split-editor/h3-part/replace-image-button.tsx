"use client";

import { Button } from "@/app/components/common/button";
import { cn } from "@repo/ui/lib/utils";
import { useState } from "react";
import { ImageReplaceModal } from "../image-replace-modal";

// 导入 ImageItem 类型
interface ImageItem {
  id: string;
  url: string;
  aspectRatio: "16:9" | "1:1" | "9:16";
  source: "ai" | "local";
  type: "static" | "interactive";
  fileType: "image" | "js" | "html";
  createdAt: Date;
  width?: number;
  height?: number;
  blockIndex?: number;
  styleH3?: number;
}

interface ReplaceImageButtonProps {
  h3Id: string;
  partId: string;
  currentImageId?: string;
  onImageReplace: (selectedImage: ImageItem) => void;
  disabled?: boolean;
  blockIndex?: number; // H3块索引，从1开始
}

/**
 * 替换图片按钮组件
 * 点击后打开图片替换弹窗
 */
export const ReplaceImageButton = ({
  h3Id,
  partId,
  currentImageId,
  onImageReplace,
  disabled = false,
  blockIndex,
}: ReplaceImageButtonProps) => {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleOpenModal = () => {
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  const handleConfirm = (selectedImage: ImageItem) => {
    onImageReplace(selectedImage);
    setIsModalOpen(false);
  };

  return (
    <>
      <Button
        disabled={disabled}
        type="text"
        onClick={handleOpenModal}
        className={cn(
          "rounded-sm border text-xs text-[#4F5CFF] hover:bg-[#F5F7FF]"
        )}
      >
        替换图片
      </Button>

      <ImageReplaceModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        onConfirm={handleConfirm}
        h3Id={h3Id}
        partId={partId}
        currentImageId={currentImageId}
        blockIndex={blockIndex}
      />
    </>
  );
};
