import { Button } from "@/app/components/common/button";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@repo/ui/components/tooltip";
import { FC } from "react";
import { useLineSplitEditorContext } from "../line-split-editor-context";
import { useH3PartContext } from "./h3-part-context";

/**
 * 文字排版按钮组件
 * 根据H3部分的有序列表结构和字符数，自动判断是否可以应用文字排版
 * 与分栏功能互斥
 */
export const H3TextLayout: FC = () => {
  const { editDisabled } = useLineSplitEditorContext();
  const {
    textLayoutAnalysis,
    hasColumnLayout,
    hasTextLayout,
    applyTextLayout,
    removeTextLayout,
  } = useH3PartContext();

  // 计算按钮状态（添加边界检查）
  const canApplyTextLayout =
    textLayoutAnalysis?.canUseTextLayout && !hasColumnLayout && !hasTextLayout;

  const canRemoveTextLayout = hasTextLayout;

  // 按钮文本和提示信息
  const getButtonText = () => {
    if (hasTextLayout) {
      return "删除排版";
    }
    return "文字排版";
  };

  const getTooltipContent = () => {
    if (editDisabled) {
      return "编辑已禁用";
    }

    if (hasColumnLayout) {
      return "已设置分栏插图，无法使用文字排版";
    }

    if (hasTextLayout) {
      return "已应用文字排版，点击删除";
    }

    // 边界检查：防止分析结果为空
    if (!textLayoutAnalysis) {
      return "正在分析内容结构...";
    }

    if (!textLayoutAnalysis.canUseTextLayout) {
      return textLayoutAnalysis.overallReason || "无法应用文字排版";
    }

    return `${textLayoutAnalysis.overallReason || "可以应用文字排版"}，点击应用`;
  };

  const handleClick = () => {
    try {
      if (hasTextLayout) {
        removeTextLayout();
      } else if (canApplyTextLayout) {
        applyTextLayout();
      }
    } catch (error) {
      console.error("文字排版操作失败:", error);
    }
  };

  const isDisabled =
    editDisabled || (!canApplyTextLayout && !canRemoveTextLayout);

  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <Button
          disabled={isDisabled}
          className="rounded-sm border text-xs font-bold"
          onClick={handleClick}
          type={hasTextLayout ? "error" : "default"}
        >
          {getButtonText()}
        </Button>
      </TooltipTrigger>
      <TooltipContent>
        <p>{getTooltipContent()}</p>
      </TooltipContent>
    </Tooltip>
  );
};
