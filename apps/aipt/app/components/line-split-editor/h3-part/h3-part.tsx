import { useGuideContext } from "@/app/context/guide-context";
import { GuideStatus, GuideTaskType } from "@/types/base";
import { useGuideTreeViewmodel } from "@repo/core/guide/viewmodels/guide-tree-viewmodel";
import { Line } from "@repo/core/types/data/widget-guide";
import { cn } from "@repo/ui/lib/utils";
import { FC, useMemo } from "react";
import { GuideEditorLine } from "../../editor-guide/guide-editor-line";
import { H3PartProvider, useH3PartContext } from "./h3-part-context";
import { H3Splitter } from "./h3-splitter";
import { H3TextLayout } from "./h3-text-layout";

const GuideH3: FC = () => {
  const { lines: data } = useH3PartContext();
  const root = useGuideTreeViewmodel(data);

  return (
    <div className="flex w-[1000px] flex-col rounded-md bg-[#FFFDFA] px-4 outline-1">
      <div className="w-section-h3">
        <GuideEditorLine
          data={root}
          editable={true}
          root={true}
          parentStyleText={undefined}
        />
      </div>
    </div>
  );
};

const H3Part: FC<{ data: Line[]; index: number }> = ({ data, index }) => {
  const { guide } = useGuideContext();
  const { flowRunType, guideWidgetStatus } = guide;

  // 检查是否正在生成分栏图片
  const isGeneratingImages = useMemo(() => {
    return (
      guideWidgetStatus === GuideStatus.Loading &&
      flowRunType === GuideTaskType.GenerateColumnImages
    );
  }, [flowRunType, guideWidgetStatus]);

  return (
    <H3PartProvider data={data} index={index}>
      <div
        className={cn(
          "flex w-full flex-row items-start",
          isGeneratingImages && "pointer-events-none opacity-50"
        )}
      >
        <GuideH3 />
        <div className="sticky top-0 ml-auto flex flex-row items-start gap-2 pl-4">
          <H3TextLayout />
          <H3Splitter />
        </div>
      </div>
    </H3PartProvider>
  );
};

export { H3Part };
