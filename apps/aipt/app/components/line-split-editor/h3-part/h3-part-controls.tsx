import { useGuideContext } from "@/app/context/guide-context";
import {
  calculateCharacterCount,
  getDefaultStyleByCharCount,
} from "@/app/utils/character-count";
import { Line } from "@repo/core/types/data/widget-guide";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@repo/ui/components/dialog";
import { cn } from "@repo/ui/lib/utils";
import { FC, useCallback, useMemo, useState } from "react";
import { Button } from "../../common/button";
import { useLineSplitEditorContext } from "../line-split-editor-context";
import { useH3PartContext } from "./h3-part-context";
import { ReplaceImageButton } from "./replace-image-button";

// 5种分栏样式配置（按照UI图排版细节）
const STYLE_OPTIONS = [
  {
    value: "style1",
    label: "样式一",
    description: "上文下图，少字配大图",
    layout: "vertical" as const,
    textRatio: 1, // 文字占全宽
    imageRatio: "16:9" as const, // 宽图
    charRange: "1-35",
    textLines: "few", // 少量文字
  },
  {
    value: "style2",
    label: "样式二",
    description: "左文右图，文字多图片小",
    layout: "horizontal" as const,
    textRatio: 0.75, // 文字占75%
    imageRatio: "1:1" as const, // 小方图
    charRange: "35-70",
    textLines: "medium",
  },
  {
    value: "style3",
    label: "样式三",
    description: "左文右图，文字图片均衡",
    layout: "horizontal" as const,
    textRatio: 0.65, // 文字占65%
    imageRatio: "1:1" as const, // 中等方图
    charRange: "70-140",
    textLines: "more",
  },
  {
    value: "style4",
    label: "样式四",
    description: "左文右图，多字配长图",
    layout: "horizontal" as const,
    textRatio: 0.55, // 文字占55%
    imageRatio: "9:16" as const, // 竖长图
    charRange: "140-280",
    textLines: "many",
  },
  {
    value: "style5",
    label: "样式五",
    description: "上文下图，大量文字配宽图",
    layout: "vertical" as const,
    textRatio: 1, // 文字占全宽
    imageRatio: "16:9" as const, // 宽图
    charRange: "280+",
    textLines: "most", // 最多文字
  },
];

const BtnReplaceImage: FC<{ lineId: string }> = ({ lineId }) => {
  const { guide } = useGuideContext();
  const { setBlockPicture, blockIndex, lines } = useH3PartContext();
  const { editDisabled } = useLineSplitEditorContext();

  // 找到当前行的图片信息
  const currentLine = lines.find((line) => line.id === lineId);
  const currentImageUrl = currentLine?.pic?.url;

  const handleImageReplace = (selectedImage: any) => {
    // 将 ImageItem 转换为 Picture 对象
    const picture = {
      url: selectedImage.url,
      width:
        selectedImage.width ||
        (selectedImage.fileType === "js" || selectedImage.fileType === "html"
          ? 800
          : 400),
      height:
        selectedImage.height ||
        (selectedImage.fileType === "js" || selectedImage.fileType === "html"
          ? 450
          : 225),
      fileType: selectedImage.fileType || "image",
    };

    setBlockPicture(lineId, picture);
  };

  return (
    <ReplaceImageButton
      h3Id={guide.guideWidgetSetId.toString()}
      partId={guide.guideWidgetId.toString()}
      currentImageId={currentImageUrl}
      onImageReplace={handleImageReplace}
      blockIndex={blockIndex}
      disabled={editDisabled}
    />
  );
};

const BtnStyle: FC<{ lineId: string }> = ({ lineId }) => {
  const { editDisabled } = useLineSplitEditorContext();
  const [open, setOpen] = useState(false);
  const { setBlockStyle, lines } = useH3PartContext();

  // 获取当前分栏block的内容
  const currentBlock = useMemo(() => {
    return lines.find((line) => line.id === lineId && line.tag === "block");
  }, [lines, lineId]);

  // 获取当前block已设置的样式，如果没有则使用智能推荐
  const getCurrentBlockStyle = useCallback(() => {
    if (!currentBlock || !currentBlock.content) return "style1";

    // 🔧 修复：优先使用用户明确选择的样式类型
    if (currentBlock.styleType) {
      return currentBlock.styleType;
    }

    // 如果没有明确的样式类型，尝试从layout和imageRatio推断（兼容旧数据）
    if (currentBlock.layout && currentBlock.imageRatio) {
      if (
        currentBlock.layout === "vertical" &&
        currentBlock.imageRatio === "16:9"
      ) {
        // 对于旧数据，默认认为是样式1（因为我们无法准确区分1和5）
        return "style1";
      } else if (
        currentBlock.layout === "horizontal" &&
        currentBlock.imageRatio === "1:1"
      ) {
        const width = parseFloat(currentBlock.width || "75%");
        return width >= 75 ? "style2" : "style3";
      } else if (
        currentBlock.layout === "horizontal" &&
        currentBlock.imageRatio === "9:16"
      ) {
        return "style4";
      }
    }

    // 如果都没有，使用智能推荐
    const blockLines = currentBlock.content as Line[];
    const charCount = calculateCharacterCount(blockLines);
    return getDefaultStyleByCharCount(charCount);
  }, [currentBlock]);

  const [selectedStyleValue, setSelectedStyleValue] = useState<string>(() => {
    return getCurrentBlockStyle();
  });

  // 只在打开弹窗时重新获取当前样式
  const handleOpenDialog = () => {
    setSelectedStyleValue(getCurrentBlockStyle());
    setOpen(true);
  };

  const characterCount = useMemo(() => {
    if (!currentBlock || !currentBlock.content) return 0;
    const blockLines = currentBlock.content as Line[];
    return calculateCharacterCount(blockLines);
  }, [currentBlock]);

  // 判断是否是用户手动设置的样式（通过检查是否调用过setBlockStyle）
  const hasExistingStyle = useMemo(() => {
    if (!currentBlock || !currentBlock.content) return false;

    // 获取智能推荐的样式
    const blockLines = currentBlock.content as Line[];
    const charCount = calculateCharacterCount(blockLines);
    const recommendedStyle = getDefaultStyleByCharCount(charCount);
    const recommendedOption = STYLE_OPTIONS.find(
      (opt) => opt.value === recommendedStyle
    );

    if (!recommendedOption) return false;

    // 检查当前block的样式是否与智能推荐一致
    const isRecommendedLayout =
      currentBlock.layout === recommendedOption.layout;
    const isRecommendedRatio =
      currentBlock.imageRatio === recommendedOption.imageRatio;
    const isRecommendedWidth =
      Math.abs(
        parseFloat(currentBlock.width || "75%") -
          recommendedOption.textRatio * 100
      ) < 1;

    // 如果与智能推荐完全一致，说明还是初始状态（智能推荐）
    // 如果不一致，说明用户手动设置过
    return !(isRecommendedLayout && isRecommendedRatio && isRecommendedWidth);
  }, [currentBlock]);

  const handleSave = () => {
    const selectedStyle = STYLE_OPTIONS.find(
      (opt) => opt.value === selectedStyleValue
    );

    if (selectedStyle) {
      const styleData = {
        layout: selectedStyle.layout,
        textRatio: selectedStyle.textRatio || 1,
        imageRatio: selectedStyle.imageRatio || ("16:9" as const),
        styleType: selectedStyle.value as
          | "style1"
          | "style2"
          | "style3"
          | "style4"
          | "style5", // 🔧 添加用户选择的样式类型
      };
      setBlockStyle(lineId, styleData);
    }
    setOpen(false);
  };

  return (
    <>
      <Button
        disabled={editDisabled}
        type="text"
        className={cn(
          "rounded-sm border text-xs text-[#4F5CFF] hover:bg-[#F5F7FF]"
        )}
        onClick={handleOpenDialog}
      >
        设置样式
      </Button>
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent close={false} className="min-w-[600px]">
          <DialogHeader>
            <DialogTitle>设置样式</DialogTitle>
            <DialogDescription>
              当前字符数: {characterCount} •{" "}
              {hasExistingStyle ? "当前样式" : "智能推荐"}:{" "}
              {STYLE_OPTIONS.find((opt) => opt.value === selectedStyleValue)
                ?.label || "未知"}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-3">
            {STYLE_OPTIONS.map((opt) => (
              <label
                key={opt.value}
                className={`flex cursor-pointer items-center rounded-lg border p-3 transition-colors hover:border-[#4F5CFF] ${
                  selectedStyleValue === opt.value
                    ? "border-[#4F5CFF] bg-[#F5F7FF]"
                    : "border-gray-200"
                }`}
                onClick={() => setSelectedStyleValue(opt.value)}
              >
                <input
                  type="radio"
                  name="style"
                  value={opt.value}
                  checked={selectedStyleValue === opt.value}
                  className="mr-3"
                  readOnly
                />

                {/* 样式预览图 */}
                <div className="mr-4 h-16 w-24 rounded border bg-white p-1">
                  {opt.value === "style1" ? (
                    // 样式一：上文下图布局预览
                    <div className="flex h-full flex-col gap-0.5">
                      <div className="h-2 bg-[#7FECE1]"></div>
                      <div className="flex-1 rounded-sm bg-[#CCB4F5]"></div>
                    </div>
                  ) : opt.value === "style2" ? (
                    // 样式二：左文右图，文6:图2
                    <div className="flex h-full gap-0.5 pt-2">
                      <div
                        className="flex flex-col gap-0.5"
                        style={{ width: "75%" }}
                      >
                        <div className="h-1 bg-[#7FECE1]"></div>
                        <div className="h-1 bg-[#7FECE1]"></div>
                        <div
                          className="h-1 bg-[#7FECE1]"
                          style={{ width: "85%" }}
                        ></div>
                      </div>
                      <div
                        className="flex-1 rounded-sm bg-[#CCB4F5]"
                        style={{ maxHeight: "50%" }}
                      ></div>
                    </div>
                  ) : opt.value === "style3" ? (
                    // 样式三：左文右图，文5:图3
                    <div className="flex h-full gap-0.5">
                      <div
                        className="flex flex-col gap-0.5 py-1"
                        style={{ width: "62.5%" }}
                      >
                        <div className="h-1 bg-[#7FECE1]"></div>
                        <div className="h-1 bg-[#7FECE1]"></div>
                        <div className="h-1 bg-[#7FECE1]"></div>
                        <div className="h-1 bg-[#7FECE1]"></div>
                      </div>
                      <div
                        className="flex-1 rounded-sm bg-[#CCB4F5]"
                        style={{ maxHeight: "60%" }}
                      ></div>
                    </div>
                  ) : opt.value === "style4" ? (
                    // 样式四：左文右图，文4.8:图3.2，竖长图
                    <div className="flex h-full gap-0.5">
                      <div
                        className="flex flex-col gap-0.5 py-0.5"
                        style={{ width: "60%" }}
                      >
                        <div className="h-0.5 bg-[#7FECE1]"></div>
                        <div className="h-0.5 bg-[#7FECE1]"></div>
                        <div className="h-0.5 bg-[#7FECE1]"></div>
                        <div className="h-0.5 bg-[#7FECE1]"></div>
                        <div className="h-0.5 bg-[#7FECE1]"></div>
                        <div className="h-0.5 bg-[#7FECE1]"></div>
                        <div className="h-0.5 bg-[#7FECE1]"></div>
                        <div className="h-0.5 bg-[#7FECE1]"></div>
                        <div className="h-0.5 bg-[#7FECE1]"></div>
                        <div className="h-0.5 bg-[#7FECE1]"></div>
                        <div className="h-0.5 bg-[#7FECE1]"></div>
                      </div>
                      <div
                        className="flex-1 rounded-sm bg-[#CCB4F5]"
                        style={{ maxHeight: "100%" }}
                      ></div>
                    </div>
                  ) : opt.value === "style5" ? (
                    // 样式五：上文下图布局预览
                    <div className="flex h-full flex-col gap-0.5 py-1">
                      <div className="flex flex-col gap-0.5">
                        <div className="h-0.5 bg-[#7FECE1]"></div>
                        <div className="h-0.5 bg-[#7FECE1]"></div>
                        <div className="h-0.5 bg-[#7FECE1]"></div>
                        <div className="h-0.5 bg-[#7FECE1]"></div>
                        <div className="h-0.5 bg-[#7FECE1]"></div>
                        <div className="h-0.5 bg-[#7FECE1]"></div>
                      </div>
                      <div className="flex-1 rounded-sm bg-[#CCB4F5]"></div>
                    </div>
                  ) : (
                    // 默认预览图
                    <div className="flex h-full">
                      <div
                        className="border-r border-gray-200 bg-[#7FECE1]"
                        style={{ width: `${opt.textRatio * 100}%` }}
                      ></div>
                      <div className="flex-1 bg-[#CCB4F5]"></div>
                    </div>
                  )}
                </div>

                {/* 样式信息 */}
                <div className="flex-1">
                  <div className="text-sm font-medium text-gray-900">
                    {opt.label}
                  </div>
                  <div className="text-xs text-gray-500">{opt.description}</div>
                  <div className="text-xs text-gray-400">
                    适用字符数: {opt.charRange} • 图片比例: {opt.imageRatio}
                  </div>
                </div>
              </label>
            ))}
          </div>
          <DialogFooter>
            <Button
              onClick={() => {
                setOpen(false);
              }}
            >
              取消
            </Button>

            <Button type="primary" onClick={handleSave}>
              确认
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

const BtnDelete: FC<{ lineId: string }> = ({ lineId }) => {
  const { editDisabled } = useLineSplitEditorContext();
  const { deleteBlock } = useH3PartContext();
  return (
    <Button
      disabled={editDisabled}
      type="text"
      className={cn(
        "rounded-sm border text-xs text-[#F56C6C] hover:bg-[#FFF0F0]"
      )}
      onClick={() => {
        deleteBlock(lineId);
      }}
    >
      删除分栏
    </Button>
  );
};

const BtnRemovePicture: FC<{ lineId: string }> = ({ lineId }) => {
  const { editDisabled } = useLineSplitEditorContext();
  const { delBlockPicture } = useH3PartContext();
  return (
    <Button
      disabled={editDisabled}
      type="text"
      className={cn(
        "rounded-sm border text-xs text-[#F56C6C] hover:bg-[#FFF0F0]"
      )}
      onClick={() => {
        delBlockPicture(lineId);
      }}
    >
      删除图片
    </Button>
  );
};

export const EditH3PartMenu: FC<{ lineId: string; styleType?: string }> = ({
  lineId,
  styleType,
}) => {
  // 只有样式3和样式4使用2行布局
  const isStyle3or4 = styleType === "style3" || styleType === "style4";

  if (isStyle3or4) {
    return (
      <div className="flex w-full flex-col gap-2">
        {/* 第一行：左右各一个按钮 */}
        <div className="flex w-full justify-between">
          <BtnRemovePicture lineId={lineId} />
          <BtnReplaceImage lineId={lineId} />
        </div>
        {/* 第二行：左右各一个按钮 */}
        <div className="flex w-full justify-between">
          <BtnStyle lineId={lineId} />
          <BtnDelete lineId={lineId} />
        </div>
      </div>
    );
  }

  // 样式1、2、5保持原来的1行布局
  return (
    <div className="flex w-full flex-row flex-wrap justify-between gap-y-2">
      <BtnRemovePicture lineId={lineId} />
      <BtnReplaceImage lineId={lineId} />
      <BtnStyle lineId={lineId} />
      <BtnDelete lineId={lineId} />
    </div>
  );
};
