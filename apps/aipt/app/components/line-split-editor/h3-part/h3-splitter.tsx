import { Button } from "@/app/components/common/button";
import { Line } from "@repo/core/types/data/widget-guide";
import { Checkbox } from "@repo/ui/components/checkbox";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  <PERSON><PERSON><PERSON>eader,
  <PERSON>alogTitle,
} from "@repo/ui/components/dialog";
import {
  Tooltip,
  TooltipTrigger,
  TooltipContent,
} from "@repo/ui/components/tooltip";
import { FC, useMemo, useState, useEffect } from "react";
import { useLineSplitEditorContext } from "../line-split-editor-context";
import { useH3PartContext } from "./h3-part-context";

const PureTextLine = ({ line }: { line: Line }) => {
  return <p>{line.content?.map((item) => item.content).join("")}</p>;
};

const CheckboxLine = ({
  index,
  line,
  onChecked,
  checked,
  disabled = false,
}: {
  index: number;
  line: Line;
  onChecked?: (index: number) => void;
  checked: boolean;
  disabled?: boolean;
}) => {
  if (line.tag === "h3") {
    return null;
  }

  if (line.tag === "block") {
    return (
      <div className="">
        {line.content?.map((item, index) => {
          return (
            <CheckboxLine
              disabled={true}
              key={index}
              checked={true}
              index={index}
              line={item as Line}
            />
          );
        })}
      </div>
    );
  }

  return (
    <div
      className="flex cursor-pointer flex-row items-center gap-2 rounded p-2 hover:bg-gray-50"
      onClick={() => {
        onChecked?.(index);
      }}
    >
      <Checkbox checked={checked} disabled={disabled} />
      <PureTextLine line={line} />
    </div>
  );
};

export const H3Splitter: FC = () => {
  const { editDisabled } = useLineSplitEditorContext();
  const { lines, addBlock: save, hasTextLayout } = useH3PartContext();
  const [open, setOpen] = useState(false);

  const [selectedIndexList, setSelectedIndexList] = useState<Set<number>>(
    new Set()
  );

  const [startIndex, setStartIndex] = useState<number | null>(null);
  const [endIndex, setEndIndex] = useState<number | null>(null);

  const selectableLines = useMemo(() => {
    return lines.map((item) => {
      if (item.tag === "h3" || item.tag === "block") {
        // 必须保留null, 用于不改变index的位置
        return null;
      }
      return item;
    });
  }, [lines]);

  // 🎯 计算默认全选的行索引（所有可分栏的行）
  const defaultSelectedIndexes = useMemo(() => {
    const indexes = new Set<number>();
    selectableLines.forEach((line, index) => {
      if (line !== null) {
        indexes.add(index);
      }
    });
    return indexes;
  }, [selectableLines]);

  // 🎯 对话框打开时默认全选，用户可以手动调整选择
  useEffect(() => {
    if (open) {
      setSelectedIndexList(new Set(defaultSelectedIndexes));
      // 重置选择状态，避免范围选择的干扰
      setStartIndex(null);
      setEndIndex(null);
    }
  }, [open, defaultSelectedIndexes]);

  const handleSelect = (index: number) => {
    if (startIndex === null) {
      // 第一次点击，设置起始索引
      setStartIndex(index);
      setEndIndex(null);
      setSelectedIndexList(new Set([index]));
    } else if (endIndex === null) {
      // 第二次点击，设置结束索引并选择范围
      const start = Math.min(startIndex, index);
      const end = Math.max(startIndex, index);
      setEndIndex(index);

      const newSet = new Set<number>();
      for (let i = start; i <= end; i++) {
        if (selectableLines[i] !== null) {
          newSet.add(i);
        }
      }
      setSelectedIndexList(newSet);
      // console.log("设置结束索引:", index, "选择范围:", start, "-", end);
    } else {
      // 重新开始选择
      setStartIndex(index);
      setEndIndex(null);
      setSelectedIndexList(new Set([index]));
      // console.log("重新开始选择，设置起始索引:", index);
    }
  };

  const handleSave = () => {
    // 如果没有选中任何可分栏的行，则不执行分栏操作
    if (selectedIndexList.size === 0) {
      setOpen(false);
      return;
    }
    save(selectedIndexList);
    setOpen(false);
  };

  const isDisabled = editDisabled || hasTextLayout;

  return (
    <>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            disabled={isDisabled}
            className="rounded-sm border text-xs font-bold"
            onClick={() => {
              setOpen(true);
            }}
          >
            分栏插图
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          {editDisabled
            ? "编辑已禁用"
            : hasTextLayout
              ? "已设置文字排版，无法使用分栏插图"
              : "创建分栏插图布局"}
        </TooltipContent>
      </Tooltip>
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent close={false} className="min-w-[600px]">
          <DialogHeader>
            <DialogTitle>创建分栏插图</DialogTitle>
            <DialogDescription>
              请勾选需要分栏的行（点击设置起始位置，再次点击设置结束位置进行范围选择）
            </DialogDescription>
          </DialogHeader>
          <div className="max-h-[400px] w-full overflow-y-auto">
            {lines.map((item, index) => (
              <CheckboxLine
                key={index}
                index={index}
                line={item}
                onChecked={handleSelect}
                checked={selectedIndexList.has(index)}
              />
            ))}
          </div>
          <DialogFooter>
            <Button
              onClick={() => {
                setOpen(false);
              }}
            >
              取消
            </Button>

            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  type="primary"
                  onClick={handleSave}
                  disabled={selectedIndexList.size === 0}
                >
                  确认
                </Button>
              </TooltipTrigger>
              {selectedIndexList.size === 0 && (
                <TooltipContent>当前段落已全部分栏</TooltipContent>
              )}
            </Tooltip>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};
