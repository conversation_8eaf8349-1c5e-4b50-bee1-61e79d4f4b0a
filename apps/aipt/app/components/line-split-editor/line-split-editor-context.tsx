import { useGuideContext } from "@/app/context/guide-context";
import { post } from "@/app/utils/fetcher";
import { useComputed, useSignal } from "@preact-signals/safe-react";
import { toH3Parts } from "@repo/core/guide/utils/h3-parts";
import { GuideWidgetData, Line } from "@repo/core/types/data/widget-guide";
import {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from "react";
import useSWRMutation from "swr/mutation";

interface LineSplitEditorContextType {
  content: Line[];
  contentChanged: boolean;
  updatePart: (partIndex: number, part: Line[]) => void;
  batchUpdateParts: (
    updates: Array<{ partIndex: number; part: Line[] }>
  ) => void;
  parts: Line[][];
  save: () => void;
  isSaving: boolean;
  editDisabled: boolean;
  manualRemoveColumn: boolean;
  setManualRemoveColumn: (val: boolean) => void;
}

const LineSplitEditorContext = createContext<LineSplitEditorContextType>(
  {} as LineSplitEditorContextType
);

const useLineSplitEditorContext = () => useContext(LineSplitEditorContext);

const LineSplitEditorProvider = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const [manualRemoveColumn, setManualRemoveColumn] = useState(false);
  const { trigger: saveTrigger, isMutating: isSaving } = useSWRMutation(
    `/api/v1/guideWidget/save/subColumn`,
    post
  );

  const { guide, refresh, isFinish, isGenerating } = useGuideContext();

  const editDisabled = isFinish || isGenerating;

  const videoData = useMemo(() => {
    if (!guide.videoJson) {
      return {} as GuideWidgetData;
    }
    if (typeof guide.videoJson === "string") {
      return JSON.parse(guide.videoJson) as GuideWidgetData;
    }
    return guide.videoJson || ({} as GuideWidgetData);
  }, [guide.videoJson]);
  const initialContent = useSignal(videoData.content || ([] as Line[]));
  const content = useSignal(initialContent.value);

  useEffect(() => {
    initialContent.value = videoData.content;
    content.value = videoData.content;
  }, [videoData.content]);
  const parts = useMemo(
    () =>
      content.value && content.value.length > 0 ? toH3Parts(content.value) : [],
    [content.value]
  );
  const updatePart = useCallback(
    (partIndex: number, part: Line[]) => {
      // 🔧 修复：创建parts的副本，避免直接修改原数组引用
      // 直接修改 parts[partIndex] 会影响到其他组件的渲染状态
      const newParts = [...parts]; // 创建parts数组的浅拷贝
      newParts[partIndex] = part; // 只修改指定索引的part

      // 基于新的parts数组重新生成完整的content
      // 这样确保只有目标part被修改，其他part保持原有状态
      content.value = newParts.flat();
    },
    [parts, content]
  );

  // 新增：批量更新多个parts，确保同时渲染
  const batchUpdateParts = useCallback(
    (updates: Array<{ partIndex: number; part: Line[] }>) => {
      // 创建parts的副本
      const newParts = [...parts];

      // 批量应用所有更新
      updates.forEach(({ partIndex, part }) => {
        newParts[partIndex] = part;
      });

      // 一次性更新content，只触发一次重新渲染
      content.value = newParts.flat();
    },
    [parts, content]
  );
  const contentChanged = useComputed(() => {
    return content.value !== initialContent.value;
  });

  const save = useCallback(async () => {
    const { guideWidgetSetId, guideWidgetId } = guide;
    if (!guideWidgetSetId || !guideWidgetId) {
      return;
    }
    const { avatar, subtitles, title } = videoData;

    // 组装 imgBlock：遍历所有分栏，把有图片的都带上
    const imgBlock: Array<{ resourceUrl: string; blockIndex: number }> = [];
    parts.forEach((part, partIdx) => {
      part.forEach((line) => {
        if (line.tag === "block" && line.pic && line.pic.url) {
          imgBlock.push({
            resourceUrl: line.pic.url,
            blockIndex: partIdx + 1, // blockIndex 通常从1开始
          });
        }
      });
    });

    const saveData = {
      guideWidgetSetId,
      guideWidgetId,
      videoJson: JSON.stringify({
        avatar,
        subtitles,
        title,
        content: content.value,
      }),
      imgBlock, // 新增字段
    };

    await saveTrigger(saveData);

    refresh?.();

    initialContent.value = content.value;
  }, [guide, videoData, content, saveTrigger, refresh, initialContent, parts]);

  const value = {
    content: content.value,
    contentChanged: contentChanged.value,
    updatePart,
    batchUpdateParts,
    parts,
    save,
    isSaving,
    editDisabled,
    manualRemoveColumn,
    setManualRemoveColumn,
  };
  return (
    <LineSplitEditorContext value={value}>{children}</LineSplitEditorContext>
  );
};

export { LineSplitEditorProvider, useLineSplitEditorContext };
