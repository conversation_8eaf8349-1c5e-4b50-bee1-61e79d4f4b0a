"use client";

import { <PERSON><PERSON> } from "@/app/components/common/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/app/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/app/components/ui/select";
import { get } from "@/app/utils/fetcher";
import { uploadFile } from "@/lib/uploader";
import { Badge } from "@repo/ui/components/badge";
import { Tabs, TabsList, TabsTrigger } from "@repo/ui/components/tabs";
import { FileCode, FileImage, Loader2, Upload } from "lucide-react";
import { useCallback, useEffect, useRef, useState } from "react";

// 辅助函数：根据宽高计算长宽比
const getAspectRatio = (
  width?: number,
  height?: number
): "16:9" | "1:1" | "9:16" => {
  if (!width || !height) return "16:9";
  const ratio = width / height;
  if (ratio > 1.5) return "16:9";
  if (ratio < 0.7) return "9:16";
  return "1:1";
};

// 获取JS/HTML文件的实际渲染尺寸
async function getFileRenderSize(
  file: File,
  fileType: string
): Promise<{ width: number; height: number }> {
  return new Promise((resolve, reject) => {
    if (fileType === "html") {
      // HTML文件：创建iframe渲染并获取尺寸
      const iframe = document.createElement("iframe");
      iframe.style.position = "absolute";
      iframe.style.left = "-9999px";
      iframe.style.top = "-9999px";
      iframe.style.border = "none";
      iframe.style.width = "auto";
      iframe.style.height = "auto";

      iframe.onload = () => {
        try {
          const iframeDoc =
            iframe.contentDocument || iframe.contentWindow?.document;
          if (iframeDoc) {
            const body = iframeDoc.body;
            const html = iframeDoc.documentElement;

            const width = Math.max(
              body.scrollWidth,
              body.offsetWidth,
              html.clientWidth,
              html.scrollWidth,
              html.offsetWidth
            );
            const height = Math.max(
              body.scrollHeight,
              body.offsetHeight,
              html.clientHeight,
              html.scrollHeight,
              html.offsetHeight
            );

            document.body.removeChild(iframe);
            resolve({ width: width || 800, height: height || 450 });
          } else {
            document.body.removeChild(iframe);
            resolve({ width: 800, height: 450 });
          }
        } catch (error) {
          document.body.removeChild(iframe);
          resolve({ width: 800, height: 450 });
        }
      };

      iframe.onerror = () => {
        document.body.removeChild(iframe);
        resolve({ width: 800, height: 450 });
      };

      document.body.appendChild(iframe);

      // 读取文件内容并设置到iframe
      const reader = new FileReader();
      reader.onload = (e) => {
        const content = e.target?.result as string;
        iframe.srcdoc = content;
      };
      reader.readAsText(file);
    } else if (fileType === "js") {
      // JS文件：创建临时容器执行并获取创建的元素尺寸
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const jsContent = e.target?.result as string;

          // 创建一个临时容器
          const container = document.createElement("div");
          container.style.position = "absolute";
          container.style.left = "-9999px";
          container.style.top = "-9999px";
          container.style.visibility = "hidden";
          document.body.appendChild(container);

          // 创建script标签执行JS
          const script = document.createElement("script");
          script.textContent = jsContent;

          // 监听DOM变化，获取新创建的元素
          const observer = new MutationObserver((mutations) => {
            let maxWidth = 0;
            let maxHeight = 0;

            mutations.forEach((mutation) => {
              mutation.addedNodes.forEach((node) => {
                if (node.nodeType === Node.ELEMENT_NODE) {
                  const element = node as HTMLElement;
                  const rect = element.getBoundingClientRect();
                  maxWidth = Math.max(maxWidth, rect.width);
                  maxHeight = Math.max(maxHeight, rect.height);
                }
              });
            });

            observer.disconnect();
            document.body.removeChild(container);

            resolve({
              width: maxWidth || 800,
              height: maxHeight || 450,
            });
          });

          observer.observe(container, { childList: true, subtree: true });
          container.appendChild(script);

          // 超时保护
          setTimeout(() => {
            observer.disconnect();
            if (document.body.contains(container)) {
              document.body.removeChild(container);
            }
            resolve({ width: 800, height: 450 });
          }, 3000);
        } catch (error) {
          console.warn("JS文件尺寸获取失败:", error);
          resolve({ width: 800, height: 450 });
        }
      };
      reader.readAsText(file);
    } else {
      resolve({ width: 800, height: 450 });
    }
  });
}

// API响应类型定义
interface ResourceItem {
  resourceUrl: string;
  proportion?: "16:9" | "1:1" | "9:16";
  resourceOri?: "ai" | "local";
  type?: "active" | "static";
}

interface BlockResourceItem {
  blockIndex: number;
  resourceList: ResourceItem[];
}

interface WidgetResourceResponse {
  total: number;
  resourceList: ResourceItem[];
}

interface SubColumnImageItem {
  resourceUrl: string;
  blockIndex: number;
  styleH3: number;
}

interface GuideWidgetInfoResponse {
  subColumnImages?: SubColumnImageItem[];
}

interface GuideWidgetResourceResponse {
  widgetResource?: WidgetResourceResponse;
  blockResource?: BlockResourceItem[];
}

// 图片数据类型
interface ImageItem {
  id: string;
  url: string;
  aspectRatio: "16:9" | "1:1" | "9:16";
  source: "ai" | "local";
  type: "static" | "interactive";
  fileType: "image" | "js" | "html";
  createdAt: Date;
  width?: number;
  height?: number;
  blockIndex?: number; // H3块索引
  styleH3?: number; // 排版样式1-5
}

interface ImageReplaceModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (selectedImage: ImageItem) => void;
  h3Id: string;
  partId: string;
  currentImageId?: string;
  blockIndex?: number; // H3块索引，从1开始
}

/**
 * 图片替换弹窗组件
 * 功能：
 * 1. 本块图片/本part图片切换
 * 2. 来源筛选：全部/AI生成/本地上传
 * 3. 图片展示：标签信息（长宽比、来源、类型）
 * 4. 本地上传功能
 * 5. 图片选择和确认
 */
export const ImageReplaceModal = ({
  isOpen,
  onClose,
  onConfirm,
  h3Id,
  partId,
  currentImageId,
  blockIndex = 1, // 默认为1，如果没有传递的话
}: ImageReplaceModalProps) => {
  // 文件输入引用
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 状态管理
  const [selectedTab, setSelectedTab] = useState<"block" | "part">("block");
  const [filterSource, setFilterSource] = useState<"all" | "ai" | "local">(
    "all"
  );
  const [selectedImageId, setSelectedImageId] = useState<string>(
    currentImageId || ""
  );
  const [isUploading, setIsUploading] = useState(false);

  // 图片数据
  const [blockImages, setBlockImages] = useState<ImageItem[]>([]);
  const [partImages, setPartImages] = useState<ImageItem[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // 用于自动选中新上传的图片
  const [pendingSelectUrl, setPendingSelectUrl] = useState<string | null>(null);

  // 获取分栏图片数据（从 subColumnImages）
  const fetchSubColumnImages = useCallback(async () => {
    try {
      const result = (await get("/api/v1/guideWidget/info", {
        query: {
          guideWidgetId: partId,
          guideWidgetSetId: h3Id,
        },
      })) as GuideWidgetInfoResponse;

      const subColumnImages = result?.subColumnImages || [];

      // 转换为 ImageItem 格式
      const convertedImages: ImageItem[] = subColumnImages.map(
        (item: any, index: number) => {
          const url = item.resourceUrl;
          const isHtml = url && url.toLowerCase().endsWith(".html");
          const isJs = url && url.toLowerCase().endsWith(".js");
          return {
            id: `subcolumn-${item.blockIndex}-${index}`,
            url: url,
            aspectRatio: "16:9" as const, // 分栏图片通常是16:9
            source: "ai" as const,
            type: "static" as const,
            fileType: (isHtml ? "html" : isJs ? "js" : "image") as
              | "image"
              | "js"
              | "html",
            createdAt: new Date(),
            blockIndex: item.blockIndex,
            styleH3: item.styleH3,
          };
        }
      );

      return convertedImages;
    } catch (error) {
      // 返回空数组，不显示模拟数据
      return [];
    }
  }, [h3Id, partId]);

  // 获取图片数据
  const fetchImages = useCallback(async () => {
    setIsLoading(true);
    try {
      // 1. 获取分栏图片（AI生成的）
      const subColumnImages = await fetchSubColumnImages();

      // 2. 分别获取本块图片和本part图片

      // 只需要调用一次接口，返回的数据包含 widgetResource 和 blockResource
      const resourceResult = (await get("/api/v1/guideWidget/resourceInfo", {
        query: {
          guideWidgetId: partId,
          guideWidgetSetId: h3Id,
        },
      })) as GuideWidgetResourceResponse;

      let blockImages: ImageItem[] = [];

      // 处理本块图片：从 blockResource 中找到当前块的数据
      if (
        resourceResult?.blockResource &&
        Array.isArray(resourceResult.blockResource)
      ) {
        // 找到当前块的数据
        const currentBlock = resourceResult.blockResource.find(
          (block: any) => block.blockIndex === blockIndex
        );

        if (currentBlock?.resourceList) {
          blockImages = currentBlock.resourceList.map(
            (resource: any, index: number) => {
              const url = resource.resourceUrl;
              const isHtml = url.includes(".html");
              const isJs = url.includes(".js");
              const isInteractive = isHtml || isJs;

              return {
                id: `block-${blockIndex}-${index}`,
                url: url,
                aspectRatio: resource.proportion || "16:9",
                source: (resource.resourceOri === "ai" ? "ai" : "local") as
                  | "ai"
                  | "local",
                type: resource.type === "active" ? "interactive" : "static",
                fileType: (isHtml ? "html" : isJs ? "js" : "image") as
                  | "image"
                  | "js"
                  | "html",
                createdAt: new Date(),
                blockIndex: blockIndex,
              };
            }
          );
        }
      }

      // 处理本part图片：从 widgetResource 获取本part所有数据
      let partImages: ImageItem[] = [];
      if (
        resourceResult?.widgetResource?.resourceList &&
        Array.isArray(resourceResult.widgetResource.resourceList)
      ) {
        partImages = resourceResult.widgetResource.resourceList.map(
          (resource: any, index: number) => {
            const url = resource.resourceUrl;
            const isHtml = url.includes(".html");
            const isJs = url.includes(".js");
            const isInteractive = isHtml || isJs;

            return {
              id: `part-${index}`,
              url: url,
              aspectRatio: resource.proportion || "16:9",
              source: (resource.resourceOri === "ai" ? "ai" : "local") as
                | "ai"
                | "local",
              type: resource.type === "active" ? "interactive" : "static",
              fileType: (isHtml ? "html" : isJs ? "js" : "image") as
                | "image"
                | "js"
                | "html",
              createdAt: new Date(),
            };
          }
        );
      }

      // 3. 设置图片数据

      // 本块图片：只包含当前块的分栏图片 + 当前块的本地图片
      const currentBlockSubColumnImages = subColumnImages.filter(
        (image) => image.blockIndex === blockIndex
      );
      const allBlockImages = [...currentBlockSubColumnImages, ...blockImages];
      // 按 URL 去重，避免同一个图片出现多次
      const uniqueBlockImages = allBlockImages.filter((image, index, array) => {
        return array.findIndex((img) => img.url === image.url) === index;
      });
      setBlockImages(uniqueBlockImages);

      // 本part图片：合并 subColumnImages 和 partImages，但要去重
      const allPartImages = [...subColumnImages, ...partImages];

      // 按 URL 去重，避免同一个文件出现多次
      const uniquePartImages = allPartImages.filter((image, index, array) => {
        return array.findIndex((img) => img.url === image.url) === index;
      });

      setPartImages(uniquePartImages);
    } catch (error) {
      console.error("获取图片数据失败:", error);

      // 失败时显示空状态
      setBlockImages([]);
      setPartImages([]);
    } finally {
      setIsLoading(false);
    }
  }, [h3Id, partId, blockIndex, fetchSubColumnImages]);

  // 弹窗打开时获取数据
  useEffect(() => {
    if (isOpen) {
      // 重置选中状态
      setSelectedImageId("");
      setSelectedTab("block");
      setFilterSource("all");
      setPendingSelectUrl(null);

      fetchImages();
    }
  }, [isOpen, fetchImages]);

  // 监听图片数据变化，自动选中新上传的图片
  useEffect(() => {
    if (pendingSelectUrl && (blockImages.length > 0 || partImages.length > 0)) {
      const currentImages = selectedTab === "block" ? blockImages : partImages;
      const uploadedImage = currentImages.find(
        (img) => img.url === pendingSelectUrl
      );

      if (uploadedImage) {
        setSelectedImageId(uploadedImage.id);
        setPendingSelectUrl(null); // 清除待选中状态
      }
    }
  }, [blockImages, partImages, pendingSelectUrl, selectedTab]);

  // 监听图片数据变化，自动选中当前图片
  useEffect(() => {
    if (currentImageId && (blockImages.length > 0 || partImages.length > 0)) {
      // 先在本块图片中查找
      const blockImage = blockImages.find((img) => img.url === currentImageId);
      if (blockImage) {
        setSelectedTab("block");
        setSelectedImageId(blockImage.id);
        return;
      }

      // 如果本块图片中没找到，再在本part图片中查找
      const partImage = partImages.find((img) => img.url === currentImageId);
      if (partImage) {
        setSelectedTab("part");
        setSelectedImageId(partImage.id);
        return;
      }
    }
  }, [blockImages, partImages, currentImageId]);

  // 筛选图片
  const getFilteredImages = useCallback(() => {
    const sourceImages = selectedTab === "block" ? blockImages : partImages;

    if (filterSource === "all") {
      return sourceImages;
    }

    return sourceImages.filter((img) => img.source === filterSource);
  }, [selectedTab, blockImages, partImages, filterSource]);

  // 排序图片（最新生成的置顶）
  const getSortedImages = useCallback(() => {
    const filtered = getFilteredImages();
    return filtered.sort(
      (a, b) => b.createdAt.getTime() - a.createdAt.getTime()
    );
  }, [getFilteredImages]);

  // 获取上传参数的函数
  const getUploadParams = useCallback(
    (file: File) => {
      // 确定文件类型
      let fileType: "image" | "js" | "html" = "image";
      if (file.name.endsWith(".js")) {
        fileType = "js";
      } else if (file.name.endsWith(".html") || file.name.endsWith(".htm")) {
        fileType = "html";
      }

      // 默认尺寸
      let width = 400;
      let height = 225;

      if (fileType === "js" || fileType === "html") {
        width = 800;
        height = 450;
      }

      return {
        guideWidgetBlockIndex: blockIndex,
        width: width.toString(),
        height: height.toString(),
        fileType,
      };
    },
    [blockIndex]
  );

  // 处理文件上传
  const handleFileUpload = useCallback(
    async (url: string, file?: File) => {
      if (!file) return;

      setIsUploading(true);
      try {
        // 确定文件类型
        let fileType: "image" | "js" | "html" = "image";
        if (file.name.endsWith(".js")) {
          fileType = "js";
        } else if (file.name.endsWith(".html") || file.name.endsWith(".htm")) {
          fileType = "html";
        }

        // 默认尺寸
        let width = 400;
        let height = 225;

        if (fileType === "js" || fileType === "html") {
          width = 800;
          height = 450;
        }

        // 调用 uploadFile 上传到 OSS，传递完整参数

        const uploadResult = await uploadFile(file, "1", {
          fileName: file.name,
          guideWidgetSetId: h3Id,
          guideWidgetId: partId,
          guideWidgetBlockIndex: blockIndex,
          width,
          height,
          fileType,
        });

        // 创建新的图片项
        const baseId = `upload-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        const newImage: ImageItem = {
          id: baseId,
          url: uploadResult.url, // 使用 OSS 返回的 URL
          aspectRatio: "16:9",
          source: "local",
          type: "static",
          fileType,
          createdAt: new Date(),
        };

        // 设置待选中的URL，等待图片数据更新后自动选中
        setPendingSelectUrl(uploadResult.url);

        // 上传成功后，延迟一下再获取图片数据，确保后端已处理完成
        setTimeout(async () => {
          await fetchImages();
        }, 1000);
      } catch (error) {
        console.error("文件上传失败:", error);
      } finally {
        setIsUploading(false);
      }
    },
    [selectedTab, h3Id, partId, blockIndex, fetchImages, setPendingSelectUrl]
  );

  // 获取图片标签
  const getImageTags = useCallback((image: ImageItem) => {
    const tags = [
      {
        label: image.aspectRatio,
        color: "bg-blue-100 text-blue-800",
      },
      {
        label: image.source === "ai" ? "AI生成" : "本地上传",
        color:
          image.source === "ai"
            ? "bg-green-100 text-green-800"
            : "bg-orange-100 text-orange-800",
      },
      {
        label: image.type === "static" ? "静态图片" : "互动图片",
        color:
          image.type === "static"
            ? "bg-gray-100 text-gray-800"
            : "bg-purple-100 text-purple-800",
      },
    ];

    // 如果是分栏图片，添加额外标签
    if (image.blockIndex !== undefined) {
      tags.push({
        label: `H3-${image.blockIndex}`,
        color: "bg-indigo-100 text-indigo-800",
      });
    }

    if (image.styleH3 !== undefined) {
      tags.push({
        label: `样式${image.styleH3}`,
        color: "bg-pink-100 text-pink-800",
      });
    }

    return tags;
  }, []);

  const sortedImages = getSortedImages();

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-h-[85vh] max-w-[1200px] sm:max-w-[1200px]">
        <DialogHeader className="pb-4">
          <DialogTitle className="text-lg font-medium">替换图片</DialogTitle>
        </DialogHeader>

        <div className="flex h-[70vh] flex-col">
          {/* Tab切换和筛选控件 */}
          <div className="mb-6 flex items-center justify-between border-b pb-4">
            <Tabs
              value={selectedTab}
              onValueChange={(value) =>
                setSelectedTab(value as "block" | "part")
              }
            >
              <TabsList className="grid w-48 grid-cols-2">
                <TabsTrigger value="block" className="text-sm">
                  本块图片
                </TabsTrigger>
                <TabsTrigger value="part" className="text-sm">
                  本part图片
                </TabsTrigger>
              </TabsList>
            </Tabs>

            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <span className="ml-1 whitespace-nowrap text-sm font-medium text-gray-700">
                  来源：
                </span>
                <Select
                  value={filterSource}
                  onValueChange={(value) => setFilterSource(value as any)}
                >
                  <SelectTrigger className="w-28">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部</SelectItem>
                    <SelectItem value="ai">AI生成</SelectItem>
                    <SelectItem value="local">本地上传</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <input
                ref={fileInputRef}
                type="file"
                accept="image/*,.js,.html,.htm"
                style={{ display: "none" }}
                onChange={(e) => {
                  const file = e.target.files?.[0];
                  if (file) {
                    handleFileUpload("", file);
                  }
                  // 清空 input，允许重复选择同一文件
                  e.target.value = "";
                }}
              />
              <Button
                type="outline"
                size="sm"
                disabled={isUploading}
                onClick={() => fileInputRef.current?.click()}
              >
                {isUploading ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <Upload className="mr-2 h-4 w-4" />
                )}
                本地上传
              </Button>
            </div>
          </div>

          {/* 图片网格 */}
          <div className="flex-1 overflow-y-auto pr-2">
            {isLoading ? (
              <div className="flex h-40 items-center justify-center">
                <Loader2 className="h-6 w-6 animate-spin text-gray-400" />
                <span className="ml-2 text-sm text-gray-600">加载中...</span>
              </div>
            ) : sortedImages.length === 0 ? (
              <div className="flex h-40 items-center justify-center">
                <div className="text-center text-gray-500">
                  <FileImage className="mx-auto mb-2 h-12 w-12 text-gray-300" />
                  <p className="text-sm">暂无图片</p>
                  <p className="text-xs text-gray-400">
                    请上传图片或生成AI图片
                  </p>
                </div>
              </div>
            ) : (
              <div className="grid grid-cols-4 gap-4 pb-4">
                {sortedImages.map((image) => (
                  <div
                    key={image.id}
                    className={`group relative cursor-pointer rounded-lg border-2 p-3 transition-all hover:shadow-md ${
                      selectedImageId === image.id
                        ? "border-blue-500 bg-blue-50 shadow-md"
                        : "border-gray-200 hover:border-gray-300"
                    }`}
                    onClick={() => setSelectedImageId(image.id)}
                  >
                    {/* 选中状态指示器 */}
                    {selectedImageId === image.id && (
                      <div className="absolute -right-1 -top-1 z-10 flex h-6 w-6 items-center justify-center rounded-full bg-blue-500 text-white">
                        <svg
                          className="h-3 w-3"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            fillRule="evenodd"
                            d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                            clipRule="evenodd"
                          />
                        </svg>
                      </div>
                    )}

                    {/* 图片预览 */}
                    <div className="mb-3 flex aspect-video items-center justify-center overflow-hidden rounded-md border border-gray-200 bg-gray-50">
                      {image.fileType === "image" ? (
                        <img
                          src={image.url}
                          alt=""
                          className="h-full w-full object-cover"
                          loading="lazy"
                        />
                      ) : image.fileType === "html" ? (
                        <div className="relative h-full w-full overflow-hidden bg-white">
                          <iframe
                            src={image.url}
                            className="absolute inset-0 border-0 bg-white"
                            title="HTML预览"
                            sandbox="allow-scripts allow-same-origin"
                            style={{
                              width: "1000%",
                              height: "1000%",
                              transform: "scale(0.1)",
                              transformOrigin: "0 0",
                              pointerEvents: "none",
                            }}
                          />
                        </div>
                      ) : image.fileType === "js" ? (
                        <div className="relative h-full w-full overflow-hidden bg-white">
                          <iframe
                            srcDoc={`<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <style>
    body {
      margin: 0;
      padding: 20px;
      font-family: Arial, sans-serif;
      background: #f5f5f5;
    }
    .loading {
      text-align: center;
      color: #666;
      padding: 40px;
    }
  </style>
</head>
<body>
  <div class="loading">正在加载JS组件...</div>
  <script src="${image.url}"></script>
  <script>
    // 监听自定义元素注册
    const originalDefine = customElements.define;
    let registeredElements = [];

    customElements.define = function(name, constructor, options) {
      registeredElements.push(name);
      return originalDefine.call(this, name, constructor, options);
    };

    // 多次尝试检测和创建元素
    function tryCreateElement() {
      const loadingDiv = document.querySelector('.loading');

      // 首先尝试新注册的元素
      if (registeredElements.length > 0) {
        const elementName = registeredElements[registeredElements.length - 1];
        const element = document.createElement(elementName);
        document.body.appendChild(element);
        if (loadingDiv) loadingDiv.remove();
        return true;
      }

      // 然后尝试预设名称和常见模式
      const possibleNames = [
        'web-component-class4-subject1',
        'test-interactive',
        'interactive-component',
        'custom-element'
      ];

      for (const name of possibleNames) {
        if (customElements.get(name)) {
          const element = document.createElement(name);
          document.body.appendChild(element);
          if (loadingDiv) loadingDiv.remove();
          return true;
        }
      }

      return false;
    }

    // 多次尝试，因为JS加载可能需要时间
    let attempts = 0;
    const maxAttempts = 10;

    function attemptCreation() {
      attempts++;

      if (tryCreateElement()) {
        return;
      }

      if (attempts < maxAttempts) {
        setTimeout(attemptCreation, 200);
      } else {
        const loadingDiv = document.querySelector('.loading');
        if (loadingDiv) {
          loadingDiv.innerHTML = 'JS文件已加载，但未找到自定义元素。请确保JS文件中调用了 customElements.define()。<br>已注册的元素: ' + registeredElements.join(', ');
        }
      }
    }

    // 开始尝试创建元素
    setTimeout(attemptCreation, 100);
  </script>
</body>
</html>`}
                            className="absolute inset-0 border-0 bg-white"
                            title="JS预览"
                            sandbox="allow-scripts allow-same-origin"
                            style={{
                              width: "1000%",
                              height: "1000%",
                              transform: "scale(0.1)",
                              transformOrigin: "0 0",
                              pointerEvents: "none",
                            }}
                          />
                        </div>
                      ) : (
                        <div className="flex flex-col items-center text-gray-400">
                          <FileCode className="mb-1 h-8 w-8" />
                          <span className="text-xs font-medium">未知文件</span>
                        </div>
                      )}
                    </div>

                    {/* 标签 */}
                    <div className="flex flex-wrap gap-1">
                      {getImageTags(image).map((tag, index) => (
                        <Badge
                          key={index}
                          variant="secondary"
                          className={`text-xs ${tag.color}`}
                        >
                          {tag.label}
                        </Badge>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* 底部按钮 */}
          <div className="mt-6 flex items-center justify-between border-t pt-4">
            <div className="text-sm text-gray-500">
              {sortedImages.length > 0 && (
                <span>共 {sortedImages.length} 张图片</span>
              )}
            </div>
            <div className="flex gap-3">
              <Button type="outline" onClick={onClose}>
                取消
              </Button>
              <Button
                type="primary"
                onClick={() => {
                  if (selectedImageId) {
                    // 找到选中的图片数据
                    const selectedImage = sortedImages.find(
                      (img) => img.id === selectedImageId
                    );
                    if (selectedImage) {
                      onConfirm(selectedImage);
                      onClose();
                    }
                  }
                }}
                disabled={!selectedImageId}
              >
                确认替换
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
