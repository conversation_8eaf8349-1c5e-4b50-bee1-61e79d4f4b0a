"use client";
import { MathJaxConfig } from "@repo/core/components/math-jax-config";
import { FC } from "react";
import { H3Part } from "./h3-part/h3-part";
import { useLineSplitEditorContext } from "./line-split-editor-context";

const GuideH3List: FC = () => {
  const { parts } = useLineSplitEditorContext();

  return (
    <MathJaxConfig>
      <div className="flex flex-col gap-4">
        {parts.map((part, index) => (
          <H3Part key={index} data={part} index={index} />
        ))}
      </div>
    </MathJaxConfig>
  );
};

export { GuideH3List };
