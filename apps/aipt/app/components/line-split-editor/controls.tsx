"use client";
import { But<PERSON> } from "@/app/components/common/button";
import { cn } from "@repo/ui/lib/utils";
import { CloudUpload } from "lucide-react";
import { useLineSplitEditorContext } from "./line-split-editor-context";

export const BtnSave = () => {
  const { contentChanged, save, isSaving, editDisabled } =
    useLineSplitEditorContext();
  return (
    <Button
      disabled={!contentChanged || editDisabled}
      type="primary"
      loading={isSaving}
      className={cn(
        "rounded-sm border bg-slate-100 text-xs font-bold text-slate-400 hover:bg-slate-50",
        contentChanged && "bg-red-600 text-white hover:bg-red-700"
      )}
      onClick={save}
      icon={<CloudUpload className="size-4" />}
    >
      提交
    </Button>
  );
};
