"use client";

import { cn } from "@repo/ui/lib/utils";
import { FC } from "react";

import { useMdEditorContext } from "./md-editor-context";
import { useGuideContext } from "@/app/context/guide-context";
import { EmptyTip } from "../panels/empty-tip";
import { Preview, type PreviewOptions } from "./md-preview";

const Editor: FC<React.ComponentProps<"textarea">> = ({
  className,
  ...props
}) => {
  const { isFinish } = useGuideContext();
  const { content } = useMdEditorContext();
  return (
    <textarea
      disabled={isFinish}
      className={cn(
        "rounded-sm px-4 py-2 outline-1 outline-zinc-300",
        isFinish && "text-muted-foreground",
        className
      )}
      {...props}
      value={content.value}
      onChange={(e) => (content.value = e.target.value)}
    />
  );
};

interface MdEditorProps extends React.ComponentProps<"div"> {
  options?: PreviewOptions;
}

export const MdEditor: FC<MdEditorProps> = ({ className, options }) => {
  const { isInit } = useGuideContext();
  const { serverType, content } = useMdEditorContext();

  if (isInit && serverType == "boardscript" && content.value == "") {
    return <EmptyTip texture="需基于逐字稿生成板书" />;
  }
  return (
    <div className={cn("min-h-50 flex flex-row gap-4 leading-8", className)}>
      <Preview className={cn(serverType == "boardscript" && "w-1/2")} options={options} />
      {serverType == "boardscript" && <Editor className="w-1/2" />}
    </div>
  );
};
