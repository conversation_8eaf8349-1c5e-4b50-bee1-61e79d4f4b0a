"use client";

import { But<PERSON> } from "@/app/components/common/button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/app/components/common/popover";
import { toast } from "@/app/components/common/toast";
import { post } from "@/app/utils/fetcher";
import DownImg from "@/public/down.svg";
import IcFinished from "@/public/ic-finished.svg";
import IcLoading from "@/public/ic-loading.svg";
import TrashImg from "@/public/trash.svg";
import UpImg from "@/public/up.svg";
import { GuideStatus } from "@/types/base";
import { RawGuide } from "@/types/guide-widget";
import { cn } from "@repo/ui/lib/utils";
import { usePathname } from "next/navigation";
import { FC, useState } from "react";
import useSWRMutation from "swr/mutation";

interface GuideSetProps {
  list: RawGuide[];
  mode?: string;
  onUpdate?: (newList: RawGuide[]) => void;
  guideWidgetSetId: number;
  guideWidgetId: string;
  setGuideWidgetId: (id: string) => void;
  guideWidgetIndex?: number;
  setGuideWidgetIndex?: (index: number) => void;
  refresh: () => void;
}

export const GuideSet: FC<GuideSetProps> = ({
  guideWidgetSetId,
  list,
  mode,
  onUpdate,
  guideWidgetId,
  setGuideWidgetId,
  setGuideWidgetIndex,
  refresh,
}) => {
  const [open, setOpen] = useState<boolean>(false);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [editingTitle, setEditingTitle] = useState<string>("");
  const [hoveredId, setHoveredId] = useState<string | null>(null);
  const pathname = usePathname();
  // 圈画工具页面part列表不显示状态
  const isGuideDraw = pathname === "/guide-draw/editor";

  const { trigger: updateTitle } = useSWRMutation(
    "/api/v1/guideWidget/update/name",
    post
  );

  const { trigger: deleteGuide } = useSWRMutation(
    "/api/v1/guideWidget/delete",
    post
  );

  const { trigger: createGuide } = useSWRMutation(
    "/api/v1/guideWidget/create",
    post
  );

  const { trigger: updateOrder } = useSWRMutation(
    "/api/v1/guideWidgetSet/update/order",
    post
  );

  if (!list || list.length === 0) {
    return null;
  }

  const handleEdit = (id: string, title: string) => {
    setEditingId(id);
    setEditingTitle(title);
  };

  const handleCancel = () => {
    setEditingId(null);
    setEditingTitle("");
  };

  const handleSave = async (id: string) => {
    if (!onUpdate) return;

    const saveData = {
      guideWidgetId: id,
      guideWidgetSetId,
      guideWidgetName: editingTitle,
    };
    // console.log(saveData);
    try {
      await updateTitle(saveData);

      const newList = list.map((item) =>
        item.guideWidgetId === id ? { ...item, title: editingTitle } : item
      );

      onUpdate(newList);
      setEditingId(null);
      setEditingTitle("");
      toast.success("标题更新成功");
      refresh();
    } catch (error: unknown) {
      toast.error(error instanceof Error ? error.message : "标题更新失败");
    }
  };

  const handleDelete = async (index: number) => {
    if (!onUpdate) return;

    try {
      const targetItem = list[index];
      await deleteGuide({
        guideWidgetId: targetItem?.guideWidgetId,
        guideWidgetSetId,
      });

      const newList = [...list];
      newList.splice(index, 1);
      onUpdate(newList);

      // 如果删除的是当前选中的项，则选择第一项
      if (targetItem?.guideWidgetId === guideWidgetId && newList.length > 0) {
        setGuideWidgetId(newList[0]?.guideWidgetId || "");
      }

      toast.success("删除成功");
      refresh();
    } catch (error: unknown) {
      toast.error(error instanceof Error ? error.message : "删除失败");
    }
  };

  const handleInsert = async (index: number, position: "top" | "bottom") => {
    if (!onUpdate) return;

    try {
      const newItem: RawGuide = {
        guideWidgetId: Date.now().toString(),
        guideWidgetName: "未命名",
        guideWidgetStatus: GuideStatus.Progress,
      };

      // 如果在上边插入，beforeGuideWidgetId位上一个 item 的 id，否则则是本身 id
      let beforeGuideWidgetIndex;
      if (position === "top") {
        beforeGuideWidgetIndex = index === 0 ? -1 : index - 1;
      } else if (position === "bottom") {
        beforeGuideWidgetIndex = index === list.length ? index - 1 : index;
      }
      const beforeGuideWidget = list[beforeGuideWidgetIndex as number];

      const saveData = {
        guideWidgetSetId: guideWidgetSetId,
        beforeGuideWidgetId: beforeGuideWidget?.guideWidgetId || 0,
      };

      await createGuide(saveData);

      const newList = [...list];
      newList.splice(index, 0, newItem);
      onUpdate(newList);
      toast.success("创建成功");
      refresh();
    } catch (error: unknown) {
      toast.error(error instanceof Error ? error.message : "创建失败");
    }
  };

  const handleMove = async (fromIndex: number, direction: "up" | "down") => {
    if (
      (direction === "up" && fromIndex === 0) ||
      (direction === "down" && fromIndex === list.length - 1) ||
      !onUpdate
    ) {
      return;
    }

    try {
      const newList = [...list];
      const toIndex = direction === "up" ? fromIndex - 1 : fromIndex + 1;
      const [movedItem] = newList.splice(fromIndex, 1);
      newList.splice(toIndex, 0, movedItem as RawGuide);

      // 调用更新顺序接口
      await updateOrder({
        guideWidgetSetId,
        orderList: newList.map((it) => it.guideWidgetId),
      });

      onUpdate(newList);
      toast.success("移动成功");
      refresh();
    } catch (error: unknown) {
      toast.error(error instanceof Error ? error.message : "移动失败");
    }
  };

  const renderInsertButton = (index: number, position: "top" | "bottom") => (
    <Button
      type="default"
      size="sm"
      className={cn(
        "h-7 w-full items-center justify-center rounded-xl border border-dashed border-[#4F46E5] text-[#4F46E5] hover:bg-[#4F46E5] hover:text-white",
        position === "top" ? "mb-[7px]" : "mt-[7px]"
      )}
      onClick={() => handleInsert(index, position)}
    >
      插入新段落
    </Button>
  );

  const renderActionButtons = (index: number) => (
    <div className="flex items-center gap-1">
      <Button
        type="text"
        size="sm"
        onClick={(e) => {
          e.stopPropagation();
          handleMove(index, "up");
        }}
        disabled={index === 0}
        className="bg-transparent text-[#4F46E5]"
        icon={<UpImg width={10} height={10} />}
      >
        上移
      </Button>
      <Button
        type="text"
        size="sm"
        onClick={(e) => {
          e.stopPropagation();
          handleMove(index, "down");
        }}
        disabled={index === list.length - 1}
        className="bg-transparent text-[#4F46E5]"
        icon={<DownImg width={10} height={10} />}
      >
        下移
      </Button>

      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            type="text"
            className="bg-transparent text-[#4F46E5]"
            size="sm"
            icon={<TrashImg width={14} height={14} alt="删除" />}
          >
            删除
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-40">
          <div>确认删除？</div>
          <div className="mt-2 flex gap-2">
            <Button
              type="primary"
              size="sm"
              className="h-6 rounded-[6px] px-3.5 text-[9px] font-normal"
              onClick={(e) => {
                e.stopPropagation();
                handleDelete(index);
              }}
            >
              确认
            </Button>
            <Button
              size="sm"
              className="h-6 rounded-[6px] px-3.5 text-[9px] font-normal"
              onClick={(e) => {
                e.stopPropagation();
                setOpen(false);
              }}
            >
              取消
            </Button>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );

  /**
   * 状态对应的一些信息
   */
  const statusDicHandler = (status: string) => {
    if (status === GuideStatus.Init) {
      return {
        label: "未开始",
        styleClass: "bg-[#F3F4F6] text-[#4B5563]",
        icon: null,
      };
    } else if (status === GuideStatus.Progress) {
      return {
        label: "待处理",
        styleClass: "bg-[#F3F4F6] text-[#4B5563]",
        icon: null,
      };
    } else if (status === GuideStatus.Loading) {
      return {
        label: "进行中",
        styleClass: "bg-[#F3F4F6] text-[#4B5563]",
        icon: <IcLoading />,
        iconClass: "animate-spin",
      };
    } else if (status === GuideStatus.Finish) {
      return {
        label: "已完成",
        styleClass: "bg-[#DCFCE7] text-[#16A34A]",
        icon: <IcFinished />,
      };
    } else {
      throw Error("稿件未知状态，未处理！");
    }
  };

  return (
    <ol className="flex flex-col items-center justify-start gap-3 p-2">
      {list.length === 0 && renderInsertButton(0, "top")}
      {list.map((item, index) => {
        const statusDic = statusDicHandler(item.guideWidgetStatus);
        return (
          <li
            key={item.guideWidgetId}
            className={cn("relative w-[200px]")}
            onMouseEnter={() => setHoveredId(item.guideWidgetId)}
            onMouseLeave={() => setHoveredId(null)}
            onClick={() => {
              setGuideWidgetId(item.guideWidgetId);
              setGuideWidgetIndex?.(index);
            }}
          >
            {editingId === item.guideWidgetId &&
              renderInsertButton(index, "top")}

            <div
              className={cn(
                "border-1 relative flex min-h-[130px] w-full flex-col rounded-2xl bg-white transition-colors",
                Number(guideWidgetId) === Number(item.guideWidgetId)
                  ? "border-1 border-[transparent] bg-[rgba(79,70,229,0.13)]"
                  : "border-1 hover:border-1 hover:border-[transparent] hover:bg-[rgba(59,130,246,0.2)]"
              )}
            >
              <div className="flex flex-col p-3">
                <div className="mb-3 flex flex-col">
                  <div className="flex items-center justify-between text-base font-medium text-[#4B5563]">
                    <div className="flex items-center gap-1">
                      <span className="text-[#2563EB]">Part</span>
                      <span className="text-[#3B82F6]">{index + 1}</span>
                      {/* {statusDic.icon && (
                        <Image
                          src={statusDic.icon}
                          alt="箭头"
                          className={cn("ml-2 h-3 w-3", statusDic.iconClass)}
                        />
                      )} */}
                      {statusDic.icon && !isGuideDraw && (
                        <span
                          className={cn("ml-2 h-3 w-3", statusDic.iconClass)}
                        >
                          {statusDic.icon}
                        </span>
                      )}
                    </div>
                    {!isGuideDraw && (
                      <div className="">
                        <span
                          className={cn(
                            "rounded-full px-2 py-1 text-xs",
                            statusDic.styleClass
                          )}
                        >
                          {statusDic.label}
                        </span>
                      </div>
                    )}
                  </div>
                  {editingId === item.guideWidgetId &&
                    renderActionButtons(index)}
                  {editingId === item.guideWidgetId ? (
                    <input
                      type="text"
                      value={editingTitle}
                      onChange={(e) => {
                        e.stopPropagation();
                        setEditingTitle(e.target.value);
                      }}
                      className="mt-2 rounded-xl border border-[#4F46E5] px-2 py-1 text-xs text-[#4F46E5] focus:border-blue-500 focus:outline-none"
                    />
                  ) : (
                    <div className="mt-2 text-base text-[#4B5563]">
                      {item.guideWidgetName}
                    </div>
                  )}
                </div>

                {mode !== "bugfix" && (
                  <div className="mt-auto flex justify-end gap-1">
                    {editingId === item.guideWidgetId ? (
                      <>
                        <Button
                          type="default"
                          size="sm"
                          className="h-6 border-[#4F46E5] bg-transparent px-2 text-[9px] font-normal text-[#4F46E5]"
                          onClick={handleCancel}
                        >
                          取消
                        </Button>
                        <Button
                          type="primary"
                          size="sm"
                          className="h-6 rounded-[6px] px-3.5 text-[9px] font-normal"
                          onClick={() => handleSave(item.guideWidgetId)}
                        >
                          保存
                        </Button>
                      </>
                    ) : (
                      hoveredId === item.guideWidgetId && (
                        <Button
                          type="primary"
                          size="sm"
                          className="bg-[#3B82F6] text-white"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleEdit(
                              item.guideWidgetId,
                              item.guideWidgetName
                            );
                          }}
                        >
                          编辑模式
                        </Button>
                      )
                    )}
                  </div>
                )}
              </div>
            </div>

            {editingId === item.guideWidgetId &&
              renderInsertButton(index, "bottom")}
          </li>
        );
      })}
    </ol>
  );
};
