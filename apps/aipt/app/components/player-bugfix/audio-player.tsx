import { useReadingEditorContext } from "@/app/components/reading-editor-bugfix/reading-editor-context";
import { useSignal, useSignalEffect } from "@preact-signals/safe-react";
import { useRef } from "react";
import { PlayerControls } from "./player-controls";

export const AudioPlayer = () => {
  const { content, playingAudioRef, playbackRate } = useReadingEditorContext();
  const { audioUrl } = content;

  const audioRef = useRef<HTMLAudioElement>(null);

  const isPlaying = useSignal(false);

  useSignalEffect(() => {
    if (playingAudioRef.value !== audioRef.current && isPlaying.value) {
      audioRef.current?.pause();
      isPlaying.value = false;
    }
  });

  return (
    <div className="flex w-full flex-col items-center gap-2">
      <audio src={audioUrl} hidden ref={audioRef} />
      <PlayerControls
        ref={audioRef}
        onPlay={() => {
          playingAudioRef.value = audioRef.current;
          isPlaying.value = true;
        }}
        onPause={() => {
          playingAudioRef.value = null;
          isPlaying.value = false;
        }}
        defaultPlaybackRate={playbackRate.value}
        onPlaybackRateChange={(value) => {
          playbackRate.value = value;
        }}
      />
    </div>
  );
};
