import { useEffect, useRef, useState } from 'react';

// 水印组件的props类型，text为水印文本
interface WatermarkProps {
  text: string;
}

// Watermark 组件：用于在页面上渲染背景水印
export const Watermark = ({ text }: WatermarkProps) => {
  // canvasRef用于操作隐藏canvas，bgImage为生成的base64背景图片
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [bgImage, setBgImage] = useState<string>("");

  useEffect(() => {
    // 获取canvas实例
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // 设置画布大小（决定水印密度）
    canvas.width = 300;
    canvas.height = 200;

    // 清空画布，准备绘制水印
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    ctx.save();
    // 设置水印字体和颜色（透明度0.05更浅）
    ctx.font = '16px Arial';
    ctx.fillStyle = 'rgba(0, 0, 0, 0.05)';
    // 平移到画布中心，旋转一定角度
    ctx.translate(150, 100); // 画布中心
    ctx.rotate(-Math.PI / 6); // 旋转30度
    // 绘制水印文本
    ctx.fillText(text, -60, 0);
    ctx.restore();

    // 生成base64图片，设置为背景
    setBgImage(canvas.toDataURL());
  }, [text]);

  return (
    // pointer-events-none 保证水印不影响交互，z-50 保证在最上层
    <div
      className="pointer-events-none fixed inset-0 z-50"
      style={{
        backgroundImage: bgImage ? `url(${bgImage})` : undefined,
        backgroundRepeat: 'repeat', // 平铺
        backgroundSize: '300px 200px', // 与canvas一致
      }}
    >
      {/* 隐藏canvas，仅用于生成图片 */}
      <canvas ref={canvasRef} className="hidden" />
    </div>
  );
}; 