"use client";

import { cn } from "@repo/ui/lib/utils";
import { FC, useEffect } from "react";
import {
  useMdEditorContext,
  MdEditorContextType,
} from "@/app/components/md-editor/md-editor-context";
import { useGuideContext } from "@/app/context/guide-context";
import { EmptyTip } from "../panels/empty-tip";
import { Preview, type PreviewOptions } from "./md-preview";
import { useSignalEffect } from "@preact-signals/safe-react";

const Editor: FC<React.ComponentProps<"textarea">> = ({
  className,
  ...props
}) => {
  const { isFinish } = useGuideContext();
  const { content } = useMdEditorContext();
  return (
    <textarea
      disabled={isFinish}
      className={cn(
        "rounded-sm px-4 py-2 outline-1 outline-zinc-300",
        isFinish && "text-muted-foreground",
        className
      )}
      {...props}
      value={content.value}
      onChange={(e) => (content.value = e.target.value)}
    />
  );
};

export interface MdEditorProps extends React.ComponentProps<"div"> {
  options?: PreviewOptions;
  setMdContext?: (mdContext: MdEditorContextType) => void;
  showPreview?: boolean;
  onMdChanged?: (content: string) => void;
}

export const MdEditor: FC<MdEditorProps> = ({
  className,
  options,
  setMdContext,
  onMdChanged,
  showPreview = true,
}) => {
  const { isInit } = useGuideContext();
  const mdMdContext = useMdEditorContext();
  const { serverType, content } = mdMdContext;

  useEffect(() => {
    if (setMdContext) {
      setMdContext(mdMdContext);
    }
  }, []);

  useSignalEffect(() => {
    const currentContent = content.value;
    onMdChanged?.(currentContent);
  });

  if (isInit && serverType == "boardscript" && content.value == "") {
    return <EmptyTip texture="需基于逐字稿生成板书" />;
  }
  return (
    <div className={cn("min-h-50 flex flex-row gap-4 leading-8", className)}>
      {showPreview && <Preview className="flex-1" options={options} />}
      <Editor className="flex-1" />
    </div>
  );
};
