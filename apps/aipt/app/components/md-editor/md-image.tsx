import { useSignal } from "@preact-signals/safe-react";
import { useEffect, useRef } from "react";

interface MdImageProps extends React.ComponentProps<"img"> {
  dataSrc: string;
}

export const MdImage = ({ dataSrc, children, ...props }: MdImageProps) => {
  const ref = useRef<HTMLImageElement>(null);
  const isError = useSignal(false);

  useEffect(() => {
    if (!ref.current) return;

    const img = ref.current;

    img.onerror = () => {
      isError.value = true;
    };
  }, [dataSrc, ref, isError]);

  if (isError.value) {
    return (
      <span className="rounded-xs flex min-h-20 items-center justify-center bg-red-50 text-sm text-red-500 outline-dashed outline-1 outline-red-300">
        图片错误: {children}
      </span>
    );
  }

  if (dataSrc) {
    return (
      // eslint-disable-next-line @next/next/no-img-element
      <img ref={ref} src={dataSrc} {...props} data-alt={children?.toString()} />
    );
  }

  return (
    <span className="rounded-xs bg-primary/10 text-primary outline-primary flex min-h-20 items-center justify-center text-sm outline-dashed outline-1">
      替换图片: {children}
    </span>
  );
};
