"use client";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/app/components/common/alert-dialog";
import { Button } from "@/app/components/common/button";
import { useMdEditorContext } from "@/app/components/md-editor/md-editor-context";
import { useSignal } from "@preact-signals/safe-react";
import { toast } from "@repo/ui/components/toast";
import { cn } from "@repo/ui/lib/utils";
import { Check, CloudUpload, Copy, RotateCcw, Upload } from "lucide-react";
import { FC } from "react";
import { Uploader } from "../uploader/uploader";

export const BtnReset: FC = () => {
  const { reset, contentChanged } = useMdEditorContext();
  const showAlert = useSignal(false);
  const handleConfirm = () => {
    showAlert.value = false;
    reset();
  };
  return (
    <>
      <Button
        disabled={!contentChanged.value}
        onClick={() => (showAlert.value = true)}
        type="default"
        className="rounded-sm text-xs font-normal"
        icon={<RotateCcw strokeWidth={1.5} className="size-4" />}
      >
        重置
      </Button>
      <AlertDialog
        open={showAlert.value}
        onOpenChange={() => (showAlert.value = false)}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>请注意</AlertDialogTitle>
            <AlertDialogDescription>
              <span className="text-zinc-600">
                确认后，将重置内容为 最近一次提交前。请谨慎操作
              </span>
            </AlertDialogDescription>
            <AlertDialogFooter>
              <AlertDialogCancel>取消</AlertDialogCancel>
              <AlertDialogAction asChild>
                <Button
                  type="primary"
                  onClick={handleConfirm}
                  className="text-sm font-normal"
                >
                  确认
                </Button>
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogHeader>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};
//TODO: 朗读稿分段：同算法规则
export const BtnSave = () => {
  const { contentChanged, save, isSaving } = useMdEditorContext();
  const disabled = !contentChanged.value;
  return (
    <Button
      disabled={disabled}
      type="primary"
      loading={isSaving}
      className={cn(
        "rounded-sm border bg-slate-100 text-xs font-bold text-slate-400 hover:bg-slate-50",
        !disabled && "bg-red-600 text-white hover:bg-red-700"
      )}
      onClick={save}
      icon={<CloudUpload className="size-4" />}
    >
      提交
    </Button>
  );
};

export const BtnCopy = () => {
  const { content } = useMdEditorContext();
  const copied = useSignal(false);

  const handleCopy = () => {
    navigator.clipboard.writeText(content.value);
    copied.value = true;
    setTimeout(() => {
      copied.value = false;
    }, 1000);
  };

  const icon = copied.value ? (
    <Check strokeWidth={1.5} className="size-4 text-green-600" />
  ) : (
    <Copy strokeWidth={1.5} className="size-4" />
  );

  return (
    <Button
      type="default"
      className="rounded-sm text-xs font-normal"
      onClick={handleCopy}
      icon={icon}
    >
      复制
    </Button>
  );
};

export const BtnUpload = () => {
  const handleUpload = () => {
    toast.success("已上传完成，可复制链接到需插入的位置");
  };
  return (
    <Uploader accept="image/*" onUpload={handleUpload}>
      <Button type="default" className="rounded-sm text-xs font-normal">
        <Upload className="mr-1 size-4" />
        上传
      </Button>
    </Uploader>
  );
};
