import { ReadingEditorProvider } from "@/app/components/reading-editor-bugfix/reading-editor-context";
import { ReadingEditor } from "@/app/components/reading-editor-bugfix/reading-editor";
// import { BtnSave } from "@/app/components/reading-editor-bugfix/controls";
import { useGuideContext } from "@/app/context/guide-context";
import { GuideStatus, GuideTaskType } from "@/types/base";
import { useMemo } from "@preact-signals/safe-react/react";


import { Panel } from "./panel";

export const ReadingScriptPanelBugfix = () => {
  const { guide } = useGuideContext();
  const { flowRunType, guideWidgetStatus } = guide;
  const isLoading = useMemo(() => {
    return (
      guideWidgetStatus === GuideStatus.Loading &&
      flowRunType === GuideTaskType.GenerateReadingAndWhiteboard
    );
  }, [flowRunType, guideWidgetStatus]);
  return (
    <ReadingEditorProvider key={`${guide.guideWidgetId}-${guide.videoGenTime}`}>
      <Panel title="2.朗读稿上传" controls={<>{/* <BtnSave /> */}</>} loading={isLoading}>
        <ReadingEditor />
      </Panel>
    </ReadingEditorProvider>
  );
};
