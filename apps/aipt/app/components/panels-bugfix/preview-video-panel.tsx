import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/app/components/common/alert-dialog";
import { But<PERSON> } from "@/app/components/common/button";
import { useGuideContext } from "@/app/context/guide-context";
import { post } from "@/app/utils/fetcher";
import { GuideStatus, GuideTaskType } from "@/types/base";
import { useSignal } from "@preact-signals/safe-react";
import { CircleAlert, Lock, SquarePen, Unlock, X } from "lucide-react";
import { useCallback, useMemo } from "react";
import useSWRMutation from "swr/mutation";
import { GuidePlayer } from "../player/guide-player";
import { Panel } from "./panel";

const BtnCancel = () => {
  const { guide, refresh, refreshSet } = useGuideContext();
  const { trigger, isMutating } = useSWRMutation(
    "/api/v1/guideWidget/cancel/generate",
    post
  );

  const showAlert = useSignal(false);

  const target = useMemo(() => {
    switch (guide.flowRunType) {
      case GuideTaskType.GenerateReadingAndWhiteboard:
        return "朗读稿和板书";
      case GuideTaskType.GenerateReading:
        return "朗读稿";
      case GuideTaskType.GenerateGuide:
        return "视频";
      case GuideTaskType.GenerateColumnImages:
        return "分栏图片";
      default:
        return "";
    }
  }, [guide.flowRunType]);

  const handleCancel = useCallback(async () => {
    const { guideWidgetId, guideWidgetSetId, taskId } = guide;
    await trigger({ guideWidgetId, guideWidgetSetId, taskId });
    refresh?.();
    refreshSet?.();
  }, [guide, refresh, trigger, refreshSet]);

  const handleConfirm = useCallback(async () => {
    showAlert.value = true;
  }, [showAlert]);

  return (
    <>
      <Button
        type="outline"
        loading={isMutating}
        onClick={handleConfirm}
        className="rounded-sm text-xs"
        icon={<X className="size-4" />}
      >
        取消生成{target}
      </Button>
      <AlertDialog
        open={showAlert.value}
        onOpenChange={() => (showAlert.value = false)}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>正在生成{target}</AlertDialogTitle>
            <AlertDialogDescription>
              <span className="text-zinc-600">
                确定取消生成吗？内容将回退到之前版本。
              </span>
            </AlertDialogDescription>
            <AlertDialogFooter>
              <AlertDialogCancel>取消</AlertDialogCancel>
              <AlertDialogAction asChild>
                <Button
                  type="primary"
                  onClick={handleCancel}
                  className="text-sm font-normal"
                >
                  确定
                </Button>
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogHeader>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};

const BtnUnlock = () => {
  const { guide, refresh, refreshSet } = useGuideContext();
  const { trigger, isMutating } = useSWRMutation(
    "/api/v1/guideWidget/reset",
    post
  );

  const handleUnlock = useCallback(async () => {
    const { guideWidgetId, guideWidgetSetId } = guide;
    await trigger({ guideWidgetId, guideWidgetSetId });
    refresh?.();
    refreshSet?.();
  }, [guide, refresh, trigger, refreshSet]);

  return (
    <Button
      type="primary"
      loading={isMutating}
      onClick={handleUnlock}
      className="rounded-sm text-xs font-normal"
      icon={<Unlock className="size-4" />}
    >
      重新编辑
    </Button>
  );
};

const BtnGuideSave = () => {
  const { guide, hasUnSavedContent, refresh, refreshSet, isProgress } =
    useGuideContext();
  const showAlert = useSignal(false);
  const { trigger, isMutating } = useSWRMutation(
    "/api/v1/guideWidget/submit",
    post
  );

  const handleSave = useCallback(async () => {
    if (hasUnSavedContent.value) {
      showAlert.value = true;
      return;
    }
    const { guideWidgetId, guideWidgetSetId } = guide;
    await trigger({ guideWidgetId, guideWidgetSetId });
    refresh?.();
    refreshSet?.();
  }, [guide, refresh, trigger, refreshSet, hasUnSavedContent, showAlert]);

  // 字幕修改按钮是否禁用
  const isEditSubtitlesDisabled = useMemo(() => {
    return !isProgress || !guide.videoJson;
  }, [isProgress, guide.videoJson]);

  // 字幕修改按钮点击事件
  const handleEditSubtitles = () => {
    window.open(
      `/guide-set-bugfix/subtitles?id=${guide.guideWidgetSetId}&guideWidgetId=${guide.guideWidgetId}&guideWidgetSetName=${guide.guideWidgetName}`
    );
  };

  return (
    <div className="flex flex-row items-center gap-2">
      {hasUnSavedContent.value && (
        <CircleAlert className="size-5 text-red-500" />
      )}
      <Button
        type="outline"
        loading={isMutating}
        onClick={handleEditSubtitles}
        disabled={isEditSubtitlesDisabled}
        className="rounded-sm text-xs font-normal"
        icon={<SquarePen className="size-4" />}
      >
        字幕修改
      </Button>
      <Button
        type="primary"
        loading={isMutating}
        onClick={handleSave}
        className="rounded-sm text-xs font-normal"
        icon={<Lock className="size-4" />}
      >
        保存完整段落
      </Button>
      <AlertDialog
        open={showAlert.value}
        onOpenChange={() => (showAlert.value = false)}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>请注意</AlertDialogTitle>
            <AlertDialogDescription>
              <span className="text-zinc-600">
                存在尚未提交的修改。请先提交所有内容。
              </span>
            </AlertDialogDescription>
            <AlertDialogFooter>
              <AlertDialogAction asChild>
                <Button
                  type="primary"
                  onClick={() => (showAlert.value = false)}
                  className="text-sm font-normal"
                >
                  确定
                </Button>
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogHeader>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

const GuideControls = () => {
  const { isGenerating, isFinish, isProgress } = useGuideContext();

  return (
    <div className="flex flex-row items-center gap-2">
      {isGenerating && <BtnCancel />}
      {isFinish && <BtnUnlock />}
      {isProgress && <BtnGuideSave />}
    </div>
  );
};

export const PreviewVideoPanelBugfix = () => {
  const { guide } = useGuideContext();
  const generateTime = useMemo(() => {
    return guide.videoGenTime
      ? new Date(guide.videoGenTime).toLocaleString()
      : "";
  }, [guide.videoGenTime]);

  const { flowRunType, guideWidgetStatus } = guide;

  const isLoading = useMemo(() => {
    return (
      guideWidgetStatus === GuideStatus.Loading &&
      flowRunType === GuideTaskType.GenerateGuide
    );
  }, [flowRunType, guideWidgetStatus]);

  return (
    <Panel
      key={`${guide.guideWidgetId}-${guide.videoGenTime}`}
      title="视频预览"
      subtitle={`更新于 ${generateTime}`}
      controls={<GuideControls />}
      loading={isLoading}
    >
      <div className="h-[600px] w-[1000px]">
        <GuidePlayer data={guide.videoJson} width={1000} height={600} />
      </div>
    </Panel>
  );
};
