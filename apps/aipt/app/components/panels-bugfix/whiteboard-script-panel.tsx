"use client";
import { MdEditorProvider } from "@/app/components/md-editor-bugfix/md-editor-context";
import { BtnCopy, BtnReset, BtnSave, BtnUpload } from "@/app/components/md-editor-bugfix/controls";
import { MdEditor } from "@/app/components/md-editor-bugfix/md-editor";
import { useGuideContext } from "@/app/context/guide-context";
import { GuideStatus, GuideTaskType } from "@/types/base";
import { useMemo } from "react";
import { Panel } from "./panel";

export const WhiteboardScriptPanelBugfix = () => {
  const { guide } = useGuideContext();
  const { flowRunType, guideWidgetStatus } = guide;

  const isLoading = useMemo(() => {
    return (
      guideWidgetStatus === GuideStatus.Loading &&
      flowRunType === GuideTaskType.GenerateReadingAndWhiteboard
    );
  }, [flowRunType, guideWidgetStatus]);
  return (
    <MdEditorProvider
      serverType="boardscript"
      key={`${guide.guideWidgetId}-${guide.videoGenTime}`}
    >
      <Panel
        title="3.板书更新"
        controls={
          <>
            <BtnUpload />
            <BtnReset />
            <BtnCopy />
            <BtnSave />
          </>
        }
        loading={isLoading}
      >
        <MdEditor
          options={{
            supportsHTML: true,
          }}
        />
      </Panel>
    </MdEditorProvider>
  );
};
