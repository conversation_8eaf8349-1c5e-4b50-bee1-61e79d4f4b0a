"use client";

import {
  MdEditorProvider,
  // useMdEditorContext,
} from "@/app/components/md-editor-bugfix/md-editor-context";
import { useGuideContext } from "@/app/context/guide-context";
// import { post } from "@/app/utils/fetcher";
// import { Button } from "@/app/components/common/aipt-button";
// import { Sparkles } from "lucide-react";
// import { useCallback } from "react";
// import useSWRMutation from "swr/mutation";
// import { BtnCopy, BtnReset, BtnSave } from "../md-editor/controls";
import { MdEditor } from "../md-editor-bugfix/md-editor";
import { Panel } from "./panel";

// const BtnGenerateReadingAndWhiteboard = () => {
//   const { guide, refresh, isGenerating } = useGuideContext();
//   const { content } = useMdEditorContext();
//   const { trigger, isMutating } = useSWRMutation(
//     "/api/v1/guideWidget/produce/from-transcript",
//     post
//   );

//   const handleGenerate = useCallback(async () => {
//     const { guideWidgetId, guideWidgetSetId } = guide;
//     try {
//       const res = await trigger({
//         guideWidgetId,
//         guideWidgetSetId,
//         transcript: content.value,
//       });
//       if (res) {
//         refresh?.();
//       }
//     } catch (error) {
//       console.error(error);
//     }
//   }, [guide, trigger, content, refresh]);

//   return (
//     <Button
//       disabled={isGenerating}
//       type="outline"
//       loading={isMutating}
//       onClick={handleGenerate}
//       className="rounded-sm text-xs"
//       icon={<Sparkles className="size-4" />}
//     >
//       生成朗读稿和板书
//     </Button>
//   );
// };

export const VerbatimScriptPanelBugfix = () => {
  const { guide } = useGuideContext();
  return (
    <MdEditorProvider
      serverType="transcript"
      key={`${guide.guideWidgetId}-${guide.taskId}`}
    >
      <Panel
        title="1.逐字稿预览"
        controls={
          <>
            {/* <BtnReset />
            <BtnCopy />
            <BtnSave /> */}
          </>
        }
      >
        <MdEditor />
      </Panel>
      {/* <BtnGenerateReadingAndWhiteboard /> */}
    </MdEditorProvider>
  );
};
