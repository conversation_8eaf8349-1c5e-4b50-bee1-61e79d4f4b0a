import { Separator } from "@/app/components/common/separator";
import { cn } from "@repo/ui/lib/utils";
import { LoaderCircle } from "lucide-react";
import { FC } from "react";

interface PanelProps {
  title: string;
  subtitle?: string;
  children?: React.ReactNode;
  controls?: React.ReactNode;
  className?: string;
  loading?: boolean;
}

const Loading = () => {
  return (
    <div className="flex h-full min-h-40 w-full items-center justify-center">
      <LoaderCircle className="stroke-primary/80 animate-spin" />
    </div>
  );
};

export const Panel: FC<PanelProps> = ({
  title,
  subtitle,
  children,
  className,
  controls,
  loading = false,
}) => {
  return (
    <section
      className={cn(
        "flex w-full flex-col gap-2 rounded-2xl bg-white px-2 py-4",
        className
      )}
    >
      <header className="flex flex-row items-center justify-between gap-4 px-2">
        <h3 className="text-base font-medium">{title}</h3>
        <span className="text-sm text-zinc-600">{subtitle}</span>
        <menu className="flex flex-1 flex-row justify-end gap-6">
          {controls}
        </menu>
      </header>
      <Separator />
      <main className="px-2">{loading ? <Loading /> : children}</main>
    </section>
  );
};
