"use client";

import { But<PERSON> } from "@/app/components/common/aipt-button";
import { toast } from "@/app/components/common/toast";
import { post } from "@/app/utils/fetcher";
import { formatTime } from "@/app/utils/frameToTime";
import IcSave from "@/public/ic-save.svg";
import { RawGuideSet } from "@/types/guide-widget";
import { Eraser } from "lucide-react";
import { FC } from "react";
import useSWRMutation from "swr/mutation";
import { useGuideContext } from "../context/guide-context";
interface NavbarProps {
  guideSetTitle?: string;
  guideWidgetSet: RawGuideSet;
  hasButton?: boolean;
  mode?: string;
}

export const Navbar: FC<NavbarProps> = ({
  guideSetTitle = "稿件生产",
  guideWidgetSet,
  hasButton = true,
  mode,
}) => {
  const { guideWidgetSetId, guideWidgetSetName, wordNum, videoLength } =
    guideWidgetSet;
  const { localDraft, refresh } = useGuideContext();

  const BtnClearLocalDraft = () => {
    localDraft.clearAll();
    refresh?.();
    toast.success("清除成功");
  };

  const { trigger: submitList, isMutating } = useSWRMutation(
    "/api/v1/guideWidgetSet/submit",
    post
  );

  const { trigger: submitListFixMode, isMutating: isMutatingFixMode } =
    useSWRMutation("/api/v1/guideWidgetSet/submit/fixMode", post);

  const handleSubmit = async () => {
    // console.log(guideWidgetSet);
    if (mode === "bugfix") {
      await submitListFixMode({
        guideWidgetSetId,
      });
    } else {
      await submitList({
        guideWidgetSetId,
      });
    }
    toast.success("保存成功");
  };

  return (
    <nav className="flex h-full w-full flex-row items-center gap-6 border-b border-b-gray-200 shadow-[0_1px_0_0_rgba(0,0,0,0.04)]">
      <div className="text-xl">{guideSetTitle}</div>
      <div className="text-sm text-gray-500">
        课程名称: {guideWidgetSetName}
      </div>
      <div className="text-[#2563EB]">视频累计: {formatTime(videoLength)}</div>
      <div className="text-[#2563EB]">逐字稿累计: {wordNum}字符</div>
      <div className="ml-auto flex items-center gap-2">
        <Button
          type="primary"
          size="sm"
          icon={<Eraser className="h-4 w-4" />}
          onClick={BtnClearLocalDraft}
        >
          清除本地缓存
        </Button>
        {hasButton && (
          <Button
            type="default"
            size="sm"
            className="bg-[#E5E7EB] text-[#4B5563]"
            icon={<IcSave />}
            onClick={handleSubmit}
            loading={mode === "bugfix" ? isMutatingFixMode : isMutating}
          >
            保存稿件
          </Button>
        )}
      </div>
    </nav>
  );
};
