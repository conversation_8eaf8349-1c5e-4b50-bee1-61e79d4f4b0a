"use client";
import { useGuideContext } from "@/app/context/guide-context";
import { GuideStatus, GuideTaskType } from "@/types/base";
import { useMemo } from "react";
import { GuideH3PartsEditor } from "../editor-guide/guide-h3-parts-editor";
import { BtnSave } from "../line-split-editor/controls";
import { GenerateImagesButton } from "../line-split-editor/generate-images-button";
import { Panel } from "./panel";

//分栏
export const LineSplitPanel = () => {
  const { guide } = useGuideContext();
  const { flowRunType, guideWidgetStatus } = guide;

  const isLoading = useMemo(() => {
    return (
      guideWidgetStatus === GuideStatus.Loading &&
      (flowRunType === GuideTaskType.GenerateReadingAndWhiteboard ||
        flowRunType === GuideTaskType.GenerateColumnImages)
    );
  }, [flowRunType, guideWidgetStatus]);
  return (
    <Panel
      title="4.分栏编辑"
      controls={
        <>
          <GenerateImagesButton />
          <BtnSave />
        </>
      }
      loading={isLoading}
    >
      <GuideH3PartsEditor />
    </Panel>
  );
};
