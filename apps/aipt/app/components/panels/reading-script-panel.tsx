import { ReadingEditorProvider } from "@/app/components/reading-editor/reading-editor-context";
import { useGuideContext } from "@/app/context/guide-context";
import { GuideStatus, GuideTaskType } from "@/types/base";
import { useMemo } from "@preact-signals/safe-react/react";
import { /*BtnSave, */BtnBatchGenerate, BtnMergeAudio, TextGenerateInfo } from "../reading-editor/controls";
import { ReadingEditor } from "../reading-editor/reading-editor";
import { Panel } from "./panel";

export const ReadingScriptPanel = () => {
  const { guide } = useGuideContext();
  const { flowRunType, guideWidgetStatus } = guide;
  const isLoading = useMemo(() => {
    return (
      guideWidgetStatus === GuideStatus.Loading &&
      flowRunType === GuideTaskType.GenerateReadingAndWhiteboard
    );
  }, [flowRunType, guideWidgetStatus]);
  

  return (
    <ReadingEditorProvider key={`${guide.guideWidgetId}-${guide.videoGenTime}`}>
      <Panel
        title="2.朗读稿编辑"
        subtitle={<TextGenerateInfo />}
        controls={
          <>
            <BtnBatchGenerate />
            <BtnMergeAudio />
            {/* <BtnSave /> */}
          </>
        }
        loading={isLoading}
      >
        <ReadingEditor />
      </Panel>
    </ReadingEditorProvider>
  );
};
