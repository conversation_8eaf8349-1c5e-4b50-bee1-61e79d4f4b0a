"use client";
import { MdEditorProvider } from "@/app/components/md-editor/md-editor-context";
import { useGuideContext } from "@/app/context/guide-context";
import { GuideStatus, GuideTaskType } from "@/types/base";
import { FC, useMemo, useRef, useState } from "react";
import { BtnCopy, BtnReset, BtnSave, BtnUpload } from "../md-editor/controls";
import { MdEditor } from "../md-editor/md-editor";
import { Panel } from "./panel";
import { MdEditorContextType } from "../md-editor/md-editor-context";
import { GuidePlayer } from "../player/guide-player";
import { post } from "@/app/utils/fetcher";
import { GuideWidgetData } from "@repo/core/types/data/widget-guide";
import { debounce } from "lodash";
import { message, Spin } from "antd";

interface WhiteboardScriptPanelProps extends React.ComponentProps<"div"> {
  setMdContext?: (mdContext: MdEditorContextType) => void;
}

export const WhiteboardScriptPanel: FC<WhiteboardScriptPanelProps> = (
  props
) => {
  const { guide } = useGuideContext();
  const { flowRunType, guideWidgetStatus } = guide;

  const isLoading = useMemo(() => {
    return (
      guideWidgetStatus === GuideStatus.Loading &&
      flowRunType === GuideTaskType.GenerateReadingAndWhiteboard
    );
  }, [flowRunType, guideWidgetStatus]);

  // 请求最后一帧数据
  const [lastFrameVideoJson, setLastFrameVideoJson] =
    useState<GuideWidgetData | null>(null);
  const mdContent = useRef("");
  const [isSyncing, setIsSyncing] = useState(true);
  const syncLastFrame = async () => {
    setIsSyncing(true);
    const params = {
      arg: {
        guideWidgetSetId: guide.guideWidgetSetId,
        guideWidgetId: guide.guideWidgetId,
        boardscript: mdContent.current,
      },
    };

    try {
      const res: { videoJson: string } = await post(
        "/api/v1/guideWidget/board/preview",
        params
      );
  
      if (res && res.videoJson) {
        setTimeout(() => {
          setLastFrameVideoJson(JSON.parse(res.videoJson) as GuideWidgetData);
          console.log("JSON.parse(res.videoJson)", JSON.parse(res.videoJson));
          setTimeout(() => {
            setIsSyncing(false);
          }, 200);
        }, 500);
      }
    } catch (error: any) {
      message.error(error.message);
      setIsSyncing(false);
    }
    
  };

  const syncLastFrameTemp = debounce(syncLastFrame, 2000);
  const onMdChanged = (content: string) => {
    mdContent.current = content;
    syncLastFrameTemp();
  };

  return (
    <MdEditorProvider
      serverType="boardscript"
      key={`${guide.guideWidgetId}-${guide.videoGenTime}`}
    >
      <Panel
        title="3.板书编辑"
        controls={
          <>
            <BtnUpload />
            <BtnReset />
            <BtnCopy />
            <BtnSave />
          </>
        }
        loading={isLoading}
      >
        <div className="flex h-[600px]">
          <div className="relative w-[1000px] bg-[#f5f5f5]">
            {isSyncing && (
              <div className="z-1 absolute left-0 top-0 flex h-full w-full items-center justify-center bg-white">
                <Spin />
              </div>
            )}
            <>
              {lastFrameVideoJson && (
                <GuidePlayer data={lastFrameVideoJson} controls={false} />
              )}
            </>
          </div>
          <div className="h-auto w-0.5 flex-1">
            <MdEditor
              className="h-full"
              setMdContext={props.setMdContext}
              showPreview={false}
              options={{
                supportsHTML: true,
              }}
              onMdChanged={onMdChanged}
            />
          </div>
        </div>
      </Panel>
    </MdEditorProvider>
  );
};
