import { Separator } from "@/app/components/common/separator";
import { cn } from "@repo/ui/lib/utils";
import { LoaderCircle } from "lucide-react";
import { FC } from "react";

interface PanelProps {
  title: string;
  subtitle?: React.ReactNode;
  children?: React.ReactNode;
  controls?: React.ReactNode;
  className?: string;
  contentClassName?: string;
  loading?: boolean;
  headerClassName?: string;
}

const Loading = () => {
  return (
    <div className="flex h-full min-h-40 w-full items-center justify-center">
      <LoaderCircle className="stroke-primary/80 animate-spin" />
    </div>
  );
};

export const Panel: FC<PanelProps> = ({
  title,
  subtitle,
  children,
  className,
  contentClassName,
  controls,
  loading = false,
  headerClassName,
}) => {
  return (
    <section
      className={cn(
        "flex w-full flex-col gap-2 rounded-2xl bg-white px-2 py-4",
        className
      )}
    >
      <header
        className={cn(
          "flex flex-row items-center justify-between gap-4 px-2",
          headerClassName
        )}
      >
        <h3 className="text-base font-medium">{title}</h3>
        <span className="text-sm text-zinc-600">{subtitle}</span>
        <menu className="flex flex-1 flex-row justify-end gap-6">
          {controls}
        </menu>
      </header>
      <Separator />
      <main className={cn("px-2", contentClassName)}>
        {loading ? <Loading /> : children}
      </main>
    </section>
  );
};
