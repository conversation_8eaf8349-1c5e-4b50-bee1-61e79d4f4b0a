"use client";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/app/components/common/alert-dialog";
import { Button } from "@/app/components/common/button";
import {
  MdEditorContextType,
  MdEditorProvider,
  useMdEditorContext,
} from "@/app/components/md-editor/md-editor-context";
import { useGuideContext } from "@/app/context/guide-context";
import { post } from "@/app/utils/fetcher";
import { Sparkles } from "lucide-react";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import useSWRMutation from "swr/mutation";
import { BtnCopy, BtnReset, BtnSave } from "../md-editor/controls";
import { MdEditor } from "../md-editor/md-editor";
import { Panel } from "./panel";
import { useSignal } from "@preact-signals/safe-react";
import { emitterPlus } from "@/lib/emitter";

const BtnGenerateReadingAndWhiteboard = (props: {
  mdContext?: MdEditorContextType | null;
}) => {
  const { guide, refresh, isGenerating, hasUnSavedContent } = useGuideContext();
  const { content } = useMdEditorContext();
  const { trigger, isMutating } = useSWRMutation(
    "/api/v1/guideWidget/produce/from-transcript",
    post
  );

  const doGenerate = useCallback(async () => {
    const { guideWidgetId, guideWidgetSetId } = guide;
    const res = await trigger({
      guideWidgetId,
      guideWidgetSetId,
      transcript: content.value,
    });
    if (res) {
      refresh?.();
    }
  }, [guide, trigger, refresh]);

  const handleGenerate = useCallback(async () => {
    if (hasUnSavedContent.value) {
      showAlert.value = true;
      return;
    }
    doGenerate();
  }, [guide, trigger, content, refresh]);

  const errorText = useMemo(() => {
    const { genAudioJsonFailedTime } = guide.genDataFailedTime || 0;
    if (genAudioJsonFailedTime === 0) {
      return null;
    }
    return (
      <span className="text-xs text-red-500">
        朗读稿和板书生成失败，请重试！
      </span>
    );
  }, [guide]);

  const showAlert = useSignal(false);

  const onDialogOk = useCallback(async () => {
    showAlert.value = false;
    if (props.mdContext) {
      await props.mdContext.save();
    }
    await doGenerate();
  }, [doGenerate, showAlert, props.mdContext]);

  const onDialogCancel = useCallback(() => {
    showAlert.value = false;
  }, [showAlert]);

  return (
    <div className="flex flex-col items-center gap-2">
      <Button
        disabled={isGenerating}
        type="outline"
        loading={isMutating}
        onClick={handleGenerate}
        className="rounded-sm text-xs"
        icon={<Sparkles className="size-4" />}
      >
        生成朗读稿和板书
      </Button>
      {errorText}

      <AlertDialog
        open={showAlert.value}
        onOpenChange={() => (showAlert.value = false)}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>请注意</AlertDialogTitle>
            <AlertDialogDescription>
              <span className="text-zinc-600">
                是否自动提交所有修改，再继续生成？
              </span>
            </AlertDialogDescription>
            <AlertDialogFooter>
              <AlertDialogAction asChild>
                  <Button
                    type="outline"
                    onClick={onDialogCancel}
                    className="text-sm font-normal"
                  >
                    关闭
                  </Button>
              </AlertDialogAction>
              <AlertDialogAction asChild>
                  <Button
                    type="primary"
                    onClick={onDialogOk}
                    className="text-sm font-normal"
                  >
                    确定
                  </Button>
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogHeader>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export const VerbatimScriptPanel = () => {
  const { guide } = useGuideContext();
  const [mdContext, setMdContext] = useState<MdEditorContextType | null>(null);

  const saveTranscript = useCallback(async () => {
    return mdContext?.save();
  }, [mdContext]);

  useEffect(() => {
    if (mdContext) {
      emitterPlus.on("saveTranscript", saveTranscript);
    }
    return () => {
      emitterPlus.off("saveTranscript", saveTranscript);
    };
  }, [mdContext]);

  return (
    <MdEditorProvider
      serverType="transcript"
      key={`${guide.guideWidgetId}-${guide.taskId}`}
    >
      <Panel
        title="1.逐字稿编辑"
        controls={
          <>
            <BtnReset />
            <BtnCopy />
            <BtnSave />
          </>
        }
      >
        <MdEditor setMdContext={setMdContext} />
      </Panel>
      <BtnGenerateReadingAndWhiteboard mdContext={mdContext} />
    </MdEditorProvider>
  );
};
