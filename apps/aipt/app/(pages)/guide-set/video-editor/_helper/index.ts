export * from "./frame-time";
export * from "./guid-helper";
export * from "./time-segment";

/**
 * 生成随机十六进制颜色值
 * @returns {string} 十六进制颜色值，格式如 "#ff0033"
 */
export const getRandomHexColor = () => {
  // 生成 0-255 的随机数，转换为 16 进制，并确保两位数
  const r = Math.floor(Math.random() * 256)
    .toString(16)
    .padStart(2, "0");
  const g = Math.floor(Math.random() * 256)
    .toString(16)
    .padStart(2, "0");
  const b = Math.floor(Math.random() * 256)
    .toString(16)
    .padStart(2, "0");

  // 组合成十六进制颜色值
  return `#${r}${g}${b}`;
};
