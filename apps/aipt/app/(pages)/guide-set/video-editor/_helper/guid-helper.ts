import {
  GuideWidgetData,
  Line,
  LineTexture,
  RoughNotation,
  Subtitle,
} from "@repo/core/types/data/widget-guide";
import cloneDeep from "lodash/cloneDeep";
import { v4 as uuid } from "uuid";

type ItemCommon = {
  id: string;
  contentText: string;
  type: string;
  inFrame: number;
  outFrame: number;
};

// 基于 Line 扩展的类型
export type PositioningItem = Line & ItemCommon;

export type HighlightItem = LineTexture & ItemCommon;

export type SubtitleItem = Subtitle & ItemCommon;

export type GuideWidgetDataNew = GuideWidgetData & {
  positionings: PositioningItem[];
  highlights: HighlightItem[];
  newSubtitles: SubtitleItem[];
};

/**
 * 获取item的宽度
 * @param start 开始时间/帧
 * @param end 结束时间/帧
 * @param total 总时间/帧
 * @returns 宽度
 */
export const getItemWidthByFrame = (
  start: number,
  end: number,
  total: number
): number => {
  return (end - start) / total;
};

/**
 * 获取item的宽度
 * @param start 开始时间/帧
 * @param total 总时间/帧
 * @returns absolute 的 left 位置
 */
export const getItemStartPos = (start: number, total: number): number => {
  return start / total;
};

// 检查当前正在播放的关键帧节点
export type ActiveNodeVal = {
  positioning: string | null;
  highlight: string | null;
  subtitle: string | null;
};

export const activeNodeValInt = {
  positioning: null,
  highlight: null,
  subtitle: null,
};

export const checkActiveNode = (
  currentFrame: number,
  guideData: GuideWidgetDataExt
): ActiveNodeVal => {
  const activeNodeVal: ActiveNodeVal = cloneDeep(activeNodeValInt);
  // 检查定位内容节点
  for (const item of guideData.remark.positionings) {
    if (currentFrame >= item.inFrame && currentFrame <= item.outFrame) {
      activeNodeVal.positioning = item.id;
      break;
    }
  }

  // 检查高亮节点
  for (const item of guideData.remark.highlights) {
    if (currentFrame >= item.inFrame && currentFrame <= item.outFrame) {
      activeNodeVal.highlight = item.id;
      break;
    }
  }

  // 检查字幕节点
  for (const item of guideData.remark.subtitles) {
    if (currentFrame >= item.inFrame && currentFrame <= item.outFrame) {
      activeNodeVal.subtitle = String(item.id);
      break;
    }
  }

  return activeNodeVal;
};

interface LineExt {
  id: string;
  contentText: string;
}

type LinePlus = Line &
  LineExt & {
    notation?: RoughNotation;
  } & {
    blockPid?: string;
  };

interface remarkType {
  positionings: Array<LinePlus>;
  highlights: Array<LinePlus>;
  subtitles: Array<
    Subtitle & {
      contentText: string;
    }
  >;
}

export type GuideWidgetDataExt = GuideWidgetData & {
  content: LinePlus[];
  remark: remarkType;
};

// 这里给 guideData 最外层扩展了remark字段，方便后续对这3种内容做渲染或处理
// 并且给guideData content的item增加了id，以及和一个contentText统一渲染字段
export const remarkGuideData = (_guideData: GuideWidgetData) => {
  // 创建一个新对象，并在类型系统中明确指定其结构
  const guideData: GuideWidgetDataExt = {
    ...(cloneDeep(_guideData) as GuideWidgetData),
    content: [], // 先创建空数组，后面会填充
    remark: {
      positionings: [],
      highlights: [],
      subtitles: [],
    },
  };

  // 重新构建 content 数组，确保每个元素都是 LinePlus 类型

  guideData.content = _guideData.content.map((originalLine) => {
    // 创建一个符合 LinePlus 类型的新对象
    const linePlus: LinePlus = {
      ...originalLine,
      id: uuid(),
      contentText: "",
    };

    // 计算 showTempContent
    if (Array.isArray(linePlus.content)) {
      linePlus.content.forEach((it: any) => {
        // 给content>content里边的每一个item也添加唯一标识，主要高亮会用
        it.id = uuid();

        if (it.content) {
          linePlus.contentText += it.content;
        }
        if (it.notation) {
          it.contentText = it.content;
          // 添加到 highlights
          guideData.remark.highlights.push(it);
        }
      });
    }
    // 添加到 positionings
    guideData.remark.positionings.push(linePlus);
    return linePlus;
  });

  // 特殊处理 元素为block的item
  guideData.remark.positionings.forEach((item, index) => {
    if (item.tag === "block") {
      console.log(89898989, item);

      const blockItem = cloneDeep(item);

      const blockContent = blockItem.content.map((it1) => {
        // 创建一个符合 LinePlus 类型的新对象
        // @ts-expect-error
        const linePlus: LinePlus = {
          ...it1,
          contentText: "",
          blockPid: item.id,
        };

        // 计算 showTempContent
        if (Array.isArray(linePlus.content)) {
          linePlus.content.forEach((it: any) => {
            // 给content>content里边的每一个item也添加唯一标识，主要高亮会用
            it.id = uuid();

            if (it.content) {
              linePlus.contentText += it.content;
            }
            if (it.notation) {
              it.contentText = it.content;
              // 添加到 highlights（这个地方可能会有坑）
              guideData.remark.highlights.push(it);
            }
          });
        }

        return linePlus;
      });
      guideData.remark.positionings.splice(index, 1, ...blockContent);
    }
  });

  guideData.remark.subtitles = guideData.subtitles.map((it) => ({
    ...it,
    contentText: it.text,
  }));

  console.log("guideData", guideData);
  return guideData;
};
