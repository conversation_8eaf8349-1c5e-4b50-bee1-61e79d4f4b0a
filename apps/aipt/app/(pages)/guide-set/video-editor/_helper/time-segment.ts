/**
 * 按指定间隔切割时间，并返回时间点、时长及占比
 * @param {number} totalSeconds 总秒数（如 56）
 * @param {number} interval 切割间隔（如 30）
 * @returns {Array} 返回包含时间点、时长和占比的数组
 */
export const getTimeSegments = (totalSeconds: number, interval: number) => {
  if (totalSeconds <= 0 || interval <= 0) {
    throw new Error("时间和间隔必须为正数");
  }

  const segments = [];
  let prevTime = 0;

  // 生成切割点并计算每段信息
  for (let current = interval; current < totalSeconds; current += interval) {
    const duration = current - prevTime;
    segments.push({
      startTime: formatTime(prevTime),
      endTime: formatTime(current),
      durationSec: duration,
      durationFormatted: formatTime(duration),
      ratio: calculateRatio(duration, totalSeconds),
    });
    prevTime = current;
  }

  // 添加最后一段（不足间隔的部分）
  const lastDuration = totalSeconds - prevTime;
  if (lastDuration > 0) {
    segments.push({
      startTime: formatTime(prevTime),
      endTime: formatTime(totalSeconds),
      durationSec: lastDuration,
      durationFormatted: formatTime(lastDuration),
      ratio: calculateRatio(lastDuration, totalSeconds),
    });
  }

  return segments;
};

// 格式化时间为 "00:00"
// function formatTime(seconds: number) {
//   const mins = Math.floor(seconds / 60)
//     .toString()
//     .padStart(2, "0");
//   const secs = Math.floor(seconds % 60)
//     .toString()
//     .padStart(2, "0");
//   return `${mins}:${secs}`;
// }

// 计算比例（百分比）
function calculateRatio(duration: number, total: number) {
  return ((duration / total) * 100).toFixed(2) + "%";
}

// // 使用示例
// const result = getTimeSegments(56, 30);
// console.log(result);

// [
//     {
//       startTime: "00:00",
//       endTime: "00:30",
//       durationSec: 30,
//       durationFormatted: "00:30",
//       ratio: "53.57%"
//     },
//     {
//       startTime: "00:30",
//       endTime: "00:56",
//       durationSec: 26,
//       durationFormatted: "00:26",
//       ratio: "46.43%"
//     }
//   ]

// 功能说明
// 字段	说明	示例
// startTime	分段开始时间（格式化）	"00:00"
// endTime	分段结束时间（格式化）	"00:30"
// durationSec	分段时长（秒数）	30
// durationFormatted	分段时长（格式化）	"00:30"
// ratio	分段占总时长的百分比	"53.57%"

// 计算时间刻度间隔
export const getTimeScale = (durationInSeconds: number) => {
  if (durationInSeconds <= 60) return 5; // 1分钟内，每5秒一个刻度
  if (durationInSeconds <= 300) return 10; // 5分钟内，每10秒一个刻度
  if (durationInSeconds <= 600) return 30; // 10分钟内，每30秒一个刻度
  if (durationInSeconds <= 1800) return 60; // 30分钟内，每1分钟一个刻度
  return 300; // 超过30分钟，每5分钟一个刻度
};

// 格式化时间显示
export const formatTime = (seconds: number) => {
  const mins = Math.floor(seconds / 60)
    .toString()
    .padStart(2, "0");
  const secs = Math.floor(seconds % 60)
    .toString()
    .padStart(2, "0");
  return `${mins}:${secs}`;
};

// 生成时间刻度
export const generateTimeTicks = (durationInSeconds: number, scale: number) => {
  const ticks = [];
  for (let time = 0; time <= durationInSeconds; time += scale) {
    const percent = (time / durationInSeconds) * 100;
    ticks.push({
      time,
      percent,
      label: formatTime(time),
    });
  }
  return ticks;
};

export const timeFormat = (seconds: number) => {
  const mins = Math.floor(seconds / 60)
    .toString()
    .padStart(2, "0");
  const secs = (seconds % 60).toString().padStart(2, "0");
  return `${mins}:${secs}`;
};
