import { Button } from "@/app/components/common/button";
import { Input } from "@/app/components/ui/input";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/app/components/ui/popover";
import {
  Ta<PERSON>,
  TabsContent,
  <PERSON><PERSON>List,
  TabsTrigger,
} from "@/app/components/ui/tabs";
import emitter from "@/lib/emitter";
import {
  DefaultNotionColorMap,
  NotionTypes,
} from "@repo/core/components/notion/types";
import { cn } from "@repo/ui/lib/utils";
import { useMount } from "ahooks";
import { cloneDeep } from "lodash";
import { Trash2 } from "lucide-react";
import { useState } from "react";
import { useVideoEditor } from "../_helper/context";
import {
  ActiveNodeVal,
  HighlightItem,
  PositioningItem,
  SubtitleItem,
} from "../_helper/guid-helper";

interface PanelContentProps {
  item: PositioningItem | HighlightItem | SubtitleItem;
  index: number;
  active: string | null;
  activeTab: ActiveTab;
  guideDataItem: Array<PositioningItem | HighlightItem | SubtitleItem>;
}

const mapDic1 = {
  positioning: "positionings",
  highlight: "highlights",
  subtitle: "subtitles",
};

const mapDic2 = {
  positionings: "positioning",
  highlights: "highlight",
  subtitles: "subtitle",
};

const highlightDic = {
  underline: "下划线",
  highlight: "高亮",
  circle: "圈画",
};

const highlightTypes = [
  { value: "underline", label: "下划线" },
  { value: "highlight", label: "高亮" },
  { value: "circle", label: "圈画" },
  // { value: "box", label: "方框" },
  // { value: "strike-through", label: "删除线" },
  // { value: "crossed-off", label: "划去" },
  // { value: "bracket", label: "括号" },
];
const SegmentCard: React.FC<PanelContentProps> = ({
  item,
  index,
  active,
  activeTab,
  guideDataItem,
}) => {
  const { playerRef, updateGuideData, guideData } = useVideoEditor();
  const isActive = active === String(item.id);

  const [isEditing, setIsEditing] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const handleTextClick = (event: React.MouseEvent) => {
    if (activeTab === "subtitles") {
      event.stopPropagation();
      event.preventDefault();
      setIsEditing(true);
    }
  };

  const onClickCard = (e: React.MouseEvent) => {
    const player = playerRef?.current;
    if (player) {
      player.seekTo(item.inFrame + 1);
    }
  };

  const onFrameChange = (type: "outFrame" | "inFrame", value: string) => {
    if (guideData) {
      const newGuideData = cloneDeep(guideData);
      // 修改remark
      const remarTypeContent = newGuideData.remark[activeTab];
      (remarTypeContent[index] as typeof item) = {
        ...item,
        [type]: Number(value),
      };
      // 修改原始数据 content
      newGuideData.content.forEach((it) => {
        if (it.id === item.id) {
          it[type] = Number(value);
        } else {
          it.content.forEach((it2: any) => {
            if (it2.id === item.id) {
              it2[type] = Number(value);
            }
          });
        }
      });

      updateGuideData(newGuideData);
    }
  };

  const onBlur = () => {
    setIsEditing(false);
  };

  const onSubtitleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newGuideData = cloneDeep(guideData)!;
    const remarTypeContent = newGuideData.remark[activeTab];
    // @ts-expect-error
    remarTypeContent[index] = {
      ...item,
      contentText: e.target.value,
    };

    newGuideData?.subtitles.forEach((it) => {
      // @ts-expect-error
      if (it.id === item.id) {
        it.text = e.target.value;
      }
    });
    updateGuideData(newGuideData);
  };

  const onClickHighlight = (type: NotionTypes) => {
    const newGuideData = cloneDeep(guideData)!;
    const remarTypeContent = newGuideData.remark[activeTab];
    // @ts-expect-error
    remarTypeContent[index] = {
      ...item,
      notation: {
        // @ts-expect-error
        ...item.notation,
        type,
      },
    };

    //  修改原始数据 content
    newGuideData.content.forEach((it) => {
      it.content.forEach((it2: any) => {
        if (it2.id === item.id) {
          // it2.notation.type = type;
          // it2.notation.color = DefaultNotionColorMap[type];

          it2.notation = {
            ...it2.notation,
            type,
            color: DefaultNotionColorMap[type],
          };
        }
      });
    });
    updateGuideData(newGuideData);
  };

  // 处理删除按钮点击，打开确认对话框
  const onDeleteItem = (e: React.MouseEvent) => {
    e.stopPropagation();
    setShowDeleteConfirm(true);
  };

  // 确认删除操作
  const handleConfirmDelete = () => {
    if (!guideData) return;
    const newGuideData = cloneDeep(guideData);

    // 从remark中删除
    newGuideData.remark.subtitles = newGuideData.remark.subtitles.filter(
      // @ts-expect-error
      (sub) => sub.id !== item.id
    );

    // 从对应的类型数组中删除
    newGuideData.subtitles = newGuideData.subtitles.filter(
      // @ts-expect-error
      (sub) => sub.id !== item.id
    );
    updateGuideData(newGuideData);
    setShowDeleteConfirm(false);
  };

  // 取消删除操作
  const handleCancelDelete = () => {
    setShowDeleteConfirm(false);
  };

  return (
    <div
      className={cn("content-card group cursor-pointer rounded-lg p-4", {
        active: isActive,
      })}
      onClick={onClickCard}
      data-item-id={item.id}
    >
      <div className="flex gap-3">
        {/* Index Circle */}
        <div
          className={`shadow-refined mt-0.5 flex h-6 w-6 flex-shrink-0 items-center justify-center rounded-full bg-amber-950 text-xs font-medium text-white`}
        >
          {index + 1}
        </div>
        {/* 内容区域 */}
        <div className="min-w-0 flex-1 space-y-3">
          {/* 文本内容 */}
          <div className="min-h-16">
            {isEditing ? (
              <textarea
                autoFocus
                value={item.contentText}
                onBlur={onBlur}
                onChange={onSubtitleChange}
                className="bg-surface-hover border-accent-coral focus:ring-accent-coral shadow-inner-subtle h-16 max-h-24 min-h-16 w-full resize-y rounded-md border px-3 py-2 text-sm leading-relaxed text-white focus:outline-none focus:ring-2"
              />
            ) : (
              <p
                className={`hover:bg-surface-hover -mx-2 line-clamp-3 cursor-text rounded-md px-2 py-1 text-sm leading-relaxed text-white transition-all duration-200 hover:bg-opacity-50`}
                onClick={handleTextClick}
                title={item.contentText}
              >
                {item.contentText}
              </p>
            )}
          </div>

          {/* 高亮内容 */}
          {activeTab === "highlights" && !isEditing && (
            <div
              className="bg-surface-hover flex items-center gap-8 rounded-md bg-opacity-30 px-2 py-2"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="flex items-center gap-2">
                <span className="text-text-muted text-xs">类型</span>
                <div className="bg-surface-tertiary text-text-muted flex h-6 cursor-pointer items-center gap-1 rounded px-2 text-xs">
                  <Popover>
                    <PopoverTrigger asChild>
                      <div className="w-10">
                        {/* @ts-expect-error */}
                        {highlightDic[item.notation.type]}
                      </div>
                    </PopoverTrigger>
                    <PopoverContent className="bg-surface-elevated border-border-subtle shadow-refined w-auto p-2">
                      <div className="flex gap-1">
                        {highlightTypes.map((highlightType) => (
                          <div
                            onClick={() =>
                              onClickHighlight(
                                highlightType.value as NotionTypes
                              )
                            }
                            className="h-7 cursor-pointer rounded-sm p-1 text-sm text-white hover:bg-red-100"
                            key={highlightType.value}
                            style={{
                              backgroundColor:
                                // @ts-expect-error
                                item.notation.type === highlightType.value
                                  ? "rgba(255,255,255,0.2)"
                                  : "transparent",
                            }}
                          >
                            {highlightType.label}
                          </div>
                        ))}
                      </div>
                    </PopoverContent>
                  </Popover>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-text-muted text-xs">颜色</span>
                {/* Disabled type color - display only */}
                <div
                  className="bg-surface-tertiary text-text-muted flex h-4 cursor-pointer items-center gap-1 rounded px-2 text-xs"
                  style={{
                    backgroundColor:
                      // @ts-expect-error
                      DefaultNotionColorMap[item.notation.type as NotionTypes],
                  }}
                ></div>
              </div>
            </div>
          )}

          {/* 帧率内容 */}
          <div className="border-border-subtle flex items-center justify-between border-t pt-2">
            <div className="flex items-center gap-3">
              <div className="flex items-center gap-1 text-xs">
                <span className="text-text-dim w-8">开始</span>
                <Input
                  type="number"
                  value={item.inFrame}
                  className={cn(
                    "bg-surface-hover border-border-subtle focus:border-accent-coral shadow-inner-subtle h-6 w-14 px-1.5 text-xs text-white",
                    {
                      "text-orange-400":
                        activeTab === "subtitles" &&
                        index > 0 &&
                        guideDataItem[index - 1] &&
                        (guideDataItem[index - 1] as any).outFrame >
                          item.inFrame,
                    }
                  )}
                  onClick={(e) => e.stopPropagation()}
                  onChange={(e) => onFrameChange("inFrame", e.target.value)}
                />
                <span className="text-text-dim text-xs">帧</span>
              </div>
              <div className="flex items-center gap-1 text-xs">
                <span className="text-text-dim w-8">结束</span>
                <Input
                  type="number"
                  value={item.outFrame}
                  className="bg-surface-hover border-border-subtle focus:border-accent-coral shadow-inner-subtle h-6 w-14 px-1.5 text-xs text-white"
                  onClick={(e) => e.stopPropagation()}
                  onChange={(e) => onFrameChange("outFrame", e.target.value)}
                />
                <span className="text-text-dim text-xs">帧</span>
                {/* 删除图标 - 带二次确认 */}
                {activeTab === "subtitles" && (
                  <Popover
                    open={showDeleteConfirm}
                    onOpenChange={setShowDeleteConfirm}
                  >
                    <PopoverTrigger asChild>
                      <button
                        onClick={onDeleteItem}
                        className="text-text-dim ml-2 opacity-0 transition-all duration-200 hover:text-red-400 group-hover:opacity-100"
                        title="删除"
                      >
                        <Trash2 size={14} />
                      </button>
                    </PopoverTrigger>
                    <PopoverContent
                      className="bg-surface-hover w-48 p-3 shadow-lg"
                      align="end"
                    >
                      <div className="space-y-3">
                        <div className="text-sm text-white">
                          确认删除这个片段吗？
                        </div>
                        <div className="flex justify-end gap-2">
                          <Button
                            type="default"
                            size="sm"
                            onClick={handleCancelDelete}
                            className="h-7 px-3 text-xs"
                          >
                            取消
                          </Button>
                          <Button
                            type="error"
                            size="sm"
                            onClick={handleConfirmDelete}
                            className="h-7 px-3 text-xs"
                          >
                            删除
                          </Button>
                        </div>
                      </div>
                    </PopoverContent>
                  </Popover>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// 定义 activeTab 的联合类型
type ActiveTab = "positionings" | "highlights" | "subtitles";
const Panel = () => {
  const { guideData, activeNode } = useVideoEditor();
  const [activeTab, setActiveTab] = useState<ActiveTab>("positionings");

  const tabs = [
    {
      value: "positionings" as ActiveTab,
      label: "定位",
    },
    {
      value: "highlights" as ActiveTab,
      label: "高亮",
    },
    {
      value: "subtitles" as ActiveTab,
      label: "字幕",
    },
  ];

  // 使用类型断言确保类型安全
  const guideDataItem = guideData?.remark[activeTab] as Array<
    PositioningItem | HighlightItem | SubtitleItem
  >;

  // 处理 onValueChange 的类型转换
  const handleTabChange = (value: string) => {
    setActiveTab(value as ActiveTab);
  };

  useMount(() => {
    const onClickedItem = (clickItem: any) => {
      setActiveTab(clickItem.type as ActiveTab);

      // 等待 tab 切换完成后再滚动
      setTimeout(() => {
        // 根据 item.id 找到对应的元素并滚动到该位置
        const targetElement = document.querySelector(
          `[data-item-id="${clickItem.item.id}"]`
        );
        if (targetElement) {
          targetElement.scrollIntoView({
            behavior: "smooth",
            block: "center",
            inline: "nearest",
          });
        }
      }, 100); // 给 tab 切换一点时间
    };
    emitter.on("clickTrackItem", onClickedItem);
    return () => {
      emitter.off("clickTrackItem", onClickedItem);
    };
  });

  let list = guideDataItem;
  if (activeTab === "positionings") {
    // @ts-expect-error
    list = guideDataItem.filter((it) => it.tag !== "block");
  }

  return (
    <div className="bg-gradient-surface shadow-subtle w-96 flex-shrink-0 border border-l border-gray-800 transition-all duration-200 ease-out">
      <div className="bg-surface-primary flex h-full flex-col">
        <Tabs
          value={activeTab}
          onValueChange={handleTabChange}
          className="flex h-full flex-col"
        >
          {/* Tab Header - Fixed height and proper alignment, removed gap below tabs */}
          <div className="bg-surface-secondary border-border-subtle border-b">
            <TabsList className="flex h-12 w-full rounded-none bg-transparent p-0">
              {tabs.map((tab) => (
                <TabsTrigger
                  key={tab.value}
                  value={tab.value}
                  className={`text-text-secondary hover:bg-surface-hover h-full flex-1 rounded-none border-b-2 border-transparent px-4 py-0 text-sm font-medium transition-all duration-200 hover:text-white ${
                    activeTab === tab.value
                      ? "tab-gradient-active border-b-accent-coral text-white"
                      : "bg-surface-secondary"
                  }`}
                >
                  {tab.label}
                </TabsTrigger>
              ))}
            </TabsList>
          </div>

          {/* Tab Content - Removed margin/border gap */}
          <div className="bg-surface-secondary flex-1 overflow-hidden">
            {tabs.map((tab) => (
              <TabsContent
                key={tab.value}
                value={tab.value}
                className="m-0 h-full overflow-auto"
              >
                {list.map(
                  (
                    item: PositioningItem | HighlightItem | SubtitleItem,
                    index: number
                  ) => {
                    const active = mapDic2[activeTab] as keyof ActiveNodeVal;
                    return (
                      <div className="space-y-3 px-4 py-2" key={index}>
                        <SegmentCard
                          key={item.id}
                          index={index}
                          item={item}
                          active={activeNode[active]}
                          activeTab={activeTab}
                          guideDataItem={guideDataItem}
                        />
                      </div>
                    );
                  }
                )}
              </TabsContent>
            ))}
          </div>
        </Tabs>
      </div>
    </div>
  );
};

export default Panel;
