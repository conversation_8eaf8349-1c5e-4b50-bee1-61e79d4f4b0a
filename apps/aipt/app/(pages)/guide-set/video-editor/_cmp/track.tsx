import emitter from "@/lib/emitter";
import GrowSvg from "@/public/grow.svg";
import ShrinkSvg from "@/public/shrink.svg";
import { useComputed } from "@preact-signals/safe-react";
import {
  DefaultNotionColorMap,
  NotionTypes,
} from "@repo/core/components/notion/types";
import { cloneDeep } from "lodash";
import { useEffect, useRef, useState } from "react";
import {
  generateTimeTicks,
  getItemStartPos,
  getItemWidthByFrame,
  timeFormat,
} from "../_helper";
import { useVideoEditor } from "../_helper/context";

const Track = () => {
  const {
    guideData,
    activeNode,
    playerRef,
    currentTimeSignal,
    updateGuideData,
  } = useVideoEditor();
  const {
    avatar: { fps, durationInFrames },
    content,
  } = guideData!;
  // 根据总帧数、帧率来计算总时长
  const duration = durationInFrames / fps;

  const timelineRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // 缩放比例：1 = 100%, 2 = 200%, ..., 10 = 1000%
  // 默认为100%，表示刚好占满屏幕，没有滚动
  const [timelineScale, setTimelineScale] = useState(1);

  // 基础宽度比例，用于计算100%时的每秒像素数
  const [basePixelsPerSecond, setBasePixelsPerSecond] = useState(50);

  useEffect(() => {
    const calculateBaseScale = () => {
      if (containerRef.current && duration > 0) {
        const containerWidth = containerRef.current.offsetWidth;
        // 计算100%时每秒应该占用多少像素，使时间轴刚好填满容器
        const pixelsPerSecond = containerWidth / duration;
        setBasePixelsPerSecond(pixelsPerSecond);
      }
    };

    calculateBaseScale();

    // 监听窗口大小变化
    const handleResize = () => {
      calculateBaseScale();
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, [duration]);

  // 计算点击位置对应的item
  const getClickedItem = (clickX: number, clickY: number) => {
    const timeline = timelineRef.current;
    if (!timeline || !guideData) return null;

    const rect = timeline.getBoundingClientRect();
    const pos = (clickX - rect.left) / rect.width;
    const clickFrame = Math.round(pos * guideData.avatar.durationInFrames);

    // 根据Y坐标判断点击的是哪个轨道
    const relativeY = clickY - rect.top;
    const timelineHeaderHeight = 26; // h-7 = 28px
    const trackHeight = 32; // h-8 = 24px

    if (relativeY < timelineHeaderHeight) {
      return null; // 点击在时间轴头部
    }

    const trackIndex = Math.floor(
      (relativeY - timelineHeaderHeight) / trackHeight
    );

    // 检查点击的帧是否在某个item范围内
    const checkItemInRange = (items: any[]) => {
      return items.find(
        (item) => clickFrame >= item.inFrame && clickFrame <= item.outFrame
      );
    };

    switch (trackIndex) {
      case 0: // positioning轨道
        const positioningItem = checkItemInRange(guideData.remark.positionings);
        return positioningItem
          ? { item: positioningItem, type: "positionings" }
          : null;

      case 1: // highlight轨道
        const highlightItem = checkItemInRange(guideData.remark.highlights);
        return highlightItem
          ? { item: highlightItem, type: "highlights" }
          : null;

      case 2: // subtitle轨道
        const subtitleItem = checkItemInRange(guideData.remark.subtitles);
        return subtitleItem ? { item: subtitleItem, type: "subtitles" } : null;

      default:
        return null;
    }
  };

  const handlePointerDown = (e: React.PointerEvent<HTMLDivElement>) => {
    const timeline = timelineRef.current;
    if (!timeline) return;

    const startX = e.clientX;
    const startY = e.clientY;
    let hasMoved = false;

    const handleMove = (moveEvent: PointerEvent) => {
      if (Math.abs(moveEvent.clientX - startX) > 3) {
        hasMoved = true;
      }
    };

    const handleUp = (upEvent: PointerEvent) => {
      document.removeEventListener("pointermove", handleMove);
      document.removeEventListener("pointerup", handleUp);

      // 只有在没有移动的情况下才执行点击逻辑
      if (!hasMoved) {
        const rect = timeline.getBoundingClientRect();
        const pos = (upEvent.clientX - rect.left) / rect.width;
        const newTime = pos * duration;

        const player = playerRef?.current;
        if (player) {
          const frame = Math.round(newTime * fps);
          player.seekTo(frame);
        }

        // 检查是否点击了某个item
        const clickedItem = getClickedItem(startX, startY);
        if (clickedItem) {
          console.log("点击的item:", clickedItem);
          // 这里可以添加选中item的逻辑
          // 例如：setActiveItem(clickedItem);

          emitter.emit("clickTrackItem", clickedItem);
        }
      }
    };

    document.addEventListener("pointermove", handleMove);
    document.addEventListener("pointerup", handleUp);
  };

  // 时间轴整体放大与缩小
  const handleShrinkOrScale = (type: "shrink" | "grow") => {
    if (type === "shrink") {
      setTimelineScale((prev) => {
        // 每次减少100%（即减少1倍），最小为100%（即1倍）
        const newScale = prev - 1;
        return Math.max(1, newScale);
      });
    } else {
      setTimelineScale((prev) => {
        // 每次增加100%（即增加1倍），最大为1000%（即10倍）
        const newScale = prev + 1;
        return Math.min(10, newScale);
      });
    }
  };

  // 根据缩放比例动态计算时间间隔
  const getTimeInterval = () => {
    // 根据缩放比例确定时间间隔
    // 缩放比例越小，间隔越大（刻度越稀疏）
    // 缩放比例越大，间隔越小（刻度越精细）
    if (timelineScale <= 1) return 10; // 100%时，10秒间隔
    if (timelineScale <= 2) return 5; // 200%时，5秒间隔
    if (timelineScale <= 4) return 3; // 400%时，3秒间隔
    if (timelineScale <= 6) return 2; // 600%时，2秒间隔
    return 1; // 更大时，1秒间隔
  };

  const timeInterval = getTimeInterval();
  const timeTicks = generateTimeTicks(duration, timeInterval);

  // 计算时间轴实际宽度
  const timelineWidth = duration * basePixelsPerSecond * timelineScale; // 每秒基础像素数 * 缩放比例

  // 过滤掉最后一个刻度，避免超出边界
  const filteredTimeTicks = timeTicks.filter(
    (tick, index) => index < timeTicks.length - 1
  );

  const percentSignal = useComputed(() =>
    duration > 0 ? Math.min(currentTimeSignal.value / duration, 1) : 0
  );

  const currentTimeStr = useComputed(() =>
    timeFormat(Math.floor(currentTimeSignal.value))
  );

  // 处理Playhead拖拽
  const handlePlayheadDragStart = (e: React.PointerEvent) => {
    const timeline = timelineRef.current;
    if (!timeline) return;

    let hasDragged = false;
    const startX = e.clientX;

    const handleDrag = (moveEvent: PointerEvent) => {
      // 只有在实际移动时才阻止默认行为和设置拖动状态
      if (!hasDragged && Math.abs(moveEvent.clientX - startX) > 3) {
        hasDragged = true;
        moveEvent.preventDefault();
      }

      if (!hasDragged) return;

      const rect = timeline.getBoundingClientRect();
      // 计算拖拽位置相对于时间轴的百分比
      let pos = (moveEvent.clientX - rect.left) / rect.width;
      // 限制在0-1范围内
      pos = Math.max(0, Math.min(1, pos));
      const newTime = pos * duration;

      // 更新播放器位置
      const player = playerRef?.current;
      if (player) {
        const frame = Math.round(newTime * fps);
        player.seekTo(frame);
      }
    };

    const handleDragEnd = () => {
      document.removeEventListener("pointermove", handleDrag);
      document.removeEventListener("pointerup", handleDragEnd);
    };

    document.addEventListener("pointermove", handleDrag);
    document.addEventListener("pointerup", handleDragEnd);
  };

  // 在文件顶部添加拖动状态
  const [dragState, setDragState] = useState<{
    isDragging: boolean;
    itemId: string | number;
    type: "move" | "resizeLeft" | "resizeRight";
    itemType: "positioning" | "highlight" | "subtitle";
    initialX: number;
    initialInFrame: number;
    initialOutFrame: number;
  } | null>(null);

  // 跟踪最近拖动的项目，用于保持高层级
  const [lastDraggedItemId, setLastDraggedItemId] = useState<
    string | number | null
  >(null);

  // 通用的拖动处理函数
  const handleItemDragStart = (
    e: React.PointerEvent,
    item: any,
    type: "move" | "resizeLeft" | "resizeRight",
    itemType: "positioning" | "highlight" | "subtitle"
  ) => {
    // 对于调整手柄，立即阻止传播
    if (type === "resizeLeft" || type === "resizeRight") {
      e.stopPropagation();
    }

    const timeline = timelineRef.current;
    if (!timeline) return;

    let hasDragged = false;
    const startX = e.clientX;

    // 用于跟踪当前实际位置的变量
    let currentInFrame = item.inFrame;
    let currentOutFrame = item.outFrame;

    // 保存初始状态到局部变量，而不是依赖状态
    const initialState = {
      isDragging: true,
      itemId: item.id,
      type,
      itemType,
      initialX: e.clientX,
      initialInFrame: item.inFrame,
      initialOutFrame: item.outFrame,
    };

    const handleDrag = (moveEvent: PointerEvent) => {
      // 只有在实际移动时才阻止默认行为和设置拖动状态
      if (!hasDragged && Math.abs(moveEvent.clientX - startX) > 3) {
        hasDragged = true;
        moveEvent.preventDefault();
        // 只有在真正开始拖动时才设置状态，并清除之前的高层级项目
        setLastDraggedItemId(null);
        setDragState(initialState);
      }

      if (!hasDragged) return;

      if (!timeline || !guideData) return;

      const rect = timeline.getBoundingClientRect();
      const { durationInFrames } = guideData.avatar;

      // 使用局部变量而不是状态
      const deltaX = moveEvent.clientX - initialState.initialX;
      const framePerPixel = durationInFrames / rect.width;
      const frameDelta = Math.round(deltaX * framePerPixel);

      const newGuideData = cloneDeep(guideData);

      // 根据类型找到当前拖动的项
      let targetItem: any = null;
      if (itemType === "positioning") {
        targetItem = newGuideData.remark.positionings.find(
          (i) => i.id === initialState.itemId
        );
      } else if (itemType === "highlight") {
        targetItem = newGuideData.remark.highlights.find(
          (i) => i.id === initialState.itemId
        );
      } else if (itemType === "subtitle") {
        targetItem = newGuideData.remark.subtitles.find(
          (i) => i.id === initialState.itemId
        );
      }

      if (!targetItem) return;

      if (initialState.type === "move") {
        // 整体移动
        const newInFrame = Math.max(
          0,
          initialState.initialInFrame + frameDelta
        );
        const duration =
          initialState.initialOutFrame - initialState.initialInFrame;
        const newOutFrame = Math.min(durationInFrames, newInFrame + duration);

        if (newOutFrame > newInFrame) {
          targetItem.inFrame = newInFrame;
          targetItem.outFrame = newOutFrame;
          // 更新当前位置跟踪变量
          currentInFrame = newInFrame;
          currentOutFrame = newOutFrame;
        }
      } else if (initialState.type === "resizeLeft") {
        // 调整左边界
        const newInFrame = Math.max(
          0,
          initialState.initialInFrame + frameDelta
        );
        if (newInFrame < targetItem.outFrame - 1) {
          targetItem.inFrame = newInFrame;
          // 更新当前位置跟踪变量
          currentInFrame = newInFrame;
        }
      } else if (initialState.type === "resizeRight") {
        // 调整右边界
        const newOutFrame = Math.min(
          durationInFrames,
          initialState.initialOutFrame + frameDelta
        );
        if (newOutFrame > targetItem.inFrame + 1) {
          targetItem.outFrame = newOutFrame;
          // 更新当前位置跟踪变量
          currentOutFrame = newOutFrame;
        }
      }

      // 更新原始数据
      if (itemType === "positioning" || itemType === "highlight") {
        // 更新 content 中的数据
        // 如果targetItem是block的子项，则更新block的inFrame和outFrame
        if (targetItem.blockPid) {
          // 找到block元素
          const blockItem = newGuideData.content.find(
            (i) => i.id === targetItem.blockPid
          );
          blockItem?.content.forEach((it) => {
            // @ts-expect-error
            if (it.id === targetItem.id) {
              it.inFrame = targetItem.inFrame;
              it.outFrame = targetItem.outFrame;
            } else if (it.content) {
              // @ts-expect-error
              it.content.forEach((it2: any) => {
                if (it2.id === targetItem.id) {
                  it2.inFrame = targetItem.inFrame;
                  it2.outFrame = targetItem.outFrame;
                  if (itemType === "highlight") {
                    it2.notation.inFrame = targetItem.inFrame;
                    it2.notation.outFrame = targetItem.outFrame;
                  }
                }
              });
            }
          });

          if (blockItem) {
            // 获取blockItem内最小的inFrame 重新计算到外部block的inFrame和outFrame
            const minInFrame = Math.min(
              ...blockItem.content.map((it) => it.inFrame || 0)
            );
            const maxOutFrame = Math.max(
              ...blockItem.content.map((it) => it.outFrame || 0)
            );
            blockItem.inFrame = minInFrame;
            blockItem.outFrame = maxOutFrame;
          }
        } else {
          newGuideData.content.forEach((it) => {
            if (it.id === targetItem.id) {
              it.inFrame = targetItem.inFrame;
              it.outFrame = targetItem.outFrame;
            } else if (it.content) {
              it.content.forEach((it2: any) => {
                if (it2.id === targetItem.id) {
                  it2.inFrame = targetItem.inFrame;
                  it2.outFrame = targetItem.outFrame;
                  if (itemType === "highlight") {
                    it2.notation.inFrame = targetItem.inFrame;
                    it2.notation.outFrame = targetItem.outFrame;
                  }
                }
              });
            }
          });
        }
      } else if (itemType === "subtitle") {
        // 更新 subtitles 中的数据
        const subtitleIndex = newGuideData.subtitles.findIndex(
          (sub) => sub.id === targetItem.id
        );
        if (subtitleIndex !== -1 && newGuideData.subtitles[subtitleIndex]) {
          newGuideData.subtitles[subtitleIndex].inFrame = targetItem.inFrame;
          newGuideData.subtitles[subtitleIndex].outFrame = targetItem.outFrame;
        }
      }

      updateGuideData(newGuideData);
    };

    const handleDragEnd = () => {
      console.log(
        "handleDragEnd called, hasDragged:",
        hasDragged,
        "initialState:",
        initialState
      );

      // 执行吸附逻辑（只有在真正拖动时才执行，避免点击时的位置偏移）
      // 注意：highlights不参与吸附特性
      if (guideData && hasDragged && initialState.itemType !== "highlight") {
        // 执行自动吸附逻辑
        const snapDistance = 20; // 吸附距离：20帧

        // 使用当前跟踪的位置而不是从guideData中获取
        const targetItem = {
          id: initialState.itemId,
          inFrame: currentInFrame,
          outFrame: currentOutFrame,
        };

        console.log("Using current tracked position:", targetItem);

        // 收集所有轨道中的项目（除了当前拖动的项目）
        // 注意：highlights不参与吸附特性
        const allItems: any[] = [
          ...guideData.remark.positionings.filter(
            (i: any) => i.id !== initialState.itemId
          ),
          // ...guideData.remark.highlights.filter(
          //   (i: any) => i.id !== initialState.itemId
          // ),
          ...guideData.remark.subtitles.filter(
            (i: any) => i.id !== initialState.itemId
          ),
        ];

        console.log(11222, guideData.remark.subtitles);

        if (targetItem) {
          console.log(
            "targetItem found:",
            targetItem,
            "type:",
            initialState.type
          );
          let hasSnapped = false;

          if (initialState.type === "resizeRight") {
            // // 右边界调整：吸附到下一个项目的开始帧
            // const nextItems = allItems
            //   .filter((item) => item.inFrame > targetItem.outFrame)
            //   .sort((a, b) => a.inFrame - b.inFrame);
            // console.log("resizeRight - nextItems:", nextItems);
            // if (nextItems.length > 0) {
            //   const nextItem = nextItems[0];
            //   const distance = nextItem.inFrame - targetItem.outFrame;
            //   console.log(
            //     "resizeRight - distance:",
            //     distance,
            //     "snapDistance:",
            //     snapDistance
            //   );
            //   // 只有在距离大于0且小于等于120帧时才吸附
            //   if (distance > 0 && distance <= snapDistance) {
            //     console.log("resizeRight - snapping!");
            //     targetItem.outFrame = nextItem.inFrame;
            //     hasSnapped = true;
            //   }
            // }
          } else if (initialState.type === "resizeLeft") {
            // 左边界调整：吸附到前一个项目的结束帧
            const prevItems = allItems
              .filter((item) => item.outFrame < targetItem.inFrame)
              .sort((a, b) => b.outFrame - a.outFrame);

            console.log("resizeLeft - prevItems:", prevItems);
            if (prevItems.length > 0) {
              const prevItem = prevItems[0];
              const distance = targetItem.inFrame - prevItem.outFrame;
              console.log(
                "resizeLeft - distance:",
                distance,
                "snapDistance:",
                snapDistance
              );

              // 只有在距离大于0且小于等于120帧时才吸附
              if (distance > 0 && distance <= snapDistance) {
                console.log("resizeLeft - snapping!");
                targetItem.inFrame = prevItem.outFrame;
                hasSnapped = true;
              }
            }
          } else if (initialState.type === "move") {
            console.log("move - checking snap for move operation");
            // 整体移动：比较左右两边距离，选择更近的进行吸附

            // 检查左边（前一个项目的结束帧）
            const prevItems = allItems
              .filter((item) => item.outFrame < targetItem.inFrame)
              .sort((a, b) => b.outFrame - a.outFrame);

            // 检查右边（下一个项目的开始帧）
            const nextItems = allItems
              .filter((item) => item.inFrame > targetItem.outFrame)
              .sort((a, b) => a.inFrame - b.inFrame);

            let leftDistance = Infinity;
            let rightDistance = Infinity;
            let leftItem = null;
            let rightItem = null;

            // 计算左边距离
            if (prevItems.length > 0) {
              leftItem = prevItems[0];
              leftDistance = targetItem.inFrame - leftItem.outFrame;
              console.log("move - left distance:", leftDistance);
            }

            // 计算右边距离
            if (nextItems.length > 0) {
              rightItem = nextItems[0];
              rightDistance = rightItem.inFrame - targetItem.outFrame;
              console.log("move - right distance:", rightDistance);
            }

            // 选择距离更近且在吸附范围内的进行吸附
            if (
              leftDistance > 0 &&
              leftDistance <= snapDistance &&
              rightDistance > 0 &&
              rightDistance <= snapDistance
            ) {
              // 两边都可以吸附，选择距离更近的
              if (leftDistance <= rightDistance) {
                console.log("move - snapping to left item (closer)!");
                const adjustment = leftItem.outFrame - targetItem.inFrame;
                targetItem.inFrame = leftItem.outFrame;
                targetItem.outFrame += adjustment;
                hasSnapped = true;
              } else {
                console.log("move - snapping to right item (closer)!");
                const adjustment = rightItem.inFrame - targetItem.outFrame;
                targetItem.outFrame = rightItem.inFrame;
                targetItem.inFrame += adjustment;
                hasSnapped = true;
              }
            } else if (leftDistance > 0 && leftDistance <= snapDistance) {
              // 只有左边可以吸附
              console.log("move - snapping to left item!");
              const adjustment = leftItem.outFrame - targetItem.inFrame;
              targetItem.inFrame = leftItem.outFrame;
              targetItem.outFrame += adjustment;
              hasSnapped = true;
            } else if (rightDistance > 0 && rightDistance <= snapDistance) {
              // 只有右边可以吸附
              console.log("move - snapping to right item!");
              const adjustment = rightItem.inFrame - targetItem.outFrame;
              targetItem.outFrame = rightItem.inFrame;
              targetItem.inFrame += adjustment;
              hasSnapped = true;
            }
          }

          // 如果发生了吸附，更新原始数据
          if (hasSnapped) {
            console.log("Snap occurred! Updating data...");
            // 只有在需要吸附时才创建新的数据副本
            const newGuideData = cloneDeep(guideData);

            // 更新吸附后的目标项目
            if (initialState.itemType === "positioning") {
              const targetInRemark = newGuideData.remark.positionings.find(
                (i: any) => i.id === initialState.itemId
              );
              if (targetInRemark) {
                targetInRemark.inFrame = targetItem.inFrame;
                targetInRemark.outFrame = targetItem.outFrame;
              }
            } else if (initialState.itemType === "subtitle") {
              const targetInRemark = newGuideData.remark.subtitles.find(
                (i: any) => i.id === initialState.itemId
              );
              if (targetInRemark) {
                targetInRemark.inFrame = targetItem.inFrame;
                targetInRemark.outFrame = targetItem.outFrame;
              }
            }

            // 更新原始数据结构
            if (initialState.itemType === "positioning") {
              newGuideData.content.forEach((it) => {
                if (it.id === targetItem.id) {
                  it.inFrame = targetItem.inFrame;
                  it.outFrame = targetItem.outFrame;
                } else if (it.content) {
                  it.content.forEach((it2: any) => {
                    if (it2.id === targetItem.id) {
                      it2.inFrame = targetItem.inFrame;
                      it2.outFrame = targetItem.outFrame;
                    }
                  });
                }
              });
            } else if (initialState.itemType === "subtitle") {
              const subtitleIndex = newGuideData.subtitles.findIndex(
                (sub) => sub.id === targetItem.id
              );
              if (
                subtitleIndex !== -1 &&
                newGuideData.subtitles[subtitleIndex]
              ) {
                newGuideData.subtitles[subtitleIndex].inFrame =
                  targetItem.inFrame;
                newGuideData.subtitles[subtitleIndex].outFrame =
                  targetItem.outFrame;
              }
            }

            updateGuideData(newGuideData);
          }
        }
      }

      // 只有在真正拖动时才清除状态，并设置最近拖动的项目
      if (hasDragged) {
        setLastDraggedItemId(initialState.itemId);
        setDragState(null);
      }
      document.removeEventListener("pointermove", handleDrag);
      document.removeEventListener("pointerup", handleDragEnd);
    };

    document.addEventListener("pointermove", handleDrag);
    document.addEventListener("pointerup", handleDragEnd);
  };

  // 为了向后兼容，保留原来的函数名
  const handlePositioningDragStart = (
    e: React.PointerEvent,
    item: any,
    type: "move" | "resizeLeft" | "resizeRight"
  ) => {
    handleItemDragStart(e, item, type, "positioning");
  };
  //-----------------

  // 在组件内添加自动滚动逻辑
  useEffect(() => {
    if (!containerRef.current || !timelineRef.current) return;

    const container = containerRef.current;

    // 计算播放头的绝对位置
    const playheadPosition = percentSignal.value * timelineWidth;

    // 获取容器的滚动信息
    const containerWidth = container.clientWidth;
    const scrollLeft = container.scrollLeft;
    const scrollRight = scrollLeft + containerWidth;

    // 检查是否需要滚动
    if (playheadPosition < scrollLeft) {
      // 播放头在左侧边界外，向左平移一个容器宽度
      container.scrollLeft = Math.max(0, scrollLeft - containerWidth);
    } else if (playheadPosition > scrollRight) {
      // 播放头在右侧边界外，向右平移一个容器宽度
      container.scrollLeft = scrollLeft + containerWidth;
    }
  }, [percentSignal.value, timelineWidth]);

  return (
    <div className="track flex w-full text-xs">
      {/* 时间轴容器，将其设置为定位，方便播放指针的定位 */}
      <div
        className="lables bg-surface-elevated w-16 shrink-0 border-r border-gray-700 text-center text-gray-400"
        style={{ userSelect: "none" }}
      >
        <div className="flex h-7 items-center justify-center border border-gray-700 text-sm">
          <button
            onClick={() => handleShrinkOrScale("shrink")}
            className="flex cursor-pointer items-center justify-center rounded p-1 transition-colors hover:bg-gray-600"
            disabled={timelineScale <= 1}
            style={{ opacity: timelineScale <= 1 ? 0.5 : 1 }}
          >
            <ShrinkSvg
              color="#71717a"
              style={{ width: "16px", height: "16px" }}
            />
          </button>
          {/* <span className="mx-1 text-xs text-gray-300">
            {Math.round(timelineScale * 100)}%
          </span> */}
          <button
            onClick={() => handleShrinkOrScale("grow")}
            className="flex cursor-pointer items-center justify-center rounded p-1 transition-colors hover:bg-gray-600"
            disabled={timelineScale >= 10}
            style={{ opacity: timelineScale >= 10 ? 0.5 : 1 }}
          >
            <GrowSvg
              color="#71717a"
              style={{ width: "16px", height: "16px" }}
            />
          </button>
        </div>
        <div className="flex h-8 items-center justify-center border-b border-gray-700">
          定位
        </div>
        <div className="flex h-8 items-center justify-center border-b border-gray-700">
          高亮
        </div>
        <div className="flex h-8 items-center justify-center border-b border-gray-700">
          字幕
        </div>
      </div>
      <div ref={containerRef} className="w-0 flex-1 overflow-x-auto text-xs">
        <div
          className="time-line relative"
          ref={timelineRef}
          onPointerDown={handlePointerDown}
          style={{
            cursor: "pointer",
            width: `${timelineWidth}px`,
            userSelect: "none",
          }}
        >
          <div className="bg-surface-elevated relative flex h-7 border-b border-t border-gray-700">
            {filteredTimeTicks.map((tick) => (
              <div
                key={tick.time}
                className="not-first-of-type:border-l border-border-muted absolute top-0 h-full"
                style={{ left: `${tick.percent}%` }}
              />
            ))}
            {filteredTimeTicks.map((tick) => (
              <div
                key={`label-${tick.time}`}
                className="absolute left-1 top-1 text-xs text-[#71717a]"
                style={{
                  left: `calc(${tick.percent}% + 4px)`,
                  userSelect: "none",
                }}
              >
                {tick.label}
              </div>
            ))}
          </div>
          <div className="flex h-8 border-b border-gray-800 bg-black">
            {guideData?.remark.positionings
              .filter((item) => item.tag !== "block")
              .map((item) => (
                <div
                  key={item.id}
                  title={item.contentText}
                  className="absolute mt-1 flex h-6 cursor-grab items-center truncate rounded bg-gray-600/80 px-2 text-white"
                  style={{
                    width: `${getItemWidthByFrame(item.inFrame, item.outFrame, durationInFrames) * 100}%`,
                    left: `${getItemStartPos(item.inFrame, durationInFrames) * 100}%`,
                    border:
                      activeNode.positioning === item.id
                        ? "2px solid white"
                        : "none",
                    // 当前拖动的项和最近拖动的项设置最高层级
                    zIndex:
                      dragState?.itemId === item.id ||
                      lastDraggedItemId === item.id
                        ? 999
                        : 1,
                    userSelect: "none",
                  }}
                  onPointerDown={(e) =>
                    handlePositioningDragStart(e, item, "move")
                  }
                >
                  <span className="w-full truncate">{item.contentText}</span>
                  {/* 左侧调整手柄 */}
                  <div
                    className="absolute left-0 top-0 h-full w-2 cursor-col-resize"
                    onPointerDown={(e) => {
                      handlePositioningDragStart(e, item, "resizeLeft");
                    }}
                  />
                  {/* 右侧调整手柄 */}
                  <div
                    className="absolute right-0 top-0 h-full w-2 cursor-col-resize"
                    onPointerDown={(e) => {
                      handlePositioningDragStart(e, item, "resizeRight");
                    }}
                  />
                </div>
              ))}
          </div>
          <div className="flex h-8 border-b border-gray-800 bg-black">
            {guideData?.remark.highlights.map((item) => (
              <div
                key={item.id}
                title={item.contentText}
                className="absolute mt-1 flex h-6 cursor-grab items-center truncate rounded p-2 text-white"
                style={{
                  width: `${getItemWidthByFrame(item.inFrame, item.outFrame, durationInFrames) * 100}%`,

                  backgroundColor:
                    DefaultNotionColorMap[item?.notation?.type as NotionTypes],

                  left: `${getItemStartPos(item.inFrame, durationInFrames) * 100}%`,
                  border:
                    activeNode.highlight === item.id
                      ? "2px solid white"
                      : "none",
                  // 当前拖动的项和最近拖动的项设置最高层级
                  zIndex:
                    dragState?.itemId === item.id ||
                    lastDraggedItemId === item.id
                      ? 999
                      : 1,
                  userSelect: "none",
                }}
                onPointerDown={(e) =>
                  handleItemDragStart(e, item, "move", "highlight")
                }
              >
                <span className="w-full truncate">{item.contentText}</span>
                {/* 左侧调整手柄 */}
                <div
                  className="absolute left-0 top-0 h-full w-2 cursor-col-resize"
                  onPointerDown={(e) => {
                    handleItemDragStart(e, item, "resizeLeft", "highlight");
                  }}
                />
                {/* 右侧调整手柄 */}
                <div
                  className="absolute right-0 top-0 h-full w-2 cursor-col-resize"
                  onPointerDown={(e) => {
                    handleItemDragStart(e, item, "resizeRight", "highlight");
                  }}
                />
              </div>
            ))}
          </div>
          <div className="flex h-8 border-b border-gray-700 bg-black">
            {guideData?.remark.subtitles.map((item) => (
              <div
                key={item.id}
                title={item.text}
                className="bg-accent-blue-purple/80 absolute mt-1 flex h-6 cursor-grab items-center rounded px-2 text-white"
                style={{
                  width: `${getItemWidthByFrame(item.inFrame, item.outFrame, durationInFrames) * 100}%`,
                  left: `${getItemStartPos(item.inFrame, durationInFrames) * 100}%`,
                  border:
                    activeNode.subtitle === String(item.id)
                      ? "2px solid white"
                      : "none",
                  // 当前拖动的项和最近拖动的项设置最高层级
                  zIndex:
                    dragState?.itemId === item.id ||
                    lastDraggedItemId === item.id
                      ? 999
                      : 1,
                  userSelect: "none",
                }}
                onPointerDown={(e) =>
                  handleItemDragStart(e, item, "move", "subtitle")
                }
              >
                <span className="w-full truncate">{item.contentText}</span>
                {/* 左侧调整手柄 */}
                <div
                  className="absolute left-0 top-0 h-full w-2 cursor-col-resize"
                  onPointerDown={(e) => {
                    handleItemDragStart(e, item, "resizeLeft", "subtitle");
                  }}
                />
                {/* 右侧调整手柄 */}
                <div
                  className="absolute right-0 top-0 h-full w-2 cursor-col-resize"
                  onPointerDown={(e) => {
                    handleItemDragStart(e, item, "resizeRight", "subtitle");
                  }}
                />
              </div>
            ))}
          </div>
          {/* Playhead */}
          <div
            className="group absolute bottom-0 top-0 z-30 cursor-ew-resize"
            style={{
              left: `calc(${percentSignal.value * 100}% - 8px)`,
              width: "16px",
            }}
            onPointerDown={handlePlayheadDragStart}
          >
            {/* 实际显示的线 */}
            <div className="absolute bottom-0 left-1/2 top-0 w-0.5 -translate-x-1/2 bg-red-500 shadow-lg" />
            {/* 拖动手柄 */}
            <div className="absolute -top-1.5 left-1/2 h-3 w-3 -translate-x-1/2 rounded-full bg-red-500 shadow-lg transition-transform group-hover:scale-125" />
            {/* 时间显示 */}
            <div
              className="pointer-events-none absolute left-1/2 top-2 -translate-x-1/2 transform whitespace-nowrap rounded bg-red-500 px-1 text-xs text-white"
              style={{ userSelect: "none" }}
            >
              {currentTimeStr}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Track;
