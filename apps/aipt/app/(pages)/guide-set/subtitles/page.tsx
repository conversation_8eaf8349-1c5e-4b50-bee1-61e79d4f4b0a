"use client";

import fetcher, { post } from "@/app/utils/fetcher";
import { frameToTime, timeToFrame } from "@/app/utils/frameToTime";
import { GuideWidget } from "@/types/guide-widget";
import { GuideWidgetData } from "@repo/core/types/data/widget-guide";
import { toast } from "@repo/ui/components/toast";
import { Button, Input, Spin } from "antd";
import { useSearchParams } from "next/navigation";
import { Suspense, useEffect, useState } from "react";
import useSWR from "swr";
import useSWRMutation from "swr/mutation";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@repo/ui/components/popover";

// 字幕项类型，包含唯一id、起止时间、字幕内容
// start/end 格式为 mm:ss:SSS
// id 用于后续唯一标识和后端同步
type SubtitleItem = {
  id: number;
  start: string; // 开始时间 00:00:000
  end: string; // 结束时间 00:00:000
  text: string; // 字幕内容
};

// 时间格式校验正则
const timeReg = /^\d{2}:\d{2}:\d{3}$/;

export default function SubtitlesEditPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <SubtitlesEditPageContent />
    </Suspense>
  );
}

function SubtitlesEditPageContent() {
  // 获取 url 参数
  const searchParams = useSearchParams();
  const guideWidgetSetId = searchParams.get("id");
  const guideWidgetId = searchParams.get("guideWidgetId");
  const guideWidgetSetName = searchParams.get("guideWidgetSetName");

  // 字幕编辑区状态
  const [subtitles, setSubtitles] = useState<SubtitleItem[]>([]);
  // 当前稿件的原始数据（含fps等信息）
  const [guideDataState, setGuideDataState] = useState<GuideWidgetData | null>(
    null
  );

  // 获取稿件详情数据
  const { data, isLoading } = useSWR<GuideWidget>(
    () =>
      guideWidgetId
        ? `/api/v1/guideWidget/info?guideWidgetId=${guideWidgetId}&guideWidgetSetId=${guideWidgetSetId}`
        : null,
    fetcher
  );

  // 解析稿件数据，初始化字幕编辑区
  useEffect(() => {
    if (data && data.videoJson) {
      let guideData: GuideWidgetData | null = null;
      if (typeof data.videoJson === "string") {
        try {
          guideData = JSON.parse(data.videoJson) as GuideWidgetData;
        } catch {
          guideData = null;
        }
      } else {
        guideData = data.videoJson;
      }
      if (guideData && Array.isArray(guideData.subtitles)) {
        setSubtitles(
          guideData.subtitles.map((sub) => ({
            ...sub,
            // 帧数转时间字符串，便于编辑
            start: frameToTime(sub.inFrame, guideData.avatar.fps),
            end: frameToTime(sub.outFrame, guideData.avatar.fps),
            text: sub.text,
          }))
        );
        setGuideDataState(guideData);
      }
    }
  }, [data]);

  // 校验字幕内容，逐句检查格式、时序、长度
  const validateSubtitles = (subs: SubtitleItem[]) => {
    for (let i = 0; i < subs.length; i++) {
      const sub = subs[i];
      if (!sub) continue; // 安全检查，虽然在循环条件下不应该发生

      const { start, end } = sub;
      // 1. 字数校验
      // if (text.length > 30) {
      //   return `第${i + 1}句内容超过30字符`;
      // }
      // 2. 时间格式校验
      if (!timeReg.test(start) || !timeReg.test(end)) {
        return `第${i + 1}句时间格式错误，应为mm:ss:SSS`;
      }
      // 3. 时间先后校验
      if (start >= end) {
        return `第${i + 1}句开始时间不能大于等于结束时间`;
      }
      // 4. 与前后句时间重叠校验
      const prevSub = subs[i - 1];
      if (i > 0 && prevSub && start < prevSub.end) {
        return `第${i + 1}句时间与前一句重叠`;
      }
    }
    return null;
  };

  // 提交接口，保存字幕内容
  const { trigger: submitList, isMutating } = useSWRMutation(
    "/api/v1/guideWidget/save/subtitles",
    post
  );

  // 提交处理，校验通过后转为帧数并提交
  const handleSubmit = async () => {
    const err = validateSubtitles(subtitles);
    if (err) {
      toast.error(err);
      return;
    }

    // 将编辑区内容转为后端需要的帧数格式
    const newSubtitles = subtitles.map((sub) => ({
      id: sub.id,
      inFrame: timeToFrame(sub.start, guideDataState?.avatar.fps ?? 24),
      outFrame: timeToFrame(sub.end, guideDataState?.avatar.fps ?? 24),
      text: sub.text,
    }));

    // 构造新的稿件数据
    const newGuideData = {
      ...guideDataState,
      subtitles: newSubtitles,
    };

    const videoJson = JSON.stringify(newGuideData);

    try {
      // 调用保存接口
      await submitList({
        guideWidgetId: Number(guideWidgetId),
        guideWidgetSetId: Number(guideWidgetSetId),
        videoJson,
      });
      toast.success("已提交，请返回稿件页面刷新～");
    } catch (error) {
      toast.error("提交失败，请重试");
      console.error(error);
    }
  };

  // 表单项变更，受控更新字幕内容
  const handleChange = (
    idx: number,
    key: keyof SubtitleItem,
    value: string
  ) => {
    const newSubs = subtitles.map((item, i) =>
      i === idx ? { ...item, [key]: value } : item
    );
    setSubtitles(newSubs);
  };

  const handleDelete = (idx: number) => {
    const newSubs = subtitles.filter((_, i) => i !== idx);
    setSubtitles(newSubs);
  };

  // 页面渲染
  return (
    <Spin
      spinning={isLoading}
      wrapperClassName="min-h-screen flex items-center justify-center"
    >
      <div className="fixed inset-0 flex h-screen w-screen flex-col overflow-hidden bg-gray-100">
        {/* 顶部导航栏 */}
        <header className="flex h-16 flex-none items-center border-b border-gray-200 bg-white px-8">
          <div className="text-lg font-semibold text-gray-700">稿件生产</div>
          <div className="ml-6 text-base text-gray-500">
            课程名称: {guideWidgetSetName}
          </div>
        </header>
        {/* 主体内容区域 */}
        <div className="flex flex-1 overflow-hidden p-8">
          {/* 字幕编辑表单整体居左，宽度适中 */}
          <div className="flex w-full min-w-[600px] flex-col overflow-hidden rounded-xl bg-white shadow-lg">
            {/* 顶部标题和提交按钮，固定在内容区顶部 */}
            <div className="sticky top-0 z-10 flex items-center justify-between border-b border-gray-100 bg-white px-8 pb-4 pt-6">
              <div className="text-lg font-bold text-gray-700">字幕编辑</div>
              <Button
                type="primary"
                onClick={handleSubmit}
                className="px-8"
                loading={isMutating}
              >
                提交
              </Button>
            </div>
            {/* 表头和字幕内容区，滚动时上下有留白 */}
            <div className="max-h-[calc(100%-100px)] flex-1 overflow-hidden px-8">
              {/* 表头固定，随内容滚动始终可见 */}
              <div className="sticky top-0 z-10 mb-2 flex bg-white pb-2 pt-2 text-xs font-medium text-gray-500">
                <div className="w-[220px]">
                  时间区间
                  <br />
                  (mm:ss:SSS)
                </div>
                <div className="flex-1 pl-4">字幕内容</div>
              </div>
              {/* 字幕内容区，内部滚动，底部留白 */}
              <div className="max-h-[calc(100vh-220px)] flex-1 overflow-y-auto pb-16">
                {subtitles.map((item, idx) => (
                  <div
                    key={item.id}
                    className="mb-2 flex items-start border-b border-dashed border-gray-200 py-2 last:border-b-0"
                  >
                    {/* 左侧序号+时间区间 */}
                    <div className="flex w-[260px] min-w-[260px] items-center gap-3">
                      {/* 序号圆圈，固定宽高，数字居中 */}
                      <div className="flex h-8 min-h-8 w-8 min-w-8 items-center justify-center rounded-full bg-blue-500 text-base font-bold text-white">
                        {idx + 1}
                      </div>
                      {/* 时间输入框组，左右对齐 */}
                      <div className="flex flex-1 flex-col">
                        <div className="flex items-center gap-2">
                          <Input
                            style={{ width: 100 }}
                            value={item.start}
                            maxLength={9}
                            onChange={(
                              e: React.ChangeEvent<HTMLInputElement>
                            ) => handleChange(idx, "start", e.target.value)}
                            placeholder="开始"
                            size="small"
                          />
                          <span className="mx-1 text-gray-400">~</span>
                          <Input
                            style={{ width: 100 }}
                            value={item.end}
                            maxLength={9}
                            onChange={(
                              e: React.ChangeEvent<HTMLInputElement>
                            ) => handleChange(idx, "end", e.target.value)}
                            placeholder="结束"
                            size="small"
                          />
                        </div>
                        {/* 时间错误提示，横排显示在时间输入框下方 */}
                        {(() => {
                          const prevSubtitle =
                            idx > 0 ? subtitles[idx - 1] : null;
                          const hasTimeOverlap =
                            prevSubtitle && item.start < prevSubtitle.end;

                          return (
                            (!timeReg.test(item.start) ||
                              !timeReg.test(item.end) ||
                              item.start >= item.end ||
                              hasTimeOverlap) && (
                              <div className="mt-1 whitespace-nowrap text-xs text-red-500">
                                {!timeReg.test(item.start) ||
                                !timeReg.test(item.end)
                                  ? "时间格式应为mm:ss:SSS"
                                  : item.start >= item.end
                                    ? "开始时间不能大于等于结束时间"
                                    : hasTimeOverlap
                                      ? "时间与前一句重叠"
                                      : null}
                              </div>
                            )
                          );
                        })()}
                      </div>
                    </div>
                    {/* 右侧字幕内容输入框，左侧加大间距 */}
                    <div className="flex flex-1 flex-col gap-1 pl-8">
                      <Input.TextArea
                        value={item.text}
                        // maxLength={30}
                        onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) =>
                          handleChange(idx, "text", e.target.value)
                        }
                        // placeholder="请输入字幕内容 (≤30字符)"
                        autoSize={{ minRows: 1, maxRows: 3 }}
                        className="text-sm"
                      />
                      {/* 字数错误提示 */}
                      {/* {item.text.length > 30 && (
                        <div className="mt-1 text-xs text-red-500">
                          不得超过30字符
                        </div>
                      )} */}
                    </div>
                    <div className="flex w-[100px] items-center justify-center">
                      <Popover>
                        <PopoverTrigger asChild>
                          <span className="cursor-pointer text-red-500">
                            删除
                          </span>
                        </PopoverTrigger>
                        <PopoverContent className="relative w-auto p-0">
                          {/* 灰色边框箭头（下层） */}
                          <div className="absolute -top-2 left-1/2 h-0 w-0 -translate-x-1/2 border-x-8 border-b-8 border-x-transparent border-b-gray-300" />
                          {/* 白色箭头（上层） */}
                          <div className="border-x-7 border-b-7 absolute -top-1.5 left-1/2 h-0 w-0 -translate-x-1/2 border-x-transparent border-b-white" />
                          <div
                            className="cursor-pointer p-2 text-sm text-gray-500 hover:text-blue-500"
                            onClick={() => handleDelete(idx)}
                          >
                            确定删除？
                          </div>
                        </PopoverContent>
                      </Popover>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
          {/* 右侧可留空或后续扩展 */}
          <div className="flex-1" />
        </div>
      </div>
    </Spin>
  );
}
