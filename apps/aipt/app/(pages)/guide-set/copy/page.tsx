"use client";

import { Button } from "@/app/components/common/button";
import { post } from "@/app/utils/fetcher";

import { toast } from "@/app/components/common/toast";

import { useState } from "react";
import useSWRMutation from "swr/mutation";

interface CopyLessonResponse {
  guide_widget_set_id: string;
  url: string;
}

export default function Page() {
  const [sourceUrl, setSourceUrl] = useState("");
  const [lessonName, setLessonName] = useState("");
  const [newLessonUrl, setNewLessonUrl] = useState("");
  const [copyToBeta, setCopyToBeta] = useState(false);
  const { trigger: copyLesson, isMutating } = useSWRMutation<
    CopyLessonResponse,
    Error,
    string,
    {
      sourceGuideWidgetSetId: number;
      guideWidgetSetName: string;
      isPhaseBetaTest: number;
    }
  >("/api/v1/guideWidgetSet/copy", post);

  const handleClear = () => {
    setSourceUrl("");
    setLessonName("");
  };

  const handleSubmit = async () => {
    if (!sourceUrl || !lessonName) {
      toast.warning("请填写完整信息");
      return;
    }

    try {
      const urlParams = new URL(sourceUrl);
      const params = urlParams.searchParams;
      const res = await copyLesson({
        sourceGuideWidgetSetId: +(params.get("id") as string),
        guideWidgetSetName: lessonName,
        isPhaseBetaTest: copyToBeta ? 1 : 0,
      });

      toast.success("课程复制成功");
      const url = `${window.location.origin}/guide-set/editor?id=${res.guide_widget_set_id}`;
      setNewLessonUrl(url);
      handleClear();
    } catch (error) {
      toast.error("复制课程失败，请重试");
      console.error(error);
    }
  };

  return (
    <div className="flex min-h-screen flex-col items-center bg-gray-100 p-8">
      <div className="w-full bg-white p-6 shadow-md">
        <p className="mb-4 text-sm font-bold">从已有课程复制</p>
        <p className="mb-4 text-sm text-[#4F46E5]">
          说明：&ldquo;确定&rdquo;后将创建一节新课，新课改动不会影响原始URL。
        </p>

        <div className="mb-4 flex items-center">
          <label className="mr-4 w-[90px] whitespace-nowrap text-sm font-medium text-gray-700">
            复制自URL：
          </label>
          <input
            type="text"
            value={sourceUrl}
            onChange={(e) => setSourceUrl(e.target.value)}
            className="flex-1 rounded-[12px] border border-[#4F46E5] p-2 focus:border-blue-500 focus:outline-none"
            placeholder="请输入您要复制的地址"
          />
        </div>

        <div className="mb-6 flex items-center">
          <label className="mr-4 w-[90px] whitespace-nowrap text-sm font-medium text-gray-700">
            课程名称：
          </label>
          <input
            type="text"
            value={lessonName}
            onChange={(e) => setLessonName(e.target.value)}
            className="flex-1 rounded-[12px] border border-[#4F46E5] p-2 focus:border-blue-500 focus:outline-none"
            placeholder="请输入重命名的课程名称"
          />
        </div>

        <div className="mb-6 flex items-center">
          <label className="mr-4 w-[90px] whitespace-nowrap text-sm font-medium text-gray-700">
            拷贝至内测学段：
          </label>
          <input
            type="checkbox"
            checked={copyToBeta}
            onChange={() => setCopyToBeta((v) => !v)}
            className="beta_copy_switch ml-2 h-5 w-5 rounded accent-[#4F46E5] focus:ring-0"
          />
        </div>

        <div className="flex justify-end gap-4">
          <Button
            type="outline"
            onClick={handleClear}
            className="rounded-md py-2 text-xs text-[#4F46E5]"
          >
            清空
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={isMutating}
            className="rounded-md border-[#4F46E5] bg-[#4F46E5] px-4 py-2 text-xs text-white hover:bg-[#4F46E5]/80 disabled:cursor-not-allowed disabled:opacity-50"
          >
            {isMutating ? "生成中..." : "生成新课程"}
          </Button>
        </div>
        {newLessonUrl && (
          <div className="mt-4 flex items-center gap-2">
            <p className="text-sm text-gray-700">新课程URL：{newLessonUrl}</p>
            <Button
              type="text"
              className="text-primary text-xs"
              onClick={() => {
                navigator.clipboard.writeText(newLessonUrl);
                toast.success("已复制到剪贴板");
              }}
            >
              复制
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}
