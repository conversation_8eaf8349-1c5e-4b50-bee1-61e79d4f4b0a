"use client";

import { Button } from "@/app/components/common/aipt-button";
import { toast } from "@/app/components/common/toast";
import { useState } from "react";

export default function Page() {
  const [sourceUrl, setSourceUrl] = useState("");

  const handleClear = () => {
    setSourceUrl("");
  };

  const handleSubmit = async () => {
    if (!sourceUrl) {
      toast.warning("请填写完整信息");
      return;
    }

    try {
      window.open(sourceUrl, "_blank");
      handleClear();
    } catch (error) {
      toast.error("复制课程失败，请重试");
      console.error(error);
    }
  };

  return (
    <div className="flex min-h-screen flex-col items-center bg-gray-100 p-8">
      <div className="w-full bg-white p-6 shadow-md">
        <p className="mb-4 text-sm font-bold">进入课程</p>
        <p className="mb-4 text-sm text-[#4F46E5]">
          说明：请将课程的圈画地址复制到输入框。
        </p>

        <div className="mb-4 flex items-center">
          <label className="mr-4 w-[90px] whitespace-nowrap text-sm font-medium text-gray-700">
            复制自URL：
          </label>
          <input
            type="text"
            value={sourceUrl}
            onChange={(e) => setSourceUrl(e.target.value)}
            className="flex-1 rounded-[12px] border border-[#4F46E5] p-2 focus:border-blue-500 focus:outline-none"
            placeholder="请输入您要复制的圈画地址"
          />
        </div>

        <div className="flex justify-end gap-4">
          <Button
            type="outline"
            onClick={handleClear}
            className="rounded-md py-2 text-xs text-[#4F46E5]"
          >
            清空
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={!sourceUrl}
            className="rounded-md border-[#4F46E5] bg-[#4F46E5] px-4 py-2 text-xs text-white hover:bg-[#4F46E5]/80 disabled:cursor-not-allowed disabled:opacity-50"
          >
            进入课程
          </Button>
        </div>
      </div>
    </div>
  );
}
