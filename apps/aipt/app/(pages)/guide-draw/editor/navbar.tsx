"use client";

import { But<PERSON> } from "@/app/components/common/aipt-button";
import { toast } from "@/app/components/common/toast";
// import { LocalDraftService } from "@/app/service/local-draft-service";
import { post } from "@/app/utils/fetcher";
import { GuideWidget, RawGuideSet } from "@/types/guide-widget";
// import { GuideWidgetData } from "@repo/core/types/data/widget-guide";
import { cn } from "@repo/ui/lib/utils";
import { CloudUpload } from "lucide-react";
import { FC } from "react";
import useSWRMutation from "swr/mutation";
interface NavbarProps {
  guideWidgetSet: RawGuideSet;
  guideWidgetId: string;
  guideWidgetData?: GuideWidget;
}

export const Navbar: FC<NavbarProps> = ({ guideWidgetSet, guideWidgetId }) => {
  const { guideWidgetSetId, guideWidgetSetName } = guideWidgetSet;

  const { trigger: submitList, isMutating } = useSWRMutation(
    "/api/v1/guideWidgetSet/submit",
    post
  );

  const BtnSave = () => {
    const handleSubmit = async () => {
      try {
        await submitList({
          guideWidgetSetId,
          saveDraw: 1,
        });
        toast.success("保存成功");
      } catch (error: any) {
        toast.error(error?.message || "保存失败，请重试");
      }
    };
    return (
      <Button
        type="primary"
        loading={isMutating}
        className={cn(
          "ml-auto rounded-sm border bg-red-600 text-xs font-bold text-white hover:bg-red-700"
        )}
        onClick={handleSubmit}
        icon={<CloudUpload className="size-4" />}
      >
        提交
      </Button>
    );
  };

  return (
    <nav className="flex h-full w-full flex-row items-center gap-6 border-b border-b-gray-200 shadow-[0_1px_0_0_rgba(0,0,0,0.04)]">
      <div className="text-xl">稿件生产</div>
      <div className="text-sm text-gray-500">
        课程名称: {guideWidgetSetName}
      </div>
      <BtnSave />
    </nav>
  );
};
