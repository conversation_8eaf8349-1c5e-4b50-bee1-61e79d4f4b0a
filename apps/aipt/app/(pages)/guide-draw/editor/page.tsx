"use client";

import { GuideDraw } from "@/app/components/guide-draw";
import { GuideSet } from "@/app/components/guide-set";

import {
  GuideSetProvider,
  useGuideSetContext,
} from "@/app/context/guide-set-context";
import fetcher from "@/app/utils/fetcher";
import { GuideWidget, RawGuideSet } from "@/types/guide-widget";
import { Suspense, useEffect } from "react";
import useS<PERSON> from "swr";
import { Navbar } from "./navbar";

/**
 * part集合编辑器组件
 * 负责渲染和管理part集合的编辑界面
 */
function GuideSetEditor() {
  const {
    guideWidgetSetId,
    guideWidgetId,
    setGuideWidgetId,
    setGuideWidgetIndex,
    guideWidgetSetData,
    setGuideWidgetSetData,
  } = useGuideSetContext();

  // 获取part集合数据
  const {
    data: fetchedGuideWidgetSetData,
    isLoading: isLoadingSet,
    mutate,
  } = useSWR<RawGuideSet>(
    guideWidgetSetId
      ? `/api/v1/guideWidgetSet/info?guideWidgetSetId=${guideWidgetSetId}`
      : null,
    fetcher,
    {
      refreshInterval: 10000, // 每10秒自动刷新一次数据
    }
  );
  // 获取稿件数据
  const { data: guideWidgetData, mutate: mutateGuideWidget } =
    useSWR<GuideWidget>(
      () =>
        guideWidgetId.value && guideWidgetSetData.value?.guideWidgetSetId
          ? `/api/v1/guideWidget/info?guideWidgetId=${guideWidgetId.value}&guideWidgetSetId=${guideWidgetSetData.value?.guideWidgetSetId}`
          : null,

      fetcher
    );

  useEffect(() => {
    // 当获取到part集合数据时，更新状态
    if (fetchedGuideWidgetSetData) {
      setGuideWidgetSetData(fetchedGuideWidgetSetData);
      // 如果没有选中的part组件，默认选中第一个
      if (
        guideWidgetId.value === "" &&
        fetchedGuideWidgetSetData.guideWidgetList.length > 0
      ) {
        setGuideWidgetId(
          fetchedGuideWidgetSetData.guideWidgetList[0]?.guideWidgetId || ""
        );
        setGuideWidgetIndex(0);
      }
    }
  }, [
    fetchedGuideWidgetSetData,
    guideWidgetId,
    setGuideWidgetId,
    setGuideWidgetIndex,
    setGuideWidgetSetData,
  ]);

  // 加载状态处理
  if (isLoadingSet) {
    return <div>加载中...</div>;
  }

  // 无数据状态处理
  if (!guideWidgetSetData.value) {
    return <div>无数据</div>;
  }

  return (
    <div className="fixed inset-0 flex h-screen w-screen flex-col overflow-hidden bg-gray-100">
      {/* 顶部导航栏 - 固定 */}
      <header className="h-20 flex-none bg-white px-3 outline-1 outline-gray-300">
        <Navbar
          guideWidgetSet={guideWidgetSetData.value}
          guideWidgetId={guideWidgetId.value}
          guideWidgetData={guideWidgetData}
        />
      </header>

      {/* 主体内容区域 */}
      <div className="flex flex-1 overflow-hidden">
        {/* 左侧part集合列表 - 独立滚动 */}
        <aside className="mt-0.5 h-full w-[216px] overflow-y-auto border-r-[1px] border-[#E5E7EB] bg-white">
          <GuideSet
            guideWidgetSetId={guideWidgetSetData.value.guideWidgetSetId}
            guideWidgetId={guideWidgetId.value}
            setGuideWidgetId={setGuideWidgetId}
            setGuideWidgetIndex={setGuideWidgetIndex}
            list={guideWidgetSetData.value.guideWidgetList}
            onUpdate={(newList) => {
              setGuideWidgetSetData({
                ...guideWidgetSetData.value!,
                guideWidgetList: newList,
              });
            }}
            refresh={mutate}
          />
        </aside>

        {/* 右侧part编辑区域 - 独立滚动 */}
        <main className="flex-1 overflow-y-auto">
          <GuideDraw
            guideWidgetData={guideWidgetData}
            mutateGuideWidget={mutateGuideWidget}
            refreshSet={mutate}
            guideId={guideWidgetId.value}
            guideSet={guideWidgetSetData.value}
          />
        </main>
      </div>
    </div>
  );
}

/**
 * part集合编辑器页面组件
 * 使用 Suspense 包装编辑器组件，提供加载状态处理
 */
export default function Page() {
  return (
    <Suspense fallback={<div>加载中...</div>}>
      <GuideSetProvider>
        <GuideSetEditor />
      </GuideSetProvider>
    </Suspense>
  );
}
