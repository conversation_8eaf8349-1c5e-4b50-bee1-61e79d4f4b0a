"use client"
  
import { useSearchParams } from 'next/navigation'
import { CourseView } from "@/app/views/preview-course/course-view";
import { Suspense } from 'react';
import { saveTokenFormUrl } from '@/lib/login-helper';
import { Toaster } from "sonner";



const Preview: React.FC = () => {
    saveTokenFormUrl();
    const searchParams = useSearchParams()
    const id = Number(searchParams.get('id'))
    const type = searchParams.get('type')??undefined;
   
    return (
        <div className="bg-white h-screen w-screen">
            <header className="h-16 flex-none bg-white px-3 outline-1 outline-gray-300"> 
                <nav className="flex h-full w-full flex-row items-center gap-6 border-b border-b-gray-200 shadow-[0_1px_0_0_rgba(0,0,0,0.04)]">
                    <div className="text-xl">课程预览</div>
                </nav>
            </header>

            <div className="flex flex-col h-full w-full">
                <div className="lesson_detail_container h-full w-full">
                    <CourseView knowledgeId={id} courseProgressBarProps={{className: 'w-50'}} type={type as "draft" | "official"}/>
                </div>
            </div>
        </div>
    );
};

const PreviewPage = ()=>(
    <Suspense fallback={<div>Loading...</div>}>
        <Preview />
        <Toaster position="top-center"/>
    </Suspense>
)

export default PreviewPage;

