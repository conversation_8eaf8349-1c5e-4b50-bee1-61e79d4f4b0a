import { useEffect, useRef, useState } from "react";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from "@/app/components/ui/form";
import { Input } from "@/app/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/app/components/ui/select";
import { Button } from "@/app/components/ui/button";
import useEnum, { EnumItem } from "@/app/hooks/useEnumManager/useEnumManager";
import { useRequest } from "ahooks";
import { get, post } from "@/lib/course-center-fetcher";
import { toast } from "sonner";
import { mergeArraysByFirstElement } from "@/lib/utils";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/app/components/ui/dialog";
import {
  Option,
  CreationProps,
  LessonDetail,
  PhaseAndSubject,
  GuideWidgetSetInfo,
} from "@/types/lesson-mgmt";
import BizTreeCascader from "./BizTreeCascader";

const formSchema = z.object({
  lessonName: z.string().min(1, "必填").max(50, "最多50字符"),
  url: z.string().min(1, "必填").max(80, "最多80字符"),
  phase: z.number().min(1, "必填"),
  subject: z.number().min(1, "必填"),
  lessonType: z.number().min(1, "必填"),
  digitalAvatar: z.string().min(1, "必填"),
  tts: z.string().min(1, "必填").max(80, "最多80字符"),
  bizTreeNodeInfo: z.any().optional(),
});

type FormValues = z.infer<typeof formSchema>;
function Creation(props: CreationProps) {
  const { enums } = useEnum();
  const hasRunRef = useRef(false);

  const readOnly = props.modal === "detail";
  const form = useForm<FormValues>({
    disabled: readOnly,
    resolver: zodResolver(formSchema),
    defaultValues: {
      lessonName: "",
      url: "",
      phase: undefined,
      subject: undefined,
      lessonType: undefined,
      digitalAvatar: "",
      tts: "",
      bizTreeNodeInfo: [],
    },
  });

  // 详情
  const [detail, setDetail] = useState<LessonDetail | null>(null);
  const syncLessonDetail = async () => {
    if (!props.lessonId) return;
    const url = "/api/v1/lesson/detail";
    const query = {
      lessonId: props.lessonId,
    };
    const res: any = await get<LessonDetail>(url, { query });
    form.setValue("lessonName", res.lessonName);
    
    const isEdit = props.modal === "edit";
    if(isEdit){
      form.setValue("lessonType", res.lessonType);
    }
   
    setDetail(res);
    // form.setValue("bizTreeNodeInfo", valuesTemp);
    if (res.guideWidgetSetId) {
      form.setValue(
        "url",
        window.origin + "/guide-set/editor?id=" + res.guideWidgetSetId
      );
      setTimeout(() => {
        fetchGuideWidgetSetInfo();
      }, 100);
    }
  };

  const bizTreeNodeInfoRef = useRef<string[][]>([]);

  useEffect(() => {
    if (
      props.open &&
      (props.modal === "edit" || props.modal === "detail") &&
      !hasRunRef.current
    ) {
      syncLessonDetail();
      hasRunRef.current = true;
    } else if (!props.open) {
      hasRunRef.current = false;
    }
  }, [props.open, props.modal]);

  const url = form.watch("url");
  const [phaseAndSubject, setPhaseAndSubject] = useState<PhaseAndSubject>({
    phase: undefined,
    subject: undefined,
  });

  const { run: fetchGuideWidgetSetInfo } = useRequest(
    () => {
      const id = new URL(url).searchParams.get("id");
      if (!id) throw new Error("Invalid URL");
      const reqUrl = "/api/v1/lesson/guideWidgetSet/info";
      const query = { guideWidgetSetId: id };
      return get<GuideWidgetSetInfo>(reqUrl, { query });
    },
    {
      manual: true,
      ready: !!url,
      onSuccess: (res) => {
         const isEdit = props.modal === "edit"
        form.setValue("phase", res.phase);
        form.setValue("subject", res.subject);
        if(!isEdit){
          form.setValue("lessonType", res.lessonType);
        }
        form.setValue("digitalAvatar", res.digitalAvatar);
        setPhaseAndSubject({
          phase: res.phase,
          subject: res.subject,
        });
      },
      onError: (err: Error) => {
        toast.error(err.message);
      },
    }
  );

  const onChange = (value: string[][], selectedOptions: Option[][]) => {
    bizTreeNodeInfoRef.current = value;
  };

  const handleCreate = async () => {
    const finalBizTreeNodeInfo = mergeArraysByFirstElement(
      bizTreeNodeInfoRef.current
    );
    const values = form.getValues();

    const isEdit = props.modal === "edit"
    try {
      const id = new URL(url).searchParams.get("id");
      if (!id) throw new Error("请输入正确的课程地址");

      await post(isEdit ? "/api/v1/lesson/update" : "/api/v1/lesson/create", {
        arg: {
          lessonId: props.lessonId,
          lessonName: values.lessonName,
          guideWidgetSetId: Number(id),
          bizTreeNodeInfo: finalBizTreeNodeInfo,
          lessonType:values.lessonType
        },
      });
      props.refresh();
      props.setOpen(false);
    } catch (error) {
      if (error instanceof Error) {
        toast.error(error.message);
      } else {
        toast.error("发生未知错误");
      }
    }
  };

  return (
    <>
      <Form {...form}>
        <form className="space-y-6">
          <FormField
            control={form.control}
            name="lessonName"
            render={({ field }) => (
              <FormItem className="flex items-center">
                <FormLabel className="w-18">
                  名称 <span className="text-red-500">*</span>
                </FormLabel>
                <FormControl className="flex-1">
                  <Input
                    {...field}
                    maxLength={50}
                    placeholder="请输入课程名称"
                  />
                </FormControl>
                <div className="text-xs text-gray-400">
                  {field.value.length}/50字符
                </div>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="url"
            render={({ field }) => (
              <FormItem className="flex items-center">
                <FormLabel className="w-18">
                  地址 <span className="text-red-500">*</span>
                </FormLabel>
                <FormControl className="flex-1">
                  <Input
                    {...field}
                    maxLength={80}
                    placeholder="请输入课程地址"
                    onBlur={() => {
                      if (url) {
                        fetchGuideWidgetSetInfo();
                      }
                    }}
                  />
                </FormControl>
                <div className="text-xs text-gray-400">
                  {field.value.length}/80字符
                </div>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="phase"
            render={({ field }) => (
              <FormItem className="flex items-center">
                <FormLabel className="w-18">
                  学段 <span className="text-red-500">*</span>
                </FormLabel>
                <FormControl className="flex flex-1">
                  <Select
                    disabled={true}
                    value={field.value?.toString()}
                    onValueChange={(value) => field.onChange(Number(value))}
                  >
                    <SelectTrigger className="flex-1 bg-gray-200">
                      <SelectValue placeholder="待输入地址，自动获取数据" />
                    </SelectTrigger>
                    <SelectContent>
                      {enums.phaseList.map((item: EnumItem) => (
                        <SelectItem key={item.value} value={String(item.value)}>
                          {item.nameZh}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="subject"
            render={({ field }) => (
              <FormItem className="flex items-center">
                <FormLabel className="w-18">
                  学科 <span className="text-red-500">*</span>
                </FormLabel>
                <FormControl className="flex flex-1">
                  <Select
                    disabled={true}
                    value={field.value?.toString()}
                    onValueChange={(value) => field.onChange(Number(value))}
                  >
                    <SelectTrigger className="flex-1 bg-gray-200">
                      <SelectValue placeholder="待输入地址，自动获取数据" />
                    </SelectTrigger>
                    <SelectContent>
                      {enums.subjectList.map((item: EnumItem) => (
                        <SelectItem key={item.value} value={String(item.value)}>
                          {item.nameZh}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="lessonType"
            render={({ field }) => (
              <FormItem className="flex items-center">
                <FormLabel className="w-18">
                  单课类型 <span className="text-red-500">*</span>
                </FormLabel>
                <FormControl className="flex flex-1">
                  <Select
                    disabled={false}
                    
                    value={field.value?.toString()}
                    onValueChange={(value) => field.onChange(Number(value))}
                  >
                    <SelectTrigger className="flex-1">
                      <SelectValue placeholder="请选择类型" />
                    </SelectTrigger>
                    <SelectContent>
                      {enums.lessonTypeList.map((item: EnumItem) => (
                        <SelectItem key={item.value} value={String(item.value)}>
                          {item.nameZh}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="digitalAvatar"
            render={({ field }) => (
              <FormItem className="flex items-center">
                <FormLabel className="w-18">
                  数字人 <span className="text-red-500">*</span>
                </FormLabel>
                <FormControl className="flex flex-1 bg-gray-200">
                  <Input
                    {...field}
                    maxLength={20}
                    placeholder="待输入地址，自动获取数据"
                    disabled={true}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </form>
      </Form>
      <div>
        <div className="text-sm font-bold">业务树知识点（可多选）</div>
        <div className="ml-20 mt-2">
          <BizTreeCascader
            onChange={onChange}
            detail={detail}
            phaseAndSubject={phaseAndSubject}
          />
        </div>
      </div>
      {!readOnly && (
        <DialogFooter>
          <Button variant="outline" onClick={() => props.setOpen(false)}>
            取消
          </Button>
          <Button className="bg-blue-500" onClick={handleCreate}>
            确定
          </Button>
        </DialogFooter>
      )}
    </>
  );
}

interface CreationModalProps {
  open: boolean;
  onClose: (open: boolean) => void;
  modal: string; // "edit" | "create" | "detail"
  lessonId?: string;
  refresh: () => void;
}

export default function CreationModal(props: CreationModalProps) {
  const { open, onClose } = props;
  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="!max-w-none w-[600px]" onInteractOutside={(e) => e.preventDefault()}>
        <DialogHeader>
          <DialogTitle>
            {props.modal === "edit" && "编辑课程"}
            {props.modal === "create" && "创建课程"}
            {props.modal === "detail" && "查看详情"}
          </DialogTitle>
          <DialogDescription></DialogDescription>
        </DialogHeader>
        <Creation {...props} setOpen={onClose} />
      </DialogContent>
    </Dialog>
  );
}
