// app/course/_components/batch-upload.tsx
"use client";

import { useEffect, useRef, useState } from "react";
import { Button } from "@/app/components/ui/button";
// import { Progress } from "@/app/components/ui/progress";
import { Input } from "@/app/components/ui/input";
import { UploadCloud, FileX2, Trash2 } from "lucide-react";
import { Separator } from "@/app/components/ui/separator";
import { uploadFile2 } from "@/lib/uploader";
import emitter from "@/lib/emitter";
import { post } from "@/lib/course-center-fetcher";
import { toast } from "sonner";
import { templFileUrl } from "@/lib/env-helper";
import _ from "lodash";

export default function BatchUpload(props: any) {
  const [file, setFile] = useState<File | null>(null);
  // const [progress, setProgress] = useState(75);
  const inputRef = useRef<HTMLInputElement>(null);
  const preprocessKey = useRef<any>(null);
  const [preprocessRes, setPreprocessRes] = useState<any>({});
  // 处理文件选择
  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];

      try {
        const { url } = await uploadFile2(file);
        // 这里可以触发上传逻辑
        setFile(file);
        try {
          const reqUrl = "/api/v1/lesson/create/batch/preprocess";
          const res: any = await post(reqUrl, { arg: { url } });
          preprocessKey.current = res.key;
          setPreprocessRes(res);
        } catch (error: any) {
          toast.error("批量处理失败：" + error.message);
        }
      } catch (error: any) {
        toast.error("上传失败");
      }
    }
  };

  // 拖拽上传
  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      setFile(e.dataTransfer.files[0]);
      handleFileChange({target: {files: e.dataTransfer.files}} as any)
    }
  };

  // 点击上传区域
  const handleClick = () => {
    inputRef.current?.click();
  };

  // 删除文件
  const handleRemove = () => {
    setFile(null);
  };

  const batchCreateCourseHandelrTemp = async () => {
    if (preprocessKey.current) {
      try {
        const res = await post("/api/v1/lesson/create/batch", {
          arg: {
            key: preprocessKey.current,
          },
        });
        props.setOpen(false);
        emitter.emit('refresh');
      } catch (error: any) {
        toast.error(error.message);
      }
    } else {
      toast.error("识别全部失败，无法完成批量创建");
    }
  };
  const batchCreateCourseHandelr = _.debounce(batchCreateCourseHandelrTemp,200);

  useEffect(() => {
    emitter.on("batchCreateCourse", batchCreateCourseHandelr);

    // 清理函数
    return () => {
      emitter.off("batchCreateCourse", batchCreateCourseHandelr);
    };
  }, [batchCreateCourseHandelr]);

  const handleDownloadTemplate = () => {
    if(window){
      window?.open(templFileUrl, "_blank");
    }
  };

  return (
    <div className="space-y-6">
      {/* 下载模板 */}
      <div>
        <Button className="bg-blue-500" onClick={handleDownloadTemplate}>
          下载模板
        </Button>
      </div>
      <Separator className="my-4" />

      {/* 批量上传课程 */}
      <div>
        <div className="mb-2 font-medium">批量上传课程</div>
        <div
          className="bg-muted/50 hover:border-primary flex cursor-pointer flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-200 p-8 transition"
          onDrop={handleDrop}
          onDragOver={(e) => e.preventDefault()}
          onClick={handleClick}
        >
          <UploadCloud className="mb-2 h-10 w-10 text-gray-400" />
          <div className="mb-1 text-gray-500">
            点击、拖拽/复制文件到这里上传
          </div>
          <div className="text-xs text-gray-400">(仅支持：xls、xlsx、csv)</div>
          <Input
            ref={inputRef}
            type="file"
            accept=".xls,.xlsx,.csv"
            className="hidden"
            onChange={handleFileChange}
          />
        </div>
        {/* 文件信息 */}
        {file && (
          <div className="mt-4 flex items-center rounded border border-green-200 bg-green-50 px-4 py-2">
            <FileX2 className="mr-2 text-green-500" />
            <a
              href="#"
              className="mr-2 text-green-700 underline"
              download={file.name}
            >
              {file.name}
            </a>
            <span className="mr-2 text-xs text-gray-500">
              {(file.size / 1024).toFixed(0)}KB
            </span>
            <Button
              variant="ghost"
              size="icon"
              className="ml-auto"
              onClick={handleRemove}
            >
              <Trash2 className="h-4 w-4 text-gray-400" />
            </Button>
          </div>
        )}
      </div>

      {/* 上传进度 */}
      {/* {file && (
        <div>
          <div className="mb-1 text-sm text-gray-600">上传进度</div>
          <Progress value={progress} className="h-2" />
        </div>
      )} */}

      {/* 上传结果 */}
      {file && (
        <>
          <Separator className="my-4" />
          <div className="mt-2 text-sm">
            表格共：{preprocessRes.total}条数据，
            <span className="text-green-600">
              成功导入：{preprocessRes.success}条
            </span>
            ，<span className="text-red-600">失败：{preprocessRes.fail}条</span>
            {preprocessRes.fail > 0 && (
              <div>
                <a
                  href={preprocessRes.url}
                  target="_blank"
                  className="ml-2 text-blue-600 underline"
                >
                  下载失败原因
                </a>
              </div>
            )}
          </div>
        </>
      )}
    </div>
  );
}
