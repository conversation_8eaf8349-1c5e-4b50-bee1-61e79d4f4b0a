"use client";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import useEnum from "@/app/hooks/useEnumManager/useEnumManager";

import { Button } from "@/app/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/app/components/ui/form";
import { Input } from "@/app/components/ui/input";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/app/components/ui/select";

const statusEnum: Record<number, string> = {
  1: "未上架",
  2: "已上架",
  3: "修改待上架",
  // 1: "待上架",
  // 2: "已发布",
  // 3: "待发布",
};

const formSchema = z.object({
  lessonName: z.string().optional(),
  phase: z.string().optional(),
  subject: z.string().optional(),
  lessonType: z.string().optional(),
  bizTreeNodeName: z.string().optional(),
  status: z.string().optional(),
});

export default function SearchForm({
  syncRequest,
  resetPage,
  setSearchParams,
}: {
  syncRequest: (values: z.infer<typeof formSchema>) => void;
  resetPage: () => void;
  setSearchParams: (params: Record<string, string>) => void;
}) {
  const { enums } = useEnum();
  // 1. Define your form.
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      lessonName: "",
      phase: "",
      subject: "",
      lessonType: "",
      bizTreeNodeName: "",
      status: "",
    },
  });

  // 2. Define a submit handler.
  function onSubmit(values: z.infer<typeof formSchema>) {
    resetPage();
    syncRequest(values);
    setSearchParams(values);
  }

  function onReset() {
    form.reset();
    resetPage();
    setSearchParams({});
    syncRequest({ page: 1 } as any);
  }

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="grid grid-cols-3 gap-6 rounded-md bg-white p-6"
      >
        <FormField
          control={form.control}
          name="lessonName"
          render={({ field }) => (
            <FormItem className="flex">
              {/* <FormLabel className="w-1/6">课程ID/名称</FormLabel> */}
              <FormControl>
                <Input placeholder="搜索课程ID/名称" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="bizTreeNodeName"
          render={({ field }) => (
            <FormItem className="flex">
              {/* <FormLabel className="w-1/6">业务知识树名称</FormLabel> */}
              <FormControl>
                <Input placeholder="搜索业务知识树名称" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="lessonType"
          render={({ field }) => (
            <FormItem className="flex">
              <FormControl>
                <Select onValueChange={field.onChange} value={field.value}>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="请选择课程类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectGroup>
                      <SelectLabel>课程类型</SelectLabel>
                      {enums.lessonTypeList.map((item) => (
                        <SelectItem key={item.value} value={String(item.value)}>
                          {item.nameZh}
                        </SelectItem>
                      ))}
                    </SelectGroup>
                  </SelectContent>
                </Select>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="phase"
          render={({ field }) => (
            <FormItem className="flex">
              <FormControl>
                <Select onValueChange={field.onChange} value={field.value}>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="请选择学段" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectGroup>
                      <SelectLabel>学段</SelectLabel>
                      {enums.phaseList.map((item) => (
                        <SelectItem key={item.value} value={String(item.value)}>
                          {item.nameZh}
                        </SelectItem>
                      ))}
                    </SelectGroup>
                  </SelectContent>
                </Select>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="subject"
          render={({ field }) => (
            <FormItem className="flex">
              <FormControl>
                <Select onValueChange={field.onChange} value={field.value}>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="请选择学科" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectGroup>
                      <SelectLabel>学科</SelectLabel>
                      {enums.subjectList.map((item) => (
                        <SelectItem key={item.value} value={String(item.value)}>
                          {item.nameZh}
                        </SelectItem>
                      ))}
                    </SelectGroup>
                  </SelectContent>
                </Select>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="status"
          render={({ field }) => (
            <FormItem className="flex">
              <FormControl>
                <Select onValueChange={field.onChange} value={field.value}>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="选择是否上架" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectGroup>
                      <SelectLabel>上架状态</SelectLabel>
                      {Object.values(statusEnum).map(
                        (item: any, index: number) => (
                          <SelectItem value={String(index + 1)} key={index}>
                            {item}
                          </SelectItem>
                        )
                      )}
                    </SelectGroup>
                  </SelectContent>
                </Select>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="col-span-3 flex justify-end gap-2">
          <Button
            className="w-40"
            type="reset"
            variant="outline"
            onClick={onReset}
          >
            重置
          </Button>
          <Button className="w-40" type="submit">
            查询
          </Button>
        </div>
      </form>
    </Form>
  );
}
