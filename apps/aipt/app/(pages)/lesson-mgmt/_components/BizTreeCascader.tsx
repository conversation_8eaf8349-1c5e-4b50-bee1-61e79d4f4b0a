import { get } from "@/lib/course-center-fetcher";
import { Cascader } from "antd";
import { useEffect, useState } from "react";
import { Option, BizTreeNode, BizTreeDetail } from "@/types/lesson-mgmt";

// 递归格式化子树
const formatTree = (nodes: BizTreeNode[]): Option[] => {
  return nodes.map((node) => {
    const isLeaf =
      !node.bizTreeNodeChildren || node.bizTreeNodeChildren.length === 0;

    return {
      // disable: !isLeaf,
      isLeaf,
      value: node.bizTreeNodeId,
      label: node.bizTreeNodeName,
      children: node.bizTreeNodeChildren
        ? formatTree(node.bizTreeNodeChildren)
        : undefined,
      bizTreeId: node.bizTreeId,
    };
  });
};

const BizTreeCascader = (props: any) => {
  const [options, setOptions] = useState<Option[]>([]);
  const [values, setValues] = useState<string[][]>([]);

  // 递归加载所有子节点
  const loadAllChildren = async (node: Option) => {
    if (!node.bizTreeId) return;
    const url = "/api/v1/lesson/question/biz/tree/detail";
    const query = { bizTreeId: node.bizTreeId };
    try {
      const res = await get<BizTreeDetail>(url, { query });
      const subtree = res.bizTreeDetail.bizTreeNodeChildren;
      if (subtree && subtree.length > 0) {
        node.children = formatTree(subtree);
        // 递归加载每个子节点
        for (const child of node.children) {
          await loadAllChildren(child);
        }
      }
    } catch (error) {
      console.error("加载子树失败:", error);
    }
  };

  // 加载第一层业务树列表（不知道后端为啥分开两个接口）
  const syncBizTreeList = async (phase: number, subject: number) => {
    const url = "/api/v1/lesson/question/biz/tree/list";
    const query = {
      phase: phase.toString(),
      subject: subject.toString(),
    };
    const res = await get<{ list: Option[] }>(url, { query });
    if (res.list.length > 0) {
      res.list.forEach((item) => {
        item.value = item.bizTreeId;
        item.label = item.bizTreeName;
        item.isLeaf = false;
      });
      setOptions(res.list);

      // 加载所有子节点
      for (const node of res.list) {
        await loadAllChildren(node);
      }
      setOptions([...res.list]); // 更新完整的树结构
    }
  };

  // 处理选择变化
  const onChange = (value: string[][], selectedOptions: Option[][]) => {
    // 过滤掉父项，只保留叶子节点的值
    const filteredValue = value.filter((val, index) => {
      const selectedOption = selectedOptions[index];
      // 只保留叶子节点的选择
      return (
        selectedOption &&
        selectedOption.length > 0 &&
        selectedOption[selectedOption.length - 1]?.isLeaf
      );
    });
    setValues(filteredValue);
    props.onChange(filteredValue);
  };

  // 监听学段和学科变化，重新加载数据
  useEffect(() => {
    if (props.phaseAndSubject?.phase && props.phaseAndSubject?.subject) {
      syncBizTreeList(
        props.phaseAndSubject.phase,
        props.phaseAndSubject.subject
      );
    }
  }, [props.phaseAndSubject]);

  // 处理详情数据的初始值
  useEffect(() => {
    if (props.detail && props.detail.bizTreeNodeList.length > 0) {
      const res = props.detail;
      const valuesTemp: string[][] = [];

      res.bizTreeNodeList.forEach((element: any) => {
        element.bizTreeDetail.forEach((item: any) => {
          valuesTemp.push(item.bizTreeNodeParentPath);
        });
      });

      setValues(valuesTemp);
      // 触发 onChange 回调，确保初始值被正确处理
      props.onChange(valuesTemp);
    }
  }, [props.detail]);

  // 添加样式到 head
  useEffect(() => {
    const styleId = "biz-tree-cascader-style";
    if (!document.getElementById(styleId)) {
      const style = document.createElement("style");
      style.id = styleId;
      style.textContent = `
        /* 隐藏所有选择框 */
        .ant-cascader-checkbox {
          display: none !important;
        }
        
        /* 只显示叶子节点的选择框 */
        .ant-cascader-menu-item:not(:has(.ant-cascader-menu-item-expand-icon)) .ant-cascader-checkbox {
          display: inline-block !important;
        }
      `;
      document.head.appendChild(style);
    }

    return () => {
      const style = document.getElementById(styleId);
      if (style) {
        style.remove();
      }
    };
  }, []);

  return (
    <Cascader
      value={values}
      disabled={props.readOnly}
      getPopupContainer={(triggerNode) => triggerNode.parentNode as HTMLElement}
      style={{ width: "100%" }}
      multiple={true}
      options={options}
      onChange={onChange}
      placeholder="请选择"
      showSearch={{
        filter: (inputValue: string, path: Option[]) => {
          // 只有叶子节点才能在搜索中显示，避免显示无法选择的中间节点
          const lastOption = path[path.length - 1];
          return (
            lastOption?.isLeaf === true &&
            path.some((option) =>
              option.label?.toLowerCase().includes(inputValue.toLowerCase())
            )
          );
        },
      }}
      showCheckedStrategy="SHOW_CHILD"
    />
  );
};

export default BizTreeCascader;
