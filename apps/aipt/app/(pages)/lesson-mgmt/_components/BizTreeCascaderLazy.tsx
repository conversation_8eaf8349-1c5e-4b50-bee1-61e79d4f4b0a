import { get } from "@/lib/course-center-fetcher";
import { Cascader } from "antd";
import { useEffect, useState } from "react";
import { Option, BizTreeNode, BizTreeDetail } from "@/types/lesson-mgmt";

// 递归格式化子树
const formatTree = (nodes: BizTreeNode[]): Option[] => {
  return nodes.map((node) => {
    const isLeaf =
      !node.bizTreeNodeChildren || node.bizTreeNodeChildren.length === 0;

    return {
      // disable: !isLeaf,
      isLeaf,
      value: node.bizTreeNodeId,
      label: node.bizTreeNodeName,
      children: node.bizTreeNodeChildren
        ? formatTree(node.bizTreeNodeChildren)
        : undefined,
    };
  });
};

const BizTreeCascader = (props: any) => {
  const [options, setOptions] = useState<Option[]>([]);

  // 加载第一层业务树列表（不知道后端为啥分开两个接口）
  const syncBizTreeList = async (phase: number, subject: number) => {
    const url = "/api/v1/lesson/question/biz/tree/list";
    const query = {
      phase: phase.toString(),
      subject: subject.toString(),
    };
    const res = await get<{ list: Option[] }>(url, { query });
    if (res.list.length > 0) {
      res.list.forEach((item) => {
        item.value = item.bizTreeId;
        item.label = item.bizTreeName;
        item.isLeaf = false;
      });
      setOptions(res.list);
    }
  };

  // 加载完整子树
  const loadTreeData = async (selectedOptions: Option[]) => {
    const targetOption = selectedOptions[selectedOptions.length - 1]!;
    if (!targetOption?.bizTreeId) return;
    targetOption.loading = true;
    const url = "/api/v1/lesson/question/biz/tree/detail";
    const query = { bizTreeId: targetOption.bizTreeId };
    try {
      const res = await get<BizTreeDetail>(url, { query });
      const subtree = res.bizTreeDetail.bizTreeNodeChildren;
      targetOption.children = formatTree(subtree);
      targetOption.loading = false;
      setOptions([...options]);
    } catch (error) {
      console.error("加载子树失败:", error);
      targetOption.loading = false;
    }
  };

  const onChange = (value: string[][]) => {
    setValues(value);
    props.onChange(value);
  };

  useEffect(() => {
    if (props.phaseAndSubject.phase && props.phaseAndSubject.subject) {
      syncBizTreeList(
        props.phaseAndSubject.phase,
        props.phaseAndSubject.subject
      );
    }
  }, [props.phaseAndSubject]);
  const [values, setValues] = useState<string[][]>([]);
  const [labels, setLabels] = useState<Record<string, string>>({});

  useEffect(() => {
    if (props.detail && props.detail.bizTreeNodeList.length > 0) {
      const res = props.detail;
      const labelsTemp: Record<string, string> = {};
      const valuesTemp: string[][] = [];
      res.bizTreeNodeList.forEach((element: any) => {
        element.bizTreeDetail.forEach((item: any) => {
          valuesTemp.push(item.bizTreeNodeParentPath);
          labelsTemp[
            item.bizTreeNodeParentPath[item.bizTreeNodeParentPath.length - 1]
          ] = item.bizTreeNodeName;
        });
      });
      setLabels(labelsTemp);
      setValues(valuesTemp);
    }
  }, [props.detail]);

  return (
    <Cascader
      value={values}
      disabled={props.readOnly}
      //   optionRender={(option) => {
      //     option.value = option.bizTreeNodeId;
      //     return (
      //       <span key={option.bizTreeNodeId}>
      //         {option.bizTreeNodeName || ""} {option.bizTreeNodeId}
      //       </span>
      //     );
      //   }}
      getPopupContainer={(triggerNode) => triggerNode.parentNode as HTMLElement}
      style={{ width: "100%" }}
      multiple={true}
      options={options}
      onChange={onChange}
      placeholder="请选择"
      loadData={loadTreeData}
      //   displayRender={(arg) => {
      //     const lastValue = arg[arg.length - 1];
      //     return lastValue ? labels[lastValue] || "" : "";
      //   }}
    />
  );
};

export default BizTreeCascader;
