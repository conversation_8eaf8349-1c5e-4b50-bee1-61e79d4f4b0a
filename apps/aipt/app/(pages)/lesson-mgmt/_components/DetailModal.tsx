import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON>Title,
} from "@/app/components/ui/dialog";
import { Input } from "@/app/components/ui/input";
import { Button } from "@/app/components/ui/button";
import { toast } from "sonner";
import { useRequest } from "ahooks";
import { get } from "@/lib/course-center-fetcher";
import { Info } from "lucide-react";
import dayjs from "dayjs";
import copyToClipboard from "@/lib/copy";

interface DetailModalProps {
  open: boolean;
  onClose: () => void;
  lessonId?: string;
}

const handleCopy = async(text: string) => {
  await copyToClipboard(text);
  toast.success("已复制到剪贴板");
};

const LabelCmp = (item: any) => {
  const tips = item.tips;
  return (
    <div className="flex items-center gap-2" key={item.label}>
      <label className="relative flex w-28 flex-shrink-0 items-center">
        {tips>0 && (
          <Info className="absolute left-[-18px] mr-1 h-4 w-4 text-red-500" />
        )}
        <span>{item.label}</span>{" "}
        {item.required && <span className="mr-1 text-red-500">*</span>}
      </label>
      <div className="flex-1">
        <div className="flex items-center">
          <Input value={item.value} readOnly />
          <Button
            variant="outline"
            className="ml-2"
            onClick={() => handleCopy(item.value)}
          >
            复制
          </Button>
        </div>
        {tips>0 && (
          <div className="mt-1 text-xs text-red-500">
            最近更新于：{dayjs(tips * 1000).format("YYYY-MM-DD HH:mm:ss")}
            ，修改尚未提交上架
          </div>
        )}
      </div>
    </div>
  );
};

export default function DetailModal({
  open,
  onClose,
  lessonId,
}: DetailModalProps) {
  const { data: lessonDetail } = useRequest<any, [string]>(
    () =>
      get(`/api/v1/lesson/address/info`, {
        query: { lessonId: String(lessonId) },
      }),

    {
      ready: !!lessonId && open,
    }
  );

  // loading骨架
  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="!max-w-none w-[700px]">
        <DialogHeader>
          <DialogTitle>课程详情</DialogTitle>
        </DialogHeader>
        {!lessonDetail ? (
          <div className="space-y-4">
            <div className="h-4 w-[250px] animate-pulse rounded bg-gray-200" />
            <div className="h-4 w-[200px] animate-pulse rounded bg-gray-200" />
            <div className="h-4 w-[300px] animate-pulse rounded bg-gray-200" />
          </div>
        ) : (
          <div className="lesson_detail_container space-y-8 p-4">
            {/* 线上信息 */}
            <div>
              <div className="mb-4 text-base font-bold">线上信息</div>
              <div className="flex flex-col gap-4">
                <LabelCmp
                  label="课程名称"
                  value={lessonDetail.lessonName}
                  required
                />
                <LabelCmp
                  label="线上预览"
                  value={lessonDetail.onlineUrl}
                  required
                />
              </div>
            </div>
            {/* 修改地址 */}
            <div>
              <div className="mb-4 text-base font-bold">修改地址</div>
              <div className="flex flex-col gap-4">
                <LabelCmp
                  label="稿件地址"
                  value={lessonDetail.guideWidgetSetUrl}
                  required
                  tips={lessonDetail.guideWidgetChangeTime}
                />
                <LabelCmp
                  label="圈画地址"
                  value={lessonDetail.drawUrl}
                  required
                  tips={lessonDetail.drawChangeTime}
                />

                <LabelCmp
                  label="配课地址"
                  value={lessonDetail.combineUrl}
                  required
                  tips={lessonDetail.combineChangeTime}
                />
                <LabelCmp
                  label="修改预览"
                  value={lessonDetail.previewUrl}
                  required
                />
                <LabelCmp
                  label="改BUG地址"
                  value={lessonDetail.fixbugUrl}
                  required
                />
              </div>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
