import emitter from "@/lib/emitter";
import AddImg from "@/public/add.svg";
import {
  BaseEdge,
  Edge,
  EdgeLabelRenderer,
  getSmoothStepPath,
  MarkerType,
  Node,
  Position,
  useReactFlow,
} from "@xyflow/react";
import InsertNodeModal from "./insert-node-modal";

interface CustomEdgeProps {
  id: string;
  sourceX: number;
  sourceY: number;
  targetX: number;
  targetY: number;
  source: string;
  target: string;
}

export default function CustomEdge(props: CustomEdgeProps) {
  const { id, sourceX, sourceY, targetX, targetY, source, target } = props;

  const { setEdges, setNodes } = useReactFlow();
  const [edgePath, labelX, labelY] = getSmoothStepPath({
    sourceX,
    sourceY,
    targetX,
    targetY,
    offset: 10, // 设置偏移量，使两端对齐
    borderRadius: 100, // 可调，0为最平滑
    sourcePosition: Position.Right,
    targetPosition: Position.Left,
  });

  const handleClick = () => {
    const newNodeId = `node-${Date.now()}`;

    setNodes((nodes) => {
      // 找到源节点和目标节点
      const sourceNode = nodes.find((node) => node.id === source);
      const targetNode = nodes.find((node) => node.id === target);

      if (sourceNode && targetNode) {
        // 计算新节点的位置
        const newNodePosition = {
          x: sourceNode.position.x + 300, // 在源节点后面300像素
          y: sourceNode.position.y,
        };

        // 找到所有需要移动的节点（x坐标大于源节点的节点）
        const updatedNodes = nodes.map((node) => {
          if (node.position.x > sourceNode.position.x) {
            return {
              ...node,
              position: {
                ...node.position,
                x: node.position.x + 300, // 向后移动300像素
              },
            };
          }
          return node;
        });

        // 添加新节点
        return [
          ...updatedNodes,
          {
            id: newNodeId,
            position: newNodePosition,
            data: { label: "新节点", desc: "xxx" },
            sourcePosition: Position.Right,
            targetPosition: Position.Left,
            type: "custom-node",
          } as Node,
        ];
      }
      return nodes;
    });

    setEdges((edges) => edges.filter((e) => e.id !== id));

    setEdges((edges) => [
      ...edges,
      {
        id: `${source}-${newNodeId}`,
        source: source,
        target: newNodeId,
        type: "custom-edge",
        markerEnd: MarkerType.ArrowClosed,
      } as Edge,
      {
        id: `${newNodeId}-${target}`,
        source: newNodeId,
        target: target,
        type: "custom-edge",
        markerEnd: MarkerType.ArrowClosed,
      } as Edge,
    ]);
  };

  const onSubmit = () => {
    emitter.emit("refresh");
  };

  return (
    <>
      <BaseEdge id={id} path={edgePath} markerEnd={MarkerType.ArrowClosed} />
      <EdgeLabelRenderer>
        <InsertNodeModal
          {...props}
          trigger={
            <AddImg
              className="z-100 absolute cursor-pointer"
              width={20}
              style={{
                pointerEvents: "all",
                transform: `translate(-50%, -50%) translate(${labelX}px,${labelY}px)`,
              }}
            />
          }
          onSubmit={onSubmit}
        />
      </EdgeLabelRenderer>
    </>
  );
}
