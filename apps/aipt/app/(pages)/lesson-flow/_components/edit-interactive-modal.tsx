import { Button } from "@/app/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogClose,
  <PERSON>alog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
} from "@/app/components/ui/dialog";
import { Input } from "@/app/components/ui/input";
import { Label } from "@/app/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/app/components/ui/radio-group";
import emitter from "@/lib/emitter";
import { post } from "@/lib/fetcher";
import { uploadFile } from "@/lib/uploader";
import DeleteImg from "@/public/delete.svg";
import { cloneDeep } from "lodash";
import { useSearchParams } from "next/navigation";
import { useRef, useState } from "react";
import { toast } from "sonner";

interface EditExcercideProps {
  onSubmit: () => void;
  setOpen: (open: boolean) => void;
  [key: string]: any;
}

const initInfo = {
  url: "",
  typeName: "",
};

const EditExcercide: React.FC<EditExcercideProps> = (props) => {
  const { onSubmit, setOpen } = props;
  const searchParams = useSearchParams();
  const lessonId = Number(searchParams.get("lessonId")); // 获取查询参数

  const [info, setInfo] = useState(
    props.data.interactive ?? cloneDeep(initInfo)
  );

  const infoRef = useRef(props.data.interactive ?? cloneDeep(initInfo));

  const onShowTypeChange = (value: string) => {
    setHidden(Number(value));
  };

  const [hidden, setHidden] = useState(props.data.hidden || 0); // 0显示 1隐藏
  const updateExercise = async () => {
    return post("/api/v1/lesson_widget/update/interactive", {
      arg: {
        lessonId,
        widgetIndex: props.data.index,
        interactiveInfo: infoRef.current,
        hidden,
      },
    });
  };

  const handleFileChange = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];
    if (file) {
      const { url } = await uploadFile(file, "2");
      setInfo({ ...infoRef.current, url });
      infoRef.current = { ...infoRef.current, url };
    }
  };

  const onSubmitHandle = async () => {
    try {
      const strContent = await fetch(info.url).then((res) => res.text());
      const regex = /customElements\.define\('([^']+)'/;
      const match = strContent.match(regex);
      if (match && match[1]) {
        const componentName = match[1];
        setInfo({ ...info, typeName: componentName });
        infoRef.current = { ...infoRef.current, typeName: componentName };
      } else {
        toast.error("请输入正确的互动组件地址");
        return;
      }
      try {
        await updateExercise();
        emitter.emit("refresh");
        setOpen(false);
        emitter.emit("openInteractiveSheet", {
          ...props,
          lessonId,
          interactiveInfo: info,
          hidden: hidden,
        });
      } catch (error: any) {
        toast.error(error.message);
      }
    } catch (error) {
      toast.error("请输入正确的互动组件地址");
      console.log(error);
      return;
    }
  };
  return (
    <div className="insert-node-modal">
      <div className="mt-4">
        <div className="items-center gap-2">
          互动组件：
          <div className="flex items-center gap-2 break-all text-sm">
            {info.url ? (
              <>
                <span className="wrap-break-word flex-1 rounded-md bg-gray-100 p-2 text-sm text-gray-500">
                  {info.url}
                </span>
                <div>
                  <DeleteImg
                    width={18}
                    height={18}
                    className="cursor-pointer text-red-500 opacity-50"
                    onClick={() => {
                      setInfo({ ...info, url: "" });
                      infoRef.current = { ...info, url: "" };
                    }}
                  />
                </div>
              </>
            ) : (
              <Input
                className="flex-1"
                type="file"
                onChange={handleFileChange}
                accept=".js"
              />
            )}
          </div>
          {/* <Input
            className="mt-4"
            placeholder="请在此输入..."
            onInput={(event: any) => {
              setInfo({ ...info, url: event.target.value });
              infoRef.current = { ...info, url: event.target.value };
            }}
            value={info.url}
          /> */}
        </div>
        <div className="mt-8">
          学生端显示状态：
          <div className="mt-4">
            <RadioGroup
              className="mt-1 flex items-center gap-6"
              defaultValue={String(hidden)}
              onValueChange={onShowTypeChange}
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="0" id="r1" />
                <Label htmlFor="r1">正常显示</Label>
              </div>

              <div className="flex items-center space-x-2">
                <RadioGroupItem value="1" id="r2" />
                <Label htmlFor="r2">隐藏模块</Label>
              </div>
            </RadioGroup>
          </div>
        </div>
      </div>
      <div className="mt-6 flex justify-end gap-10">
        <DialogClose asChild>
          <Button variant="outline">取消</Button>
        </DialogClose>
        <Button onClick={onSubmitHandle}>提交</Button>
      </div>
    </div>
  );
};

const EditExcercideModal = (props: any) => {
  return (
    <Dialog open={props.open} onOpenChange={props.setOpen}>
      <DialogContent
        onInteractOutside={(e) => e.preventDefault()}
        className="bg-white"
      >
        <DialogHeader>
          <DialogTitle>编辑互动组件</DialogTitle>
        </DialogHeader>
        <EditExcercide {...props} setOpen={props.setOpen} />
      </DialogContent>
    </Dialog>
  );
};

export default EditExcercideModal;
