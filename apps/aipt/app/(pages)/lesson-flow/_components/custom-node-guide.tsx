import PlayImg from "@/public/play_fill.svg";
import { Hand<PERSON>, NodeProps, Position } from "@xyflow/react";

export default function CustomNode(props: NodeProps<any>) {
  return (
    <div className="min-h-25 flex w-[180px] min-w-40 flex-col items-center justify-center gap-1 rounded-md bg-white shadow-xl">
      <Handle type="target" position={Position.Left} />
      <div className="flex gap-1">
        <PlayImg width={20} height={20} />
        {props.data.label}
      </div>
      <div className="mx-2 rounded-md bg-gray-100 px-2 py-1 text-xs text-gray-500">
        {props.data.desc}
      </div>
      <Handle type="source" position={Position.Right} />
    </div>
  );
}
