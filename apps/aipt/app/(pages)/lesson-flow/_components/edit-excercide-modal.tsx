import { useState } from "react";
import { <PERSON><PERSON> } from "@/app/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/app/components/ui/dialog";
import { post } from "@/lib/fetcher";
import { useSearchParams } from "next/navigation";
import { toast } from "sonner";
import { Textarea } from "@/app/components/ui/textarea";
import emitter from "@/lib/emitter";

interface EditExcercideProps {
  onSubmit: () => void;
  setOpen: (open: boolean) => void;
  [key: string]: any;
}

const EditExcercide: React.FC<EditExcercideProps> = (props) => {
  const { onSubmit, setOpen } = props;
  const searchParams = useSearchParams();
  const lessonId = Number(searchParams.get("lessonId")); // 获取查询参数
  const [fromVal, setFromVal] = useState(props.data.exercise || "");
  const updateExercise = async () => {
    return post("/api/v1/lesson_widget/update/exercise", {
      arg: {
        lessonId,
        widgetIndex: props.data.index,
        exercise: fromVal,
      },
    });
  };

  const onSubmitHandle = async () => {
    await updateExercise();
    emitter.emit("refresh");
    setOpen(false);
    emitter.emit("openSheet", props);
  };
  return (
    <div className="insert-node-modal">
      请按照顺序输入题目 id（竖线分割、换行分组）：
      <div>
        <Textarea
          placeholder="请在此输入..."
          onInput={(event: any) => setFromVal(event.target.value)}
          value={fromVal}
        />
      </div>
      <div className="mt-6 flex justify-end gap-10">
        <DialogClose asChild>
          <Button variant="outline">取消</Button>
        </DialogClose>
        <Button onClick={onSubmitHandle}>提交</Button>
      </div>
    </div>
  );
};

const EditExcercideModal = (props: any) => {
  return (
    <Dialog open={props.open} onOpenChange={props.setOpen}>
      <DialogContent onInteractOutside={(e) => e.preventDefault()} className="bg-white">
        <DialogHeader>
          <DialogTitle>编辑全部题目</DialogTitle>
        </DialogHeader>
        <EditExcercide {...props} setOpen={props.setOpen} />
      </DialogContent>
    </Dialog>
  );
};

export default EditExcercideModal;
