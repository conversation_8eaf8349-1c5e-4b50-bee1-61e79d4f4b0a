import {
  <PERSON>et,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/app/components/ui/sheet";
import { Button } from "@/app/components/ui/button";
import Image from "next/image";
import { get, post } from "@/lib/fetcher";
import { useRequest } from "ahooks";
import { QuestionItem } from "@repo/core/views/tch-question-view";
import { transformQuestionData } from "@/lib/utils";
import { Input } from "@/app/components/ui/input";
import { useEffect, useRef, useState } from "react";
import { Textarea } from "@/app/components/ui/textarea";
import emitter from "@/lib/emitter";
import { Modal } from "antd";
import "./style.scss";
import Interactive from "@repo/core/components/interactive-component";

interface ExerciseProps {
  lessonId: string;
  open: boolean;
  setAllValue?: (value: string) => void;
  onClose: () => void;
  widgetIndex: string;
  refresh?: () => void;
}

const Exercise = (props: ExerciseProps) => {
  const { data, loading } = useRequest<any, [string]>(
    () =>
      get("/api/v1/lesson_widget", {
        query: { lessonId: props.lessonId },
      }),
    {
      ready: !!props.lessonId && props.open,
    }
  );

  if (loading) {
    return <div>loading...</div>;
  }

  if (!data) {
    return <div>请求参数错误，请刷新重试</div>;
  }

  const currentWidget = data.widgetList[props.widgetIndex];
  const currentInteractive = currentWidget.interactive;
  if (props.setAllValue) {
    props.setAllValue(currentWidget);
  }

  // useEffect(() => {
  // const handleUpdateExercise = async (value: string) => {
  //   await updateExercise(value);
  //   refresh();
  // };

  // const handleAddExercise = async (value: string) => {
  //   let finalValue = value;
  //   if (questionDatasRef.current) {
  //     const ids = questionDatasRef.current
  //       .map((item: any) => item.filter((q:any)=>q).map((q: any) => q.questionId).join("｜"))
  //       .join("\n");
  //     finalValue = ids + "\n" + value;
  //   }

  //   await updateExercise(finalValue);
  //   refresh();
  // };

  // const handleRefresh = () => {
  //   refresh();
  // };

  // // @ts-ignore
  // emitter.on("updateExercise", handleUpdateExercise);
  // // @ts-ignore
  // emitter.on("addExercise", handleAddExercise);
  // emitter.on("refresh", handleRefresh);

  // // 清理函数
  // return () => {
  //   // @ts-ignore
  //   emitter.off("updateExercise", handleUpdateExercise);
  //   // @ts-ignore
  //   emitter.off("addExercise", handleAddExercise);
  //   emitter.off("refresh", handleRefresh);
  // };
  // }, [refresh]);

  return (
    <div className="overflow-y-auto px-4">
      <Interactive
        url={currentInteractive.url}
        type={currentInteractive.typeName}
        onReport={(e) => {
          console.log(e);
        }}
      >
        <div>loding...</div>
      </Interactive>
    </div>
  );
};

const ExerciseSheet = (props: ExerciseProps) => {
  const [allValue, setAllValue] = useState({});

  const editAll = async () => {
    //   emitter.emit("editAll", {
    //     index: props.widgetIndex,
    //     exercise: ids,
    //   });

    emitter.emit("openEditModal", {
      ...props,
      ...allValue,
      widgetType: "interactive",
      previous: Number(props.widgetIndex) - 1,
    });
  };

  return (
    <Sheet open={props.open} onOpenChange={props.onClose}>
      <SheetContent className="w-2/3 !max-w-none gap-2 bg-gray-50">
        <SheetHeader className="gap-0 pb-0">
          <div className="flex items-center justify-between">
            <SheetTitle>互动预览</SheetTitle>
          </div>
          <SheetDescription></SheetDescription>
        </SheetHeader>
        <Exercise {...props} setAllValue={setAllValue} />
        <SheetFooter>
          <SheetClose asChild>
            <div className="flex items-center justify-end gap-2">
              <Button onClick={editAll}>编辑互动组件</Button>
            </div>
          </SheetClose>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
};

export default ExerciseSheet;
