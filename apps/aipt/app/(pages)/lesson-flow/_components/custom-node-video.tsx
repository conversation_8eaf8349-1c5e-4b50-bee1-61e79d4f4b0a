import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from "@/app/components/ui/dropdown-menu";
import emitter from "@/lib/emitter";
import { post } from "@/lib/fetcher";
import MoreImg from "@/public/more.svg";
import VideoImg from "@/public/video.svg";
import { Handle, NodeProps, Position, useReactFlow } from "@xyflow/react";
import { Modal } from "antd";
import dayjs from "dayjs";
import { useSearchParams } from "next/navigation";
import { useMemo, useState } from "react";
import { toast } from "sonner";
import EditVideoModal from "./edit-video-modal";

export default function CustomNode(props: NodeProps<any>) {
  const { getNodes } = useReactFlow();
  const searchParams = useSearchParams();
  const lessonId = Number(searchParams.get("lessonId")); // 获取查询参数
  const deleteWidget = () => {
    return post("/api/v1/lesson_widget/delete", {
      arg: { lessonId, widgetIndex: props.data.index },
    });
  };

  const moveWidget = (item: any) => {
    if (item === 0) {
      return post("/api/v1/lesson_widget/update/order", {
        arg: {
          lessonId,
          originIndex: props.data.index,
          moveToIndex: -1, // 往前移动需要加 1，往后不需要
        },
      });
    }
    const toAffter = props.data.index > item.data.index;
    return post("/api/v1/lesson_widget/update/order", {
      arg: {
        lessonId,
        originIndex: props.data.index,
        moveToIndex: toAffter ? item.data.index + 1 : item.data.index, // 往前移动需要加 1，往后不需要
      },
    });
  };

  const [modal, contextHolder] = Modal.useModal();
  const onDelete = async () => {
    if (props.data.videoInfo?.name) {
      const res = await modal.confirm({
        title: "确认删除？",
        okText: "确定",
        cancelText: "取消",
      });
      if (res) {
        try {
          await deleteWidget();
          emitter.emit("refresh");
        } catch (error: any) {
          toast.error(error.message);
        }
      }
    } else {
      try {
        await deleteWidget();
        emitter.emit("refresh");
      } catch (error: any) {
        toast.error(error.message);
      }
    }
  };

  const onMove = async (item: any) => {
    await moveWidget(item);
    emitter.emit("refresh");
  };

  const nodes = useMemo(() => getNodes(), [getNodes]);
  const [openEditExcercideModal, setOpenEditExcercideModal] = useState(false);

  return (
    <div className="min-h-30 w-[300px] rounded-md bg-white p-4 shadow-xl">
      <Handle type="target" position={Position.Left} />

      <div className="flex justify-between">
        <div className="flex gap-1">
          <VideoImg width={20} height={20} className="opacity-50" />
          {props.data.label}
        </div>

        <div className="flex gap-1">
          {props.data?.videoInfo?.duration > 0 && (
            <div className="rounded-sm bg-gray-200 px-2 py-1 text-xs text-gray-500">
              {dayjs(props.data.videoInfo.duration * 1000).format("mm分ss秒")}
            </div>
          )}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <MoreImg
                width={20}
                height={20}
                className="cursor-pointer opacity-50"
              />
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-20 bg-white">
              <DropdownMenuGroup>
                <DropdownMenuItem onClick={onDelete}>删除</DropdownMenuItem>
              </DropdownMenuGroup>
              <DropdownMenuGroup>
                <DropdownMenuSub>
                  <DropdownMenuSubTrigger>移动至</DropdownMenuSubTrigger>
                  <DropdownMenuSubContent
                    className="w-20 bg-white"
                    sideOffset={5}
                    alignOffset={-5}
                  >
                    <DropdownMenuItem key={0} onClick={() => onMove(0)}>
                      首位
                    </DropdownMenuItem>
                    {nodes.map((item: any) => {
                      return (
                        <DropdownMenuItem
                          key={item.id}
                          onClick={() => onMove(item)}
                        >
                          {item.data.label}{" "}
                          <span className="text-sm text-gray-500">之后</span>
                        </DropdownMenuItem>
                      );
                    })}
                  </DropdownMenuSubContent>
                </DropdownMenuSub>
              </DropdownMenuGroup>
              <DropdownMenuGroup>
                <DropdownMenuItem
                  onClick={() => setOpenEditExcercideModal(true)}
                >
                  编辑
                </DropdownMenuItem>
              </DropdownMenuGroup>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
      <div className="mt-6 flex">
        <div className="w-28"> 名称：</div>

        <div className="rounded-sm bg-gray-200 px-2 py-1 text-xs text-gray-500">
          {props.data.videoInfo?.name || "暂未上传"}
        </div>
      </div>
      <div className="mt-2 flex">
        <div className="w-28"> 最近更新时间：</div>

        <div className="rounded-sm bg-gray-200 px-2 py-1 text-xs text-gray-500">
          {dayjs(props.data.lastUpdateTime * 1000).format(
            "YYYY-MM-DD HH:mm:ss"
          )}
        </div>
      </div>
      <EditVideoModal
        open={openEditExcercideModal}
        setOpen={setOpenEditExcercideModal}
        {...props}
      />
      <Handle type="source" position={Position.Right} />
      <div>{contextHolder}</div>
    </div>
  );
}
