import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from "@/app/components/ui/dropdown-menu";
import emitter from "@/lib/emitter";
import { post } from "@/lib/fetcher";
import LibImg from "@/public/lib.svg";
import MoreImg from "@/public/more.svg";
import { Handle, NodeProps, Position, useReactFlow } from "@xyflow/react";
import { useSearchParams } from "next/navigation";
import { useMemo, useState } from "react";
import { toast } from "sonner";
import EditExcercideModal from "./edit-excercide-modal";

export default function CustomNode(props: NodeProps<any>) {
  const { getNodes } = useReactFlow();
  const searchParams = useSearchParams();
  const lessonId = Number(searchParams.get("lessonId")); // 获取查询参数
  const deleteWidget = () => {
    return post("/api/v1/lesson_widget/delete", {
      arg: { lessonId, widgetIndex: props.data.index },
    });
  };

  const moveWidget = (item: any) => {
    return post("/api/v1/lesson_widget/delete", {
      arg: {
        lessonId,
        originIndex: props.data.index,
        moveToIndex: item.data.index + 1,
      },
    });
  };

  const onDelete = async () => {
    try {
      await deleteWidget();
      emitter.emit("refresh");
    } catch (error: any) {
      toast.error(error.message);
    }
  };

  const onMove = async (item: any) => {
    await moveWidget(item);
    emitter.emit("refresh");
  };

  const onEdit = () => {
    // console.log(props);
  };

  const nodes = useMemo(() => getNodes(), [getNodes]);

  const [openEditExcercideModal, setOpenEditExcercideModal] = useState(false);

  return (
    <div className="min-w-70 min-h-30 rounded-md bg-white p-4 shadow-xl">
      <Handle type="target" position={Position.Left} />
      <div className="flex justify-between">
        <div className="flex gap-1">
          <LibImg width={20} height={20} className="opacity-50" />
          {props.data.label}
        </div>
        <div className="flex gap-1">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <MoreImg
                width={20}
                height={20}
                className="cursor-pointer opacity-50"
              />
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-20">
              <DropdownMenuGroup>
                <DropdownMenuItem onClick={onDelete}>删除</DropdownMenuItem>
              </DropdownMenuGroup>
              <DropdownMenuGroup>
                <DropdownMenuSub>
                  <DropdownMenuSubTrigger>移动</DropdownMenuSubTrigger>
                  <DropdownMenuSubContent
                    className="w-20"
                    sideOffset={5}
                    alignOffset={-5}
                  >
                    {nodes.map((item: any) => {
                      return (
                        <DropdownMenuItem
                          key={item.id}
                          onClick={() => onMove(item)}
                        >
                          {item.data.label}
                        </DropdownMenuItem>
                      );
                    })}
                  </DropdownMenuSubContent>
                </DropdownMenuSub>
              </DropdownMenuGroup>
              <DropdownMenuGroup>
                <DropdownMenuItem
                  onClick={() => setOpenEditExcercideModal(true)}
                >
                  编辑
                </DropdownMenuItem>
              </DropdownMenuGroup>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
      <div className="mt-6 flex">
        模板名称：
        <div className="rounded-sm bg-gray-200 px-2 py-1 text-xs text-gray-500">
          {props.data.desc}
        </div>
      </div>
      <EditExcercideModal
        open={openEditExcercideModal}
        setOpen={setOpenEditExcercideModal}
        {...props}
      />
      <Handle type="source" position={Position.Right} />
    </div>
  );
}
