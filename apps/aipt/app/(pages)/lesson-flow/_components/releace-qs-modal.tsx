import { useState } from "react";
import Image from "next/image";
import { But<PERSON> } from "@/app/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  DialogHeader,
  <PERSON>alogTitle,
  DialogClose,
} from "@/app/components/ui/dialog";
import { post, get } from "@/lib/fetcher";
import { useSearchParams } from "next/navigation";
import { toast } from "sonner";
import emitter from "@/lib/emitter";
import { Input } from "@/app/components/ui/input";
import { uploadFile } from "@/lib/uploader";
import { useEffect } from "@preact-signals/safe-react/react";

interface EditExcercideProps {
  onSubmit: () => void;
  setOpen: (open: boolean) => void;
  [key: string]: any;
}

// ids,
//         index

const EditExcercideModal = (props: any) => {
  // const handleReplace = async (itemGroup: any, index: number) => {
  //   const ids = questionDatas
  //     .map((item: any) => item.filter(item=>item).map((q: any) => q.questionId).join("｜"))
  //     .join("\n");
  //   // 根据指定行数替换
  //   const lines = ids.split("\n");
  //   lines[index] = replaceQuestionIds;
  //   const newIds = lines.join("\n");
  //   await updateExercise(newIds);
  //   refresh();
  // };

  const [replaceQuestionIds, setReplaceQuestionIds] = useState("");
  // const handleReplace = async (itemGroup: any, index: number) => {
  //   const ids = questionDatas
  //     .map((item: any) => item.filter(item=>item).map((q: any) => q.questionId).join("｜"))
  //     .join("\n");
  //   // 根据指定行数替换
  //   const lines = ids.split("\n");
  //   lines[index] = replaceQuestionIds;
  //   const newIds = lines.join("\n");
  //   await updateExercise(newIds);
  //   refresh();
  // };

  const updateExercise = async (exercise: string) => {
    return post("/api/v1/lesson_widget/update/exercise", {
      arg: {
        lessonId: Number(props.lessonId),
        widgetIndex: props.widgetIndex,
        exercise,
      },
    });
  };

  useEffect(() => {
    if (props.ids) {
      const lines = props.ids.split("\n");
      setDataultVal(lines[props.index]);
    }
  }, [props.ids]);

  const [dataultVal, setDataultVal] = useState("");

  const onOk = async () => {
    const lines = props.ids.split("\n");
    lines[props.index] = dataultVal;
    const newIds = lines.join("\n");
    await updateExercise(newIds);
    props.setOpen(false);
    emitter.emit('refresh')
  };

  return (
    <Dialog open={props.open} onOpenChange={props.setOpen}>
      <DialogContent className="bg-white">
        <DialogHeader>
          <DialogTitle>更换题目</DialogTitle>
        </DialogHeader>
        <div>
          <Input value={dataultVal} onChange={e=>setDataultVal(e.target.value)}/>
        </div>
        <div className="flex justify-end gap-2">
          <Button variant="outline" onClick={() => props.setOpen(false)}>
            取消
          </Button>
          <Button onClick={onOk}>确认</Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default EditExcercideModal;
