"use client";
import { Watermark } from "@/app/components/watermark";
import { useDailyWatermarkText } from "@/app/hooks/use-daily-watermark-text";
import fetcher from "@/app/utils/fetcher";
import { SWRConfig } from "swr";
import { Suspense } from "react";

export default function Layout({ children }: { children: React.ReactNode }) {
  // 获取全局水印文本（格式：AI产课+当天第一次访问的时间戳后4位）
  const watermarkText = useDailyWatermarkText();

  return (
    <SWRConfig
      value={{
        fetcher,
        refreshInterval: 0,
        onError: (error, key) => {
          console.error(error, key);
          // toast.error(`操作失败: ${key} ${error.toString()}`);
        },
      }}
    >
      {/* 全局水印，覆盖所有页面内容 */}
      <Watermark text={watermarkText} />
      <div className="min-h-screen w-full overflow-hidden">
        <Suspense>{children}</Suspense>
      </div>
    </SWRConfig>
  );
}
