export interface Option {
  value?: string;
  label?: string;
  isLeaf?: boolean;
  children?: Option[];
  loading?: boolean;
  bizTreeId?: string;
  bizTreeName?: string;
  bizTreeNodeId?: string;
  bizTreeNodeName?: string;
}

export interface BizTreeNode {
  bizTreeId: string;
  bizTreeNodeId: string;
  bizTreeNodeName: string;
  bizTreeNodeParentPath: string[];
  bizTreeDetail: {
    bizTreeNodeId: string;
    bizTreeNodeName: string;
    bizTreeNodeParentPath: string[];
  }[];
  bizTreeNodeChildren?: BizTreeNode[];
}

export interface BizTreeDetail {
  bizTreeDetail: {
    bizTreeNodeChildren: BizTreeNode[];
  };
}

export interface CreationProps {
  modal: string; // "edit" | "create" | "detail";
  lessonId?: string;
  open: boolean;
  setOpen: (open: boolean) => void;
  refresh: () => void;
}

export interface LessonDetail {
  lessonName: string;
  guideWidgetSetId?: string;
  bizTreeNodeList: BizTreeNode[];
  // [key: string]: any;
}

export interface PhaseAndSubject {
  phase: number | undefined;
  subject: number | undefined;
}


export interface GuideWidgetSetInfo {
    phase: number;
    subject: number;
    lessonType: number;
    digitalAvatar: string;
  }