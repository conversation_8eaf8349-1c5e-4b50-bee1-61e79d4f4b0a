import { siteUrl } from "@/lib/env-helper";

export const parseQueryString = (query: string) => {
  const params = new URLSearchParams(query);
  const result: Record<string, string> = {};
  params.forEach((value, key) => {
    result[key] = value;
  });
  return result;
};

// 从 url 中获取 token 并保存到 localStorage
export const saveTokenFormUrl = () => {
  if (typeof window === "undefined") return;
  const { search } = window.location;
  const query = parseQueryString(search);
  const { token } = query;
  if (token) {
    localStorage.setItem("token", token);
  }
};
// 移除 url 中的指定参数
export const removeFieldFromUrl = (url: string, field: string) => {
  const urlObj = new URL(url);
  urlObj.searchParams.delete(field);
  return urlObj.toString();
};

function hasQueryParams(url: string) {
  try {
    const urlObj = new URL(url);
    return urlObj.search.length > 1; // search 包含问号，所以长度大于1表示有参数
  } catch (e) {
    console.error("Invalid URL");
    return false;
  }
}

// 获取登录 url
const loginUrl = siteUrl + "/user/login";
export const getLoginUrl = (isRoot = false, error = "") => {
  if (typeof window === "undefined") return "";
  if (isRoot) {
    const oldUrl = window.location.origin;
    const isHasQueryParams = hasQueryParams(oldUrl);
    const redirect = encodeURIComponent(
      `${oldUrl}${isHasQueryParams ? "&" : "?"}error=${error}`
    );

    return `${loginUrl}?redirect=${redirect}`;
  }
  const oldUrl = window.location.href;
  const isHasQueryParams = hasQueryParams(oldUrl);
  const url = removeFieldFromUrl(oldUrl, "token");
  const redirect = encodeURIComponent(
    `${url}${isHasQueryParams ? "&" : "?"}error=${error}`
  );
  return `${loginUrl}?redirect=${redirect}`;
};
