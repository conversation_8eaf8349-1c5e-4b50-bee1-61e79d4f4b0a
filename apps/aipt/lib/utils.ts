import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";
import { v4 as uuid } from "uuid";
import { Position as FlowPosition, Node } from "@xyflow/react";

export const cn = (...inputs: ClassValue[]) => twMerge(clsx(inputs));

/**
 * 从URL中提取文件名
 * @param url 文件URL
 * @param withExtension 是否包含文件后缀，默认为true
 * @returns 文件名
 * @example
 * getFilenameFromUrl("https://example.com/path/to/file.mp4") // "file.mp4"
 * getFilenameFromUrl("https://example.com/path/to/file.mp4", false) // "file"
 */
export const getFilenameFromUrl = (
  url: string,
  withExtension: boolean = true
): string => {
  if (!url) return "";

  // 获取URL中的文件名部分
  const filename = url.split("/").pop() || "";

  if (!withExtension) {
    // 移除文件后缀
    return filename.split(".").slice(0, -1).join(".");
  }

  return filename;
};

interface NodeData {
  id: string;
  [key: string]: any;
}

interface NodePosition {
  x: number;
  y: number;
}

// 使用示例
// const nodes = [{id:'a'}, {id:'b'}, {id:'c'}];
// const connections = generateConnections(nodes);
export function genConnections(
  nodes: NodeData[]
): { id: string; source: string; target: string; type: string }[] {
  if (!Array.isArray(nodes) || nodes.length < 2) {
    return [];
  }
  const connections = [];
  for (let i = 0; i < nodes.length - 1; i++) {
    const current = nodes[i]!;
    const next = nodes[i + 1]!;
    connections.push({
      id: `${current.id}->${next.id}`,
      source: current.id,
      target: next.id,
      type: "custom-edge",
    });
  }
  return connections;
}

const getNodeLabel: { [key: string]: string } = {
  guide: "Part",
  exercise: "练习",
  video: "视频",
  interactive: "互动",
};

export const genNodes = (widgetList: any[]): Node[] => {
  // Keep track of counts for each type
  const typeCounts: { [key: string]: number } = {};

  const nodes = widgetList.map((item: any, index: number) => {
    // Initialize count for this type if not exists
    if (!typeCounts[item.lessonWidgetType]) {
      typeCounts[item.lessonWidgetType] = 0;
    }
    // Increment count for this type
    typeCounts[item.lessonWidgetType]!++;

    return {
      id: uuid(),
      data: {
        label: `${getNodeLabel[item.lessonWidgetType]}${typeCounts[item.lessonWidgetType]}`,
        desc: item.lessonWidgetName,
        ...item,
        index,
      },
      position: calculateNodePosition(index, item.lessonWidgetType, widgetList),
      sourcePosition: FlowPosition.Right,
      targetPosition: FlowPosition.Left,
      type: item.lessonWidgetType,
    };
  });
  return nodes;
};

const genTemplateNode = (widgetList: any[]): Node => {
  // 先创建模板节点
  const templateNode = {
    id: uuid(),
    type: "tmpl",
    data: {
      label: "UI 模板",
      desc: "高中数学-模板1",
    },
    position: {
      x: 100,
      y: 40,
    },
    sourcePosition: FlowPosition.Right,
    targetPosition: FlowPosition.Left,
  };
  return templateNode;
};

export function mergeArraysByFirstElement(
  arr: any[][]
): { bizTreeId: string; bizTreeNodeIds: string[] }[] {
  const resultMap = new Map<string, string[]>();
  // 遍历每个子数组
  arr.forEach((subArray) => {
    const firstElement = subArray[0];
    const lastElement = subArray[subArray.length - 1];
    // 如果Map中已有这个第一个元素，则添加最后一个元素
    if (resultMap.has(firstElement)) {
      resultMap.get(firstElement)?.push(lastElement);
    }
    // 否则创建新条目
    else {
      resultMap.set(firstElement, [lastElement]);
    }
  });
  // 转换为目标格式
  return Array.from(resultMap.entries()).map(([bizTreeId, bizTreeNodeIds]) => ({
    bizTreeId,
    bizTreeNodeIds,
  }));
}

export const calculateNodePosition = (
  index: number,
  nodeType: string,
  widgetList: any[]
): NodePosition => {
  const nodeWidths = {
    exercise: 300,
    video: 300,
    interactive: 300,
    guide: 180,
  };
  const nodeYpos = {
    exercise: 600,
    video: 600,
    interactive: 600,
    guide: 300,
  };
  // 基础配置
  const baseX = 100; // 起始X坐标
  const minSpacing = 50; // 最小间距
  if (index === 0) {
    return { x: baseX, y: nodeYpos[nodeType as keyof typeof nodeYpos] };
  }
  // 获取前一个节点
  const prevNode = widgetList[index - 1];
  const prevNodeType = prevNode.lessonWidgetType;
  const prevNodeWidth = nodeWidths[prevNodeType as keyof typeof nodeWidths];
  // 计算前一个节点的位置
  const prevPosition = calculateNodePosition(
    index - 1,
    prevNodeType,
    widgetList
  );
  // 计算X坐标：前一个节点的位置 + 前一个节点的宽度 + 间距
  const x = prevPosition.x + prevNodeWidth + minSpacing;
  return { x, y: nodeYpos[nodeType as keyof typeof nodeYpos] };
};

// 将后端返回的题目数据转换为前端所需格式（教师端的题目数据格式）
export function transformQuestionData(backendData: any) {
  return {
    questionId: backendData.questionId,
    content: backendData.questionContent.questionStem,
    questionTags: [], // 需要从其他字段获取或留空
    questionType: backendData.questionType, // 用不到
    answer:
      backendData.questionAnswer.answerOptionList
        ?.map((item: any) => item.optionKey)
        .join(",") ?? [],
    questionAnswer: {
      // 用不到
      answerOptionList:
        backendData.questionAnswer.answerOptionList?.map((item: any) => ({
          optionKey: item.optionKey,
          optionVal: item.optionVal,
        })) ?? [],
    }, // 用不到
    answerExplain: backendData.questionExplanation || "", // 用不到
    avgCostTime: backendData.estimatedDuration || 0, // 本地所需花费时间
    resourceId: backendData.originPaperId || "", // 用不到
    resourceType: 1, // 根据实际情况调整
    answerDetails: [], // 用不到
    options: backendData.questionContent.questionOptionList?.map(
      (item: any) => ({
        // 用不到
        key: item.optionKey,
        content: item.optionVal,
      })
    ),
  };
}

// 将后端返回的题目数据转换为前端所需格式（学生端的题目数据格式）
export function transformQuestionData2(backendData: any) {
  return {
    questionId: backendData.questionId,
    questionContent: backendData.questionContent.questionStem,
    questionTags: [], // 需要从其他字段获取或留空
    questionType: backendData.questionType, // 用不到
    answer:
      backendData.questionAnswer.answerOptionList
        ?.map((item: any) => item.optionKey)
        .join(",") ?? [],
    questionAnswer: {
      // 用不到
      answerOptionList:
        backendData.questionAnswer.answerOptionList?.map((item: any) => ({
          optionKey: item.optionKey,
          optionVal: item.optionVal,
        })) ?? [],
    }, // 用不到
    answerExplain: backendData.questionExplanation || "", // 用不到
    avgCostTime: backendData.estimatedDuration || 0, // 本地所需花费时间
    resourceId: backendData.originPaperId || "", // 用不到
    resourceType: 1, // 根据实际情况调整
    answerDetails: [], // 用不到
    options: backendData.questionContent.questionOptionList.map(
      (item: any) => ({
        // 用不到
        key: item.optionKey,
        content: item.optionVal,
      })
    ),
  };
}
