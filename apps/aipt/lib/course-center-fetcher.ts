import { courseCenterApiBase } from './env-helper';
import { getLoginUrl } from './login-helper';

interface Response<T> {
  code: number;
  data: T;
  message: string;
}

const apiHost = courseCenterApiBase;
const fetcher = async <T>(
  url: string | URL | globalThis.Request,
  init?: RequestInit
) => {
  if (!apiHost) {
    throw new Error("API_HOST 未设置, 需在.env.local中设置");
  }

  // 这里检查 token（或跳转登录）
  const token = localStorage.getItem('token');
  if(!token){
    const loginUrl = getLoginUrl();
    location.href = loginUrl;
  }

  // eslint-disable-next-line no-useless-catch
  try {
    const deviceId = '';
    const response = await fetch(`${apiHost}${url}`, {
      ...init,
      headers: {
        ...init?.headers,
        device_id: deviceId,
        Authorization: `Bearer ${token}`
      },
    });
    if(response.status === 401){
      const loginUrl = getLoginUrl();
      location.href = loginUrl;
    }
    if (!response.ok) {
      const error = new Error("网络请求失败");
      // 将额外的信息附加到错误对象上。
      error.message = await response.json();
      throw error;
    }
    const res = (await response.json()) as Response<T>;
    const { code, message, data } = res;
    // console.log(url, code, data);
    if (code !== 0) {
      throw new Error(message);
    }
    return data as T;
  } catch (err) {
    throw err;
  }
};

export async function get<T>(
  url: string,
  { query }: { query?: Record<string, string> }
) {
  const params = query ? new URLSearchParams(query) : undefined;
  return await fetcher<T>(`${url}?${params?.toString()}`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
    },
  });
}
export async function post<T>(url: string, { arg }: { arg?: object }) {
  return await fetcher<T>(url, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: arg ? JSON.stringify(arg) : undefined,
  });
}

export default fetcher;
