{"audioJson": {"audioUrl": "https://video.xiaoluxue.cn/www/audios/opening.mp3", "sentenceList": [{"audioSentenceText": "这是第一句话", "audioSentenceUrl": "https://video.xiaoluxue.cn/www/audios/transition1.mp3"}, {"audioSentenceText": "这是第二句话", "audioSentenceUrl": "https://video.xiaoluxue.cn/www/audios/transition1.mp3"}]}, "boardscript": "## 公式\nThe lift coefficient ($$C_L$$) is a <mark data-type=\"underline\">dimensionless</mark> coefficient. \n\n奥术<mark>大师</mark>$$N_b$$", "flowRunType": 1, "taskId": "1234567890", "guideWidgetId": 1001, "guideWidgetName": "高中数学-集合的基本性质", "guideWidgetSetId": 5001, "guideWidgetStatus": "finish", "audioJsonDraft": {"audioUrl": "https://video.xiaoluxue.cn/www/audios/opening.mp3", "sentenceList": [{"audioSentenceText": "这是当前正在处理的句子", "audioSentenceUrl": "https://example.com/audio/current.mp3", "index": 1, "isCompleted": false}]}, "transcript": "The lift coefficient ($$C_L$$) is a dimensionless coefficient.", "videoGenTime": 1677649200000, "videoJson": "{\"title\":\"React组件开发\",\"avatar\":{\"url\":\"/videos/teacher-avatar.mp4\",\"width\":1920,\"height\":1080,\"fps\":30,\"durationInFrames\":3600},\"subtitles\":[{\"id\":1,\"text\":\"今天我们来学习React组件开发的基础知识\",\"inFrame\":0,\"outFrame\":120},{\"id\":2,\"text\":\"首先我们来看看组件的基本结构\",\"inFrame\":120,\"outFrame\":240}],\"content\":[{\"tag\":\"h1\",\"level\":1,\"content\":[{\"tag\":\"bold\",\"content\":\"React组件开发基础\",\"inFrame\":0,\"outFrame\":120}],\"inFrame\":0,\"outFrame\":120},{\"tag\":\"h2\",\"level\":2,\"content\":[{\"tag\":\"normal\",\"content\":\"1. 组件的概念\",\"inFrame\":120,\"outFrame\":240}],\"inFrame\":120,\"outFrame\":240},{\"tag\":\"ul\",\"level\":1,\"content\":[{\"tag\":\"normal\",\"content\":\"可重用的UI单元\",\"inFrame\":240,\"outFrame\":360,\"notation\":{\"type\":\"highlight\",\"color\":\"#ffeb3b\",\"strokeWidth\":2,\"inFrame\":260,\"duration\":30}}],\"inFrame\":240,\"outFrame\":360},{\"tag\":\"ul\",\"level\":1,\"content\":[{\"tag\":\"normal\",\"content\":\"独立的功能模块\",\"inFrame\":360,\"outFrame\":480}],\"inFrame\":360,\"outFrame\":480},{\"tag\":\"h2\",\"level\":2,\"content\":[{\"tag\":\"normal\",\"content\":\"2. 组件的类型\",\"inFrame\":480,\"outFrame\":600}],\"inFrame\":480,\"outFrame\":600},{\"tag\":\"ul\",\"level\":1,\"content\":[{\"tag\":\"bold\",\"content\":\"函数组件\",\"inFrame\":600,\"outFrame\":720,\"notation\":{\"type\":\"box\",\"color\":\"#4caf50\",\"strokeWidth\":2,\"inFrame\":620,\"duration\":30}}],\"inFrame\":600,\"outFrame\":720},{\"tag\":\"ul\",\"level\":1,\"content\":[{\"tag\":\"bold\",\"content\":\"类组件\",\"inFrame\":720,\"outFrame\":840,\"notation\":{\"type\":\"box\",\"color\":\"#2196f3\",\"strokeWidth\":2,\"inFrame\":740,\"duration\":30}}],\"inFrame\":720,\"outFrame\":840},{\"tag\":\"h2\",\"level\":2,\"content\":[{\"tag\":\"normal\",\"content\":\"3. Props特性\",\"inFrame\":840,\"outFrame\":960}],\"inFrame\":840,\"outFrame\":960},{\"tag\":\"ul\",\"level\":1,\"content\":[{\"tag\":\"normal\",\"content\":\"只读性\",\"inFrame\":960,\"outFrame\":1080,\"notation\":{\"type\":\"circle\",\"color\":\"#f44336\",\"strokeWidth\":2,\"inFrame\":980,\"duration\":30}}],\"inFrame\":960,\"outFrame\":1080},{\"tag\":\"ul\",\"level\":1,\"content\":[{\"tag\":\"normal\",\"content\":\"单向数据流\",\"inFrame\":1080,\"outFrame\":1200}],\"inFrame\":1080,\"outFrame\":1200},{\"tag\":\"h2\",\"level\":2,\"content\":[{\"tag\":\"normal\",\"content\":\"4. State管理\",\"inFrame\":1200,\"outFrame\":1320}],\"inFrame\":1200,\"outFrame\":1320},{\"tag\":\"ul\",\"level\":1,\"content\":[{\"tag\":\"normal\",\"content\":\"useState Hook\",\"inFrame\":1320,\"outFrame\":1440,\"notation\":{\"type\":\"underline\",\"color\":\"#9c27b0\",\"strokeWidth\":2,\"inFrame\":1340,\"duration\":30}}],\"inFrame\":1320,\"outFrame\":1440},{\"tag\":\"ul\",\"level\":1,\"content\":[{\"tag\":\"normal\",\"content\":\"setState方法\",\"inFrame\":1440,\"outFrame\":1560}],\"inFrame\":1440,\"outFrame\":1560},{\"tag\":\"h2\",\"level\":2,\"content\":[{\"tag\":\"normal\",\"content\":\"5. 生命周期\",\"inFrame\":1560,\"outFrame\":1680}],\"inFrame\":1560,\"outFrame\":1680},{\"tag\":\"ul\",\"level\":1,\"content\":[{\"tag\":\"bold\",\"content\":\"挂载阶段\",\"inFrame\":1680,\"outFrame\":1800,\"notation\":{\"type\":\"bracket\",\"color\":\"#ff9800\",\"strokeWidth\":2,\"inFrame\":1700,\"duration\":30}}],\"inFrame\":1680,\"outFrame\":1800},{\"tag\":\"ul\",\"level\":1,\"content\":[{\"tag\":\"bold\",\"content\":\"更新阶段\",\"inFrame\":1800,\"outFrame\":1920}],\"inFrame\":1800,\"outFrame\":1920},{\"tag\":\"ul\",\"level\":1,\"content\":[{\"tag\":\"bold\",\"content\":\"卸载阶段\",\"inFrame\":1920,\"outFrame\":2040}],\"inFrame\":1920,\"outFrame\":2040},{\"tag\":\"h2\",\"level\":2,\"content\":[{\"tag\":\"normal\",\"content\":\"6. 性能优化\",\"inFrame\":2040,\"outFrame\":2160}],\"inFrame\":2040,\"outFrame\":2160},{\"tag\":\"ul\",\"level\":1,\"content\":[{\"tag\":\"normal\",\"content\":\"React.memo\",\"inFrame\":2160,\"outFrame\":2280,\"notation\":{\"type\":\"highlight\",\"color\":\"#8bc34a\",\"strokeWidth\":2,\"inFrame\":2180,\"duration\":30}}],\"inFrame\":2160,\"outFrame\":2280},{\"tag\":\"ul\",\"level\":1,\"content\":[{\"tag\":\"normal\",\"content\":\"useMemo/useCallback\",\"inFrame\":2280,\"outFrame\":2400,\"notation\":{\"type\":\"highlight\",\"color\":\"#8bc34a\",\"strokeWidth\":2,\"inFrame\":2300,\"duration\":30}}],\"inFrame\":2280,\"outFrame\":2400}]}"}