{"avatar": {"url": "https://static.test.xiaoluxue.cn/algo/demo2/course/3ccd5687e48d4bf1fbd5ad8ee7a4c06f/video/323a2447c0534f7fb00affaf26b44473.mp4", "width": 1280, "height": 720, "fps": 30, "durationInFrames": 1200}, "subtitles": [{"start": 0, "end": 5, "text": "大家好，欢迎来到我们的编程课堂"}, {"start": 5, "end": 10, "text": "今天我们将学习JavaScript的基础知识"}, {"start": 10, "end": 15, "text": "让我们开始吧！"}], "title": "JavaScript基础入门", "content": [{"tag": "h1", "level": 1, "content": [{"tag": "normal", "content": "JavaScript基础入门", "inFrame": 0, "outFrame": 120}], "inFrame": 0, "outFrame": 120}, {"tag": "default", "level": 1, "content": [{"tag": "normal", "content": "JavaScript是一种广泛应用于网页开发的编程语言", "inFrame": 15, "outFrame": 30}], "inFrame": 15, "outFrame": 30}, {"tag": "h2", "level": 1, "content": [{"tag": "bold", "content": "变量声明", "inFrame": 30, "outFrame": 60, "notation": {"type": "underline", "color": "#ff0000", "strokeWidth": 2, "inFrame": 32, "duration": 10, "outFrame": 60}}], "inFrame": 30, "outFrame": 60}, {"tag": "ul", "level": 1, "content": [{"tag": "normal", "content": "使用 var 声明变量（旧方式）", "inFrame": 40, "outFrame": 50}], "inFrame": 40, "outFrame": 50}, {"tag": "ul", "level": 1, "content": [{"tag": "normal", "content": "使用 let 声明变量（推荐）", "inFrame": 50, "outFrame": 60}], "inFrame": 50, "outFrame": 60}, {"tag": "ul", "level": 1, "content": [{"tag": "normal", "content": "使用 const 声明常量", "inFrame": 60, "outFrame": 70}], "inFrame": 60, "outFrame": 70}, {"tag": "h2", "level": 1, "content": [{"tag": "bold", "content": "数据类型", "inFrame": 95, "outFrame": 120, "notation": {"type": "box", "color": "#0000ff", "strokeWidth": 2, "inFrame": 97, "duration": 10, "outFrame": 120}}], "inFrame": 95, "outFrame": 120}, {"tag": "ol", "level": 1, "content": [{"tag": "normal", "content": "字符串 (String)", "inFrame": 100, "outFrame": 105}], "inFrame": 100, "outFrame": 105, "pic": {"url": "https://gil-test.oss-cn-beijing.aliyuncs.com/guide_widget/audio_sentence/url/corner_red_1747297634.png", "width": 120, "height": 120}}, {"tag": "ol", "level": 1, "content": [{"tag": "normal", "content": "数字 (Number)", "inFrame": 105, "outFrame": 110}], "inFrame": 105, "outFrame": 110}, {"tag": "block", "level": 1, "content": [{"tag": "ol", "level": 2, "content": [{"tag": "normal", "content": "布尔值 (Bo<PERSON><PERSON>)", "inFrame": 110, "outFrame": 115}], "inFrame": 110, "outFrame": 115}], "inFrame": 115, "outFrame": 120, "width": "80%", "pic": {"url": "https://gil-test.oss-cn-beijing.aliyuncs.com/guide_widget/audio_sentence/url/corner_red_1747297634.png", "width": 120, "height": 120}}, {"tag": "ol", "level": 1, "content": [{"tag": "normal", "content": "布尔值 (Bo<PERSON><PERSON>)", "inFrame": 110, "outFrame": 115}], "inFrame": 110, "outFrame": 115}]}